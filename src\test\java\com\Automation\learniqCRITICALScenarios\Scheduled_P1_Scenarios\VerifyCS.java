package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_RecordDocumentReading;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Offline RE type Session without assessment for scheduled course and
 * make atleast one employee should be pending at record document reading and
 * one should be qualified by viewing Individual employee report at each
 * transaction starting from course session.
 */

public class VerifyCS extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/RE_OFF_WITHOUT_ASSESSMENT.xlsx";

	public VerifyCS() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)

	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.masterPlant();
		// epiclogin.plant1();

		InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
				Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.navigateTolearnIQPlant();

		InitiateTopic.topic_Registration(testData);

		//
	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}
		epiclogin.navigateToMasterPlant();
		// epiclogin.plant1();

		Initiate_Course.courseConfiguration_Reg(testData);

		epiclogin.navigateTolearnIQPlant();

		Initiate_Course.Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Configuration, Modification, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Configuration, Modification, Approve with Audit Trails");
		}

		TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);

	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		// Compare Outisde and Inside To be Planned count at CS Screen
		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession,
				CM_VerifyCourseSessionScreen.beforeAnySessionInsideCount,
				"Outside To Be Planned Count at CourseSession Before Any Session",
				"Inside To Be Planned Count at CourseSession Before Any Session");

		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession,
				CM_VerifyCourseSessionScreen.totalTraineesCountBeforeSession,
				"Outside To Be Planned Count at CourseSession Before Any Session",
				"Outside Total Traines Count Before Sesion");
		
		compareCount(CM_VerifyCourseSessionScreen.totalTraineesCountBeforeSession,
				CM_VerifyCourseSessionScreen.totalTraineeInsideCountBeforeSession,
				"Outside Total Traines Count at CourseSession Before Any Session",
				"Inside Total Traines Count at CourseSession Before Any Session");
		
		
		CSRReport.TBPCSRReport();

		// Compare Outisde and Inside To be Planned count at Course Session Report
		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore, CM_CSRReport.tobePlannedinsideCountBefore,
				"Outside To Be Planned Count at Course Session Report Before Any Session",
				"Inside To Be Planned Count at CourseSession Report Before Any Session");

		// Data between Course Session Screen and Course Session Report Before Session
		// for To be Planned Count

		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataBeforeSession, CM_CSRReport.combinedArrayBefore,
				"CourseSession To be Planned Count Before", "Course Session Report To be Plannd Count Before Session");

		// Propose Session
		verifyCSScreen.courseSession_Offline_DocumentReading(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		// Cheeck that Planned users are not displayed in Course Session To b Planned
		// List

		checkCommonElements(CM_VerifyCourseSessionScreen.columnDataAfterSession,
				CM_VerifyCourseSessionScreen.selectedEmployees, "Employees in To Be planned After Session",
				"Selected Employees");

		// Compare the To Be planned Count in Course Session with Previous Count minus
		// Selected Candidates

		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession - CM_VerifyCourseSessionScreen.SessionProposedFor,
				CM_VerifyCourseSessionScreen.AfterAnySession, "Course session before minus Selected employees Count",
				"After Course Session To be Planned Count");

		// Compare Course Session To Be Planned Count with Inside Count After Course
		// Session
		compareCount(CM_VerifyCourseSessionScreen.AfterAnySession,
				CM_VerifyCourseSessionScreen.AfterAnySessionInsideCount,
				"Outside To Be Planned Count at CourseSession After Session Proposal",
				"Inside To Be Planned Count at CourseSession After Session Session");

		// Open Report
		CSRReport.TBPCSRReport();

		// Cheeck that Planned users are not displayed in Course Session Report To b
		// Planned List

		checkCommonElements(CM_CSRReport.combinedArrayAfter, CM_VerifyCourseSessionScreen.selectedEmployees,
				"Employees in To Be planned After Session", "Selected Employees");

		// Compare the To Be planned Count in Course Session Report with Previous Count
		// minus Selected Candidates
		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore - CM_VerifyCourseSessionScreen.SessionProposedFor,
				CM_CSRReport.toBePlannedOutSideCountAfter,
				"Course session Report before minus Selected employees Count",
				"After Course Session To be Planned Count");

		// Compare Course Session Data with Course Session Report Data After Session
		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataAfterSession, CM_CSRReport.combinedArrayAfter,
				"CourseSession To be Planned Data After Session",
				"Course Session Report To be Plannd Data After Session");

	}

	// Test Method for Record Document Reading

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void RecordDocumentReading(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Record Document Reading").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Document Reading");
		}
		RecordDR.NewRecordDocumentReading();
		verifyCSScreen.OpenCourseSessionScreen(testData, "AfterRecordDR");
		CSRReport.QualifiedCount();
		compareCount(CM_RecordDocumentReading.ActualQualifiedCount,
				CM_CSRReport.qualifiedCountDisplayedAtCourseSessionReport, "Actual Qualified Count",
				"Qualified Count at CourseSession Report");
		compareCount(CM_CSRReport.qualifiedCountDisplayedAtCourseSessionReport,
				CM_CSRReport.QualifiedCountInsideCourseSessionReport, "Qualified Count Outside at CourseSession Report",
				"Qualified Count Inside at CourseSession Report");
		Logout.signOutPage();

	}

}
