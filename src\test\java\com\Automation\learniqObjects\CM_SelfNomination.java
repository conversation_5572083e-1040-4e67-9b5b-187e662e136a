package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.Strings.BatchFormationStrings;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseInvitationStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.SelfNominationStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_SelfNomination extends OQActionEngine {
	public static String selfNominatedTraineeID = "";
	public static String selfNominatedEmployee = "";
	public static String selfNominatedTraineePsw = "";
	public static String selfNominatedEmployeeID = "";

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[14]/a[1]")
	WebElement configMenu;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']/li[7]")
	WebElement selfNominationMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Approve']/li[8]")
	WebElement selfNominationMenuApproval;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Approve']//following-sibling::ul//li//a[text()='Course']")
	WebElement approveCourseMenu;

	@FindBy(xpath = "//input[@id = 'SelectedDecision_2']/following-sibling::label[text() = 'Approve']")
	WebElement selfNominationApproval;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Propose']/li[5]")
	WebElement proposSelfNomination;
	@FindBy(id = "SnSesBtn")
	WebElement selectSession;
	@FindBy(xpath = "//textarea[@id='SelfNomination_Remarks']")
	WebElement remarks1;
	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement remarks2;

	@FindBy(xpath = "//*[@id=\"ListTab\"]/tbody/tr/td[1]")
	WebElement courseSelect;
	@FindBy(id = "btnClientSearch")
	WebElement searchField;
	@FindBy(xpath = "//*[@id=\"ListTab_filter\"]/label/input")
	WebElement textBox;

	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;

	@FindBy(xpath = "//div[@id='CrsListTab_wrapper']/descendant::table/thead/following-sibling::tbody/tr[1]")
	WebElement displayCourse;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[15]")
	WebElement audittrails;

	@FindBy(id = "TMS_Course Manager_Audit Trails_MEN115")
	WebElement audittrailsSelfNomination;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByCourseUniqueCodeDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Self-Nomination Name')]")
	WebElement searchBySelfNominationDropdown;
	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniqueCodeLike;
	@FindBy(xpath = "//input[@id='SelfNominationName']")
	WebElement selfnominationLike;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Session Name')]")
	WebElement searchByCourseSessionnameDropdown;

	@FindBy(id = "SelfNominationName")
	WebElement courseSessionNameLike;

	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Self-Nomination Name')]")
	WebElement searchByselfnominationDropdown;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Nomination for Session']//following-sibling::span")
	WebElement auditTrailsCourseSessionName;
	@FindBy(xpath = "//label[@for='SelfNominationV1_SelfNominationCode']/following-sibling::span")
	WebElement auditTrailsCourseUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Course Name']//following-sibling::span")
	WebElement auditTrailsCourseName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Course Description']//following-sibling::span")
	WebElement auditTrailsCourseDescription;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']/following-sibling::span")
	WebElement auditCompareTRNTrainerUniqueCode;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Course Name']//following-sibling::span")
	WebElement auditCompareTRNCourseName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Trainee Name']//following-sibling::span")
	WebElement auditCompareTraninee;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Remarks']//following-sibling::span")
	WebElement auditCompareRemarks;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/child::li/a[contains(text(),'Approve')]")
	WebElement approvemenu;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Approve_MEN134']")
	WebElement approvetopicmenu;

	@FindBy(id = "TMS_Course Manager_Audit Trails_MEN134")
	WebElement aduitTrailsTopic;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Topic Name')]")
	WebElement searchByTopicNameDropdown;

	@FindBy(xpath = "(//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')])[1]")
	WebElement searchByUniquecodeDropdown;

	@FindBy(xpath = "//li[contains(text(),'Topic Name')]")
	WebElement approvesearchByTopicNameDropdown;

	@FindBy(xpath = "(//li[contains(text(),'Unique Code')])[1]")
	WebElement approvesearchByUniqueCodeDropdown;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callAtEsignInitiationLabel;

	@FindBy(id = "Description")
	WebElement topicNameLike;

	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniquecodeLike;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(id = "TMS_Course Manager_Configure_MEN134")
	WebElement configTopicMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li[2]//a")
	WebElement initiate;
	@FindBy(id = "TMS_Course Manager_Initiate_MEN134")
	WebElement topicMenu;
	@FindBy(id = "CMTopic_TopicDesc")
	WebElement topicName;
	@FindBy(id = "CMTopic_UserCode")
	WebElement topicUniqCode;
	@FindBy(id = "CMTopic_Description")
	WebElement description;
	@FindBy(xpath = "//span[text()='Document Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement documentName;
	@FindBy(xpath = "//label[text()='Document Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement documentName320;
	@FindBy(id = "CMTopic_TreeVC_SearchTxt")
	WebElement documentSearchTextBox;
	@FindBy(xpath = "//a[@class='anchorTxt']")
	WebElement fetchRecords;
	@FindBy(xpath = "//div[@id='CMTopic_AvailableDocs']/ul[@id='CMTopic_AvailableDocs_ul']/li[1]")
	WebElement greenTickMark;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	public CM_SelfNomination() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * This method is to propose SelfNomination
	 */
	/**
	 * This method is for SelfNomination Configuration
	 */

	public void selfNominationConfiguration(HashMap<String, String> testData) {
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(configMenu);
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		scrollToViewElement(selfNominationMenu);
		click2(selfNominationMenu, SelfNominationStrings.SelfNominationForMenu_DC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationForConfig_AC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationForConfig_AR.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationForConfig_SS.getSelfNominationStrings());
		switchToBodyFrame(driver);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "RegistrationApproval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("No Of Approvals"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);

		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("Configuration Remarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());

		click2(confirmationDone, CommonStrings.Click_Done_DC.getCommonStrings(),
				CourseSessionStrings.Click_DoneatCSConfig_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_DoneatCSConfig_AR.getCourseSessionStrings(),
				CommonStrings.Click_Done_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	// method for self nomination registration
	public void selfNomination_Registration(HashMap<String, String> testData) {
		// String CourseNameValue = "02 Sep Common subgroup ";

		selfNominatedTraineeID = testData.get("selfNominatedTraineeID");
		selfNominatedEmployee = testData.get("selfNominatedEmployee");
		selfNominatedTraineePsw = testData.get("selfNominatedTraineePsw");
		selfNominatedEmployeeID = testData.get("selfNominatedEmployeeID");

		String CourseNameValue = CM_Course.getCourse();
		
		TimeUtil.shortWait();
		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(proposSelfNomination);
		click2(proposSelfNomination, SelfNominationStrings.SelfMenu_DC.getSelfNominationStrings(),
				SelfNominationStrings.SelfMenu_AC.getSelfNominationStrings(),
				SelfNominationStrings.SelfMenu_AR.getSelfNominationStrings(),
				SelfNominationStrings.SelfMenu_SS.getSelfNominationStrings());
		switchToBodyFrame(driver);
		click2(searchField, SelfNominationStrings.SearchBy_DC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SS.getSelfNominationStrings());
		sendKeysAndRemoveFocus(textBox, SelfNominationStrings.SearchBy2_DC.getSelfNominationStrings(), CourseNameValue,
				SelfNominationStrings.SearchBy2_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy2_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy2_SS.getSelfNominationStrings());
		click2(courseSelect, SelfNominationStrings.SearchBy1_DC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy1_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy1_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy1_SS.getSelfNominationStrings());
		waitForElementVisibile(selectSession);
		click2(selectSession, CourseInvitationStrings.UserSelectionScreenNextBtn_DC.getCourseInvitationStrings(),
				SelfNominationStrings.UserSelectionScreenNextBtn_AC.getSelfNominationStrings(),
				SelfNominationStrings.UserSelectionScreenNextBtn_AR.getSelfNominationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_SS.getCourseInvitationStrings());
		sendKeysAndRemoveFocus(remarks1, CourseInvitationStrings.remarks_DC.getCourseInvitationStrings(),
				testData.get("Remarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseInvitationStrings.remarks_SS.getCourseInvitationStrings());
		click2(submit, SelfNominationStrings.Submit_DC.getSelfNominationStrings(),
				SelfNominationStrings.Submit_AC.getSelfNominationStrings(),
				SelfNominationStrings.Submit_AR.getSelfNominationStrings(),
				SelfNominationStrings.SubmitwithEsign_SS.getSelfNominationStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), selfNominatedTraineePsw,
						CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
						CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		switchToDefaultContent(driver);
		
		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(audittrails);
		click2(audittrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		scrollToViewElement(audittrailsSelfNomination);
		click2(audittrailsSelfNomination, SelfNominationStrings.SelfMenu_DC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_AC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_AR.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_SS.getSelfNominationStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SelfNominationStrings.AT_SearchBy_AC.getSelfNominationStrings(),
				SelfNominationStrings.AT_SearchBy_AR.getSelfNominationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByselfnominationDropdown,
				SelfNominationStrings.SearchBy_SelfNominationName_DC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_SS.getSelfNominationStrings());
		sendKeys2(courseSessionNameLike, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SelfNominationStrings.Click_Self_for_AuditTrails_DC.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_AC.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_AR.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_SS.getSelfNominationStrings());
		driver.switchTo().frame(0);
		waitForElementVisibile(auditTrailsCourseUniqueCode);
		scrollToViewElement(auditCompareTRNApprovalReqVal);
		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AR.getCourseSessionStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}

	// method for selfnomination approval
	public void SelfNominationRegistrationApproval_With_AuditTrails(HashMap<String, String> testData) {
		// scrollToViewElement(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(approve, CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_AC.getCommonStrings(), CommonStrings.CM_ApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());

		click2(selfNominationMenuApproval, SelfNominationStrings.SelfNominationForMenu_DC.getSelfNominationStrings(),
				SelfNominationStrings.SelfMenu_Approval_AC.getSelfNominationStrings(),
				SelfNominationStrings.SelfMenu_Approval_AR.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationForConfig_SS.getSelfNominationStrings());
		// String CourseNameValue = "02 Sep Common subgroup";
		String CourseNameValue = CM_Course.getCourse();
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SelfNominationStrings.AT_SearchBy_AC.getSelfNominationStrings(),
				SelfNominationStrings.AT_SearchBy_AR.getSelfNominationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySelfNominationDropdown,
				SelfNominationStrings.SearchBy_SelfNominationName_DC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_SS.getSelfNominationStrings());
		sendKeys2(selfnominationLike, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(courseSelect, SelfNominationStrings.SearchBy1_DC.getSelfNominationStrings(),
				SelfNominationStrings.ApprovalScreen_AC.getSelfNominationStrings(),
				SelfNominationStrings.ApprovalScreen_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy1_SS.getSelfNominationStrings());

		click2(selfNominationApproval, CourseStrings.CourseApproval_DC.getCourseStrings(),
				CourseStrings.CourseApproval_AC.getCourseStrings(), CourseStrings.CourseApproval_AR.getCourseStrings(),
				CourseStrings.CourseApproval_SS.getCourseStrings());
		sendKeysAndRemoveFocus(remarks2, CourseInvitationStrings.remarks_DC.getCourseInvitationStrings(),
				testData.get("Approver Remarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseInvitationStrings.remarks_SS.getCourseInvitationStrings());
		click2(submit, SelfNominationStrings.Submit_DC.getSelfNominationStrings(),
				SelfNominationStrings.Submit_Approve_AC.getSelfNominationStrings(),
				SelfNominationStrings.Submit_Approve_AR.getSelfNominationStrings(),
				SelfNominationStrings.SubmitwithEsign_SS.getSelfNominationStrings());

		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				SelfNominationStrings.Esign_ProceedSelfNominationFor_APR_AC.getSelfNominationStrings(),
				SelfNominationStrings.Esign_ProceedSelfNominationFor_APR_AR.getSelfNominationStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		waitForElementVisibile(confirmationText);

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(audittrails);
		click2(audittrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());

		scrollToViewElement(audittrailsSelfNomination);
		click2(audittrailsSelfNomination, SelfNominationStrings.SelfMenu_DC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_AC.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_AR.getSelfNominationStrings(),
				SelfNominationStrings.SelfNominationFor_AuditTrails_Menu_SS.getSelfNominationStrings());

		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SelfNominationStrings.AT_SearchBy_AC.getSelfNominationStrings(),
				SelfNominationStrings.AT_SearchBy_AR.getSelfNominationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByselfnominationDropdown,
				SelfNominationStrings.SearchBy_SelfNominationName_DC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AC.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_AR.getSelfNominationStrings(),
				SelfNominationStrings.SearchBy_SelfNominationName_SS.getSelfNominationStrings());

		sendKeys2(courseSessionNameLike, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SelfNominationStrings.Click_Self_for_AuditTrails_DC.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_AC.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_AR.getSelfNominationStrings(),
				SelfNominationStrings.Click_Self_for_AuditTrails_SS.getSelfNominationStrings());
		driver.switchTo().frame(0);
		waitForElementVisibile(auditTrailsCourseUniqueCode);
		scrollToViewElement(auditCompareTRNApprovalReqVal);
		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AR.getCourseSessionStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

}
