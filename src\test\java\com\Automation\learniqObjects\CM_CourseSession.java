package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_CourseSession extends OQActionEngine {
	WebDriverWait waitForCourse = new WebDriverWait(driver, Duration.ofSeconds(600));
	Properties prop;
	public static String RegAverageSessionSizeValue = "";
	public static String SkippedTraineeID = "";
	public static String SkippedEmployee = "";
	public static String SkippedEmployeeID = "";
	public static String SkippedTraineePsw = "";

	public static String AbsentTraineeID = "";
	public static String AbsentEmployee = "";
	public static String AbsentEmployeeID = "";
	public static String AbsentTraineePsw = "";

	public static String QualifiedTraineeID = "";
	public static String QualifiedEmployee = "";
	public static String QualifiedEmployeeID = "";
	public static String QualifiedTraineePsw = "";

	public static String ToBeRetrainedTraineeID = "";
	public static String ToBeRetrainedEmployee = "";
	public static String ToBeRetrainedTraineePsw = "";
	public static String ToBeRetrainedEmployeeID = "";

	public static String CIAcceptTraineeID = "";
	public static String CIAcceptEmployee = "";
	public static String CIAcceptTraineePsw = "";
	public static String CIAcceptEmployeeID = "";

	public static String CIREjectEmployee = "";
	public static String CIRejectTraineeID = "";
	public static String CIRejectTraineePsw = "";
	public static String CIRejectEmployeeID = "";

	public static String AdditionalUSerQulaified = "";
	public static String AdditionalUSerQulaifiedID = "";
	public static String AdditionalUSerQualifiedEmployeeID = "";
	public static String AdditionalUSerQulaifiedPsw = "";

	public static String AdditionalUSerTBR = "";
	public static String AdditionalUSerTRBID = "";
	public static String AdditionalUSerTRBEmployeeID = "";
	public static String AdditionalUSerTRBPsw = "";

	public static String mqaTraineeID = "";
	public static String mqaEmployeeName = "";
	public static String mqaTraineePassword = "";
	public static String mqaTraineeEmployeeID = "";

	public static String QPPendingUser = "";
	public static String QPPendingUserID = "";
	public static String QPPendingUserPsw = "";
	public static String QPPendingEmployeeID = "";

	public static String getSkippedTraineeID() {
		return SkippedTraineeID;
	}

	public static void setSkippedTraineeID(String skippedTraineeID) {
		SkippedTraineeID = skippedTraineeID;
	}

	public static String getSkippedEmployeeName() {
		return SkippedEmployee;
	}

	public static void SetSkippedEmployeeName(String skippedEmployee) {
		SkippedEmployee = skippedEmployee;
	}

	public static String getAbsentTraineeID() {
		return AbsentTraineeID;
	}

	public static void setAbsentTraineeID(String absentTraineeID) {
		AbsentTraineeID = absentTraineeID;
	}

	public static String getAbsentEmployee() {
		return AbsentEmployee;
	}

	public static void setAbsentEmployee(String absentTraineeName) {
		AbsentEmployee = absentTraineeName;
	}

	public static String getQualifiedTraineeID() {
		return QualifiedTraineeID;
	}

	public static void setQualifiedTraineeID(String qualifiedTraineeID) {
		QualifiedTraineeID = qualifiedTraineeID;
	}

	public static String getQualifiedEmployee() {
		return QualifiedEmployee;
	}

	public static void setQualifiedEmployeeName(String qualifiedEmp) {
		QualifiedEmployee = qualifiedEmp;
	}

	public static String getToBeRetrainedTraineeID() {
		return ToBeRetrainedTraineeID;
	}

	public static void setToBeRetrainedTraineeID(String toBeRetrainedTraineeID) {
		ToBeRetrainedTraineeID = toBeRetrainedTraineeID;
	}

	public static String getToBeRetrainedEmployeeName() {
		return ToBeRetrainedEmployee;
	}

	public static void SetToBeRetrainedEmployeeName(String toBeRetrainedEmployee) {
		ToBeRetrainedEmployee = toBeRetrainedEmployee;
	}

	public static String getCIRejectTraineeID() {
		return CIRejectTraineeID;
	}

	public static void setCIRejectTraineeID(String cIRejectTraineeID) {
		CIRejectTraineeID = cIRejectTraineeID;
	}

	public static String getCIRejectEmployee() {
		return CIREjectEmployee;
	}

	public static void setCIRejectEmployee(String cIRejectEmployee) {
		CIREjectEmployee = cIRejectEmployee;
	}

	public static String getCIAcceptTraineeID() {
		return CIAcceptTraineeID;
	}

	public static void setCIAcceptTraineeID(String cIAcceptTraineeID) {
		CIAcceptTraineeID = cIAcceptTraineeID;
	}

	public static String getCIAcceptEmployee() {
		return CIAcceptEmployee;
	}

	public static void setCIAcceptEmployee(String cIAcceptEmployee) {
		CIAcceptEmployee = cIAcceptEmployee;
	}

	public static String getAddUserQ() {
		return AdditionalUSerQulaified;
	}

	public static void setAddUserQ(String AddQualifiedEmp) {
		AdditionalUSerQulaified = AddQualifiedEmp;
	}

	public static String getAddUserQID() {
		return AdditionalUSerQulaifiedID;
	}

	public static void setAddUserQID(String AddUserQID) {
		AdditionalUSerQulaifiedID = AddUserQID;
	}

	public static String getAddUserTBR() {
		return AdditionalUSerTBR;
	}

	public static void setAddUserTBR(String AddTBREmp) {
		AdditionalUSerTBR = AddTBREmp;
	}

	public static String getAddUserTBRID() {
		return AdditionalUSerTRBID;
	}

	public static void setAddUserTBRID(String AddUserTRBID) {
		AdditionalUSerTRBID = AddUserTRBID;
	}

	public static String getMqaEmployeeName() {
		return mqaEmployeeName;
	}

	public static void setMqaEmployeeName(String mqaEmployeeName) {
		CM_CourseSession.mqaEmployeeName = mqaEmployeeName;
	}

	public static String getMqaTraineeID() {
		return mqaTraineeID;
	}

	public static void setMqaTraineeID(String mqaTraineeID) {
		CM_CourseSession.mqaTraineeID = mqaTraineeID;
	}

	public static String getQPPendingUser() {
		return QPPendingUser;
	}

	public static void setQPPendingUser(String QPPendingEmp) {
		QPPendingUser = QPPendingEmp;
	}

	public static String getQPPendingUserID() {
		return QPPendingUserID;
	}

	public static void setQPPendingUserID(String QPPendingUID) {
		QPPendingUserID = QPPendingUID;
	}

	public static String getRegAverageSessionSizeValue() {
		return RegAverageSessionSizeValue;
	}

	public static void setRegAverageSessionSizeValue(String regAverageSessionSizeValue) {
		RegAverageSessionSizeValue = regAverageSessionSizeValue;
	}

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']/preceding-sibling::a[text()='Configure']")
	WebElement configMenu;
	@FindBy(id = "TMS_Course Manager_Configure_MEN24")
	WebElement configCoursesessionMenu;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeMenu;
	@FindBy(id = "TMS_Course Manager_Propose_MEN24")
	WebElement courseSession;
	@FindBy(xpath = "//button[@id='UnScheduledCourse']")
	WebElement unScheduleCourse;
	@FindBy(xpath = "//button[@id='InterimCourse']")
	WebElement InterimCourse;
	@FindBy(xpath = "//button[@id='ScheduledCourse']")
	WebElement scheduleCourse;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement searchTxt;
	@FindBy(id = "CourseName")
	WebElement courseNameLike_New;

	@FindBy(xpath = "//button[@id='displayBtn']")
	WebElement apply_New;
	@FindBy(xpath = "//div[@id='CrsListTab_wrapper']/descendant::table/thead/following-sibling::tbody/tr[1]")
	WebElement displayCourse;
	@FindBy(id = "btnModal_CMSESSIONS_SubgroupList")
	WebElement addItem;
	@FindBy(xpath = "//input[@id='CMSESSIONS_SubgroupList_FindTxt']")
	WebElement subGroupNameFind_New;
	@FindBy(id = "CMSESSIONS_SubgroupList_DisplayBtn")
	WebElement subGroupApply_New;
	@FindBy(id = "CMSESSIONS_SubgroupList_selectBtn")
	WebElement addNew;
	@FindBy(id = "CMSESSIONS_CrsSesTneListbtnAddAll")
	WebElement addAll;
	@FindBy(xpath = "//button[@id='NextBtn2']")
	WebElement nextNew;
	@FindBy(xpath = "//button[@id='NextBtn3']")
	WebElement nextNew1;
	@FindBy(xpath = "//span[@id='select2-SesTypeDD-container']")
	WebElement sessionInfoManagement;
	@FindBy(xpath = "//span[@class='select2-container select2-container--default select2-container--open']//ul[1]//li[1]")
	WebElement sessionInfoManagemntSrchOpt;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//button[@id='btnModal_CMSESSIONS_CrsSesTneList']")
	WebElement traineeAddItem;
	@FindBy(xpath = "//span[@id='CMSESSIONS_CrsSesTneListbtnClientSearch']//i[@class='ft-search']")
	WebElement traineeAddCommonGlobalSearch;
	@FindBy(xpath = "//div[@id='multipopupfilter2_filter']//label//input")
	WebElement sendTextTraineeAddCommonGlobalSearch;
	@FindBy(xpath = "//table[@id='multipopupfilter2']//tr//td[10]//button")
	WebElement traineeAdd;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr/td[3]")
	WebElement displayedRecord;
	@FindBy(xpath = "//button[@id='CMSESSIONS_CrsSesTneList_selectBtn']")
	WebElement traineeAdd_New;
	@FindBy(id = "RTCBRD_1")
	WebElement documentReadingCompleted;
	@FindBy(id = "TrngMethod_1")
	WebElement internalClassroom;
	@FindBy(xpath = "//input[@id='AvgSesSize']")
	WebElement avgSessionSize;
	@FindBy(xpath = "//input[@id='ContDelRD_1']")
	WebElement contentDeliveryNo;
	@FindBy(xpath = "//input[@id='ExmReqRD_0']")
	WebElement examinationYes;
	@FindBy(xpath = "//input[@id='MqaReqRD_1']")
	WebElement MQA_No;
	@FindBy(xpath = "//input[@id='EvalReqRD_1']")
	WebElement evaluationNo;
	@FindBy(id = "CMSESSIONS_OJTRem")
	WebElement OJTRemarks;
	@FindBy(xpath = "//input[@id='FdbReqRD_0']")
	WebElement feedBackYes;
	@FindBy(xpath = "//input[@id='FdbReqRD_1']")
	WebElement feedBackNo;
	@FindBy(id = "longtermEval")
	WebElement longtermBtn;
	@FindBy(id = "shorttermEval")
	WebElement shorttermBtn;
	@FindBy(xpath = "//button[@id='CMSESSIONS_StartDate_0_btn']")
	WebElement startDate;
	@FindBy(xpath = "//button[@id='CMSESSIONS_EndDate_0_btn']")
	WebElement endDate;
	@FindBy(xpath = "//button[@id='CMSESSIONS_LastRespDate_0_btn']")
	WebElement lastDateResponse;
	@FindBy(id = "courseSessionDetails_0__Duration")
	WebElement trainingHours;
	@FindBy(id = "btnModal_CMSESSIONS_Trainer_0")
	WebElement trainerAddItem1;
	@FindBy(xpath = "//table[@id='multipopupfilter21']/tbody/tr[1]/td[4]/button")
	WebElement trainerAddNew1;
	@FindBy(xpath = "//table[@id='multipopupfilter2']/tbody/tr[3]/td[4]/button")
	WebElement trainerAddNew2;
	@FindBy(id = "CMSESSIONS_Trainer_0_selectBtn")
	WebElement trainer_AddNew1;
	@FindBy(id = "btnModal_CMSESSIONS_Venue_0")
	WebElement trainingVenue;
	@FindBy(xpath = "//table[@id='multipopupfilter22']/tbody/tr[1]/td[3]/button")
	WebElement trainingVenueAddNew;
	@FindBy(id = "CMSESSIONS_Venue_0_selectBtn")
	WebElement trainingVenue_AddNew;
	@FindBy(xpath = "/html[1]/body[1]/span[1]/span[1]/span[2]/ul[1]/li[1]")
	WebElement approverNewOption;
	@FindBy(id = "CMSESSIONS_SesRemarks")
	WebElement unScheduleRemarks;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Name']")
	WebElement searchByCourseName;

	@FindBy(xpath = "//input[@id='TxtSelUsersCnt']")
	WebElement totalSelectedTrainees;
	@FindBy(xpath = "//input[@id = 'UniqueCode']")
	WebElement uniqueCodeLike;
	@FindBy(id = "CourseSessionName")
	WebElement courseSessionNameLike;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Session Name')]")
	WebElement searchByCourseSessionnameDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByUniqueCodeDropdown;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(xpath = "//button[@class='CMSESSIONS_SubgroupListAddRemoveBtn btn btn-sm  notselected SgpCheck']")
	WebElement subGroupAdd_New1;
	@FindBy(xpath = "//input[@aria-controls= 'select2-CMSESSIONS_AppSubGrpsMul0-results']")
	WebElement LineofApproverValue;
	@FindBy(xpath = "//span[@class='select2-search select2-search--dropdown']//following-sibling::span")
	WebElement SelectLineofApproverValue;
	@FindBy(id = "select2-CMSESSIONS_AppSubGrpsMul0-container")
	WebElement LineofApprover;
	@FindBy(xpath = "//input[@id='ExmReqRD_0']//following-sibling::label")
	WebElement assesmentYesButton;

	@FindBy(xpath = "//input[@id='ExmReqRD_1']//following-sibling::label")
	WebElement assesmentNoButton;

	@FindBy(xpath = "//input[@id='MqaReqRD_1']//following-sibling::label")
	WebElement MQANoButton;
	@FindBy(xpath = "//div[@id='CMSESSIONS_Venue_0quickSearch']//label//input")
	WebElement searchTrainingVenue;
	@FindBy(xpath = "//span[@id='CMSESSIONS_Venue_0btnClientSearch']//i[@class='ft-search']")
	WebElement trainingvenueAddCommonGlobalSearch;
	@FindBy(id = "courseSessionDetails_0__Duration")
	WebElement trainingHoursValue;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement esignWindowTitle;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configuration Audit Trails']//li//a[text()='Course Sessions']")
	WebElement configurationCoursesessionAuditTrails;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[15]")
	WebElement audittrails;
	@FindBy(id = "TMS_Course Manager_Audit Trails_MEN24")
	WebElement audittrailsCourseSession;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Approve']")
	WebElement courseManagerApproveMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Approve']//li//a[text()='Course Sessions']")
	WebElement approveCourseSession;
	@FindBy(id = "SelectedDecision_2")
	WebElement approveRadioBtn;
	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement approveRemarks;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(id = "FdbReqRD_0")
	WebElement feedbackReqYes;

	@FindBy(xpath = "//div[contains(text(), 'Select Feedback Template')]")
	WebElement selectFeedbackReqHeader;

	@FindBy(xpath = "//div[contains(text(), 'Select Feedback Template')]/parent::div/parent::div/parent::div/parent::div/following-sibling::div//span[@class='select2-selection__arrow']")
	WebElement feedbackTemplateBtn;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement longtermSearchField;

	@FindBy(xpath = "//ul[@id='select2-feedbackdd-results']/li[1]")
	WebElement feedbackTemplateOption;

	@FindBy(id = "feedback_href")
	WebElement feedbackPreview;

	@FindBy(id = "editBtn")
	WebElement feedbackPreviewClose;

	@FindBy(id = "FdbReqRD_1")
	WebElement feedbackReqNo;
	@FindBy(id = "TrngMethod_4")
	WebElement documentReading;
	@FindBy(xpath = "//*[contains(text(),'Select All')]/input")
	WebElement selectDoc;
	@FindBy(xpath = "//input[@id='MqaReqRD_0']")
	WebElement MQAYes;
	@FindBy(xpath = "//div[text()='Number of Approvals Required for Initiation']")
	WebElement noOfApprovalsReqForInitiationHeading;
	// @FindBy(xpath = "//table[@id='multipopupfilter2']/tbody/tr[1]/td[3]/button")

	@FindBy(xpath = "//button[@class='CMSESSIONS_SubgroupListAddRemoveBtn btn btn-sm  notselected SgpCheck']")
	WebElement subGroupAdd_New;
	@FindBy(id = "ExmReqRD_1")
	WebElement assesmentNo;
	@FindBy(id = "ContDelRD_0")
	WebElement contentDeliveryYes;
	@FindBy(xpath = "//th[contains(text(),'Select All')]//input[@type='checkbox']")
	WebElement selectAllOptions;
	@FindBy(xpath = "/html/body/span/span/span[2]/ul/li[2]")
	WebElement abssessionInfoManagemntSrchOpt;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[2]//a//span")
	WebElement tobePlanned_Link;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[3]//a//span")
	WebElement skipped_Link;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[4]//a//span")
	WebElement absentees_Link;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[5]//a//span")
	WebElement tobeRetrained_Link;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[6]//a//span")
	WebElement totalTrainees_Link;
	@FindBy(xpath = "//table[@id='CrsDetailsTab']//tbody//tr[1]//td[7]//a//span")
	WebElement toRetakeEvaluation_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[1]//a//span")
	WebElement afterSubgroup_tobePlanned_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[2]//a//span")
	WebElement afterSubgroup_skipped_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[3]//a//span")
	WebElement afterSubgroup_absentees_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[4]//a//span")
	WebElement afterSubgroup_tobeRetrained_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[5]//a//span")
	WebElement afterSubgroup_totalTrainees_Link;
	@FindBy(xpath = "//table[@id='ListTabUser2']//tbody//tr[1]//td[6]//a//span")
	WebElement afterSubgroup_toRetakeEvaluation_Link;
	@FindBy(xpath = "//button[@id='Transfer_cancelBtn']")
	WebElement cancelButton;
	@FindBy(xpath = "//span[@id='TransferModal_CloseBtn']")
	WebElement closeButton;
	@FindBy(xpath = "//button[@id='selUser_cancelBtn']")
	WebElement afterSubgroup_cancelButton;
	@FindBy(xpath = "//div[@class='sub-page-layout sub-page-layout-list']//span[@id='TransferModal_CloseBtn']")
	WebElement afterSubgroup_closebutton;
	@FindBy(xpath = "//span[@id='CMSESSIONS_CrsSesTneList_CloseBtn']")
	WebElement traineeListCloseButton;
	@FindBy(xpath = "//button[@id='CMSESSIONS_CrsSesTneList_cancelBtn']")
	WebElement traineeListCancelButton;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Code']")
	WebElement searchByCourseCode;
	@FindBy(id = "CourseUCode")
	WebElement courseCode;

	public CM_CourseSession() {
		PageFactory.initElements(driver, this);
	}

	// This method is for course session configuration registration

	public void courseSessionConfiguration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(configMenu);
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(configCoursesessionMenu);
		TimeUtil.shortWait();
		click2(configCoursesessionMenu, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionConfig_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionConfig_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionConfig_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtInitAprCheckBox, "Approval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());
		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		scrollToViewElement(remarks);
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("configureRemarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);
	}

	// 1. This method is for course session reg from scheduled tab
	// 2. Here we do course session registration with 1 approval along with Audit
	// trails
	// 3. Here we select 5 trainees from subgroup and 1 trainee from user
	// registration

	public void courseSession_Online_WithExam_ManualEval_WithPrequisiteTrainee(HashMap<String, String> testData)
			throws InterruptedException {

		String CourseNameValue = CM_Course.getCourse();

		setQualifiedTraineeID(QualifiedTraineeID = SSO_UserRegistration.getUserID());
		QualifiedTraineePsw = ConfigsReader.getPropValue("SSONewUserRegPassword");
		QualifiedEmployeeID = SSO_UserRegistration.getEmployeeID();

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		SkippedEmployeeID = testData.get("skippedEmployeeID");

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));
		AbsentTraineePsw = testData.get("absentTraineePsw");
		AbsentEmployeeID = testData.get("AbsentEmployeeID");

		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		ToBeRetrainedTraineePsw = testData.get("toBeRetrainedTraineePsw");
		ToBeRetrainedEmployeeID = testData.get("ToBeRetrainedEmployeeID");

		setCIRejectTraineeID(CIRejectTraineeID = testData.get("ciRejectTraineeID"));
		setCIRejectEmployee(CIREjectEmployee = testData.get("CIRejectEmployeeName"));
		CIRejectTraineePsw = testData.get("ciRejectTraineePsw");
		CIRejectEmployeeID = testData.get("CIRejectEmployeeID");

		setCIAcceptTraineeID(CIAcceptTraineeID = testData.get("ciAcceptTraineeID"));
		setCIAcceptEmployee(CIAcceptEmployee = testData.get("CIAcceptEmployeeName"));
		CIAcceptTraineePsw = testData.get("ciAcceptTraineePsw");
		CIAcceptEmployeeID = testData.get("CIAcceptEmployeeID");

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));
		AdditionalUSerQulaifiedPsw = testData.get("additionalUSerQulaifiedPsw");

		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));
		AdditionalUSerTRBPsw = testData.get("additionalUSerTRBPsw");

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//		click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SSO_UserRegistration.EmployeeName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIREjectEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIAcceptEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentYesButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_Esign_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_Esign_AR.getCourseSessionStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());

		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	// This method is for course session Approve along with audit trails

	public void courseSession_Online_WithOutExam(HashMap<String, String> testData) throws InterruptedException {

		String CourseNameValue = CM_Course.getCourse();
//		String qualifyTraineeID = SSO_UserRegistration.getEmployeeID();

		// String CourseNameValue = "Coursequery543";

		// setQualifiedTraineeID(QualifiedTraineeID = "trainee81");
		// QualifiedTraineePsw = testData.get("SkippedEmployee");

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		SkippedEmployeeID = testData.get("SkippedEmployeeID");

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));
		AbsentTraineePsw = testData.get("absentTraineePsw");

		AbsentEmployeeID = testData.get("AbsentEmployeeID");

		setCIRejectTraineeID(CIRejectTraineeID = testData.get("ciRejectTraineeID"));
		setCIRejectEmployee(CIREjectEmployee = testData.get("CIRejectEmployeeName"));
		CIRejectTraineePsw = testData.get("ciRejectTraineePsw");
		CIRejectEmployeeID = testData.get("CIRejectEmployeeID");

		setCIAcceptTraineeID(CIAcceptTraineeID = testData.get("ciAcceptTraineeID"));
		setCIAcceptEmployee(CIAcceptEmployee = testData.get("CIAcceptEmployeeName"));
		CIAcceptTraineePsw = testData.get("ciAcceptTraineePsw");
		CIAcceptEmployeeID = testData.get("CIAcceptEmployeeID");

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));
		AdditionalUSerQulaifiedPsw = testData.get("AddUserQualifiedPsw");
		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));
		AdditionalUSerTRBPsw = testData.get("AddUserTBRPsw");

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//		click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		Thread.sleep(20000);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		waitForElementVisibile(displayCourse);
		TimeUtil.longwait();

		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		subGroupNameFind_New.clear();
//		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
//				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		
//		
//		

		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();

		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIREjectEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIAcceptEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNoButton);
		click2(assesmentNoButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	public void courseSession_Offline_WithExam(HashMap<String, String> testData) throws InterruptedException {

		String CourseNameValue = CM_Course.getCourse();
//String CourseNameValue="CRSNewYIRY";
//String qualifyTraineeID = SSO_UserRegistration.getEmployeeID();

		// String CourseNameValue = "Coursequery543";
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifiedEmployee"));
		setQualifiedTraineeID(QualifiedTraineeID = testData.get("QualifiedTraineeID"));
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		SkippedEmployeeID = testData.get("skippedEmployeeID");

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));

		AbsentEmployeeID = testData.get("absentEmployeeID");

		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		ToBeRetrainedEmployeeID = testData.get("toBeRetrainedEmployeeID");

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));
		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));

		QualifiedTraineePsw = testData.get("qualifyTraineePsw");
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		AbsentTraineePsw = testData.get("absentTraineePsw");
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineePsw");

		TimeUtil.mediumWait();
		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//		CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//		CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//		CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		Thread.sleep(20000);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		waitForElementVisibile(displayCourse);
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();

		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedTraineeID,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNoButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// This method is for course session Approve along with audit trails

	// 1. This method is for course session reg from scheduled tab
	// 2. Here we do course session registration with 1 approval along with Audit
	// trails
	// 3. Here we select 5 trainees from subgroup and 1 trainee from user
	// registration

	public void courseSession_Online_WithExam_SystemEvaluation(HashMap<String, String> testData)
			throws InterruptedException {

		String CourseNameValue = CM_Course.getCourse();
//			String CourseNameValue = "CRSNewPYRK";

		setQualifiedTraineeID(QualifiedTraineeID = testData.get("qualifyTraineeID"));
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifyEmployee"));
		QualifiedTraineePsw = testData.get("qualifyTraineePsw");
		QualifiedEmployeeID = testData.get("QualifiedEmployeeID");

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		SkippedEmployeeID = testData.get("SkippedEmployeeID");

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));
		AbsentTraineePsw = testData.get("absentTraineePsw");
		AbsentEmployeeID = testData.get("AbsentEmployeeID");

		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineePsw");
		ToBeRetrainedEmployeeID = testData.get("ToBeRetrainedEmployeeID");

		setCIRejectTraineeID(CIRejectTraineeID = testData.get("ciRejectTraineeID"));
		setCIRejectEmployee(CIREjectEmployee = testData.get("CIRejectEmployeeName"));
		CIRejectTraineePsw = testData.get("ciRejectTraineePsw");
		CIRejectEmployeeID = testData.get("CIRejectEmployeeID");

		setCIAcceptTraineeID(CIAcceptTraineeID = testData.get("ciAcceptTraineeID"));
		setCIAcceptEmployee(CIAcceptEmployee = testData.get("CIAcceptEmployeeName"));
		CIAcceptTraineePsw = testData.get("ciAcceptTraineePsw");
		CIAcceptEmployeeID = testData.get("CIAcceptEmployeeID");

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));
		AdditionalUSerQulaifiedPsw = testData.get("addUserQualifiedPsw");
		AdditionalUSerQualifiedEmployeeID = testData.get("AdditionalUSerQualifiedEmployeeID");

		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));
		AdditionalUSerTRBPsw = testData.get("AddUserTBRPsw");
		AdditionalUSerTRBEmployeeID = testData.get("AdditionalUSerTRBEmployeeID");

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		Thread.sleep(20000);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);
		TimeUtil.longwait();

		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIREjectEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIAcceptEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentYesButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		clickAndWaitforNextElement(submit, esignWindowTitle, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveCourseSessionUniqueCode(driver, confirmationText);
		switchToDefaultContent(driver);

	}

	public void courseSession_Offline_DocumentReading(HashMap<String, String> testData) {

		String CourseNameValue = CM_Course.getCourse();

		setQualifiedTraineeID(QualifiedTraineeID = testData.get("qualifiedTraineeID"));
		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		QualifiedEmployee = testData.get("QualifiedEmployeee");
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");

		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		if (CM_Course.getSCHType().equals("Interim")) {

			click2(InterimCourse, "", "", "", "");
		}

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

//		click2(searchByCourseCode, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
//		CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
//		CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
//		CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
//   sendKeys2(courseCode, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
//		OQActionEngine.uniqueCode, CommonStrings.sendKeys_AC.getCommonStrings(),
//		CommonStrings.sendKeys_AR.getCommonStrings(),
//		CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());

		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		waitForElementVisibile(displayCourse);
//		TimeUtil.mediumWait();
//		TimeUtil.longwait();
		TimeUtil.longwait();

		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());

		// waitForElementVisibile(nextNew);
		if (!CM_Course.getSCHType().equals("Interim")) {
//			TimeUtil.longwait();
			// waitForElementVisibile(addItem);
			WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
			WebElement addItem = wait.until(ExpectedConditions
					.elementToBeClickable(By.cssSelector("button#btnModal_CMSESSIONS_SubgroupList.btn-popup")));
			addItem.click();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
			waitForElementVisibile(subGroupNameFind_New);
			TimeUtil.mediumWait();
			subGroupNameFind_New.clear();
			sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
					CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
			click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
					CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
					CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
					CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
			click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Offline_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), testData.get("QualifiedEmployeee"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("ToBeRetrainedEmployeee"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		scrollToViewElement(documentReading);
		click2(documentReading, CourseSessionStrings.Select_TM_DR_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_SS.getCourseSessionStrings());
		scrollToViewElement(nextNew1);
		click2(nextNew1, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Next2_AC.getCourseSessionStrings(),
				CourseSessionStrings.Next2_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(selectDoc);
		scrollToViewElement(selectDoc);
		click2(selectDoc, CommonStrings.Select_DC.getCommonStrings(), CommonStrings.Select_AC.getCommonStrings(),
				CommonStrings.Select_AR.getCommonStrings(), CommonStrings.Select_SS.getCommonStrings());
		scrollToViewElement(submit);
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void courseSession_Online_DocumentReading_WithExam_MQA_Yes(HashMap<String, String> testData) {

		setMqaTraineeID(mqaTraineeID = testData.get("MQATraineeID"));
		setMqaEmployeeName(mqaEmployeeName = testData.get("MQATraineeName"));
		mqaTraineePassword = testData.get("MQATraineePassword");
		mqaTraineeEmployeeID = testData.get("mqaTraineeEmployeeID");
		String CourseNameValue = CM_Course.getCourse();

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		clickAndWaitforNextElement(searchByNew, searchByCourseName, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(searchByCourseName, courseNameLike_New,
				CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		// courseUniqueCode
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

//		TimeUtil.extralongwait();
//		TimeUtil.extralongwait();
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(addItem, subGroupNameFind_New,
				CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());

		// ----------------Sub Group Selection
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), mqaEmployeeName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(documentReading, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentYesButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQAYes, CourseSessionStrings.MQA_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		scrollToViewElement(nextNew1);
		click2(nextNew1, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Next2_AC.getCourseSessionStrings(),
				CourseSessionStrings.Next2_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		waitForElementVisibile(selectDoc);
		scrollToViewElement(selectDoc);
		TimeUtil.shortWait();
		click2(selectDoc, CommonStrings.Select_DC.getCommonStrings(), CommonStrings.Select_AC.getCommonStrings(),
				CommonStrings.Select_AR.getCommonStrings(), CommonStrings.Select_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		clickAndWaitforNextElement(submit, esignWindowTitle, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveCourseSessionUniqueCode(driver, confirmationText);
		switchToDefaultContent(driver);

	}

	// Online
	public void courseSession_Online_DocumentReading_WithOutExam(HashMap<String, String> testData) {

//		QualifiedTraineeID = testData.get("qualifiedTraineeID");
//		System.out.println(QualifiedTraineeID);
//		QualifiedEmployee

		setQualifiedTraineeID(QualifiedTraineeID = testData.get("qualifiedTraineeID"));
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifyEmployee"));
		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		QualifiedTraineePsw = testData.get("qualifiedTraineeIDPsw");
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineeIDPsw");

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		String CourseNameValue = CM_Course.getCourse();
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				// "AutomationCourseBHZB" + Constants.PERCENTAGE_SIGN,
				// CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		waitForElementVisibile(displayCourse);
		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.longwait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		String subgroup = CM_Course.getSubgroupName();
//	String subgroup = "AnjiSubgroup";
		TimeUtil.longwait();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(), subgroup,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Offline_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), testData.get("QualifyEmployee"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("ToBeRetrainedEmployeee"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		scrollToViewElement(documentReading);
		click2(documentReading, CourseSessionStrings.Select_TM_DR_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNo);
		click2(assesmentNo, CourseSessionStrings.Assessment_No_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		scrollToViewElement(nextNew1);
		click2(nextNew1, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Next2_AC.getCourseSessionStrings(),
				CourseSessionStrings.Next2_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(selectDoc, CommonStrings.Select_DC.getCommonStrings(), CommonStrings.Select_AC.getCommonStrings(),
				CommonStrings.Select_AR.getCommonStrings(), CommonStrings.Select_SS.getCommonStrings());

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_Esign_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_Esign_AR.getCourseSessionStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());

		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void courseSession_Online_WithExam_MQA_Yes(HashMap<String, String> testData) {

		String CourseNameValue = CM_Course.getCourse();
		mqaTraineeID = testData.get("mqaTraineeID");
		mqaEmployeeName = testData.get("mqaTraineeName");
		mqaTraineePassword = testData.get("mqaTraineeIDPsw");
		mqaTraineeEmployeeID = testData.get("mqaTraineeEmployeeID");

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(proposeMenu);
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());

		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		TimeUtil.shortWait();

		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);

		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());

		String subgroup = CM_Course.getSubgroupName();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(), subgroup,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(subGroupAdd_New, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		TimeUtil.shortWait();

		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());

		TimeUtil.mediumWait();

		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());

		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), mqaEmployeeName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		scrollToViewElement(internalClassroom);
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentYesButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQAYes, CourseSessionStrings.MQA_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue, CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveCourseSessionUniqueCode(driver, confirmationText);
		TimeUtil.mediumWait();
		switchToDefaultContent(driver);

	}

	// Below function is for Online RE Session with Assessment for multiple users.

	public void courseSession_Online_DocumentReading_WithExam(HashMap<String, String> testData) {

		setQualifiedTraineeID(QualifiedTraineeID = testData.get("qualifyTraineeID"));
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifyEmployee"));
		QualifiedEmployeeID = testData.get("QualifiedEmployeeID");
		QualifiedTraineePsw = testData.get("qualifyTraineePsw");

		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		ToBeRetrainedEmployeeID = testData.get("tobeRetrainedEmployeeID");
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineePsw");

		setQPPendingUser(QPPendingUser = testData.get("RespondQuestionPaperPendingUser"));
		setQPPendingUserID(QPPendingUserID = testData.get("RespondQuestionPaperPendingUserID"));
		QPPendingUserPsw = testData.get("RespondQuestionPaperPendingUserPsw");
		QPPendingEmployeeID = testData.get("RespondQuestionPaperPendingEmployeeID");

		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		String CourseNameValue = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.mediumWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(searchByCourseName, courseNameLike_New,
				CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		waitForElementVisibile(displayCourse);

		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());

		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
//		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QPPendingUser,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}

		scrollToViewElement(documentReading);
		click2(documentReading, CourseSessionStrings.Select_TM_DR_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_SS.getCourseSessionStrings());
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		click2(selectDoc, CommonStrings.Select_DC.getCommonStrings(), CommonStrings.Select_AC.getCommonStrings(),
				CommonStrings.Select_AR.getCommonStrings(), CommonStrings.Select_SS.getCommonStrings());
		scrollToViewElement(submit);
		// scrollToViewElement(LineofApprover);
//			click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
//					CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
//					CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
//					CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//			sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
//					testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
//					CommonStrings.sendKeys_AR.getCommonStrings(),
//					CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//			click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
//					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//					CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
//			sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
//					CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//					CommonStrings.Password_SS.getCommonStrings());
//			TimeUtil.shortWait();
//			click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//					CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
//					CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
//					CommonStrings.Esign_Proceed_SS.getCommonStrings());
		// saveCourseSessionUniqueCode(driver, confirmationText);

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_Esign_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_Esign_AR.getCourseSessionStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());

		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	// This method is for Course Session Offline without Assessment.

	public void course_session_Offline_ClassroomType_Without_Assessment(HashMap<String, String> testData) {

		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifiedEmployee"));
		setQualifiedTraineeID(QualifiedTraineeID = testData.get("qualifiedTraineeID"));

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));
		SkippedEmployeeID = testData.get("skippedEmployeeID");
		AbsentEmployeeID = testData.get("absentEmployeeID");
		AdditionalUSerQualifiedEmployeeID = testData.get("AddUserQualifiedEmployeeID");
		AdditionalUSerTRBEmployeeID = testData.get("AddUserTBRID");
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");
		AbsentTraineePsw = testData.get("absentTraineePsw");
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		QualifiedTraineePsw = testData.get("qualifiedTraineeIDPsw");

		TimeUtil.mediumWait();
		String CourseNameValue = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.mediumWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		waitForElementVisibile(displayCourse);
		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		TimeUtil.longwait();
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(addItem);
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));

		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());

		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNoButton);

		click2(assesmentNo, CourseSessionStrings.Select_AssessmentRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_OfflineAssessmentRequiredYes_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_OfflineAssessmentRequiredYes_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_AssessmentRequiredYes_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingvenueAddCommonGlobalSearch, searchTrainingVenue,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}
	// Online Classroom Type session Without Assessment and With FeedBack as 'Yes'

	public void CourseSession_Classroomtype_Without_Assess_Feedback_Yes(HashMap<String, String> testData) {

		QualifiedEmployee = testData.get("QualifyEmployee");
		QualifiedTraineeID = testData.get("qualifyTraineeID");
		QualifiedTraineePsw = testData.get("qualifyTraineePsw");
		QualifiedEmployeeID = testData.get("QualifiedEmployeeID");
		String CourseNameValue = CM_Course.getCourse();
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//		click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		// Thread.sleep(20000);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		waitForElementVisibile(displayCourse);
		TimeUtil.extralongwait();
//		TimeUtil.extralongwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
		// TimeUtil.longwait();

		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();

		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());

		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());

		TimeUtil.mediumWait();

		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				CM_CourseSession.getQualifiedEmployee(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());

		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);

		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}

		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());

		click2(feedbackReqYes, CourseSessionStrings.Select_FeedbackRequiredYes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_SS.getCourseSessionStrings());

		click2(feedbackTemplateBtn, CourseSessionStrings.FeedbackTemplateSelect_DC.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_AC.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_AR.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_SS.getCourseSessionStrings());

		sendKeys2(longtermSearchField, CourseSessionStrings.Enter_Newfeedback_DC.getCourseSessionStrings(),
				testData.get("FeedbackTemplate"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_Newfeedback_SS.getCourseSessionStrings());

		click2(feedbackTemplateOption, CourseSessionStrings.Click_Newfeedback_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_SS.getCourseSessionStrings());

		click2(feedbackPreview, CourseSessionStrings.feedbackPreview_DC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_AC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_AR.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_SS.getCourseSessionStrings());

		TimeUtil.shortWait();

		click2(feedbackPreviewClose, CourseSessionStrings.feedbackPreviewClose_DC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_AC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_AR.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_SS.getCourseSessionStrings());

		click2(assesmentNoButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());

		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	// This method is for course session Approve along with audit trails

	public void courseSession_Approve(HashMap<String, String> testData) {

		String CourseNameValue = CM_Course.getCourse();

		// String CourseNameValue = "Courseafgv";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerApproveMenu, CommonStrings.ApproveMenu_DC.getCommonStrings(),
				CommonStrings.ApproveMenu_AC.getCommonStrings(), CommonStrings.ApproveMenu_AR.getCommonStrings(),
				CommonStrings.ApproveMenu_SS.getCommonStrings());
		click2(approveCourseSession, CourseSessionStrings.CourseSessionApproveMenu_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproveMenu_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproveMenu_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproveMenu_SS.getCourseSessionStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.AT_SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.AT_SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByCourseSessionnameDropdown,
				CourseSessionStrings.SearchBy_CoursSessionName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_SS.getCourseSessionStrings());
		sendKeys2(courseSessionNameLike, CourseSessionStrings.Like_CSName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CSName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, CourseSessionStrings.Click_CS_for_AuditTrails_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_Approve_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_Approve_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_Approve_SS.getCourseSessionStrings());
		highLightElement(driver, highlightAuditScreenWindowTitle, "Approval screen", test);
		scrollToViewElement(approveRadioBtn);
		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		sendKeys2(approveRemarks, CommonStrings.Approve_Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approve_Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_AR.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(audittrails);
		click2(audittrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(audittrailsCourseSession);
		click2(audittrailsCourseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.AT_SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.AT_SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByCourseSessionnameDropdown,
				CourseSessionStrings.SearchBy_CoursSessionName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_SS.getCourseSessionStrings());
		sendKeys2(courseSessionNameLike, CourseSessionStrings.Like_CSName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CSName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, CourseSessionStrings.Click_CS_for_AuditTrails_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_ApproveAuditTrails_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_ApproveAuditTrails_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_AuditTrails_SS.getCourseSessionStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Audit trails screen", test);
		scrollToViewElement(auditCompareTRNFinalStatus);
		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseSessionStrings.Close_AuditTrails_CourseSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Close_AuditTrails_CourseSession_AR.getCourseSessionStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void courseSessionAuditTrails() {

		String CourseNameValue = CM_Course.getCourse();

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(audittrails);
		click2(audittrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(audittrailsCourseSession);
		click2(audittrailsCourseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_AuditTrails_Menu_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.AT_SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.AT_SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByCourseSessionnameDropdown,
				CourseSessionStrings.SearchBy_CoursSessionName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CoursSessionName_SS.getCourseSessionStrings());
		sendKeys2(courseSessionNameLike, CourseSessionStrings.Like_CSName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CSName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, CourseSessionStrings.Click_CS_for_AuditTrails_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_AuditTrails_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_AuditTrails_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_CS_for_AuditTrails_SS.getCourseSessionStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Audit trails screen", test);
		scrollToViewElement(auditCompareTRNFinalStatus);
		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseSessionStrings.Close_AuditTrails_CourseSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Close_AuditTrails_CourseSession_AR.getCourseSessionStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}
	// This method is for Course Session Online with Assessment Feedback Yes

	public void courseSession_OnLine_WithExam_Feedback_Yes(HashMap<String, String> testData)
			throws InterruptedException {

		String CourseNameValue = CM_Course.getCourse();
//	String CourseNameValue="CRSNewYIRY";
//	String qualifyTraineeID = SSO_UserRegistration.getEmployeeID();

		// String CourseNameValue = "Coursequery543";
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("QualifiedEmployee"));
		setQualifiedTraineeID(QualifiedTraineeID = testData.get("QualifiedTraineeID"));
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");
		QualifiedTraineePsw = testData.get("qualifiedTraineePsw");

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//	click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//			CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//			CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//			CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		Thread.sleep(20000);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.extralongwait();
		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));

		waitForElementVisibile(displayCourse);
		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		TimeUtil.mediumWait();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();

		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedbackReqYes, CourseSessionStrings.Select_FeedbackRequiredYes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredYes_SS.getCourseSessionStrings());

		click2(feedbackTemplateBtn, CourseSessionStrings.FeedbackTemplateSelect_DC.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_AC.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_AR.getCourseSessionStrings(),
				CourseSessionStrings.FeedbackTemplateSelect_SS.getCourseSessionStrings());

		sendKeys2(longtermSearchField, CourseSessionStrings.Enter_Newfeedback_DC.getCourseSessionStrings(),
				testData.get("FeedbackTemplate"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_Newfeedback_SS.getCourseSessionStrings());

		click2(feedbackTemplateOption, CourseSessionStrings.Click_Newfeedback_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_Newfeedback_SS.getCourseSessionStrings());

		click2(feedbackPreview, CourseSessionStrings.feedbackPreview_DC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_AC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_AR.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreview_SS.getCourseSessionStrings());

		TimeUtil.shortWait();

		click2(feedbackPreviewClose, CourseSessionStrings.feedbackPreviewClose_DC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_AC.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_AR.getCourseSessionStrings(),
				CourseSessionStrings.feedbackPreviewClose_SS.getCourseSessionStrings());

		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue, CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	// This method is for Course Session Offline without Assessment.

	public void course_session_Offline_ClassroomType_Without_Assessment_VerbalEvaluation_3addUsers(
			HashMap<String, String> testData) {

		setQualifiedEmployeeName(QualifiedEmployee = testData.get("qualifiedEmployee"));
		setQualifiedTraineeID(QualifiedTraineeID = testData.get("QualifiedTraineeID"));

		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("ToBeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("tobeRetrainedEmployee"));

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("absentEmployee"));

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));

		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));

		SkippedEmployeeID = testData.get("skippedEmployeeID");
		AbsentEmployeeID = testData.get("absentEmployeeID");
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");
		AdditionalUSerQualifiedEmployeeID = testData.get("AddUserQualifiedEmployeeID");
		AdditionalUSerTRBEmployeeID = testData.get("AdditionalUSerTRBEmployeeID");
		ToBeRetrainedEmployeeID = testData.get("tobeRetrainedEmployeeID");

		QualifiedTraineePsw = testData.get("qualifyTraineePsw");
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		AbsentTraineePsw = testData.get("absentTraineePsw");
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineePsw");

		TimeUtil.mediumWait();
		String CourseNameValue = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.longwait();
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();
//		
		waitForElementVisibile(displayCourse);
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(addItem);
		TimeUtil.mediumWait();

		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));

		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());

		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNoButton);

		click2(assesmentNo, CourseSessionStrings.Select_AssessmentRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_OfflineAssessmentRequiredYes_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_OfflineAssessmentRequiredYes_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_AssessmentRequiredYes_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}

	// using Existing user
	public void courseSession_Online_WithExam_ManualEval_WithoutPrequisiteTrainee(HashMap<String, String> testData) {

		String CourseNameValue = CM_Course.getCourse();

		SetSkippedEmployeeName(SkippedEmployee = testData.get("SkippedEmployee"));
		setSkippedTraineeID(SkippedTraineeID = testData.get("skippedTraineeID"));
		SkippedTraineePsw = testData.get("skippedTraineePsw");
		SkippedEmployeeID = testData.get("skippedEmployeeID");

		setAbsentTraineeID(AbsentTraineeID = testData.get("absentTraineeID"));
		setAbsentEmployee(AbsentEmployee = testData.get("AbsentEmployee"));
		AbsentTraineePsw = testData.get("absentTraineePsw");
		AbsentEmployeeID = testData.get("AbsentEmployeeID");

		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		ToBeRetrainedTraineePsw = testData.get("toBeRetrainedTraineePsw");
		ToBeRetrainedEmployeeID = testData.get("ToBeRetrainedEmployeeID");

		setCIRejectTraineeID(CIRejectTraineeID = testData.get("ciRejectTraineeID"));
		setCIRejectEmployee(CIREjectEmployee = testData.get("CIRejectEmployeeName"));
		CIRejectTraineePsw = testData.get("ciRejectTraineePsw");
		CIRejectEmployeeID = testData.get("CIRejectEmployeeID");

		setCIAcceptTraineeID(CIAcceptTraineeID = testData.get("ciAcceptTraineeID"));
		setCIAcceptEmployee(CIAcceptEmployee = testData.get("CIAcceptEmployeeName"));
		CIAcceptTraineePsw = testData.get("ciAcceptTraineePsw");
		CIAcceptEmployeeID = testData.get("CIAcceptEmployeeID");

		setAddUserQ(AdditionalUSerQulaified = testData.get("AddUserQualified"));
		setAddUserQID(AdditionalUSerQulaifiedID = testData.get("AddUserQualifiedID"));
		AdditionalUSerQulaifiedPsw = testData.get("additionalUSerQulaifiedPsw");

		setAddUserTBR(AdditionalUSerTBR = testData.get("AddUserTBR"));
		setAddUserTBRID(AdditionalUSerTRBID = testData.get("AddUserTBRID"));
		AdditionalUSerTRBPsw = testData.get("additionalUSerTRBPsw");

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
//		click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
//				CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		WebElement element2 = waitForCourse.until(
				ExpectedConditions.elementToBeClickable(By.xpath("//td[contains(text(),'" + CM_Course.Course + "')]")));
		TimeUtil.mediumWait();
		// TimeUtil.longwait();
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);
		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddCommonGlobalSearch, sendTextTraineeAddCommonGlobalSearch,
				CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), SkippedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), AbsentEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
//		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
//				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedTraineeID,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), ToBeRetrainedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIREjectEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		sendKeysAndRemoveFocus(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), CIAcceptEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeysAndRemoveFocus(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryNo, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentYesButton);
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(trainingVenue, trainingvenueAddCommonGlobalSearch,
				CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_Esign_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_Esign_AR.getCourseSessionStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());

		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	public void verify_Employees_At_Coursesession() {

		String CourseNameValue = CM_Course.getCourse();
//		String CourseNameValue = "AutomationCourseNDZH";

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);
		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		if (!tobePlanned_Link.getText().equals("0")) {
			click2(tobePlanned_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		if (!skipped_Link.getText().equals("0")) {
			click2(skipped_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		if (!absentees_Link.getText().equals("0")) {
			click2(absentees_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		if (!tobeRetrained_Link.getText().equals("0")) {
			click2(tobeRetrained_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		if (!totalTrainees_Link.getText().equals("0")) {
			click2(totalTrainees_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		if (!toRetakeEvaluation_Link.getText().equals("0")) {
			click2(toRetakeEvaluation_Link, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(cancelButton);
			click2(closeButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(addItem);
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
//		subGroupNameFind_New.clear();
//		TimeUtil.shortWait();
//		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
//				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.shortWait();
		waitForElementVisibile(subGroupAdd_New1);
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		waitForElementVisibile(addNew);
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(afterSubgroup_tobePlanned_Link);
		if (!afterSubgroup_tobePlanned_Link.getText().equals("0")) {
			click2(afterSubgroup_tobePlanned_Link, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			waitForElementVisibile(afterSubgroup_cancelButton);
			scrollToViewElement(afterSubgroup_cancelButton);
			waitForElementVisibile(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(afterSubgroup_skipped_Link);
		if (!afterSubgroup_skipped_Link.getText().equals("0")) {
			click2(afterSubgroup_skipped_Link, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(afterSubgroup_absentees_Link);
		if (!afterSubgroup_absentees_Link.getText().equals("0")) {
			click2(afterSubgroup_absentees_Link, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(afterSubgroup_tobeRetrained_Link);
		if (!afterSubgroup_tobeRetrained_Link.getText().equals("0")) {
			click2(afterSubgroup_tobeRetrained_Link, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(afterSubgroup_totalTrainees_Link);
		if (!afterSubgroup_totalTrainees_Link.getText().equals("0")) {
			click2(afterSubgroup_totalTrainees_Link, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		waitForElementVisibile(afterSubgroup_toRetakeEvaluation_Link);
		if (!afterSubgroup_toRetakeEvaluation_Link.getText().equals("0")) {
			click2(afterSubgroup_toRetakeEvaluation_Link,
					CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
					CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
			scrollToViewElement(afterSubgroup_cancelButton);
			click2(afterSubgroup_closebutton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
					CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
			TimeUtil.shortWait();
		}
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys("Off-Line");
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Offline_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Offline_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		// scrollToViewElement(traineeListCancelButton);
		waitForElementVisibile(traineeListCloseButton);
		click2(traineeListCloseButton, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Below function is for Online RE Session with Assessment for multiple users
	// with Pre Requisite.

	public void courseSession_Online_DocumentReading_WithExam_With_newUser(HashMap<String, String> testData) {

		setQualifiedTraineeID(QualifiedTraineeID = SSO_UserRegistration.getUserID());
		setQualifiedEmployeeName(QualifiedEmployee = SSO_UserRegistration.getEmployeeName());
		QualifiedEmployeeID = SSO_UserRegistration.getEmployeeID();
		QualifiedTraineePsw = ConfigsReader.getPropValue("SSONewUserRegPassword");
		SetToBeRetrainedEmployeeName(ToBeRetrainedEmployee = testData.get("ToBeRetrainedEmployeee"));
		setToBeRetrainedTraineeID(ToBeRetrainedTraineeID = testData.get("tobeRetrainedTraineeID"));
		ToBeRetrainedEmployeeID = testData.get("tobeRetrainedEmployeeID");
		ToBeRetrainedTraineePsw = testData.get("tobeRetrainedTraineePsw");

		setQPPendingUser(QPPendingUser = testData.get("RespondQuestionPaperPendingUser"));
		setQPPendingUserID(QPPendingUserID = testData.get("RespondQuestionPaperPendingUserID"));
		QPPendingUserPsw = testData.get("RespondQuestionPaperPendingUserPsw");
		QPPendingEmployeeID = testData.get("RespondQuestionPaperPendingEmployeeID");

		TimeUtil.mediumWait();
		String CourseNameValue = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, courseSession, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Registration screen", test);
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(searchByCourseName, courseNameLike_New,
				CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		clickAndWaitforNextElement(apply_New, displayCourse, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(displayCourse);

		clickAndWaitforNextElement(displayCourse, addItem,
				CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredScheduledCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(addItem);
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(sessionInfoManagement);
		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		searchTxt.sendKeys(testData.get("SessionInfMngtValue"));
		click2(sessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());

		clickAndWaitforNextElement(traineeAddItem, traineeAddCommonGlobalSearch,
				CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
//			click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
//					CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
//					CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
//					CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
//			TimeUtil.shortWait();
		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				CM_CourseSession.getQualifiedEmployee(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				CM_CourseSession.getToBeRetrainedEmployeeName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("RespondDocReadingPendingUser"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				CM_CourseSession.getQPPendingUser(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());

		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}

		scrollToViewElement(documentReading);
		click2(documentReading, CourseSessionStrings.Select_TM_DR_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_DR_SS.getCourseSessionStrings());
		click2(assesmentYesButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());
		click2(MQA_No, CourseSessionStrings.MQA_NO_DC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQA_NO_SS.getCourseSessionStrings());
		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		click2(selectDoc, CommonStrings.Select_DC.getCommonStrings(), CommonStrings.Select_AC.getCommonStrings(),
				CommonStrings.Select_AR.getCommonStrings(), CommonStrings.Select_SS.getCommonStrings());
		scrollToViewElement(submit);
		// scrollToViewElement(LineofApprover);
//				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
//						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
//						CommonStrings.sendKeys_AR.getCommonStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
//						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
//				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
//						CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//						CommonStrings.Password_SS.getCommonStrings());
//				TimeUtil.shortWait();
//				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
//						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
//						CommonStrings.Esign_Proceed_SS.getCommonStrings());
		// saveCourseSessionUniqueCode(driver, confirmationText);

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.CourseSession_Esign_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSession_Esign_AR.getCourseSessionStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());

		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	public void courseSession_OnLine_WithExam_Content_Delivery_Yes(HashMap<String, String> testData) {
		setQualifiedEmployeeName(QualifiedEmployee = testData.get("qualifiedEmployeee"));
		QualifiedTraineePsw = testData.get("qualifiedTraineePsw");
		// QualifiedEmployee = testData.get("qualifiedEmployeee");
		QualifiedTraineeID = testData.get("qualifiedTraineeID");
		QualifiedEmployeeID = testData.get("qualifiedEmployeeID");
		System.out.println(QualifiedTraineeID);
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(proposeMenu);
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		scrollToViewElement(courseSession);
		click2(courseSession, CourseSessionStrings.CourseSession_Menu.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AC.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_AR.getCourseSessionStrings(),
				CourseSessionStrings.Propose_CoursSession_SS.getCourseSessionStrings());

		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		TimeUtil.shortWait();

		if (CM_Course.getSCHType().equals("Unscheduled")) {
			click2(unScheduleCourse, CourseSessionStrings.UnscheduledTab_DC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnscheduledTab_SS.getCourseSessionStrings());
		}
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseSessionStrings.SearchBy_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_AR.getCourseSessionStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByCourseName, CourseSessionStrings.SearchBy_CourseName_DC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AC.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_AR.getCourseSessionStrings(),
				CourseSessionStrings.SearchBy_CourseName_SS.getCourseSessionStrings());

		String CourseNameValue = CM_Course.getCourse();
//		String CourseNameValue = "AutomationCourseWBXV";
		sendKeys2(courseNameLike_New, CourseSessionStrings.Like_CourseName_DC.getCourseSessionStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());

		click2(apply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();
		waitForElementVisibile(displayCourse);
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		click2(displayCourse, CourseSessionStrings.RegisteredCourse_DC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AC.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_AR.getCourseSessionStrings(),
				CourseSessionStrings.RegisteredCourse_SS.getCourseSessionStrings());

		waitForElementVisibile(addItem);
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		subGroupNameFind_New.clear();
		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		scrollToViewElement(nextNew);
		click2(nextNew, CommonStrings.NextButton_DC.getCommonStrings(),
				CourseSessionStrings.Click_NextButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_NextButton_AR.getCourseSessionStrings(),
				CommonStrings.NextButton_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(sessionInfoManagement, CourseSessionStrings.SessionInfo_DC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AC.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_AR.getCourseSessionStrings(),
				CourseSessionStrings.SessionInfo_SS.getCourseSessionStrings());

		TimeUtil.mediumWait();

		click2(abssessionInfoManagemntSrchOpt, CourseSessionStrings.SIM_Online_DC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AC.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_AR.getCourseSessionStrings(),
				CourseSessionStrings.SIM_Online_SS.getCourseSessionStrings());

		TimeUtil.shortWait();
		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), QualifiedEmployee,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		removeFocus(sendTextTraineeAddCommonGlobalSearch);
		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());

//		TimeUtil.mediumWait();
//
//		click2(traineeAddItem, CourseSessionStrings.AddItem_Trainee_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Trainee_SS.getCourseSessionStrings());
//
//		TimeUtil.mediumWait();
//
//		click2(traineeAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
//				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
//				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
//				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
//
//		sendKeys2(sendTextTraineeAddCommonGlobalSearch,
//				CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(), testData.get("QualifiedEmployeee"),
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
//		removeFocus(sendTextTraineeAddCommonGlobalSearch);
//		click2(traineeAdd, CourseSessionStrings.Add_RequiredTrainee_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		click2(traineeAdd_New, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_Trainee_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		isAlertPresent(driver);
		String AverageSessionSizeValue1 = totalSelectedTrainees.getAttribute("value");
		setRegAverageSessionSizeValue(RegAverageSessionSizeValue = AverageSessionSizeValue1);
		if (OJTRemarks.isDisplayed()) {
			sendKeys2(OJTRemarks, CourseSessionStrings.Enter_OJTRemarks_DC.getCourseSessionStrings(),
					testData.get("OJTremarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseSessionStrings.Enter_OJTRemarks_SS.getCourseSessionStrings());
		}
		scrollToViewElement(internalClassroom);
		click2(internalClassroom, CourseSessionStrings.Select_TM_IntClassRoom_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_TM_IntClassRoom_SS.getCourseSessionStrings());
		sendKeys2(avgSessionSize, CourseSessionStrings.Enter_AvgSessSize_DC.getCourseSessionStrings(),
				RegAverageSessionSizeValue, CourseSessionStrings.Enter_AvgSessSize_AC.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_AR.getCourseSessionStrings(),
				CourseSessionStrings.Enter_AvgSessSize_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(contentDeliveryYes, CourseSessionStrings.Select_ContentDeliveryNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_ContentDeliveryNo_SS.getCourseSessionStrings());
		click2(evaluationNo, CourseSessionStrings.Select_EvaluationRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_EvaluationRequiredNo_SS.getCourseSessionStrings());
		click2(feedBackNo, CourseSessionStrings.Select_FeedbackRequiredNo_DC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AC.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_AR.getCourseSessionStrings(),
				CourseSessionStrings.Select_FeedbackRequiredNo_SS.getCourseSessionStrings());
		scrollToViewElement(assesmentNoButton);
		click2(assesmentNoButton, CourseSessionStrings.Assessment_Yes_DC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AC.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_AR.getCourseSessionStrings(),
				CourseSessionStrings.Assessment_No_SS.getCourseSessionStrings());

		click2WithScrollDown(nextNew1, CourseSessionStrings.UserSelectionScreenNextBtn_DC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AC.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtnWithApprove_AR.getCourseSessionStrings(),
				CourseSessionStrings.UserSelectionScreenNextBtn_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(startDate, CourseSessionStrings.StartDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
		selectStartdateCurrentdate();
		click2(endDate, CourseSessionStrings.EndDate_DC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AC.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_AR.getCourseSessionStrings(),
				CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
		selectEnddateCurrentdate();
		TimeUtil.shortWait();
		click2(lastDateResponse, CourseSessionStrings.Response_DC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AC.getCourseSessionStrings(),
				CourseSessionStrings.Response_AR.getCourseSessionStrings(),
				CourseSessionStrings.Response_SS.getCourseSessionStrings());
		selectlastdateCurrentdate();
		selectStartTimeInCourseSessionOQFormat();
		selectEndTimeInCourseSession();
		click2(trainerAddItem1, CourseSessionStrings.TrainerAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainerAddNew1, CourseSessionStrings.AddButtonAgainstTrainer_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainer_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainer_AddNew1, CourseSessionStrings.TrainerAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainerAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue, CourseSessionStrings.TrainingVenueAddItem_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddItem_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingvenueAddCommonGlobalSearch, CommonStrings.Click_SearchThisPage_DC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AC.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_AR.getCommonStrings(),
				CommonStrings.Click_SearchThisPage_SS.getCommonStrings());
		sendKeysAndRemoveFocus(searchTrainingVenue, CourseSessionStrings.Enter_TraineeName_DC.getCourseSessionStrings(),
				testData.get("trainingVenueName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Enter_TraineeName_SS.getCourseSessionStrings());
		click2(trainingVenueAddNew, CourseSessionStrings.AddButtonAgainstTrainingVenue_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddButtonAgainstTrainingVenue_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(trainingVenue_AddNew, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());

		TimeUtil.shortWait();
		if (CM_Course.getSCHType().equals("Unscheduled")) {
			sendKeys2(unScheduleRemarks, CourseSessionStrings.UnsheduleReamarks_DC.getCourseSessionStrings(),
					testData.get("RegistrationRemarks"),
					CourseSessionStrings.UnsheduleReamarks_AC.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_AR.getCourseSessionStrings(),
					CourseSessionStrings.UnsheduleReamarks_SS.getCourseSessionStrings());
		}
		click2(selectAllOptions, CourseSessionStrings.TrainingVenueAddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.TrainingVenueAddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		try {

			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {

				scrollToViewElement(noOfApprovalsReqForInitiationHeading);

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}
}
// This method is for Course Session Offline without Assessment.
