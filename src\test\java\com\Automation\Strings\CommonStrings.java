package com.Automation.Strings;

public enum CommonStrings {

	Selction_AC("Selection should be accepted.</div>"), Selction_AR("Selection is getting accepted.</div>"),

	EnterValue_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	dropdown_AR("Selection is getting accepted.</div>"), dropdown_AC("Selection should be accepted.</div>"),
	sendKeys_AC("Entered value should be displayed for the field."),
	sendKeys_AR("Entered value is getting displayed for the field."),

	// Company

	Company_DC_320("Open the epiq 3.2.0 application URL and enter the valid 'Company' in 'Company' field."),
	
	

	Company_DC(
			"Open the epiq 3.1.0 (Service Pack 2) application URL and enter the valid 'Company' in 'Company' field."),
	
	
	Company_DC_310(
			"Open the epiq 3.1.0 (Service Pack 10) application URL and enter the valid 'Company' in 'Company' field."),
	
	
	SSO_Company_DC(
			"Open the SSO 2.0.0 (Service Pack 2) application URL and enter the valid 'Company' in 'Company' field."),
	SSO_210_Company_DC("Open the SSO 2.1.0 application URL and enter the valid 'Company' in 'Company' field."),
	SSO_200_Company_DC("Open the SSO 2.0.0 application URL and enter the valid 'Company' in 'Company' field."),
	Company_New_DC("Enter the valid 'Company' in 'Company' field."),
	Company_AC("Entered value should be  displayed  in 'Company' field."),
	Company_AR("Entered value is getting displayed in 'Company' field."), Company_SS("'Company'"),

	// User Name

	RespondCI_DC(
			"Enter the valid 'User ID' of the user who is selected at above proposed course session in 'User ID' field."),

	// Self Nominatiion login
	SelfNomination_DC("Enter the valid user id of the user other than the above selected subgroup in course session"),

	ApproverSubgroupUsername_DC(
			"Enter the valid 'User ID' of the authorized user to approve above registered Subgroup in 'User ID' field."),
	ApproverTopicUsername_DC(
			"Enter the valid 'User ID' of the authorized user to approve above registered Topic in 'User ID' field."),

	ApproverDocumentRegistrationUsername_DC(
			"Enter the valid 'User ID' of the authorized user to approve above registered Document Name in 'User ID' field."),

	// Self Nomination Respond Question paper login
	SelfNominated_RespondQP_DC("Enter the valid 'User ID' of the self nominated user  in 'User ID' field."),

	TraineeResDoc_DC(
			"Enter the valid 'User ID' of the user who is selected as a trainee in the above proposed session in 'User ID' field."),
	TraineeResQP_DC(
			"Enter the valid 'User ID' of the user who is selected as a trainee in the above proposed session in 'User ID' field."),
	reportsUser_DC("Enter the valid 'User ID' of the authorized user to view reports in 'User ID' field."),
	UserName_DC("Enter the valid 'User ID' in 'User ID' field."),
	LAUserName_DC(
			"Enter the valid 'User ID' of the authorized user to approve above registred Job Responsibility in 'User ID' field."),
	DefaultUserName_DC("Enter the valid default 'User ID' in 'User ID' field."),

	OJTUsername_DC(
			"Enter the 'User ID' of the above user who is selected for On Job Training to respond Question Paper in 'User ID' field."),

	Evaluator_DC(
			"Enter the valid 'User ID' of the user who is selected as a 'Evaluator' at Record Attendance for the above proposed batch in 'User ID' field."),

	UserName_Reports("Enter the valid 'User ID' of the user who has right to view reports in 'User ID' field."),
	UserName_AC("Entered value should displayed in 'User ID' field in hidden mode.</div>"),
	UserName_AR("Entered value is getting displayed in 'User ID' field in hidden mode."), UserName_SS("'User ID"),

	UserName_AR1(
			"Login with the registered user with the assigned mail generated temporary password field in hidden mode."),

	UserName_RESET_PASSWORD_DC("Enter valid 'User ID' of the user registration re-initiated in 'User ID' field."),

	// Password

	Password_DC("Enter valid Password in the 'Password' field."),
	Password_AC("Entered value should be displayed in 'Password' field in hidden mode."),
	Password_AR("Entered value is getting displayed in 'Password' field in hidden mode."), Password_SS("'Password'"),

	// Login Button

	Loginbutton_DC("Click on 'Login' button."), Loginbutton_AC("Option to select plants should be available."),
	Loginbutton_AR("Option to select plants are available."), Loginbutton_SS("'Login'"),

	// Login Button

	Loginbutton1_AC("‘User Confirmation’ screen should be displayed."
			+ "<div><b>*</b> The screen should be displayed with the details of the User entered during registration.<div>"
			+ "<div><b>*</b> Screen should contain ‘Company Code’, ‘Company Name’, 'Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’, fields in readable format only..<div>"
			+ "<div><b>*</b>Option to select ‘Decision’and enter ‘Remark(s) /Reason(s)’ fields should be available.<div>"
			+ "<div><b>*</b>All the particulars should  be displayed in read only format.<div>"),

	Loginbutton1_AR("‘User Confirmation’ screen is getting displayed."
			+ "<div><b>*</b> The screen is getting displayed  with the details of the User entered during registration.<div>"
			+ "<div><b>*</b> Screen is getting displayed  with ‘Company Code’, ‘Company Name’, 'Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’, fields in readable format only.<div>"
			+ "<div><b>*</b>Option to select ‘Decision’and enter ‘Remark(s) /Reason(s)’ fields are available.<div>"
			+ "<div><b>*</b>All the particulars is displayed in read only format.<div>"),

	Submit_Button1_AC("‘Login Password - Change Login Password’ screen should be displayed"
			+ "<div><b>*</b> Screen should contain ‘User ID’, ‘Current Password’, ‘NewPassword’, ‘Confirm Password’ fields.<div>"
			+ "<div><b>*</b>‘User ID’ should be displayed in non-readable format.<div>"),

	Submit_Button1_AR("‘Login Password - Change Login Password’ screen is getting displayed"
			+ "<div><b>*</b> Screen is getting displayed with ‘User ID’, ‘Current Password’, ‘NewPassword’, ‘Confirm Password’ fields.<div>"
			+ "<div><b>*</b>‘User ID’ is getting displayed in non-readable format.<div>"),

	// SSO Login Button

	// SignOut Button

	SignOut_icon_DC("Click on 'Signout' icon."), SignOut_icon_AC("'Login' screen should be displayed.</div>"),
	SignOut_icon_AR("'Login' screen is getting displayed.</div>"), SignOut_icon_SS("'Signout'"),

	Signoutbutton_DC("Click on 'Signout'" + "</br>" + "button."),
	Signoutbutton_AC("<div><b>*</b> 'Signout' button" + "</br>" + "should be clicked" + "</br>" + "successfully and"
			+ "</br>" + "'Login' screen should" + "</br>" + "be displayed</div>"),
	Signoutbutton_AR("'Signout' button" + "</br>" + "is clicked" + "</br>" + "successfully and" + "</br>"
			+ "'Login' screen has" + "</br>" + "been displayed"),
	Signoutbutton_SS("'Signout'"),

	// Logout Button
	LogOut_icon_DC("Click on 'Logout' button."),
	// learn-iq Plant Selection

	Plant1_DC("Click on 'Plant1'."), Plant1_AC("Home screen of the login user should be displayed.</div>"),
	Plant1_AR("Home Screen of the login user is getting displayed."), Plant1_SS("'Plant1'"),

	// Master Plant Selection

	MasterPlant_DC("Click on 'Master Plant'."),
	MasterPlant_AC("Home screen of the login user should be displayed.</div>"),
	MasterPlant_AR("Home Screen of the login user is getting displayed.</div>"), MasterPlant_SS("'Master Plant'."),

	// Plant Navigation

	PlantSelectionDropDown_DC("Click on plant selection dropdown."),
	PlantSelectionDropDown_AC("Option to select the plants should be displayed."),
	PlantSelectionDropDown_AR("Option to select the plants is getting displayed."),
	PlantSelectionDropDown_SS("'Plants'"),
//Master Plant
	MasterPlantDropDown_DC("Select the 'Master Plant'."),
	MasterPlantDropDown_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Home screen of the login user is getting displayed.</div>"),
	MasterPlantDropDown_AC("Selection should be accepted." + "<div><b>*</b> Home screen of the login user "
			+ "should be displayed.<>/div"),
	MasterPlantDropDown_SS("''Master Plant"),

	// Learn-iq icon

	learnIQPlantDropDown_DC("Select the 'Plant1'."),
	learnIQPlantDropDown_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Home screen of the login user is getting displayed.</div>"),
	learnIQPlantDropDown_AC("Selection should be accepted." + "<div><b>*</b> Home screen of the login user "
			+ "should be displayed.</div>"),
	learnIQPlantDropDown_SS("'Plant1'"),
//Learn icon
	LearnIQ_ICON_DC("Click on 'learn-iq' icon."),
	LearnIQ_ICON_AC(
			"Option to select 'Course Manager, Document Manager and System Manager' should be available.</div>"),
	Default_LearnIQ_ICON_AC("Option to select 'System Manager' should be available.</div>"),
	LearnIQ_ICON_AR("Option to select 'Course Manager, Document Manager and System Manager' are available.</div>"),
	Default_LearnIQ_ICON_AR("Option to select 'System Manager' is available.</div>"),
	LearnIQ_ICON_SS("'learn-iq' icon"),

	// Default User

	Click_SYSM_DC("Click on 'System Manager'."),
	Click_SYSM_AC("Option to select 'Admin & Security' should be available."),
	Click_SYSM_AR("Option to select 'Admin & Security' is available."), Click_SYSM_SS("'System Manager'"),

	Click_AdminSec_DC("Click on 'Admin & Security' menu."),
	Click_AdminSec_AC("Assigned sub menus for the user under 'Admin & Security' menu should be displayed."),
	Click_AdminSec_AR("'Assigned sub menus for the user under 'Admin & Security' menu should be displayed."),
	Click_AdminSec_SS("'Admin & Security'"),

	Click_DefaultInitiate_AC("'Initiate' menu should be displayed."),
	Click_DefaultInitiate_AR(
			"Option to select 'Set Global Profile, Register Role and Set Central Comfiguration' menus should be displayed."),
	Click_DefaultInitiate_DC(
			"Option to select 'Set Global Profile, Register Role and Set Central Comfiguration' menus are getting displayed."),

	Click_SetCentralConfig_DC("Click on 'Set Central Configuration' menu"),
	Click_SetCentralConfig_AC("'Central Configuration Central Configuration Setting' screen should be displayed."),
	Click_SetCentralConfig_AR("'Central Configuration Central Configuration Setting' screen is getting displayed."),
	Click_SetCentralConfig_SS("'Set Central Configuration'"),

	Click_CentralConfigurationAuditTrails_DC("Click on 'Central Configuration Audit Trails' submenu."),
	Click_CentralConfigurationAuditTrails_AC("'Central Configuration Audit Trails' screen should be displayed."),
	Click_CentralConfigurationAuditTrails_AR("'Central Configuration Audit Trails' screen is getting displayed."),
	Click_CentralConfigurationAuditTrails_SS("'Central Configuration Audit Trails'"),

	Close_CentralConfigurationAuditTrails_AC("'Central Configuration Audit Trails' screen should be displayed.</div>"),
	Close_CentralConfigurationAuditTrails_AR("'Central Configuration Audit Trails' screen is getting displayed.</div>"),

	// OJT Yes

	Select_OJT_YES_DC("Select 'On Job Training Required' as 'Yes''"),
	Select_OJT_NO_DC("Select 'On Job Training Required' as 'No''"), Select_OJT_YES_SS("'On Job Training'"),

	// Job Responsibility

	Select_UserAcceptance_NO_DC("Select 'Job Responsibility Acceptance User Acceptance' as 'No'"),
	Select_UserAcceptance_NO_SS("'User Acceptance'"),

	Select_AD_NO_DC("Select 'Authorized Deputy Acceptance' as 'No'"), Select_AD_NO_SS("'Authorized Deputy Acceptance'"),

	// Default User Submit button

	Esign_Default_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Activity: Central Configuration Setting'.</div>"),
	Esign_Default_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Activity: Central Configuration Setting'.</div>"),

	// Configuration Screen

	Click_DoneatConfig_DC("Click on 'Done' button."), Click_DoneatConfig_SS("'Done'"),

	// Done after object transactions

	Click_Done_DC("Click 'Done' button."), Click_Done_AC("Home screen of the login user should be displayed.</div>"),
	Click_Done_AR("Home screen of the login user is getting displayed.</div>"), Click_Done_SS("'Done'"),

	// No of Approvals in Configuration
	NoOfApprovals_DC("Click on 'Registration' dropdown under 'No. of Approvals Required:'."),
	NoOfApprovals_AC("Option to select from '0' to '6' should be available."),
	NoOfApprovals_AR("Option to select from '0' to '6' are available.</div>"),
	NoOfApprovals_SS("'No of Approvals Required'"),

	// No of Approvals for Modification
	Mod_NoOfApprovals_DC("Click on 'Modification' dropdown under 'No. of Approvals Required:'."),

	Zero_Approvals_DC("Select '0' approvals."), Zero_Approvals_AC("Selection  should be accepted."),
	Zero_Approvals_AR("Selection is getting accepted"), Zero_Approvals_SS("'0' Approvals."),

	One_Approval_DC("Select '1' approval"),

	// Call E-Sign at Registration Initiation
	Call_ESign_RegInitiation_DC("Select 'Registration' check box under 'Call E-Sign: At: Initiation'"),
	Call_ESign_RegInitiation_AC("Selection should be accepted.</div>"),
	Call_ESign_RegInitiation_AR("Selection is getting accepted."), Call_ESign_RegInitiation_SS("'Registration'"),

	// Call E-Sign at Modification Initiation
	Call_ESign_Modification_DC("Select 'Modification' check box under 'Call E-Sign: At: Initiation'"),
	Call_ESign_Modification_AC("Selection should be accepted.</div>"),
	Call_ESign_Modification_AR("Selection is getting accepted."), Call_ESign_Modification_SS("'Modification'"),

	Esign_Proceed_DC("Click on 'Proceed' button."), Esign_Proceed_SS("'Proceed'"), SubmitwithEsign_SS("'E-Sign'"),

	// Close audit trails
	Close_AuditTrails_DC("Click on 'Close' icon."), Close_AuditTrails_SS("'Audit Trails'"), Close_Icon_SS("'Close'"),

	// Approval Value
	Enter_Approval1_DC("Enter the value '1' under 'Registration' of 'No. of Approvals Required:'"),
	Approval_1SS("'1' Approval"),

	// Remarks
	Remarks_DC("Enter the value less than or equals to 250 characters in 'Remark(s)/Reason(s)' field."),
	Remarks_AC("Entered value should be displayed for the field.</div>"),
	Remarks_AR("Entered value is getting displayed for the field."), Remarks_SS("'Remarks/Reason(s)'."),

	// Submit Button
	Submit_Button_DC("Click on 'Submit' Button."),
	Submit_Button_AC("'Configuration Registered' confirmation message should be displayed with 'Done' button.</div>"),
	Submit_Button_AR("'Configuration Registered' confirmation message is getting displayed with 'Done' button.</div>"),
	Submit_Button_SS("'Submit' button"), Submit_Button_Esign_SS("E-Sign window"), Submit_SS("'Submit' button"),

	// ApplyButton
	ApplyButton_DC("Click on 'Apply' button."),
	ApplyButton_AC("Records should be displayed based on the search criteria."),
	ApplyButton_AR("Records are getting displayed based on the search criteria.</div>"),
	ApplyButton_SS("'Apply' button"),

	// Menus
	CM_Menus_DC("Click on 'Course Manager'."), SYS_Menus_DC("Click on 'System Manager'."),
	DM_Menus_DC("Click on 'Document Manager'."), CM_Menus_AC("Assigned menus for user  should  be displayed.</div>"),
	CM_Menus_AR("Assigned menus for user are getting displayed.</div>"), CM_Menus_SS("Menus"),

	CourseManagerMenu_AC("<div><b>*</b> 'CourseManager' menu" + "</br>" + " should be displayed," + "</br>"
			+ "other menus should" + "</br>" + "be displayed(if any).</div>"),
	CourseManagerMenu_AR("'CourseManager' menu" + "</br>" + "and other menus" + "</br>" + "are displayed"),
	CourseManagerMenu_SS("'CourseManager'"),

	// Configure Menu
	CM_ConfigureMenu_DC("Click on 'Configure' menu"),
	CM_ConfigureMenu_AC("Assigned sub menus for the user under 'Configure' menu should be displayed.</div>"),
	CM_ConfigureMenu_AR("Assigned sub menus for user under 'Configure' menu are getting displayed."),
	CM_ConfigureMenu_SS("'Configure' menu"),
//Initiate Menu
	CM_InitiateMenu_DC("Click on 'Initiate' menu"), SM_InitiateMenu_DC("Click on 'Initiate' submenu"),

	CM_InitiateMenu_AC("Assigned sub menus for the user under 'Initiate' menu should be displayed.</div>"),
	CM_InitiateMenu_AR("Assigned sub menus for the user under 'Initiate' menu are getting displayed.</div>"),
	CM_InitiateMenu_SS("'Initiate'"),

	Centalconfigurationaudittrail_DC("Click on 'Central Configuration AuditTrails' menu"),
	Centalconfigurationaudittrail_AC(
			"Assigned sub menus for the user under 'Central Configuration AuditTrails' menu should be displayed.</div>"),
	Centalconfigurationaudittrail_AR(
			"Assigned sub menus for the user under 'Central Configuration AuditTrails' menu are getting displayed.</div>"),
	Centalconfigurationaudittrail_SS("'Central Configuration AuditTrails'"),

	// Approve Menu
	CM_ApproveMenu_DC("Click on 'Approve' menu"),
	CM_ApproveMenu_AC("Assigned sub menus for the user under 'Approve' menu should be displayed.</div>"),
	CM_ApproveMenu_AR("Assigned sub menus for the user under 'Approve' menu are getting displayed.</div>"),
	CM_ApproveMenu_SS("'Approve'"),

	// Audit Trails

	CM_AudiTrailsMenu_DC("Click on 'Audit Trails' menu"),
	CM_AudiTrailsMenu_AC("Assigned sub menus for the user under 'Audit Trails' menu should be displayed.<div>"),
	CM_AudiTrailsMenu_AR("Assigned sub menus for the user under 'Audit Trails' menu are getting displayed.<div>"),
	CM_AudiTrailsMenu_SS("'Audit Trails'"),

	CM_AuditTrailsMenu_DC("Click  on 'Course Manager'."), CM_AuditTrailsMenu_SS("Menus"),
//Record Menu 
	Record_menu_DC("Click on 'Record' menu."),
	Record_menu_AC("Assigned sub menus for the user under 'Record' menu should be displayed.</div>"),
	Record_menu_AR("Assigned sub menus for the user under 'Record' menu are getting displayed.</div>"),
	Record_menu_SS("'Record'"),

	// Propose Menu

	Propse_Menu_DC("Click 'Propose' menu'."),
	Propse_Menu_AC("Assigned sub menus for the user under 'Propose' menu should be displayed.</div>"),
	Propse_Menu_AR("Assigned sub menus for the user under 'Propose' menu is getting displayed.</div>"),
	Propse_Menu_SS("'Propose'"),

	// Next Button

	NextButton_DC("Click on 'Next' button"), NextButton_SS("'Next' button"),

	// search this page icon clicking

	Click_SearchThisPage_DC("Click on 'Search This Page' icon."),
	Click_SearchThisPage_AC("'Search This Page' text box should be displayed.</div>"),
	Click_SearchThisPage_AR("'Search This Page' text box is getting displayed.</div>"),
	Click_SearchThisPage_SS("'Search This Page'"),

	// Selecting the documents

	Select_DC("Select 'Select All' checkbox"), Select_AC("Selected document(s) should be accepted."),
	Select_AR("Selected document(s) is getting accepted."), Select_SS("Select Documents."),

	// Search By

	SearchBy_DC("Click on 'Search By' dropdown."), SearchBy_SS("'Search By' dropdown."),

	// Prepare Menu

	Prepare_DC("Click on 'Prepare' menu"),
	Prepare_AC("Assigned sub menus for the user under 'Prepare' menu should be displayed.</div>"),
	Prepare_AR("Assigned sub menus for the user under 'Prepare' menu are getting displayed.</div>"),
	Prepare_SS("'Prepare'"),
//Click on User  Groups
	SYS_UserGroupsMenu_DC("Click on 'User Groups' menu"),
	SYS_UserGroupsMenu_AC("Assigned sub menus for the user under 'User Groups' menu should be displayed.</div>"),
	SYS_UserGroupsMenu_AR("Assigned sub menus for user under 'User Groups' menu are getting displayed."),
	SYS_UserGroupsMenu_SS("'User Groups'"),

	// Click on Admin&Security
	SYS_AdminSecurityMenu_DC("Click on 'Admin & Security' menu."),
	SYS_AdminSecurityMenu_AC(
			"Assigned sub menus for the user under 'Admin & Security' menu should be displayed.</div>"),
	SYS_AdminSecurityMenu_AR("Assigned sub menus for user under 'Admin & Security' menu are getting displayed."),
	SYS_AdminSecurityMenu_SS("'Admin & Security'"),

	// Click on
	SSO_AudittrailsUserRegistrationMenu_DC("Click on 'UserRegistration' menu."),
	SSO_AudittrailsUserRegistrationMenu_AC(
			"Assigned sub menus for the user under 'Admin & Security' menu should be displayed.</div>"),
	SSO_AudittrailsUserRegistrationMenu_AR(
			"Assigned sub menus for user under 'Admin & Security' menu are getting displayed."),
	SSO_AudittrailsUserRegistrationMenu_SS("'Admin & Security'"),

	RoleAudittrails_DC("Click on 'Role' menu."),
	RoleAudittrails_AC("'Role Audit Trails' screen should be displayed.</div>"),
	RoleAudittrails_AR("'Role Audit trails' screen is getting displayed.</div>"),
	RoleAudittrails_SS("'Role Audit Trails' screen."),

	SYS_InitiateMenu_DC("Click on 'Initiate' menu."),
	SYS_InitiateMenu_AC("Assigned sub menus for the user under 'Initiate' menu should be displayed.</div>"),
	SYS_InitiateMenu_AR("Assigned sub menus for user under 'Initiate' menu are getting displayed."),
	SYS_InitiateMenu_SS("'Initiate'"),

	// Respond Menu

	Respond_menu_DC("Click on 'Respond' menu."),
	Respond_menu_AC("Assigned sub menus under 'Respond' menu for the user should  be displayed.</div>"),
	Respond_menu_AR("Assigned sub menus under 'Respond' menu for the user are getting  displayed.</div>"),
	Respond_menu_SS("'Menus'"),

	// Record Menu
	SYS_ModifyMenu_DC("Click on 'Modify' menu"),
	SYS_ModifyMenu_AC("Assigned sub menus for the user under 'Modify' menu should be displayed.</div>"),
	SYS_ModifyMenu_AR("Assigned sub menus for user under 'Modify' menu are getting displayed."),
	SYS_ModifyMenu_SS("'Modify'"),

	SelectRadioBtn_DC("'Select' radio button"), SelectRadioBtn_AC("Radio button should be selected"),
	SelectRadioBtn_AR("Radio button is getting selected"), SelectRadioBtn_SS("'Select'"),

	CM_RecordMenu_DC("Click on 'Record' menu"),
	CM_RecordMenu_AC("Assigned sub menus for the user under 'Record' menu should be displayed.</div>"),
	CM_RecordMenu_AR("Assigned sub menus for the user under 'Record' menu are getting displayed.</div>"),
	CM_RecordMenu_SS("'Record'"),

	// Add Button
	Click_Add_DC("Click on 'Add' button."),

	// System Manager

	Click_SM_DC("Click on 'System Manager'"), Click_SM_AC("Assigned menus under 'System Manger' should be displayed.'"),
	Click_SM_AR("Assigned menus under 'System Manger' are getting displayed.'"), Click_SM_SS("'System Manager'"),

	// Admin % Security SM

	Click_SYS_AdminSec_AC("Assigned menus under 'Admin & Security' should be displayed."),
	Click_SYS_AdminSec_AR("Assigned menus under 'Admin & Security' are getting displayed."),

	// Advance Search ICON

	Click_AdvSearch_DC("Click on 'Advance Search' icon."),
	Click_AdvSearch_AC("Available search options should be displayed."),
	Click_AdvSearch_AR("Available search options are getting displayed."),
	Click_AdvSearch_SS("Click on 'Advance Search' icon."),

	// Central Esign Proceed

	Esign_Proceed_AC("'Updated Successfully' confirmation message should be displayed with 'Done' button."),
	Esign_Proceed_AR("'Updated Successfully' confirmation message is getting displayed with 'Done' button."),

	// New User registered login confirmation through SSO

	NewUser_Login_DC("Enter the  'User ID' of the above registered user in 'User ID' field."),
	TemporaryPassword_DC("Enter valid 'Temporary Password Allotted' in the 'Password' field."),
	NewUserLoginbutton_AC("'User Registraion' screen should be displayed."),
	NewUserLoginbutton_AR("'User Registraion' screen is getting displayed."),

	NewUserLoginbuttonEPIQ_AC("'User Confirmation' screen should be displayed."),
	NewUserLoginbuttonEPIQ_AR("'User Confirmation' screen is getting displayed."),

	// Approve

	ApproveMenu_DC("Click on 'Approve' menu"),
	ApproveMenu_AC("Assigned sub menus for the user under 'Approve' menu should be displayed.</div>"),
	ApproveMenu_AR("Assigned sub menus for user under 'Approve' menu are getting displayed."),
	ApproveMenu_SS("'Approve'"),

	// Biometric Required

	Select_Biometric_No("Select 'Biometric Required' as 'No'."), BiometricRequired_SS("'Biometric Required'"),

	// Target Date
	Select_TargetDate_No("Select 'Target Date for Question Paper Response' as 'No'."),
	Select_TargetDate_SS("'Target Date for Question Paper Response'"),

	Select_AutoDeactivation_No("Select 'Auto Deactivation' as 'No'."),
	Select_AutoDeactivation_SS("'Auto Deactivation'"),

	// Retake Assessment
	Select_RetakeAssessment_No("Select 'Retake Assessment Required' as 'No'."),
	Select_RetakeAssessment_SS("'Retake Assessment Required'"),

	// Jumbled Questions

	Select_JumbledQuestions_DC("Select 'Jumbled Questions Required' as 'No'"),
	Select_JumbledQuestions_SS("'Jumbled Questions Required'"),

	// Pending Evaluation days
	Enter_PendingEvalZero_DC("Enter the value '0' in 'Evaluation Days Period' text box"),
	Enter_PendingEval_SS("'Evaluation Days Period'"),

	// Answerpaper Submission Days Period

	Enter_AnswerPaperSubZero_DC("Enter the value '0' in 'Answer paper Submission Days Period' text box"),
	Enter_AnswerPaperSub_SS("'Answer paper Submission Days Period'"),

	// AssignmentRequired

	Select_AssignmentsReqNo_DC("Select 'Assignments Required' as 'No'"),
	Select_AssignmentsReq_SS("'Assignments Required'"),

	// Add_Button

	Click_AddButton_DC("Click 'Add' Button"), Click_AddButton_SS("'Add'"),

	// Search filter

	Click_SearchFilter_DC("Click on 'Search Filter'"),
	Click_SearchFilter_AC(
			"Screen should be displayed with 'Records Per Page','Page No.','Search By' fields and 'Apply' button."),
	Click_SearchFilter_AR(
			"Screen is getting displayed with 'Records Per Page','Page No.','Search By' fields and 'Apply' button."),
	Click_SearchFilter_SS("'Search Filter'"),

	// Select Approve Decision
	Select_Approve_DC("Select 'Approve' decision"),

	Select_Approve_SS("'Approve'"),

	// Click on Configuration AuditTrails
	ConfigAuditTrailsMenu_DC("Click on 'Configuration Audit Trails' menu"),
	ConfigAuditTrailsMenu_AC(
			"Assigned sub menus for the user under 'Configuration Audit Trails' menu should be displayed.</div>"),
	ConfigAuditTrailsMenu_AR(
			"Assigned sub menus for user under 'Configuration Audit Trails' menu are getting displayed."),
	ConfigAuditTrailsMenu_SS("'Configuration Audit Trails'"),

	// Click latest modified Modification tab

	Click_LastesModTab_DC("Click on the latest Modification Revision No.:(No) Tab."),
	Click_LastesModTab_AC("'Selected tab should be displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button should be enabled.<div>"),
	Click_LastesModTab_AR("'Selected tab is getting displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button is getting enabled.<div>"),
	Click_LastesModTab_SS("Modification Revision No.:(No) Tab."),

	// Profile Icon
	Click_ProfileIconofUser_DC("Click on 'Profile Icon' of the logged in user"),
	Click_ProfileIconofUser_AC(
			"Dropdown should be displayed with 'Employee ID, Home, Plant Selection, Session Rebuild, Refresh, Personal Particulars, Change Password' menus."),
	Click_ProfileIconofUser_AR(
			"Dropdown is getting displayed with 'Employee ID, Home, Plant Selection, Session Rebuild, Refresh, Personal Particulars, Change Password' menus."),
	Click_ProfileIconofUser_SS("'Profile Icon'"),

	Click_PersonalParticulars_DC("Click on 'Personal Particulars'"),
	Click_PersonalParticulars_AC(
			"Personal Particulars Personal Particulars for :(User Name) screen should be displayed."
					+ "<div><b>*</b> The screen should be displayed with the details 'First Name, Last Name, Employee ID, Email ID, Reporting To, Phone Extn., and Address'.<div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"
					+ "<div><b>*</b> The screen should be displayed with 'Back' and 'Edit' buttons.</div>"),
	Click_PersonalParticulars_AR(
			"Personal Particulars Personal Particulars for :(User Name) screen is getting displayed."
					+ "<div><b>*</b> The screen is getting displayed with the details 'First Name, Last Name, Employee ID, Email ID, Reporting To, Phone Extn., and Adress'.<div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"
					+ "<div><b>*</b> The screen is getting displayed with 'Back' and 'Edit' buttons.</div>"),
	Click_PersonalParticulars_SS("'Personal Particulars' menu"),

	// Search By Unique Code

	Select_UniqueCode_DC("Select 'Unique Code'."), Select_UniqueCode_SS("'Unique Code'"),

	// Enter Unique Code

	Enter_UniqueCode_DC("Enter the 'Unique Code' of the above registred transaction."),
	Enter_APRUniqueCode_DC("Enter the 'Unique Code' of the above approved transaction."),
	Enter_UniqueCode_SS("'Unique Code'"),

	// one approval for registration in configuration screen

	One_Approvals_DC("Select '1' approvals."), One_Approvals_AC("Selection  should be accepted."),
	One_Approvals_AR("Selection is getting accepted."), One_Approvals_SS("'1' Approvals."),

	// User groups approve menu
	SYS_UserGroupsApproveMenu_DC("Click on 'Approve' menu."),
	SYS_UserGroupsApproveMenu_AC("Assigned sub menus for the user under 'User Groups' menu should be displayed.</div>"),
	SYS_UserGroupsApproveMenu_AR("Assigned sub menus for user under 'User Groups' menu are getting displayed."),
	SYS_UserGroupsApproveMenu_SS("'Approve'"),

	// Click on Configuration AuditTrails
	SYS_ConfigAuditTrailsMenu_DC("Click on 'Configuration Audit Trails' menu"),
	SYS_ConfigAuditTrailsMenu_AC(
			"Assigned sub menus for the user under 'Configuration Audit Trails' menu should be displayed.</div>"),
	SYS_ConfigAuditTrailsMenu_AR(
			"Assigned sub menus for user under 'Configuration Audit Trails' menu are getting displayed."),
	SYS_ConfigAuditTrailsMenu_SS("'Configuration Audit Trails'"),

	Approve_Remarks_DC("Enter the value less than or equals to 250 characters in 'Remark(s)/Reason(s)' field."),
	Approve_Remarks_SS("Remark(s) / Reason(s)"),

	// Topic configuration Audit trails
	Click_Centralfor_ConfigAuditTrails_DC("Click on the above registered 'Unique Code'."),
	Click_Centralfor_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Centralfor_ConfigAuditTrails_AR("'Transactions' screen is getting.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Centralfor_ConfigAuditTrails_SS("'Central Configuration-Audit Trails."),

	// DocumentRegistration configuration proceed
	Click_CentralConfig_Proceed_AC(
			"'Central Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Central Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_CentralConfig_Proceed_AR(
			"'Central Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Central Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_CentralConfig_Proceed_SS("'Central Configuration - Audit Trails'."),

	// Configuration Audit Trailss
	Click_UC_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_UC_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should be displayed with Registration and Modification tabs with Revsion numbers.</div>"
			+ "<div><b>* </b>The screen should be displayed with 'Proceed' button.</div>"
			+ "<div><b>* </b>Proceed button should be displayed in disabled mode.</div>"),
	Click_UC_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with Registration and Modification tabs with Revsion numbers.</div>"
			+ "<div><b>* </b>The screen is getting displayed with 'Proceed' button.</div>"
			+ "<div><b>* </b>Proceed button is getting displayed in disabled mode.</div>"),
	Click_UC_ConfigAuditTrails_SS("'Transactions'."),

	// Call E Sign at Registration Approval
	Call_ESign_RegApproval_DC("Select 'Registration' check box under 'Call E-Sign: At: Approval'"),
	Call_ESign_RegApproval_SS("'Call E-Sign: At: Approval'"),

	SearchByDropdown_DC("Click on 'Search By' dropdown."), SearchByDropdown_SS("'Search By' dropdown"),

	Approval_DC("Select 'Decision' as 'Approve'."),
	Approval_AC("Selected option should be accepted for 'Decision' field.</div>"),
	Approval_AR("Selected option is getting accepted for 'Decision' field.</div>"), Approval_SS("'Approve'"),

	Click_Menu_for_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_Menu_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Menu_for_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Menu_for_ConfigAuditTrails_SS("'Transactions'"),
	SYS_UserGroupsPrintMenu_AC("Assigned sub menus for the user under 'Print' menu should be displayed.</div>"),
	SYS_UserGroupsPrintMenu_AR("Assigned sub menus for user under 'Print' menu are getting displayed."),

	// Employee
	// Search By Employee Name
	Select_EmployeeName_DC("Select 'EmployeeName'."), Select_EmployeeName_SS("'Employee Name'"),

	Enter_EmployeeName_DC("Enter the 'Employee Name' of the above registred transaction."),
	Enter_APREmployeeName_DC("Enter the 'Employee Name' of the above approved transaction."),
	Enter_EmployeeName_SS("'Employee Name'"),
	// Esign Window
	Esign_Window_SS("'E-Sign'"),
	
	REMARKS_REASONS("Remarks/Reasons"),

	// Record Induction
	RecordInduction_DC("Click on 'Record' menu"),
	RecordInduction_AC("Assigned sub menus for the user under 'Record' menu should be displayed.</div>"),
	RecordInduction_AR("Assigned sub menus for user under 'Record' menu are getting displayed."),
	RecordInduction_SS("'Record menu'"),

	// Respond Induction
	RespondInduction_DC("Click on 'Respond' menu"),
	RespondInduction_AC("Assigned sub menus for the user under 'Respond' menu should be displayed.</div>"),
	RespondInduction_AR("Assigned sub menus for user under 'Respond' menu are getting displayed."),
	RespondInduction_SS("'Respond menu'"),

	RespondInductionMenu_DC("Click on 'Induction Training' menu"),
	RespondInductionMenu_AC("'Accept Induction Training' menu should be displayed.</div>"),
	RespondInductionMenu_AR("'Accept Induction Training' menu is getting displayed."),
	RespondInductionMenu_SS("'Induction Training'"),

	CheckBoxStatus_DC("Click on 'Status' check box"), CheckBoxStatus_AC("'Status' check box should be clicked.</div>"),
	CheckBoxStatus_AR("'Status' check box is clicked."), CheckBoxStatus_SS("'Status' check box"),

	TopHyperlink_DC("Click on 'Topic' hyperlink"), TopHyperlink_AC("'Topic' hyperlink should be clicked.</div>"),
	TopHyperlink_AR("'Topic' hyperlink is clicked."), TopHyperlink_SS("'Topic' hyperlink"),

	cllick_OK_DC("Click on 'Ok' button"), cllick_OK_AC("'Ok' button should be clicked.</div>"),
	cllick_OK_AR("'Ok' button is clicked."), cllick_OK_SS("'Ok' button"),

	SubmitButton_DC("Click on 'Submit' Button."),
	SubmitButton_AC("Confirmation message should be displayed with 'Done' button.</div>"),
	SubmitButton_AR("Confirmation message is getting displayed with 'Done' button.</div>"),
	SubmitButton_SS("'Submit' button"), SubmitButton_Esign_SS("E-Sign window"),

	FindText_DC("Enter employee name on 'Find' text."), FindText_AC("'Employee Name' should be displayed.</div>"),
	FindText_AR("'Employee Name' is getting displayed.</div>"), FindText_SS("'Employee Name text box'"),

	// Completion
	CompletionInduction_DC("Click on 'Completion Induction Training' menu"),
	CompletionInduction_AC("'Induction Training- Confirm Completion' screen should be displayed.</div>"),
	CompletionInduction_AR("'Induction Training- Confirm Completion' screen is displayed."),
	CompletionInduction_SS("'Completion Induction Training screen'"),

	// Induction Training Strings
	ProposeInduction_DC("Click on 'Induction Training' menu"),
	ProposeInduction_AC("'Induction Training Registration Initiation' screen should be displayed."),
	ProposeInduction_AR("'Induction Training Registration Initiation' screen is getting displayed."),
	ProposeInduction_SS("'Induction Training'"),

	Click_AddItem_DC("Click on 'Add Item' popup"), Click_AddItem_AC("'Employee List' screen should be displayed."),
	Click_AddItem_AR("'Employee List' screen is getting displayed."), Click_AddItem_SS("'Employee List'"),

	Click_SearchBy_DC("Click on 'Search By' popup"),
	Click_SearchBy_AC("'Employee Name' and 'Employee ID' options should be displayed."),
	Click_SearchBy_AR("'Employee Name' and 'Employee ID' options are getting displayed."),
	Click_SearchBy_SS("'Search By'"),

	Click_EmployeeName_DC("Click on 'Employee Name' popup"),
	Click_EmployeeName_AC("'Employee Name' option should be selected."),
	Click_EmployeeName_AR("'Employee Name' options is getting selected."),
	Click_EmployeeName_SS("'Employee Name Search'"),

	Click_TrainingDate_DC("Click on 'Training Date'"),
	Click_TrainingDate_AC("'Training Date' option should be clicked."),
	Click_TrainingDate_AR("'Training Date' option is clicked."), Click_TrainingDate_SS("'Training Date Field'"),

	Click_CurrentTrainingDate_DC("Select current as 'Training Date'"),
	Click_CurrentTrainingDate_AC("Current date should be selected as 'Training Date'."),
	Click_CurrentTrainingDate_AR("'Current date should be selected as 'Training Date'."),
	Click_CurrentTrainingDate_SS("'Date Field'"),

	InductionAuditTrails_DC("Click on 'Induction Training' menu."),
	InductionAuditTrails_AC("'Induction Training' screen should be displayed.</div>"),
	InductionAuditTrails_AR("'Induction Training Audit trails' screen is getting displayed.</div>"),
	InductionAuditTrails_SS("'Induction Training Audit Trails' screen."),

	CourseSelect_DC("Click on 'Course'"), CourseSelect_AC("Course should be clicked.</div>"),
	CourseSelect_AR("Course is clicked"), CourseSelect_SS("'Course'"),

	// Close audit trails

	Close_AuditTrails_AC("'Close' icon should be clicked"), Close_AuditTrails_AR("'Close' icon is clicked"),
	ENTER_REMARKS_REASONS_DC("Enter 'Remark(s) / Reason(s)'"),
	ENTER_REMARKS_REASONS_AC("Entered value should be displayed for the field."),
	ENTER_REMARKS_REASONS_AR("Entered value is getting displayed");
	;

	private final String commonStrings;

	CommonStrings(String CommonStrings) {

		this.commonStrings = CommonStrings;

	}

	public String getCommonStrings() {
		return commonStrings;
	}
}
