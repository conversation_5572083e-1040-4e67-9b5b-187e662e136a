package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_BatchFormation;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_RecordAttendance;
import com.Automation.learniqObjects.CM_SelfNomination;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Offline IT Session without assessment for scheduled course and make
 * atleast one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, present by viewing Individual employee
 * report at each transaction starting from course session, also add at least
 * one user and check IER report and to be retrained and view IER for those
 * employees.
 */

public class IT_OFF_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/IT_OFF_WITHOUT_ASSESSMENT.xlsx";

	public IT_OFF_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		// epiclogin.masterPlant();
		epiclogin.plant1();

//		InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//				Constants.CONFIGURE_CONFIRMATION_TEXT);

		// epiclogin.navigateTolearnIQPlant();

//		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}
//		epiclogin.navigateToMasterPlant();
//		// epiclogin.plant1();
//
//		Initiate_Course.courseConfiguration_Reg(testData);

		// epiclogin.navigateTolearnIQPlant();

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Configuration, Modification, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Configuration, Modification, Approve with Audit Trails");
		}

		// TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);
	}

	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
		}

		// trainer.TrainerModificationConfigurations(testData);

		trainer.trainer_Modification_AuditTrails(testData);

	}
	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();

		verifyCSScreen.course_session_Offline_ClassroomType_Without_Assessment(testData);

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();

	}

	// Test Method for BatchFormation Configuration, Registration with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
		}

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Offline_Users_CountMisMatch(testData);

	}
	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}

		RecordAttendance.recordAttendance_OnlineSession_CountMismatch(testData);

		// Logout.signOutPage();
	}

	@Test(priority = 8, dataProvider = "CourseSession", enabled = true)
	public void verifyEmployees(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("").assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("");
		}

		verifyCSScreen.VerifyCountAfterRecordAttendance(testData, "BeforeSession");

		// To be planned Count Outside and Inside.

		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				CM_VerifyCourseSessionScreen.afterRecordAttendance_Inside_ToBePlannedCount,
				"Outside To Be Planned Count at CourseSession After Record Attendance",
				"Inside To Be Planned Count at CourseSession After Record Attendance");

		// Skipped Count

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, CM_VerifyCourseSessionScreen.SkippedInsideCount,
				"Outside Skipped at CourseSession After Record Attendance",
				"Inside Skipped at CourseSession After Record Attendance");
		// Actual Skipped Users with Skipped Data Displayed in Skipped Column
		toBePlannedCountData(CM_BatchFormation.SkippedUsers, CM_VerifyCourseSessionScreen.SkippedData,
				"Actual Skipped users", "Skipped Users At Course Session Scren");

		// Checking Skipped Employyes are not available in To be planned

		checkCommonElements(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_VerifyCourseSessionScreen.SkippedData, "Employees in To Be planned After Session",
				"Skipped Employees");

		// Absent Count

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, CM_VerifyCourseSessionScreen.AbsentInsideCount,
				"Outside Absent Count at CourseSession After Record Attendance",
				"Inside Absent Count at CourseSession After Record Attendance");
		// Actual Absent Users with Absent Data Displayed in Skipped Column
		toBePlannedCountData(CM_RecordAttendance.AbsentUsers, CM_VerifyCourseSessionScreen.AbsentData,
				"Actual Absent users", "Absent Users At Course Session Screen");

		// Checking Absent Employees are not available in To be planned
		checkCommonElements(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_VerifyCourseSessionScreen.AbsentData, "Employees in To Be planned After Session",
				"Absent Employees");

		CSRReport.skipped_Absent_Qualified_AfterRecordAttendance();

		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				CM_CSRReport.AfterRecordAttendanceToBePlannedCountOutside,
				"Outside To Planned Count at CourseSession After Record Attendance",
				"Outside To Be Planned at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_CSRReport.combinedArrayBefore, "CourseSession To be Planned Data After Record Attendance",
				"CSR To be Planned Data After Record Attendance");

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde,
				CM_CSRReport.AfterRecordAttendnaceSkippedCountOurside,
				"Outside Skipped Count at CourseSession After Record Attendance",
				"Outside Skipped at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, CM_CSRReport.SkippedData,
				"CourseSession Skippped Data After Record Attendance", "CSR Skipped Data After Record Attendance");

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount,
				CM_CSRReport.AfterRecordAttendanceAbsentOutsideCount,
				"Outside Absent Count at CourseSession After Record Attendance",
				"Outside Absent at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, CM_CSRReport.AbsentData,
				"CourseSession Absent Data After Record Attendance", "CSR Absent Data After Record Attendance");

		InProgresUsersQualified();
	}

	@Test(priority = 9, dataProvider = "CourseSession", enabled = true)
	public void courseRetraining(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("").assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		// epiclogin.masterPlant();
//		epiclogin.plant1();
		CourseRetraining.course_Retraining_Classroom_Type_Without_Assessment();
		CourseRetraining.course_Retraining_AuditTrials_Yes();
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_ScheduledTab(testData, " ");
		Logout.signOutPage();
	}

}
