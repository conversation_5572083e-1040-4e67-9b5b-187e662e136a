package com.Automation.Strings;

public enum TrainingScheduleStrings {

	TrainingScheduleSubMenu_DC("Click on 'Training Schedule' submenu."), TrainingSchedule_SS("'Training Schedule'"),

	TrainingScheduleConfig_AC("'Training Schedule Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and "
			+ "'No of Approvals Required' should be available.</div>"),
	TrainingScheduleConfig_AR("'Training Schedule Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' are available.</div>"),
	TrainingScheduleConfig_SS("'Configuration'"),

	Click_DoneatTrainSche_AC("'Training Schedule Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatTrainSche_AR("'Training Schedule Configuration Registration' screen is getting displayed.</div>"),

	TrainingSchedule_Initiate_AC("'Training Schedule' screen should be displayed.</div>"
			+ "<div><b>* <b>The screen should contain ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons</div>"
			+ "<div><b>*</b> Screen should contain 'Group Name, Group Unique Code, Number of subgroups' columns.</div>"
			+ "<div><b>* </b>List of registered active Groups should be displayed.</div>"
			+ "<div><b>* </b>The number of subgroups mapped to each listed group and in active state should be displayed under the 'Number of Subgroups' column for each group.</div>"),
	TrainingSchedule_Initiate_AR("'Training Schedule' screen is getting displayed.</div>"
			+ "<div><b>* <b>The screen is getting displayed with ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Group Name, Group Unique Code, Number of subgroups' columns.</div>"
			+ "<div><b>* </b>List of registered active Groups should be displayed.</div>"
			+ "<div><b>* </b>The number of subgroups mapped to each listed group and in active state are getting displayed under the 'Number of Subgroups' column for each group.</div>"),

	// Approve Training Schedule Tasks screen
	TrainingSchedule_Approve_AC("'Training Schedule Approval Tasks' screen should be displayed."
			+ "<div><b>* </b>The screen should contain ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Training Schedule Name’, ‘Unique Code’, ‘Subgroup Name’, ‘Initiated By’ and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>By default, ‘Records Per Page’ should be displayed as ‘20’.</div>"
			+ "<div><b>* </b>List of Training Schedule Name(s) for which registration request is to be approved by the current login user should be listed and available for approval under the ‘Registration’ tab.</div>"),
	TrainingSchedule_Approve_AR("'Training Schedule Approval Tasks' screen is getting displayed."
			+ "<div><b>* </b>The screen is getting diplayed with ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details are getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting diplayed with ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen is gettting displayed with ‘Training Schedule Name’, ‘Unique Code’, ‘Subgroup Name’, ‘Initiated By’ and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>By default, ‘Records Per Page’ are getting displayed as ‘20’.</div>"
			+ "<div><b>* </b>List of Training Schedule Name(s) for which registration request is to be approved by the current login user are listed and available for approval under the ‘Registration’ tab.</div>"),

	SearchBy_Group_AC(
			"The option to search with ‘Top 250 Records’, ‘Group Name’, ‘Group Unique Code’ should be available.</div>"),
	SearchBy_Group_AR(
			"The option to search with ‘Top 250 Records’, ‘Group Name’, ‘Group Unique Code’ is available.</div>"),

	Click_GroupName_DC(" Click on the group name for which the above selected subgroup in course is linked."),
	TrainingSchedule_InitiateScreen2_AC("'Training Schedule' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Subgroup Name, Subgroup Code, Number of Employees' columns.</div>"
			+ "<div><b>*</b> List of subgroup(s) mapped to the selected Group and in active state should be displayed.</div>"
			+ "<div><b>*</b> The number of Employee(s) mapped to each listed Subgroup and in active state count should be displayed under the 'Number of Employees column for each Subgroup.</div>"),
	TrainingSchedule_InitiateScreen2_AR("'Training Schedule' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'Subgroup Name, Subgroup Code, Number of Employees' columns.</div>"
			+ "<div><b>*</b> List of subgroup(s) mapped to the selected Group and in active state are getting displayed.</div>"
			+ "<div><b>*</b> The number of Employee(s) mapped to each listed Subgroup and in active state count is gettting displayed under the 'Number of Employees column for each Subgroup.</div>"),
	Click_GroupName_SS("'Group Name'"),

	Click_SubGroupName_DC("Click on above ‘Subgroup  Name’ for which Group Training Plan is proposed."),
	Click_SubGroupName_AC("'Training Schedule Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>* </b>Screen should contain 'Training Schedule Name, Unique Code, Subgroup Name' fields.</div>"
			+ "<div><b>* </b>Unique Code should be system generated.</div>"
			+ "<div><b>* </b>Selected Subgroup Name should be displayed for ‘Subgroup Name’ field in read only format.</div>"
			+ "<div><b>* </b>The screen should contain ‘Search this Page’ option.</div>"
			+ "<div><b>* </b>The screen should contain ‘Course Name’, ‘Unique Code’, ‘Select ALL’ and  ‘Jan to Dec’ months columns.</div>"
			+ "<div><b>* </b>The screen should contain ‘Current Year Number’ and ‘Next Year Number’ tabs</div>"
			+ "<div><b>* </b>By default, Current year calendar details should be displayed.</div>"
			+ "<div><b>* </b>List of course(s) for which selected subgroup is mapped should be displayed.</div>"
			+ "<div><b>* </b>The option to select the less than current month should not be available and respective months checkboxes should be displayed in disabled mode for current year calendar</div>"
			+ "<div><b>*</b> Option to select the Course Name should be available .</div>"),
	Click_SubGroupName_AR("'Training Schedule Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>* </b>Screen should is getting displayed with 'Training Schedule Name, Unique Code, Subgroup Name' fields.</div>"
			+ "<div><b>* </b>Unique Code is system generated.</div>"
			+ "<div><b>* </b>Selected Subgroup Name is getting displayed for ‘Subgroup Name’ field in read only format.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Search this Page’ option.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Course Name’, ‘Unique Code’, ‘Select ALL’ and  ‘Jan to Dec’ months columns.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Current Year Number’ and ‘Next Year Number’ tabs</div>"
			+ "<div><b>* </b>By default, Current year calendar details are getting displayed.</div>"
			+ "<div><b>* </b>List of course(s) for which selected subgroup is mapped is getting displayed.</div>"
			+ "<div><b>* </b>The option to select the less than current month is not available and respective months check boxes are getting displayed in disabled mode for current year calendar</div>"
			+ "<div><b>*</b> Option to select the Course Name is available .</div>"),
	Click_SubGroupName_SS("'Subgroup Name'"),

	TrainingSchName_DC("Enter the value less than or equals to 250 characters in 'Training Schedule Name' field"),
	TrainingSchName_SS("'Training Schedule Name'"),

	Click_SearchThisPage_DC("Click on 'Search this Page' icon."),
	Click_SearchThisPage_AC("'Search this Page' text box should be displayed"),
	Click_SearchThisPage_AR("'Search this Page' text box is getting displayed"),
	Click_SearchThisPage_SS("'Search this Page'"),

	EnterCourseName_DC("Enter the above registered course name"), EnterCourseName_SS("'Course Name'"),

	TrainingScheduleAudittrails_AC("'Training Schedule Audit Trails' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Training Schedule Name’, ‘Unique Code’, Subgroup Name’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>* </b>By default, ‘Records Per Page’ should be displayed as ‘20’</div>"
			+ "<div><b>* </b>The screen should contain a list of Training Schedule proposed records</div>"),
	TrainingScheduleAudittrails_AR("'Training Schedule Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Training Schedule Name’, ‘Unique Code’, Subgroup Name’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>* </b>By default, ‘Records Per Page’ is getting displayed as ‘20’</div>"
			+ "<div><b>* </b>The screen is getting displayed with a list of Training Schedule proposed records</div>"),
	TrainingScheduleAudittrails_SS("'Training Schedule Audit Trails' screen."),

	SearchBy_TrainScheAudit_AC(
			"Option to search with 'Top 250 Records, Training Schedule Name, Unique Code, Initiated Between, Subgroup Name' should be available.</div>"),
	SearchBy_TrainScheAudit_AR(
			"Option to search with 'Top 250 Records, Training Schedule Name, Unique Code, Initiated Between, Subgroup Name' are available.</div>"),

	Select_TrainScheName_DC("Select 'Training Schedule Name'."),

	// Select Unique Code

	Select_UniqueCode_DC("Select 'Unique Code'."), Select_UniqueCode_SS("'Unique Code'"),

	Like_TrainScheName_DC("Enter the above registered 'Training Schedule Name' in 'Like' field."),

	Like_Training_Schedule_Name_SS("Training Schedule Name"),

	// Enter Unique Code

	Enter_UniqueCode_DC("Enter the 'Unique Code' of the above registred Training Schedule"),

	// After Approvl

	Enter_APPRUniqueCode_DC("Enter the 'Unique Code' of the just approved Training Schedule"),
//Before Approval
	Click_TrainScheName_for_AuditTrails_DC(
			"Click on the above registered 'Training Schedule Name' for which Training Schedule is proposed for the current year."),
	Click_TrainScheName_for_AuditTrails_AC(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the  Training Schedule entered/ selected during registration.<div>"
					+ "<div><b>* </b>Final Status’ should be displayed as ‘Initiated’..<div>"
					+ "<div><b>* </b>The ‘Events’ section should contain registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks / Reasons’ details<div>"
					+ "<div><b>* </b>The screen should contain the details of the  Training Schedule entered/ selected during registration.<div>"),
	Click_TrainScheName_for_AuditTrails_AR(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),

	Click_TrainScheNameBeforeApr_for_AuditTrails_AC(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the  Training Schedule entered/ selected during registration.<div>"
					+ "<div><b>* </b>Final Status’ should be displayed as ‘Initiated’..<div>"
					+ "<div><b>* </b>The ‘Events’ section should contain registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks / Reasons’ details<div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as 1 and ‘No. of Approvals completed’ should be read as 0<div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.<div>"),
	Click_TrainScheNameBeforeApr_for_AuditTrails_AR(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration’ is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displyaed with the details of the  Training Schedule entered/ selected during registration.<div>"
					+ "<div><b>* </b>Final Status’ is getting displayed as ‘Initiated’..<div>"
					+ "<div><b>* </b>The ‘Events’ section is getting displayed with registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks / Reasons’ details<div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is read as 1 and ‘No. of Approvals completed’ is read as 0<div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.<div>"),
	Click_TrainScheName_for_AuditTrails_SS("'Training Schedule' - Audit Trails."),

	Close_AuditTrails_TrainSche_AC("'Training Schedule Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_TrainSche_AR("'Training Schedule Audit trails' screen  is getting displayed.</div>"),

	SubmitTrainingSchewithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Training Schedule: Registration Initiation'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name(Employee ID)> should be displayed for the ‘Signed By’ field</div>"
					+ "<div><b>* </b>The option to enter ‘Password’ should be available.</div>"
					+ "<div><b>* </b>'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>* </b>'Proceed' button should be displayed in disabled mode.</div>"),

	SubmitTrainingSchewithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Training Schedule: Registration Initiation'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name(Employee ID)> is getting displayed for the ‘Signed By’ field</div>"
					+ "<div><b>* </b>The option to enter 'Password' is available.</div>"
					+ "<div><b>* </b>'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>* </b>'Proceed' button is getting displayed in disabled mode.</div>"),

	Esign_ProceedTrainingSche_AC(
			"'Training Schedule  Registration Initiated Unique Code:(Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedTrainingSche_AR(
			"'Training Schedule  Registration Initiated Unique Code:(Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Like_GroupNameInTrainingSche_DC(
			"Enter the group name for which Group Training Plan proposed for the above subgroup is mapped."),

	selectRequiredMonth_DC("Select the required months and make sure that current month should also be selected."),

	// Select Drop Dropdown

	Click_Select_Appr_DC("Click the dropdown for 'Approval 1' field."),
	Click_Select_Appr_AC(
			"All active Group(s), along with their respective mapped Subgroup(s), should be displayed in the dropdown menu."),
	Click_Select_Appr_AR(
			"All active Group(s), along with their respective mapped Subgroup(s), are getting displayed in the dropdown menu."),
	Click_Select_Appr_SS("'Select'"),

	// Enter Group/Sungroup Name

	Enter_GroupSubgroupName_DC("Enter the required 'Group/Subgroup(s)'"),
	Enter_GroupSubgroupName_SS("'Group/Subroup(s)'"),

	Click_Req_LOP_DC("Select required Group/ Subgroup(s) from the dropdown list."),
	Click_Req_LOP_AC("Selected ‘Group/ Subgroup(s)’ should be displayed for ‘Approver 1’ field."),
	Click_Req_LOP_AR("Selected ‘Group/ Subgroup(s)’ is getting displayed for ‘Approver 1’ field."),

	// Approval Screen after clicking on record

	ClickReqRecord_AprvScreen_AC("'Training Schedule- Registration Approval’ screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain the details of the Training Schedule Name entered/ selected during registration.</div>"
			+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section should contain the ‘Initiated’ transaction details only with ‘Username, Date & Time, and Remarks / Reasons details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as 1 and the ‘No. of Approvals Completed’ should be read as 0.</div>"
			+ "<div><b>* </b>‘Approve, Return & Drop’ options should be available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>‘Drop’ option should be in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter/ select ‘Remark(s) / Reason(s)’ should be available.</div>"),

	ClickReqRecord_AprvScreen_AR("'Training Schedule- Registration Approval’ screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with the details of the Training Schedule Name entered/ selected during registration.</div>"
			+ "<div><b>* </b>‘Final Status’ is getting displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section is getting displayed with the ‘Initiated’ transaction details only with ‘Username, Date & Time, and Remarks / Reasons details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is read as 1 and the ‘No. of Approvals Completed’ is read as 0.</div>"
			+ "<div><b>* </b>‘Approve, Return & Drop’ options are available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>‘Drop’ option is in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter/ select ‘Remark(s) / Reason(s)’ are available.</div>"),

	ClickReqRecord_AprvScreen_SS("'Training Schedule- Registration Approval’"),

	// Approve Esign
	Esign_Approve_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Training Schedule: Registration Approval: Approve'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name (Employee ID)> should be displayed for the ‘Signed By’ field.</div>"
					+ "<div><b>* </b>The option to enter ‘Password’ should be available.</div>"
					+ "<div><b>* </b>'Cancel' and ‘Proceed’ button should be available.</div>"
					+ "<div><b>* </b>'roceed’ button should be displayed in disabled mode.</div>"),
	Esign_Approve_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Training Schedule: Registration Approval: Approve'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the ‘Signed By’ field.</div>"
					+ "<div><b>* </b>The option to enter ‘Password’ is available.</div>"
					+ "<div><b>* </b>'Cancel' and ‘Proceed’ button are available.</div>"
					+ "<div><b>* </b>'Proceed’ button is getting displayed in disabled mode.</div>"),

	// Click on AT Record After Approval
	Click_TrainScheNameAppr_for_AuditTrails_DC(
			"Click on above ‘Training Schedule Name’ for which registration request was just approved"),
	Click_TrainScheNameAfterApr_for_AuditTrails_AC(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the  Training Schedule entered/ selected during registration.<div>"
					+ "<div><b>* </b>Final Status’ should be displayed as ‘Approved’..<div>"
					+ "<div><b>* </b>The ‘Events’ section should contain registration ‘Initiated’ and 'Approved' transaction with ‘Username, Date& Time and Remarks / Reasons’ details<div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as '1' and ‘No. of Approvals completed’ should be read as '1'<div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.<div>"),
	Click_TrainScheNameAfterApr_for_AuditTrails_AR(
			"'Training Schedule - Audit Trails: Revision No.: 0 -Registration’ is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displyaed with the details of the  Training Schedule entered/ selected during registration.<div>"
					+ "<div><b>* </b>Final Status’ is getting displayed as ‘Approved’..<div>"
					+ "<div><b>* </b>The ‘Events’ section is getting displayed with registration ‘Initiated’ and 'Approved' transaction with ‘Username, Date& Time and Remarks / Reasons’ details<div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is read as '1' and ‘No. of Approvals completed’ is read as '1'<div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.<div>"),

	// Close Audit Trails

	Close_AuditTrailsTopic_AC("'Training Schedule Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsTopic_AR("'Training Schedule Audit Trails' screen is getting displayed.</div>"),

	// QB Configuration

	TSCH_Config_AC("'Training Schedule Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	TSCH_Config_AR("'Training Schedule Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	TSCH_Config_SS("'Configuration'"),

	// Training Schedule Config Audit Trails

	TSCH_ConfigAudit_DC("Click on 'Training Schedule' submenu."),
	TSCH_ConfigAudit_AC("'Training Schedule Configuration Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	TSCH_ConfigAudit_AR("'Training Schedule Configuration Audit Trails' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	TSCH_ConfigAudit_SS("'Training Schedule Configuration Audit Trails'"),

	// Config Audit Trails window
	Click_Config_Proceed_AC(
			"'Training Schedule Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should be displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>* </b>The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Training Schedule Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>* </b>The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Training Schedule Configuration - Audit Trails'."),

	// Close Audit Trails

	Close_ConfigAuditTrails_TSCH_AC("'Training Schedule Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_TSCH_AR(
			"'Training Schedule Configuration Audit Trails' screen is getting displayed.</div>"),

	MODIFY_MENU("Click 'Modify' menu"),
	TRAINING_SCHEDULE_MENU_AC("'Training Schedule' under 'Modify' menu should be displayed.</div>"),
	TRAINING_SCHEDULE_MENU_AR("'Training Schedule' under 'Modify' menu is getting displayed.</div>"),
	TRAINING_SCHEDULE_MENU_SS("'Training Schedule'"),

	APPROVE_MENU("Click 'Approve' menu"),
	APPROVE_TRAINING_SCHEDULE_MENU_AC("'Training Schedule' under 'Approve' menu should be displayed.</div>"),
	APPROVE_TRAINING_SCHEDULE_MENU_AR("'Training Schedule' under 'Approve' menu is getting displayed.</div>"),
	APPROVE_TRAINING_SCHEDULE_MENU_SS("'Training Schedule'"),

	TRAINING_SCHEDULE_MENU("Click 'Training Schedule' menu"),
	TRAINING_SCHEDULE_AC("'Training Schedule Modification' screen should be displayed.</div>"),
	TRAINING_SCHEDULE_AR("'Training Schedule Modification' screen is getting displayed.</div>"),
	TRAINING_SCHEDULE_SS("'Training Schedule'"),

	APPROVAL_TRAINING_SCHEDULE_AC("'Training Schedule Approval Tasks' screen should be displayed.</div>"),
	APPROVAL_TRAINING_SCHEDULE_AR("'Training Schedule Approval Tasks' screen is getting displayed.</div>"),

	CLICK_MODIFICATION_TAB_DC("CLick 'Modification' tab"),
	APPROVAL_MOD_RECORD_AC("<div>* Modified record should be displayed at 'Modification' tab.</div>"
			+ "<div>* Screen should display with 'Training Schedule Name', 'Unique Code', 'Subgroup Name', 'Initiated By', 'Initiated On' and 'Revision No.' columns."),
	APPROVAL_MOD_RECORD_AR("<div>* Modified record is getting displayed at 'Modification' tab.</div>"
			+ "<div>* Screen contains 'Training Schedule Name', 'Unique Code', 'Subgroup Name', 'Initiated By', 'Initiated On' and 'Revision No.' columns."),

	MODIFICATION_SS("Modification"),

	SearchBy_AC(
			"Option to search with 'Top 250 Records', 'Training Schedule Name', 'Unique Code', 'Initiated between' and 'Subgroup Name' should be displayed.</div>"),
	SearchBy_AR(
			"Option to search with 'Top 250 Records', 'Training Schedule Name', 'Unique Code', 'Initiated between' and 'Subgroup Name' are getting displayed.</div>"),

	SEARCH_BY_TRAINING_SCHEDULE_NAME_DC("Select 'Training Schedule Name'."),
	SEARCH_BY_TRAINING_SCHEDULE_NAME_AC("Selection should be accepted.</div>"),
	SEARCH_BY_TRAINING_SCHEDULE_NAME_AR("Selection is getting accepted.</div>"),
	SEARCH_BY_TRAINING_SCHEDULE_NAME_SS("'Training Schedule Name'"),

	TRAINING_SCHEDULE_NAME_DC("Click on above registered 'Training Schedule Name'."),

	MOD_TRAINING_SCHEDULE_NAME_AC("Training Schedule Modification Approval screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the Course Name should be displayed for the Current Month and for future month.</div>"),
	MOD_TRAINING_SCHEDULE_NAME_AR("Training Schedule Modification Initiation screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the Course Name is getting displayed for the Current Month and for the future month.</div>"),
	MOD_TRAINING_SCHEDULE_NAME_SS("'Training Schedule Name'"),

	APPROVE_MOD_TRAINING_SCHEDULE_NAME_AC(
			"'Training Schedule Modification Initiation' screen should be displayed.</div>"
					+ "<div><b>*</b> Modified Course Name which is selected at modification Initiation should be displayed.</div>"),
	APPROVE_MOD_TRAINING_SCHEDULE_NAME_AR(
			"'Training Schedule Modification Initiation' screen is getting displayed.</div>"
					+ "<div><b>*</b> Modified Course Name which is selected at modification Initiation is getting displayed.</div>"),

	SELECT_CURRENT_MONTH_DC("Select Current month checkbox"), SELECT_CURRENT_MONTH_SS("Current month checkbox"),

	GROUP_SUBGROUP_SEARCH_BY_DC("Click on 'Group/Subgroup(s)' dropdown"),
	GROUP_SUBGROUP_SEARCH_BY_AC("Options to search with 'Approvers Group/Approver Group' should be displayed"),
	GROUP_SUBGROUP_SEARCH_BY_AR("Options to search with 'Approvers Group/Approver Group' are getting displayed"),
	GROUP_SUBGROUP_SEARCH_BY_SS("Group/Subgroup(s)"),

	CLICK_APPROVER_GROUP_DC("Click 'Approvers Group/Approvers Subgroup'"),
	APPROVER_GROUP_AC("Selected value should be displayed for the field."),
	APPROVER_GROUP_AR("Selected value is displayed for the field."),
	APPROVER_GROUP_SS("'Approvers Group/Approvers Subgroup'"),

	Submit_DC("Click on 'Submit' button."),
	MOD_Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Training Schedule: Modification Initiation'.</div>"),
	MOD_Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Training Schedule: Modification Initiation'.</div>"),
	Submit_SS("'Submit'"),

	APPROVAL_MOD_Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Training Schedule: Modification Approval'.</div>"),
	APPROVAL_MOD_Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Training Schedule: Modification Approval'.</div>"),

	ESIGN_MOD_TRAINING_SCHEDULE_AC(
			"'Training Schedule Change Request Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	ESIGN_MOD_TRAINING_SCHEDULE_AR(
			"'Training Schedule Change Request Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	ESIGN_APPROVAL_MOD_TRAINING_SCHEDULE_AC(
			"'Training Schedule Change Request Approved Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	ESIGN_APPROVAL_MOD_TRAINING_SCHEDULE_AR(
			"'Training Schedule Change Request Approved Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>");

	private final String trainingScheduleStrings;

	TrainingScheduleStrings(String trainingScheduleStrings) {

		this.trainingScheduleStrings = trainingScheduleStrings;

	}

	public String getTrainingScheduleStrings() {
		return trainingScheduleStrings;
	}

}
