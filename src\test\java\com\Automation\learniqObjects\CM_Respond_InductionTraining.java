package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Respond_InductionTraining extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement learnIQ_Icon;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Respond']/preceding-sibling::a")
	WebElement respondSubMenu;

	@FindBy(id = "TMS_Course Manager_Respond_MEN63")
	WebElement inductionTrainingSubmenu;

	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement textBox_Remarks;

	@FindBy(xpath = "//button[@class='caliber-button-primary SubmitCls' and @id='btnSubmit']")
	WebElement button_Submit;

	@FindBy(xpath = "//button[@id='VUserAccStatus_selectBtn']")
	WebElement button_OK;

	@FindBy(id = "cfnMsg_Next")
	WebElement button_Done;

	public void respondInductionTraining(HashMap<String, String> testData) {
		waitForElementVisibile(learnIQ_Icon);
		click2(learnIQ_Icon, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		waitForElementVisibile(courseManagerMenu);
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(respondSubMenu, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_SS.getCommonStrings());
		scrollToViewElement(inductionTrainingSubmenu);
		click2(inductionTrainingSubmenu, CommonStrings.RespondInductionMenu_DC.getCommonStrings(),
				CommonStrings.RespondInductionMenu_AC.getCommonStrings(),
				CommonStrings.RespondInductionMenu_AR.getCommonStrings(),
				CommonStrings.RespondInductionMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		WebElement CourseTable = driver.findElement(By.xpath("//table[@id='CrsListTab']"));
		List<WebElement> CourseRows = CourseTable.findElements(By.xpath(".//tr"));
		try {
			for (WebElement row : CourseRows) {
				List<WebElement> elementObjectAll = row.findElements(By.xpath(".//td"));
				for (WebElement element : elementObjectAll) {
					String course = element.getText();
					if (course.equals(testData.get("courseName"))) {
						WebElement topicHyperLink = row.findElement(By.xpath(".//td[4]/a"));
						WebElement statusCheckBox = row.findElement(By.xpath(".//td[5]/input"));
						click2(statusCheckBox, CommonStrings.CheckBoxStatus_DC.getCommonStrings(),
								CommonStrings.CheckBoxStatus_AC.getCommonStrings(),
								CommonStrings.CheckBoxStatus_AR.getCommonStrings(),
								CommonStrings.CheckBoxStatus_SS.getCommonStrings());
						click2(topicHyperLink, CommonStrings.TopHyperlink_DC.getCommonStrings(),
								CommonStrings.TopHyperlink_AC.getCommonStrings(),
								CommonStrings.TopHyperlink_AR.getCommonStrings(),
								CommonStrings.TopHyperlink_SS.getCommonStrings());

					}
				}
			}
		} catch (Exception e) {
			System.out.println("Execption Ocuured");
		}

		waitForElementVisibile(textBox_Remarks);
		sendKeys2(textBox_Remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("remarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		waitForElementVisibile(button_OK);
		click2(button_OK, CommonStrings.cllick_OK_DC.getCommonStrings(), CommonStrings.cllick_OK_AC.getCommonStrings(),
				CommonStrings.cllick_OK_AR.getCommonStrings(), CommonStrings.cllick_OK_SS.getCommonStrings());
		waitForElementVisibile(button_Submit);
		scrollToViewElement(button_Submit);
		click2(button_Submit, CommonStrings.SubmitButton_DC.getCommonStrings(),
				CommonStrings.SubmitButton_AC.getCommonStrings(), CommonStrings.SubmitButton_AR.getCommonStrings(),
				CommonStrings.SubmitButton_SS.getCommonStrings());
		waitForElementVisibile(button_Done);
		click2(button_Done, CommonStrings.Click_Done_DC.getCommonStrings(),
				CommonStrings.Click_Done_AC.getCommonStrings(), CommonStrings.Click_Done_AR.getCommonStrings(),
				CommonStrings.Click_Done_SS.getCommonStrings());

	}

}
