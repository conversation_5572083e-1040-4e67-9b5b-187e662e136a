package com.Automation.Strings;

public enum EvaluateStrings {

	Click_Evaluate_DC("Click on 'Evaluate' menu"), Click_Evaluate_AC("'Answer Paper' sub menu should be displayed."),
	Click_Evaluate_AR("'Answer Paper' sub menu is getting displayed"), <PERSON>lick_Evaluate_SS("'Evaluate'"),

	<PERSON>lick_AnswerPaper_DC("Click on 'Answer Paper' submenu."),
	Click_AnswerPaper_AC("'Evaluate Answer Paper' screen should be displayed.</div>"
			+ "<div><b>* </b>'Non Evaluated List' and 'Evaluated List' tabs should be displayed.</div>."
			+ "<div><b>* </b>By default 'Non Evaluated List' tab should be in selected.</div>"),
	Click_AnswerPaper_AR("'Evaluate Answer Paper' screen is getting displayed.</div>"
			+ "<div><b>* </b>'Non Evaluated List' and 'Evaluated List' tabs are getting displayed.</div>"
			+ "<div><b>* </b>By default 'Non Evaluated List' tab is selected.</div>."),
	Click_AnswerPaper_SS("'Answer Paper'"),

	Search_By_AC("Option to search with 'Top 250 Records, Batch Name and 'Initiated Between' should be available."),
	Search_By_AR("Option to search with 'Top 250 Records, Batch Name and 'Initiated Between' is available."),

	SearchBy_BatchName_DC("Select 'Batch Name'."),

	SearchBy_BatchName_SS("'Search By' 'Batch Name'."),

	Enter_BatchName_DC("Enter the batch name for which the above user is respond to question paper."),
	Enter_BatchName_SS("'Batch Name'"),

	Click_Toggle_DC(
			"Click on 'Toggle' dropdown against the batch for which the user is responded to question paper.</div>"),
	Click_Toggle_AC("'Submitted' status should be displayed for the employee who is responded to question paper."),
	Click_Toggle_AR(
			" 'Submitted' status is getting displayed for the employee who is responded to question paper.</div>"),
	Click_Toggle_SS("'Employees List'"),

	Click_Employee_DC("Click on the employee name hyperlink of the user who is responded to question paper."),
	Click_Employee_AC(
			"Option to enter marks for 'Essay Type and Fill in the Blanks questions Type should be available if(any)"),
	Click_Employee_AR(
			"Option to enter marks for 'Essay Type and Fill in the Blanks questions Type is available if(any)"),
	Click_Employee_SS("'Evaluate Answer Paper'"),

	Click_Next_DC(
			"Select or enter the marks if any by making sure that employee should be qualified and click on 'Next'  button."),
	Click_Next_AC(
			"'Qualifying Mark, Qualifying Percentage, Acquired Marks, Acquired Percentage' should be updated accurately.</div>"
					+ "<div><b>* </b>'Qualified' status should be displayed in green colour under 'Result'</div>"),
	Click_Next_AR(
			"'Qualifying Mark, Qualifying Percentage, Acquired Marks, Acquired Percentage' are getting updated accurately.</div>"
					+ "<div><b>* </b>'Qualified' status is getting displayed in green colour under 'Result'</div>"),

	Click_Next_SS("'Qualified'"),

	Click_Submit_AC("'Answer Paper Evaluated' confirmation message should be displayed with 'Done' button.</div>"),
	Click_Submit_AR("'Answer Paper Evaluated' confirmation message is getting displayed with 'Done' button.</div>"),
	Submit_SS("'Submit'");

	private final String evaluateStrings;

	EvaluateStrings(String EvaluateStrings) {

		this.evaluateStrings = EvaluateStrings;

	}

	public String getEvaluateStrings() {
		return evaluateStrings;
	}

}
