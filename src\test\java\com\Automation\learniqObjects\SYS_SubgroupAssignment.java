package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class SYS_SubgroupAssignment extends OQActionEngine {

	public static String RegEmployeeNameSearch = "";
	String RegEmpName = "";
	public static String initiator = "";
	public static String FirstNameLastName = "";
	public static String UniqueCode = "";

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement ApproveMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Modify')]")
	WebElement modifyMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN17")
	WebElement modSubgroupAssignMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement usergroups;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Configure')]")
	WebElement configurationMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN17")
	WebElement subGroupAssignmentConfig;
	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN17")
	WebElement modifySubGroupAssignment;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN17")
	WebElement subGroupAssignment;
	@FindBy(xpath = "//*[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]//parent::li/ul/li/a[@id='TMS_System Manager_User Groups_MEN07_SUBMEN17']")
	WebElement ApproveSubgroupAssignment;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//*[@id='TMS_System Manager_User Groups']/li/a[text()='Configure']//parent::li/ul/li/a[text()='Subgroup Assignment']")
	WebElement SubgroupCnfgMenu;

	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;

	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']//li//a[text()='Subgroup Assignment']")
	WebElement configurationSubgroupAssigAuditTrails;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;

	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callEsignAtInitiaiton;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callEsignAtApproval;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;

	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtRegApprCheckBox;

	@FindBy(xpath = "//span[text()='Employee Name']//preceding-sibling::input[@value='1']")
	WebElement employeeName310;
	@FindBy(xpath = "//span[text()='Subgroup']//preceding-sibling::input[1]")
	WebElement subgroupNameRB310;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//label[text()='Employee Name']//preceding-sibling::input[@value='1']")
	WebElement employeeName1;
	@FindBy(xpath = "//span[text()='Search By']/following-sibling::input[1]")
	WebElement employee_Name;

	@FindBy(id = "SgpAsn_TreeVC_SearchTxt")
	WebElement empSearchTxt;
	@FindBy(xpath = "//a[contains(text(),'Fetch Records')]")
	WebElement fetchRecords;
	@FindBy(xpath = "//ul[@id='SgpAsn_Subgrps_ul']/li[1]")
	WebElement addSubGroupname;
	@FindBy(id = "SubGroupAssignment_Remarks")
	WebElement remarksval;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//ul[@id='SgpAsn_Users_ul']/li[1]")
	WebElement addEmpname;
	@FindBy(xpath = "//label[text()='Subgroup']//preceding-sibling::input[1]")
	WebElement subgroupNameRB;
	@FindBy(xpath = "//span[text()='Search By']/following-sibling::input[2]")
	WebElement subgroup_Name;
	@FindBy(xpath = "//*[@id='esign_Activity']")
	WebElement EsignTitle;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//*[@id='TMS_System Manager_User Groups']/li/a[text()='Audit Trails']")
	WebElement UsergroupsAuditTrailsMenu;
	@FindBy(xpath = "//*[@id='TMS_System Manager_User Groups']/li/a[text()='Audit Trails']//parent::li/ul/li/a[text()='Subgroup Assignment']")
	WebElement SubgroupAssignmebtAuditTrailsMenu;
	@FindBy(xpath = "//span[text()='Top 250 Records']")
	WebElement clickSearchBy;
	@FindBy(xpath = "//li[text()='Unique Code']")
	WebElement SearchByUniqueCode;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[text()='Employee Name']")
	WebElement SearchByEmployeeName;
	@FindBy(id = "UniqueCode")
	WebElement EnterUniqueCode;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement EnterEmployeeName;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title pb-0 pt-2']")
	WebElement AuditTrailsWindowTitle;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//label[contains(text(),'Employee Name')]/following-sibling::span")
	WebElement verifyEmployeeName;
	@FindBy(xpath = "//label[contains(text(),'Assigned Subgroups')]/following-sibling::span")
	WebElement verifySubgroupName;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement verifyApprovedLabel;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement verifyApprovedDateandTime;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(id = "SelectedDecision_2")
	WebElement Approve;
	@FindBy(xpath = "//*[@id='Remarks']")
	WebElement enterRemarks;

	// ==========SubGroup Assignment============//
	public void subGroupAssignment_Configuration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(configurationMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(SubgroupCnfgMenu, SubgroupAssignmentStrings.SubGroupAssignMenu_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssign_Config_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssign_Config_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssign_Config_SS.getSubgroupAssignmentStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(eSignAtInitCheckBox);
	//	String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckboxRegistrationApproval(driver, eSignAtRegApprCheckBox, "Call E-sign At: Approval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());
		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	// String employeeName = "";

//	
	public void subGroupAssignment_Registration_AuditTrails(HashMap<String, String> testData) {

//		String username = "sepuser3";
		// String Subgroupvalue = "sateeshsubgroup";
//		String employeeName = "Sanjay2.Sanjay2";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(subGroupAssignment, SubgroupAssignmentStrings.SubGroupAssignmMenu_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignm_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignm_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignm_SS.getSubgroupAssignmentStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Initiation", test);
		click2(employee_Name,
				SubgroupAssignmentStrings.SubGroupAssignmEmployeeselectMenu_DC.getSubgroupAssignmentStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmEmployeeselectMenu_SS.getSubgroupAssignmentStrings());
		sendKeys2(empSearchTxt, SubgroupAssignmentStrings.SubGroupAssignmEmployee_DC.getSubgroupAssignmentStrings(),
				SSO_UserRegistration.getEmployeeName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				// "VijayFYJXD.VijayLYJXD", CommonStrings.sendKeys_AC.getCommonStrings(),

				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmEmployeeselectMenu_SS.getSubgroupAssignmentStrings());
		TimeUtil.mediumWait();
		click2(fetchRecords, SubgroupAssignmentStrings.fetchrecords_DC.getSubgroupAssignmentStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				SubgroupAssignmentStrings.fetchrecords_SS.getSubgroupAssignmentStrings());
		WebElement ele = driver.findElement(By.xpath("//ul[@id='SgpAsn_Users_ul']/li[1]"));
		// employeeName = ele.getText();
		TimeUtil.mediumWait();
		click2(addEmpname, SubgroupAssignmentStrings.SubGroupAssignmaddEmployee_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddEmployee_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddEmployee_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddEmployee_SS.getSubgroupAssignmentStrings());
		TimeUtil.shortWait();
		click2(subgroup_Name,
				SubgroupAssignmentStrings.SubGroupAssignmsubgroupselectMenu_DC.getSubgroupAssignmentStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmsubgroupselectMenu_SS.getSubgroupAssignmentStrings());
		sendKeys2(empSearchTxt, SubgroupAssignmentStrings.SubGroupAssignmsubgroupReg_DC.getSubgroupAssignmentStrings(),
				testData.get("Subgroup") + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmsubgroupselectMenu_SS.getSubgroupAssignmentStrings());
		TimeUtil.mediumWait();
		click2(fetchRecords, SubgroupAssignmentStrings.fetchrecordssubgroup_DC.getSubgroupAssignmentStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				SubgroupAssignmentStrings.fetchrecords_SS.getSubgroupAssignmentStrings());
		TimeUtil.mediumWait();
		click2(addSubGroupname, SubgroupAssignmentStrings.SubGroupAssignmaddsubgroup_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddsubgroup_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddsubgroup_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignmaddsubgroup_SS.getSubgroupAssignmentStrings());

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Esign_sub_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Esign_sub_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Submit_SS.getCommonStrings());

//		clickAndWaitforNextElement(submit, EsignTitle, CommonStrings.Submit_Button_DC.getCommonStrings(),
//				SubgroupAssignmentStrings.Esign_sub_AC.getSubgroupAssignmentStrings(),
//				SubgroupAssignmentStrings.Esign_sub_AR.getSubgroupAssignmentStrings(),
//				CommonStrings.Submit_SS.getCommonStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Esign_SubProceed_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Esign_SubProceed_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		waitForElementVisibile(confirmationText);
		switchToDefaultContent(driver);

		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(UsergroupsAuditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(SubgroupAssignmebtAuditTrailsMenu,
				SubgroupAssignmentStrings.SubGroupAssignmMenu_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_SS.getSubgroupAssignmentStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Assignment Audit Trails", test);
		click2(clickSearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubgroupAssignmentStrings.SearchBy_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SearchBy_AR.getSubgroupAssignmentStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(SearchByEmployeeName, CommonStrings.Select_EmployeeName_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_EmployeeName_SS.getCommonStrings());
		sendKeys2(EnterEmployeeName, CommonStrings.Enter_EmployeeName_DC.getCommonStrings(),
				SSO_UserRegistration.getEmployeeName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Enter_EmployeeName_SS.getCommonStrings());

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubgroupAssignmentStrings.Click_Employee_AT_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_AT_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_AT_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_AT_SS.getSubgroupAssignmentStrings());
		TimeUtil.shortWait();
		driver.switchTo().frame(0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
		TimeUtil.mediumWait();

	}

	public void subGroupAssignment_Approve_AuditTrails(HashMap<String, String> testData) {
//		String username = "sepuser3";
//		 String Subgroupvalue = "ATSCSubgroupUFN";
//		String uniqueCode = "Sanjay1.Sanjay1/1121211/1";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(ApproveMenu, CommonStrings.CM_ApproveMenu_DC.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_AC.getCommonStrings(), CommonStrings.CM_ApproveMenu_AR.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_SS.getCommonStrings());
		waitForElementVisibile(ApproveSubgroupAssignment);
		// subGroupAssignment.click();
		click2(ApproveSubgroupAssignment,
				SubgroupAssignmentStrings.ApproveSubGroupAssignmMenu_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.ApproveSubGroupAssignmMenu_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.ApproveSubGroupAssignmMenu_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.ApproveSubGroupAssignmMenu_SS.getSubgroupAssignmentStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Assignment Approval Tasks Screen", test);
		click2(clickSearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubgroupAssignmentStrings.SearchBy_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SearchBy_AR.getSubgroupAssignmentStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
//		clickAndWaitforNextElement(SearchByUniqueCode, EnterUniqueCode,
//				CommonStrings.Select_UniqueCode_DC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
//				CommonStrings.Selction_AR.getCommonStrings(), CommonStrings.Select_UniqueCode_SS.getCommonStrings());

//		sendKeys2(EnterUniqueCode, CommonStrings.Enter_UniqueCode_DC.getCommonStrings(), employeeName,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CommonStrings.Enter_UniqueCode_SS.getCommonStrings());

		click2(SearchByEmployeeName, CommonStrings.Select_EmployeeName_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_EmployeeName_SS.getCommonStrings());
		sendKeys2(EnterEmployeeName, CommonStrings.Enter_EmployeeName_DC.getCommonStrings(),
				SSO_UserRegistration.getEmployeeName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Enter_EmployeeName_SS.getCommonStrings());

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubgroupAssignmentStrings.Click_Employee_AT_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_APR_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_APR_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_APR_SS.getSubgroupAssignmentStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Assignment Audit Trails", test);
		click2(Approve, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		sendKeys2(enterRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApprovalRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Esign_Approve_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Esign_Approve_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Submit_SS.getCommonStrings());

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Esign_Approve_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Esign_Approve_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Submit_SS.getCommonStrings());

		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Esign_APRProceed_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Esign_APRProceed_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(UsergroupsAuditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());

		click2(SubgroupAssignmebtAuditTrailsMenu,
				SubgroupAssignmentStrings.SubGroupAssignmMenu_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SubGroupAssignAudittrails_SS.getSubgroupAssignmentStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Assignment Audit Trails", test);
		click2(clickSearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubgroupAssignmentStrings.SearchBy_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.SearchBy_AR.getSubgroupAssignmentStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(SearchByEmployeeName, CommonStrings.Select_EmployeeName_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_EmployeeName_SS.getCommonStrings());
		sendKeys2(EnterEmployeeName, CommonStrings.Enter_EmployeeName_DC.getCommonStrings(),
				SSO_UserRegistration.getEmployeeName(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Enter_EmployeeName_SS.getCommonStrings());

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();
		click2(displayedRecord, SubgroupAssignmentStrings.Click_Employee_APRAT_DC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_APRAT_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_APRAT_AR.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Click_Employee_AT_SS.getSubgroupAssignmentStrings());
		TimeUtil.shortWait();
		driver.switchTo().frame(0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
		TimeUtil.mediumWait();

	}

}
