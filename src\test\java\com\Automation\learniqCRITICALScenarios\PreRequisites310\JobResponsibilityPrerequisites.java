package com.Automation.learniqCRITICALScenarios.PreRequisites310;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class JobResponsibilityPrerequisites extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/JobResponsibilityPrerequisites.xlsx";

	public JobResponsibilityPrerequisites() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "Jobresponsibility");

	@DataProvider(name = "JobResp")
	public Object[][] getRegistrationUser() throws Exception {
		Object[][] obj = new Object[excel1.getRowCount()][1];
		for (int i = 1; i <= excel1.getRowCount(); i++) {
			HashMap<String, String> testData = excel1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 0, dataProvider = "JobResp", enabled = true)
	public void userReg(HashMap<String, String> testData) {

		test = extent.createTest("Pre Requisite: 4.1 Job Responsibility Configuration")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Pre Requisite: 4.1 Job Responsibility  Configuration");
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		JobResponsibility.jobresponsbility_Configuration(testData);
		test = extent.createTest("Pre Requisite: 4.2  Job Responsibility Registration with AuditTrails")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 4.2  Job Responsibility Registration with AuditTrails");

		JobResponsibility.jobResponsibiltyRegWith1Approval(testData);
		Logout.signOutPage();

		test = extent.createTest("Pre Requisite: 4.3  Job Responsibility Line of Approvers Approve with AuditTrails")

				.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))

				.assignCategory("Pre Requisite: 4.3  Job Responsibility Line of Approvers Approve with AuditTrails");

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));

		epiclogin.plant1();

	//	JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData);

		Logout.signOutPage();
		test = extent.createTest("Pre Requisite: 4.4  Job Responsibility User Acceptance Approve with AuditTrails")

				.assignAuthor(SSO_UserRegistration.getEmployeeID())

				.assignCategory("Pre Requisite: 4.4  Job Responsibility User Acceptance Approve with AuditTrails");

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), SSO_UserRegistration.getUserID(),
				ConfigsReader.getPropValue("SSONewUserRegPassword"));

		epiclogin.plant1();

		JobResponsibility.jobResponsibilityUserAcceptanceApprove(testData);

		Logout.signOutPage();

		test = extent
				.createTest(
						"Pre Requisite: 4.5  Job Responsibility Authorized Deputy Acceptance Approve with AuditTrails")

				.assignAuthor(ConfigsReader.getPropValue("EPIQADUserID"))

				.assignCategory(
						"Pre Requisite: 4.5  Job Responsibility Authorized Deputy Acceptance Approve with AuditTrails");

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("ADUserID"),
				testData.get("ADPassword"));
		epiclogin.plant1();
		JobResponsibility.jobResponsibility_AuthorizedDeputyAcceptance_ApproveWithAudiTrails(testData);
		test = extent.createTest("Pre Requisite: 4.6  Print Job Responsibility")

				.assignAuthor(ConfigsReader.getPropValue("EPIQADUserID"))

				.assignCategory("Pre Requisite: 4.6  Print Job Responsibility");

		//Reports.jobResponsibilityReport();
		TimeUtil.shortWait();

		Logout.signOutPage();

		n = 0;
		screenshotCounter = 0;

	}
}