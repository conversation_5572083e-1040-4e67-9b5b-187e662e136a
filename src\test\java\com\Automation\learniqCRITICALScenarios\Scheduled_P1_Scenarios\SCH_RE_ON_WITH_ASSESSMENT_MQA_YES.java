package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Online RE Type Session with assessment for scheduled course with MQA
 * as 'Yes' and make sure that at at least user should be qualified but not 100%
 * and view the MQA report before and after respoding to MQA.
 */

public class SCH_RE_ON_WITH_ASSESSMENT_MQA_YES extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SCH_RE_ON_WITH_ASSESSMENT_MQA_YES.xlsx";

	public SCH_RE_ON_WITH_ASSESSMENT_MQA_YES() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// ----------Topic - Test Method---------- //

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 0, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic  Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//			epiclogin.masterPlant();
		//
//			InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//					Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//			Logout.signOutPage();

//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//			epiclogin.plant1();
		//
//			InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//			Logout.signOutPage();
	}
	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//			ConfigsReader.getPropValue("EpicUserPWD"));

		// epiclogin.masterPlant();
		//
		// Initiate_Course.courseConfiguration_Reg(testData);

		// epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

		// Logout.signOutPage();

		// epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//			ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
		// epiclogin.plant1();
		//
		// Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
		// Logout.signOutPage();
	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule  Modification");
		}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
		//
//				epiclogin.plant1();
		//
//				TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);

//				TrainingShcedule.trainingScheduleAuditTrail();
		//
//				Logout.signOutPage();
		//
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//				epiclogin.plant1();
//				TrainingShcedule.approveModifyTrainingScheduled(testData);
//				TrainingShcedule.trainingScheduleAuditTrail();
//				Logout.signOutPage();
	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Online_DocumentReading_WithExam_MQA_Yes(testData);

//		CourseSession.courseSessionAuditTrails();
//
//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//
//		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Proposed Status");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);
//
//		Logout.signOutPage();
	}

	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getQuestionBank_Configuration_Registration_Approve() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "QuestionBank", enabled = true)
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank  Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.masterPlant();
//
//		PrepareQB.QBRegistrationApproval_Configuration(testData);
//
//		epiclogin.navigateTolearnIQPlant();

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);

//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);

		// ; Logout.signOutPage();

	}

	// Test Method for QuestionPaper Registration
	// -------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);

		Logout.signOutPage();

	}

	// Test Method for 'Respond Document Reading', 'Question paper'
	// and 'Missed Question
	// Analysis'-------------------------------------------------------------------------------------------

	@Test(priority = 6, enabled = true)
	public void respond_DocumentReading_Respond_QuestionPaper_Respond_MQA() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading").assignAuthor(CM_CourseSession.mqaTraineeID)
					.assignCategory("Respond Document Reading");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.mqaTraineeID,
				CM_CourseSession.mqaTraineePassword);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_CourseSession.mqaTraineePassword);

		test = extent.createTest("Respond Question paper (Not100%Qualified)")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("Respond Question paper (Not100%Qualified)");

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_Not_100_QUALIFIED, Constants.REType,
				CM_CourseSession.mqaTraineePassword);
		 if (isReportedRequired == true) {
		test = extent.createTest("MissedQuestionAnalysisLog before responding MQA")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("MissedQuestionAnalysisLog before responding MQA");
}
		reports.missedQuestionAnalysisLog_Report(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_PENDING_LIST);

		if (isReportedRequired == true) {
			test = extent.createTest("Respond Missed Question Analysis")
					.assignAuthor(CM_CourseSession.mqaTraineeEmployeeID)
					.assignCategory("Respond Missed Question Analysis");
		}
		respondMQA.respondMissedQuestionAnalysis(CM_CourseSession.mqaTraineePassword);

		if (isReportedRequired == true) {
		test = extent.createTest("MissedQuestionAnalysisLog after responding MQA")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("MissedQuestionAnalysisLog after responding MQA");
		}
		reports.missedQuestionAnalysisLog_Report(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_COMPLETED_LIST);

//		test = extent.createTest("Check Trainees at Course Session Screen")
//				.assignAuthor( ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Check Trainees at Course Session Screen");
//		
//		CourseSession.verify_Employees_At_Coursesession();

		Logout.signOutPage();

	}
}