package com.Automation.Strings;

public enum SSORoleAsssingmentStrings {
	
	
	//Role Assignment Menu
	Click_RoleAssgn_DC("Click on 'Role Assignment'."),
	Click_RoleAssgn_AC("'Role Assignment Registration Initiation' screen should be displayed."),
	Click_RoleAssgn_AR("'Role Assignment Registration Initiation' screen is getting displayed."),
	Click_RoleAssgn_SS("'Role Assignment'."),
	
	SearchBy_AC("Option to search with 'Top 250 Records, User ID, User Name' should be available."),
	SearchBy_AR("Option to search with 'Top 250 Records, User ID, User Name' is available."),
	
	//Enter User Name Search By
	Enter_UserName_DC("Enter 'User Name in the 'Search By'"),
	Enter_UserName_SS("'User Name'"),
	
	//Select User NameSearch By
	Select_UserName_DC("Select 'User Name'"),
	
	//Enter Registred User name
	
	Enter_RegUser_DC("Enter the above registered 'User Name'."),
	
	//Role drop down
	
	Click_RoleDD_DC("Click on 'Select' under 'Role' column against the above registred User Name"),
	Click_RoleDD_AC("List of 'Roles' should be displayed for the selection."),
	<PERSON>lick_RoleDD_AR("List of 'Roles' are getting displayed for the selection."),
	Click_RoleDD_SS("'Role'"),
	
	Enter_Role_DC("Enter the required valid 'Role'."),
	
	Select_Role_DC("Select the required Role."),
	
	
	Submit_AC(
			"'Role Assignment Registration Initiated'  confirmation message should be displayed with ‘Done’ button.</div>"),
	Submit_AR(
			"'Role Assignment Registration Initiated' confirmation message is getting displayed with ‘Done’ button.</div>");
	

	
	
	private final String roleAssignStrings;

	SSORoleAsssingmentStrings(String RoleAssign) {

		this.roleAssignStrings = RoleAssign;

	}

	public String getSSORoleAsssingmentStrings() {
		return roleAssignStrings;
	}

}
