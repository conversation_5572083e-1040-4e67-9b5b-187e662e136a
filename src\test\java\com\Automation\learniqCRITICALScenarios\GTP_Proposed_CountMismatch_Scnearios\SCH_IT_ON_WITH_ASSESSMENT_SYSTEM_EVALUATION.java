package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_SelfNomination;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online IT Session with assessment for scheduled course and make at
 * least one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, qualified, To Be Retrained with System
 * evaluation by viewing Individual employee report at each transaction starting
 * from course session, also add at least 2 additional users and make sure that
 * should be qualified and to be retrained and view IER for those employees.
 */

public class SCH_IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/SCH_IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/SCH_IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";

	public SCH_IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic  Registration");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course  Registration");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule  Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule  Modification");
		}

		TrainingShcedule.modifyTrainingScheduled(testData);
	}

	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}

		trainer.trainer_Modification_AuditTrails(testData);

	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionNew");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {

		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();

		verifyCSScreen.courseSession_Online_WithExam_SystemEvaluation(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
	}

//	// Test Method for BatchFormation Configuration, Registration with
//	// AuditTrails---------------------------------------------------------------------------------------------
//
	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Registration");
		}

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_NotResponded_Skipped(testData);

	}
	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionBank")
	public void QuestionBank_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
	}
	// Test Method for QuestionPaper Registration
	// -------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);

	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}

		RecordAttendance.recordAttendance_OnlineSession_CountMismatch(testData);
		Logout.signOutPage();

	}
//
	// Test Method for RespondQuestionPaper
	// (Qualify)-------------------------------------------------------------------------------------------

	@Test(priority = 10, enabled = true)
	public void Respond_QuestionPaper_Qualify() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Question paper (Qualify)")
					.assignAuthor(CM_VerifyCourseSessionScreen.QualifiedTraineeID)
					.assignCategory("Respond Question paper (Qualify)");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.QualifiedTraineeID, CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		Logout.signOutPage();

	}

	// Test Method for RespondQuestionPaper
	// (ToBeRetrained)-------------------------------------------------------------------------------------------

	@Test(priority = 11, enabled = true)
	public void Respond_QuestionPaper_ToBeRetrained() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Question paper (ToBeRetrained)")

					.assignAuthor(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID)

					.assignCategory("Respond Question paper (ToBeRetrained)");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		Logout.signOutPage();

	}

	// Test Method for Retake Attempt Pending
	// (ToBeRetrained)-------------------------------------------------------------------------------------------

	@Test(priority = 12, enabled = true)
	public void retake_Attempts_Pending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Question paper and make user To Retake pending")

					.assignAuthor(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID)

					.assignCategory("Respond Question paper and make user To Retake pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_VerifyCourseSessionScreen.retakeUserID,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		epiclogin.plant1();
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);

		// Logout.signOutPage();

	}

	@Test(priority = 13, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states before course retraining");
		}
		verifyCSScreen.checkCourseSession_IT_With_Assessment_After_Keeping_Employees_In_Different_States();
	}

	@Test(priority = 14, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session Report  Screen after keping employees in different states before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session Report Screen after keping employees in different states before course retraining");
		}
		CSRReport.TBPCSRReport_IT_WithAssesment_After_keping_Emplpoyees_In_Diff_States();
	}

	@Test(priority = 15, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Retraining").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Course Retraining");
		}
		CourseRetraining.Scheduled_course_Retraining_IT_Online_With_Assessment_();

	}

	@Test(priority = 16, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states after course retraining in Unscheduled Tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states after course retraining in Unscheduled Tab");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_();
	}

	@Test(priority = 17, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining1() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states after course retraining in Scheduled Tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states after course retraining in Scheduled Tab");
		}
		verifyCSScreen.scheduled_IT_online_With_Assessment_VerifyCourseSession_Screen_After_CourseRetraining();
	}

	@Test(priority = 18, enabled = true)
	public void verifyCSR_AfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session Report Screen after keping employees in different states after course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session Report Screen after keping employees in different states after course retraining");
		}
		CSRReport.TBPCSRReport_IT_WithAssesment_After_CourseRetraining();

		Logout.signOutPage();

	}
}
