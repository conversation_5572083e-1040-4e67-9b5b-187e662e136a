package com.Automation.Strings;

public enum GroupStrings {

	GroupMenu_DC("Click on 'Group' submenu."), GroupMenu_SS("'Group' submenu"),

	// Group submenu
	Group_Config_DC("Click on 'Group' submenu."),
	Group_Config_AC("'Group Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	Group_Config_AR("'Group Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	Group_Config_SS("'Group'"),
	// Group Configuration
	Click_DoneatGroupConfig_AC("'Group Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatGroupConfig_AR("'Group Configuration Registration' screen is getting displayed.</div>"),

	Group_Initiate_AC("'Groups - Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should be displayed with 'Group Name', 'Unique Code', 'Description' and 'Subgroup Name(s)'.</div>"
			+ "<div><b>*</b> The screen should be displayed with 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b> Search option should be available for both 'Available Subgroups' and 'Selected Subgroups' list boxes.</div>"),
	Group_Initiate_AR("'Groups - Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'Group Name', 'Unique Code', 'Description' and 'Subgroup Name(s)'.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b> Search option is available for both 'Available Subgroups' and 'Selected Subgroups' list boxes.</div>"),

	GroupName_DC("Enter the value less than or equals to 250 characters in 'Group Name' field"),
	GroupName_SS("'Group Name'"),

	Description_DC("Enter the value less than or equals to 250 characters in 'Description' field"),
	Description_SS("'Description'"),

	SearchAvailableSubgrp_DC("Enter the above register Subgroup in 'Available Subgroups' textbox."),
	SearchAvailableSubgrp_SS("Search 'Subgroup Name'"),

	SelectAvailableSubgrp_DC("Select the above registered Subgroup under 'Available Subgroups' listbox."),
	SelectAvailableSubgrp_AC("Subgroup should be selected."), SelectAvailableSubgrp_AR("Subgroup is getting selected."),
	SelectAvailableSubgrp_SS("Select 'Subgroup'"),

	ClickRightToggle_DC("Click 'right toggle' button."),
	ClickRightToggle_AC("Selected Subgroup should be moved to 'Selected  Subgroups' listbox."),
	ClickRightToggle_AR("Selected Subgroup is getting moved to 'Selected  Subgroups' listbox."),
	ClickRightToggle_SS("'right toggle' button."),

	SubmitGroupwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Groups: Registration Initiation'.</div>"),
	SubmitGroupwithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Groups: Registration Initiation'.</div>"),

	Esign_ProceedGroup_AC(
			"'Group  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedGroup_AR(
			"'Group  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	GroupAudittrails_AC("'Groups Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
			+ "<div><b>*</b>"
			+ " The screen should be displayed with 'Group Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>" + " The screen should be displayed with list of all the registered Groups</div>"),
	GroupAudittrails_AR("'Groups Audit Trails Audit Trails' screen is getting be displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
			+ "<div><b>*</b>"
			+ " The screen is getting displayed with 'Group Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>" + " The screen is getting displayed with list of all the registered Groups</div>"),
	GroupAudittrails_SS("'Groups Audit Trails' screen."),

	SearchBy_GroupAudit_AC(
			"Option to search with 'Top 250 Records, Group Name, Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_GroupAudit_AR(
			"Option to search with 'Top 250 Records, Group Name, Unique Code, Initiated Between' are available.</div>"),

	Select_GroupName_DC("Select 'Group Name'."),

	Like_GroupName_DC("Enter the above registered 'Group Name' in 'Like' field."),

	Click_Group_for_AuditTrails_DC("Click on the above registered 'Group Name'."),
	Click_UniqueCode_for_AuditTrails_DC("Click on the 'Unique Code'."),
	Click_Group_for_AuditTrails_AC(
			"'Groups - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should display the details of the Group entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Initiated'.<div>"
					+ "<div><b>*</b> The 'Events' section should display only the Registration 'Initiated' transaction with 'Username, Date& Time and Remark(s)/ Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be as '1' and the 'No. of Approvals Completed' should be read as '0'.<div>"
					+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),
	Click_Group_for_AuditTrails_AR(
			"'Groups - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is displaying the details of the Group entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Initiated'.<div>"
					+ "<div><b>*</b> The 'Events' section is displaying only the Registration 'Initiated' transaction with 'Username, Date& Time and Remark(s)/ Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' is '1' and the 'No. of Approvals Completed' reads as '0'.<div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),
	Click_Group_for_AuditTrails_SS("'Groups' - Audit Trails."),

	// Group configuration Audit trails

	Group_ConfigAudit_DC("Click on 'Groups' submenu."),
	Group_ConfigAudit_AC("'Groups Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Group_ConfigAudit_AR("'Group Configuration Registration' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Group_ConfigAudit_SS("'Groups' submenu"),

	Click_Config_Proceed_AC(
			"'Groups Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Groups Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Groups Configuration - Audit Trails'."),

	Click_Group_for_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_Group_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Group_for_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Group_for_ConfigAuditTrails_SS("'Transactions'"),

	Close_AuditTrails_Group_AC("'Group Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_Group_AR("'Group Audit Trails' screen is getting displayed.</div>"),

	Close_ConfigAuditTrails_Group_AC("'Groups Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_Group_AR("'Groups Configuration Audit Trails' screen is getting displayed.</div>"),

	SYS_UserGroupsSubgroupApproveMenu_DC("Click on 'Subgroup' submenu."),

	SYS_UserGroupsSubgroupApproveMenu_SS("'Subgroup'"),

	SYS_UserGroups_groupApproveMenu_AC("'Groups Approval Tasks' screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details should be displayed.<div>"
			+ "<div><b>*</b> All the Groups whose registration request is to be approved by the current user should be listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen should contain 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen should contain 'Group Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.<div>"),
	SYS_UserGroups_groupApproveMenu_AR("'Groups Approval Tasks' screen is getting displayed."
			+ "<div><b>*</b> Screen contains 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details are getting displayed.<div>"
			+ "<div><b>*</b> All the Groups whose registration request is to be approved by the current user is listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen contains 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen contains 'Group Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is getting displayed as '20'.<div>"),

	// search by
	SearchBy_Group_AC(
			"Option to search with 'Top 250 Records, Group Name, Unique Code ,Initiated Between' should be available.</div>"),
	SearchBy_Group_AR(
			"Option to search with 'Top 250 Records,Group Name,Unique Code ,Initiated Between' are available.</div>"),

	Click_Group_for_Approve_DC("Click on the above registered 'Group Name'."),
	Click_Group_for_Approve_AC(" 'Groups Registration Approval' screen should be displayed</div>" + "<div><b>*</b>"
			+ " The screen should display the details of the Group entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' should be displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section should display the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' should be reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options should be available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' should be available.</div>"),
	Click_Group_for_Approve_AR(" 'Groups Registration Approval' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with details of the Group entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' is getting displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section is displaying the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options are available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' are available.</div>"),
	Click_Group_for_Approve_SS("'Group' menu"),

	// approve radio button
	GroupApproval_DC("Select 'Decision' as 'Approve'."),
	GroupApproval_AC("Selected option should be accepted for 'Decision' field.</div>"),
	GroupApproval_AR("Selected option is getting accepted for 'Decision' field.</div>"), GroupApproval_SS("'Approve'"),

	// submit at Subgroup approval
	Submit_Group_Approval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Groups: Registration Approval: Approve'.</div>"),
	Submit_Group_Approval_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Groups: Registration Approval: Approve'.</div>"),
	Submit_Group_Approval_SS("'E-Sign window'"),

	Esign_ProceedGroup_Approval_AC(
			"'Group Registration Approved Unique Code:(Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedGroup_Approval_AR(
			"'Group Registration Approved Unique Code:(Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Click_Group_for_Approve_AuditTrails_AC(
			"'Groups - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should display the details of the Group entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section should be updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be as '1' and the 'No. of Approvals Completed' should be read as '1'.<div>"
					+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),
	Click_Group_for_Approve_AuditTrails_AR(
			"'Groups - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is displaying the details of the Group entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section is updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' is '1' and the 'No. of Approvals Completed' reads as '1'.<div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),

	Approve_Group_Remarks_DC("Enter the value less than or equals to 250 characters in 'Remark(s)/Reason(s)' field."),
	Approve_Group_Remarks_SS("Remark(s) / Reason(s)"),

	;

	private final String groupStrings;

	GroupStrings(String groupStrings) {

		this.groupStrings = groupStrings;

	}

	public String getGroupStrings() {
		return groupStrings;
	}

}
