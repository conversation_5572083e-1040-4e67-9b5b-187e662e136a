package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Topic extends OQActionEngine {
	Properties prop;
	public static String Topic = "";
	public static String Uniquecode = "";
	public static String Description = "";
	public static String DocumentName = "";
	public static String TopicUniqueCode = "";
	public static String Approver = "";
	public static String SubjectTagName = "";

	public static String getDocumentName() {
		return DocumentName;
	}

	public static void setDocumentName(String documentName) {
		DocumentName = documentName;
	}

	public static String getSubjectTagName() {
		return SubjectTagName;
	}

	public static void setSubjectTagName(String subjectTagName) {
		SubjectTagName = subjectTagName;
	}

	public static String getTopic() {
		return Topic;
	}

	public static void setTopic(String topic) {
		Topic = topic;
	}

	public static String getTopicUniqueCode() {
		return TopicUniqueCode;
	}

	public static void setTopicUniqueCode(String topicuniqecode) {
		TopicUniqueCode = topicuniqecode;
	}

	public static String getUniqueCode() {
		return Uniquecode;
	}

	public static void setUniquecode(String uniquecode) {
		Uniquecode = uniquecode;
	}

	public static String getApproverName() {
		return Approver;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[9]/a[1]")
	WebElement configMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/child::li/a[contains(text(),'Approve')]")
	WebElement approvemenu;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Approve_MEN134']")
	WebElement approvetopicmenu;

	@FindBy(id = "TMS_Course Manager_Audit Trails_MEN134")
	// TMS_Course Manager_Audit Trails
	WebElement aduitTrailsTopic;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Topic Name')]")
	WebElement searchByTopicNameDropdown;

	@FindBy(xpath = "(//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')])[1]")
	WebElement searchByUniquecodeDropdown;

	@FindBy(xpath = "//li[contains(text(),'Topic Name')]")
	WebElement approvesearchByTopicNameDropdown;

	@FindBy(xpath = "(//li[contains(text(),'Unique Code')])[1]")
	WebElement approvesearchByUniqueCodeDropdown;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callAtEsignInitiationLabel;

	@FindBy(id = "Description")
	WebElement topicNameLike;

	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniquecodeLike;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(id = "TMS_Course Manager_Configure_MEN134")
	WebElement configTopicMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li[2]//a")
	WebElement initiate;
	@FindBy(id = "TMS_Course Manager_Initiate_MEN134")
	WebElement topicMenu;
	@FindBy(id = "CMTopic_TopicDesc")
	WebElement topicName;
	@FindBy(id = "CMTopic_UserCode")
	WebElement topicUniqCode;
	@FindBy(id = "CMTopic_Description")
	WebElement description;
	@FindBy(xpath = "//span[text()='Document Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement documentName;
	@FindBy(xpath = "//label[text()='Document Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement documentName320;
	@FindBy(id = "CMTopic_TreeVC_SearchTxt")
	WebElement documentSearchTextBox;
	@FindBy(xpath = "//a[@class='anchorTxt']")
	WebElement fetchRecords;
	@FindBy(xpath = "//div[@id='CMTopic_AvailableDocs']/ul[@id='CMTopic_AvailableDocs_ul']/li[1]")
	WebElement greenTickMark;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approvetext;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDrpdown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")

	WebElement noOfAprReqForSTCDropdwn;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']//li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']//li[1]")
	WebElement searchSel3;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Topic Name']//following-sibling::span")
	WebElement auditCompareTRNTopicName;

	@FindBy(xpath = "//label[@for='CMTopicV1_NewSubTags']//following-sibling::div")
	WebElement auditCompareTRNSubjectName;
	@FindBy(xpath = "//label[@for='CMTopicV1_NewCatTags']//following-sibling::div")
	WebElement auditCompareTRNCategoryName;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']//following-sibling::span")
	WebElement auditCompareTRNUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Topic Unique Code']//following-sibling::span")
	WebElement auditCompareTRNTopicUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Description']//following-sibling::span")
	WebElement auditCompareTRNTopicDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement auditCompareTRNDocument1;
	@FindBy(xpath = "//a[@class='anchorTxt']")
	WebElement verifyDocCode;

	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[2]")
	WebElement auditComparenextreviewdate;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;

	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[3]")
	WebElement auditComparedocumentcode;

	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[2]//a")
	WebElement auditCompareTRNDocument1hyper;
	@FindBy(xpath = "//a[@class='anchorTxt']")
	WebElement auditCompareTRNDocument1hyper1;
	@FindBy(xpath = "//div[@id='PreviewModal']//span[@id='PreviewCloseBtn']")
	WebElement documentReadClose;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Topic Unique Code']//following-sibling::span")
	WebElement topicuniquecode;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement employeeName;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Subgroup Name')]")
	WebElement searchBySubgroupNameDropdown1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditCompareTRNLevel1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditCompareTRNRole1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement iniatiatedby;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement iniatiatedon;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement revno;
	@FindBy(xpath = "//span[@id='MainTitle']")
	WebElement subroupsreen;
	@FindBy(xpath = "//span[@id='SubTitle']")
	WebElement subroupsreen1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title pb-0 pt-2']")
	WebElement subroupauditsreen;
	@FindBy(xpath = "//span[text()='Subgroup']")
	WebElement subroupauditsreen1;
	@FindBy(xpath = "//span[text()='Audit Trails']")
	WebElement subroupauditsreen2;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "(//a[text()='Configuration Audit Trails'])[1]")
	WebElement configurationTopicAuditTrails;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Configuration Audit Trails_NIC00014']")
	WebElement configurationTopicAuditTrails1;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callAtEsignApprovalLabel;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callEsignAtInitiaiton;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
//	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
//	WebElement select1ForInitDropdown;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[6]")
	WebElement auditTrailPageColumn6;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[7]")
	WebElement auditTrailPageColumn7;
	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationText2;
	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal2;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callEsignAtApproval;

	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;

	@FindBy(xpath = "//a[@id='AddSubtagbtn']")
	WebElement subjecttags;
	@FindBy(xpath = "//input[@id='CMTopic_SubTagsSrch']")
	WebElement subjectsearch;

	@FindBy(xpath = "//a[@id='AddCattagbtn']")
	WebElement Categorytags;
	@FindBy(xpath = "//input[@id='CMTopic_CatTagsSrch']")
	WebElement Categorysearch;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title pb-0 pt-2']")
	WebElement auditComparesubtitle;

	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement approveremarks;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	public CM_Topic() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * 
	 * This method is for topic configuration
	 * 
	 */

	public void topicConfiguration_Registration_Approval(HashMap<String, String> testData, String ConfigureRemarks,
			String configureConfirmationText1) {

		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(configTopicMenu, TopicStrings.Topic_Config_DC.getTopicStrings(),
				TopicStrings.Topic_Config_AC.getTopicStrings(), TopicStrings.Topic_Config_AR.getTopicStrings(),
				TopicStrings.Topic_Config_SS.getTopicStrings());
		switchToBodyFrame(driver);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "RegistrationApproval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("TopicApprovalRequired"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), "Remarks",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		waitForElementVisibile(confirmationDone);
		highLightElement(driver, confirmationText, "ConfirmationText", test);
		click2(confirmationDone, CommonStrings.Click_DoneatConfig_DC.getCommonStrings(),
				TopicStrings.Click_DoneatTopicConfig_AC.getTopicStrings(),
				TopicStrings.Click_DoneatTopicConfig_AR.getTopicStrings(),
				CommonStrings.Click_DoneatConfig_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	/**
	 * 
	 * This method is for topic registration and audit trails
	 * 
	 */

	public void topic_Registration(HashMap<String, String> testData) {

		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}

		setTopic(Topic = testData.get("TopicName") + s);
		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiate, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(topicMenu, TopicStrings.TopicRegistrationScreen_DC.getTopicStrings(),
				TopicStrings.TopicRegistrationScreen_AC.getTopicStrings(),
				TopicStrings.TopicRegistrationScreen_AR.getTopicStrings(),
				TopicStrings.TopicRegistrationScreen_SS.getTopicStrings());
		switchToBodyFrame(driver);
		sendKeys2(topicName, TopicStrings.TopicName_DC.getTopicStrings(), Topic,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.TopicName_SS.getTopicStrings());
		sendKeys2(topicUniqCode, TopicStrings.TopicUniqueCode_DC.getTopicStrings(), TopicUniqueCode + s,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.TopicUniqueCode_SS.getTopicStrings());
		sendKeys2(description, TopicStrings.TopicDescription_DC.getTopicStrings(), Topic,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.TopicDescription_SS.getTopicStrings());

		click2(documentName, TopicStrings.ClickDocumentName_DC.getTopicStrings(),
				TopicStrings.ClickDocumentName_AC.getTopicStrings(),
				TopicStrings.ClickDocumentName_AR.getTopicStrings(),
				TopicStrings.ClickDocumentName_SS.getTopicStrings());
		TimeUtil.shortWait();
		// setDocumentName(DocumentName = testData.get("New"));
		scrollToViewElement(documentSearchTextBox);
		sendKeys2(documentSearchTextBox, TopicStrings.TopicdocumentSearchTextBox_DC.getTopicStrings(),
				testData.get("Document") + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.TopicdocumentSearchTextBox_SS.getTopicStrings());
		setDocumentName(testData.get("Document"));
		TimeUtil.shortWait();
		click2(fetchRecords, TopicStrings.Topicfetchrecords_DC.getTopicStrings(),
				TopicStrings.Topicfetchrecords_AC.getTopicStrings(),
				TopicStrings.Topicfetchrecords_AR.getTopicStrings(),
				TopicStrings.Topicfetchrecords_SS.getTopicStrings());
		TimeUtil.shortWait();
		scrollToViewElement(greenTickMark);
		click2(greenTickMark, TopicStrings.greenTickMark_DC.getTopicStrings(),
				TopicStrings.greenTickMark_AC.getTopicStrings(), TopicStrings.greenTickMark_AR.getTopicStrings(),
				TopicStrings.greenTickMark_SS.getTopicStrings());
		scrollToViewElement(submitButton);
		click2(submitButton, TopicStrings.Submit_DC.getTopicStrings(), TopicStrings.Submit_AC.getTopicStrings(),
				TopicStrings.Submit_AR.getTopicStrings(), TopicStrings.Submit_SS.getTopicStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.longwait();
		switchToDefaultContent(driver);
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		scrollToViewElement(courseManagerAuditTrails);
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		scrollToViewElement(aduitTrailsTopic);
//		click2(aduitTrailsTopic, TopicStrings.click_TopicAuditTrails_DC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_AC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_AR.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_SS.getTopicStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, TopicStrings.SearchBy_TP_DC.getTopicStrings(),
//				TopicStrings.SearchBy_TP_AC.getTopicStrings(), TopicStrings.SearchBy_TP_AR.getTopicStrings(),
//				TopicStrings.SearchBy_TP_SS.getTopicStrings());
//		click2(searchByTopicNameDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
//		sendKeys2(topicNameLike, TopicStrings.Like_TopicName_DC.getTopicStrings(), Topic,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				TopicStrings.Like_TopicName_SS.getTopicStrings());
//		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		click2(displayedRecord, TopicStrings.TP_for_AuditTrails_DC.getTopicStrings(),
//				TopicStrings.TP_for_AuditTrails_AC.getTopicStrings(),
//				TopicStrings.TP_for_AuditTrails_AR.getTopicStrings(),
//				TopicStrings.TP_for_AuditTrails_SS.getTopicStrings());
//		driver.switchTo().frame(0);
//		scrollToViewElement(auditCompareTRNDocument1);
//		auditCompareTRNDocument1hyper.click();
//		switchToDefaultContent(driver);
//		documentScrollUpDown();
//		documentReadClose.click();
//		isAlertPresent(driver);
//		switchToBodyFrame(driver);
//		driver.switchTo().frame(0);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				TopicStrings.Close_AuditTrailsTopic_AC.getTopicStrings(),
//				TopicStrings.Close_AuditTrailsTopic_AR.getTopicStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);

	}

	/**
	 * 
	 * This method is for topic Approval and audit trails
	 * 
	 */

	public void TopicRegistration_Approval_AuditTrails_Yes(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(approvemenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(approvetopicmenu, TopicStrings.TopicRegistrationScreen_DC.getTopicStrings(),
				TopicStrings.TopicApproveScreen_AC.getTopicStrings(),
				TopicStrings.TopicApproveScreen_AR.getTopicStrings(),
				TopicStrings.TopicApproveScreen_SS.getTopicStrings());
		switchToBodyFrame(driver);

		click2(searchByNew, TopicStrings.SearchBy_TP_DC.getTopicStrings(),
				TopicStrings.SearchBy_TP_AC.getTopicStrings(), TopicStrings.SearchBy_TP_AR.getTopicStrings(),
				TopicStrings.SearchBy_TP_SS.getTopicStrings());
		click2(searchByTopicNameDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
		sendKeys2(topicNameLike, TopicStrings.Like_TopicName_DC.getTopicStrings(), Topic,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.Like_TopicName_SS.getTopicStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, TopicStrings.TP_for_AuditTrails_DC.getTopicStrings(),
				TopicStrings.TP_for_AuditTrails1_AC.getTopicStrings(),
				TopicStrings.TP_for_AuditTrails1_AR.getTopicStrings(),
				TopicStrings.TP_for_AuditTrails_SS.getTopicStrings());

		highLightElement(driver, approvetext, "Subgroup Registration Intiation", test);
		click2(approveRadioBtn, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproverRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				TopicStrings.SubmitTopicapproval_AC.getTopicStrings(),
				TopicStrings.SubmitTopicapproval_AR.getTopicStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
//		TimeUtil.mediumWait();
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		scrollToViewElement(courseManagerAuditTrails);
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		scrollToViewElement(aduitTrailsTopic);
//		click2(aduitTrailsTopic, TopicStrings.click_TopicAuditTrails_DC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_AC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_AR.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails_SS.getTopicStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, TopicStrings.SearchBy_TP_DC.getTopicStrings(),
//				TopicStrings.SearchBy_TP_AC.getTopicStrings(), TopicStrings.SearchBy_TP_AR.getTopicStrings(),
//				TopicStrings.SearchBy_TP_SS.getTopicStrings());
//		click2(searchByTopicNameDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
//				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
//		sendKeys2(topicNameLike, TopicStrings.Like_TopicName_DC.getTopicStrings(), Topic,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				TopicStrings.Like_TopicName_SS.getTopicStrings());
//		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, TopicStrings.TP_for_AuditTrails_DC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails1_AC.getTopicStrings(),
//				TopicStrings.click_TopicAuditTrails1_AR.getTopicStrings(),
//				TopicStrings.TP_for_AuditTrails_SS.getTopicStrings());
//		driver.switchTo().frame(0);
//		auditCompareTRNDocument1hyper.click();
//		switchToDefaultContent(driver);
//		documentScrollUpDown();
//		documentReadClose.click();
//		isAlertPresent(driver);
//		switchToBodyFrame(driver);
//		driver.switchTo().frame(0);
//		scrollToViewElement(auditClose);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				TopicStrings.Close_AuditTrailsTopic_AC.getTopicStrings(),
//				TopicStrings.Close_AuditTrailsTopic_AR.getTopicStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
	}

}
