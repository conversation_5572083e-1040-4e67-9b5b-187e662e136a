package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;

import java.util.HashMap;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Offline IT Session without assessment for scheduled course and make
 * atleast one employee absent, present with Evaluation method as 'No' and by
 * viewing Individual employee report at each transaction starting from course
 * session, also add at least one user and check IER report
 */
public class UNSCH_IT_OFF_WITHOUT_ASSESSMENT extends OQActionEngine {

	public UNSCH_IT_OFF_WITHOUT_ASSESSMENT() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	String ExcelPath = "./learnIQTestData/UnscheduledScenariosData/UNSCH_IT_OFF_WITHOUT_ASSESSMENT.xlsx";

	// Test Method for Topic Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//			epiclogin.masterPlant();
		//
//			InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//					Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//			Logout.signOutPage();

//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//			epiclogin.plant1();
		//
//			InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//			Logout.signOutPage();

	}

	// Test Method for Course Registration

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));

//			epiclogin.masterPlant();
		//
//			Initiate_Course.courseConfiguration_Reg(testData);

//			epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//			Logout.signOutPage();

//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//			epiclogin.plant1();
		//
//			Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
//			Logout.signOutPage();

	}


	// Test Method for Trainer Modification

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
//				epiclogin.plant1();
//				trainer.TrainerModificationConfigurations(testData);
		trainer.trainer_Modification_AuditTrails(testData);
//				Logout.signOutPage();
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//				epiclogin.plant1();
//				trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//				Logout.signOutPage();

	}

	// ----------Course Session Reg Initiation - Test Method---------- //

	ExcelUtilUpdated excel5 = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "courseSession")
	public Object[][] getCourseSession() throws Exception {
		Object[][] obj = new Object[excel5.getRowCount()][1];
		for (int i = 1; i <= excel5.getRowCount(); i++) {
			HashMap<String, String> testData = excel5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "courseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
		test = extent.createTest("CourseSession Registration")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// CourseSession.courseSessionConfiguration(testData);

		CourseSession.course_session_Offline_ClassroomType_Without_Assessment(testData);

//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");

//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		CourseSession.courseSession_Approve(testData);
//		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Proposed Status");
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);

		// Logout.signOutPage();
	}

	// Test Method for BatchFormation  Registration 

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
		test = extent.createTest("BatchFormation Registration")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("BatchFormation Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

//		BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Offline_Users(testData);

	//	BatchFormation.proposeBatchFormationAuditTrail();
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Employee not selected in batch")
					.assignAuthor(CM_CourseSession.SkippedEmployeeID)
					.assignCategory("Individual Employee Report For Employee not selected in batch");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED, Constants.ITType);

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Employee selected in batch")
					.assignAuthor(CM_CourseSession.SkippedEmployeeID)
					.assignCategory("Individual Employee Report For Employee selected in batch");
		}

		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);

		// Logout.signOutPage();

		// Logout.signOutPage();
	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Record Attendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Attendance");
			}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		RecordAttendance.recordAttendance_OnlineSession_1AdditionalUser(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Skipped Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Skipped Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID, Constants.EMPLOYEESTATUS_AS_SKIPPED,
				Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Absent Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Absent Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.AbsentEmployeeID, Constants.EMPLOYEESTATUS_AS_ABSENT,
				Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Present Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Present Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.ITType);

		Logout.signOutPage();
	}
}
