package com.Automation.Strings;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public enum SetGlobleProfileStrings {

	SetGlobalProfile_DC("Click on 'Set Global Profile' submenu."),
	SetGlobalProfile_AC("'Global Profile Registration Initiation’ screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should be displayed with 'Category' and 'Role' fields.</div>"
			+ "<div><b>*</b> The screen should be displayed with 'View Existing' and 'Submit' buttons.</div>"),
	SetGlobalProfile_AR("'Global Profile Registration Initiation’ is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'Category' and 'Role' fields.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'View Existing' and 'Submit' buttons.</div>"),
	SetGlobalProfile_SS("'Set Global Profile'"),

	CategoryDropDown_DC("Click the 'Category' drop down list."),
	CategoryDropDown_AC("'Login and Non-Login' categories should be displayed."),
	CategoryDropDown_AR("'Login and Non-Login' categories are getting displayed."), CategoryDropDown_SS("'Category'"),

	SelectLogin_DC("Select 'Login' category"), SelectLogin_SS("'Login' category"),

	RoleDropDown_DC("Click the 'Role' drop down list."),
	RoleDropDown_AC("'List of Roles registered under the selected category should be displayed."),
	RoleDropDown_AR("'List of Roles registered under the selected category are getting displayed."),
	RoleDropDown_SS("'Role' DropDown"),

	Enter_RequiredRole_DC("Enter the above registered 'Role' in search field."),
	Enter_RequiredRole_SS("'Role' search field"),

	Select_RequiredRole_DC("Select the required 'Role'"),
	Select_RequiredRole_AC(
			"'List Of Home Screen Menu / Sub Menu Items' of Course Manager, Document Manager and System Manager should be displayed with an option to select the required menus"),
	Select_RequiredRole_AR(
			"'List Of Home Screen Menu / Sub Menu Items' of Course Manager, Document Manager and System Manager are getting displayed with an option to select the required menus"),
	Select_RequiredRole_SS("Select 'Role'"),

	CM_InitiateDropDown_DC("Click the toggle sign against the 'Initiate' under the 'Course Manager' section."),
	CM_InitiateDropDown_SS("'Initiate' toggle sign"),

	SubmenuItems_AC("The sub menu items should be displayed"),
	SubmenuItems_AR("The sub menu items are getting displayed"),

	CM_SelfStudyOpenCourses_DC("Check 'Self-Study Open Courses' option under 'Initiate' menu."),
	CM_SelfStudyOpenCourses_AC("'Self-Study Open Courses' submenu under 'Initiate' menu should be selected"),
	CM_SelfStudyOpenCourses_AR("'Self-Study Open Courses' submenu under 'Initiate' menu is getting selected"),
	CM_SelfStudyOpenCourses_SS("'Self-Study Open Courses' checkbox"),

	CM_ProposeDropDown_DC("Click the toggle sign against the 'Propose' under the 'Course Manager' section."),
	CM_ProposeDropDown_SS("'Propose' toggle sign"),

	CM_SelfNomination_DC("Check 'Self-Nomination' option under 'Propose' menu."),
	CM_SelfNomination_AC("'Self-Nomination' submenu under 'Propose' menu should be selected"),
	CM_SelfNomination_AR("'Self-Nomination' submenu under 'Propose' menu is getting selected"),
	CM_SelfNomination_SS("'Self-Nomination' checkbox"),

	CM_RespondDropDown_DC("Click the toggle sign against the 'Respond' under the 'Course Manager' section."),
	CM_RespondDropDown_SS("'Respond' toggle sign"),

	CM_CourseInvitation_DC("Check 'Course Invitation' option under 'Respond' menu."),
	CM_CourseInvitation_AC("'Course Invitation' submenu under 'Respond' menu should be selected"),
	CM_CourseInvitation_AR("'Course Invitation' submenu under 'Respond' menu is getting selected"),
	CM_CourseInvitation_SS("'Course Invitation' checkbox"),

	CM_QuestionPaper_DC("Check 'Question Paper' option under 'Respond' menu."),
	CM_QuestionPaper_AC("'Question Paper' submenu under 'Respond' menu should be selected"),
	CM_QuestionPaper_AR("'Question Paper' submenu under 'Respond' menu is getting selected"),
	CM_QuestionPaper_SS("'Question Paper' checkbox"),

	CM_feedbackRequest_DC("Check 'Feedback Request' option under 'Respond' menu."),
	CM_feedbackRequest_AC("'Feedback Request' submenu under 'Respond' menu should be selected"),
	CM_feedbackRequest_AR("'Feedback Request' submenu under 'Respond' menu is getting selected"),
	CM_feedbackRequest_SS("'Feedback Request' checkbox"),

	CM_documentReading_DC("Check 'Document Reading' option under 'Respond' menu."),
	CM_documentReading_AC("'Document Reading' submenu under 'Respond' menu should be selected"),
	CM_documentReading_AR("'Document Reading' submenu under 'Respond' menu is getting selected"),
	CM_documentReading_SS("'Document Reading' checkbox"),

	CM_inductionTraining_DC("Check 'Induction Training' option under 'Respond' menu."),
	CM_inductionTraining_AC("'Induction Training' submenu under 'Respond' menu should be selected"),
	CM_inductionTraining_AR("'Induction Training' submenu under 'Respond' menu is getting selected"),
	CM_inductionTraining_SS("'Induction Training' checkbox"),

	CM_MQA_DC("Check 'Missed Question Analysis' option under 'Respond' menu."),
	CM_MQA_AC("'Missed Question Analysis' submenu under 'Respond' menu should be selected"),
	CM_MQA_AR("'Missed Question Analysis' submenu under 'Respond' menu is getting selected"),
	CM_MQA_SS("'Missed Question Analysis' checkbox"),

	CM_Assignment_DC("Check 'Assignment' option under 'Respond' menu."),
	CM_Assignment_AC("'Assignment' submenu under 'Respond' menu should be selected"),
	CM_Assignment_AR("'Assignment' submenu under 'Respond' menu is getting selected"),
	CM_Assignment_SS("'Assignment' checkbox"),

	CM_ReportsDropDown_DC("Click the toggle sign against the 'Reports' under the 'Course Manager' section."),
	CM_ReportsDropDown_SS("'Reports' toggle sign"),

	CM_queriesAndClar_DC("Check 'Queries and Clarifications Log' option under 'Reports' menu."),
	CM_queriesAndClar_AC("'Queries and Clarifications Log' submenu under 'Reports' menu should be selected"),
	CM_queriesAndClar_AR("'Queries and Clarifications Log' submenu under 'Reports' menu is getting selected"),
	CM_queriesAndClar_SS("'Queries and Clarifications Log' checkbox"),

	CM_AssignmentsLog_DC("Check 'Assignments Log' option under 'Reports' menu."),
	CM_AssignmentsLog_AC("'Assignments Log' submenu under 'Reports' menu should be selected"),
	CM_AssignmentsLog_AR("'Assignments Log' submenu under 'Reports' menu is getting selected"),
	CM_AssignmentsLog_SS("'Assignments Log' checkbox"),

	SYS_usergroupsDropDown_DC("Click the toggle sign against the 'User Groups' under the 'System Manager' section."),
	SYS_usergroupsDropDown_SS("'User Groups' toggle sign"),

	SYS_AcceptDropDown_DC("Click the toggle sign against the 'Accept' under the 'User Groups' section."),
	SYS_AcceptDropDown_SS("'Accept' toggle sign"),

	SYS_JR_DC("Check 'Job Responsibility' option under 'Accept' menu."),
	SYS_JR_AC("'Job Responsibility' submenu under 'Accept' menu should be selected"),
	SYS_JR_AR("'Job Responsibility' submenu under 'Accept' menu is getting selected"),
	SYS_JR_SS("'Job Responsibility' checkbox"),

	SYS_PrintDropDown_DC("Click the toggle sign against the 'Print' under the 'User Groups' section."),
	SYS_PrintDropDown_SS("'Print' toggle sign"),

	SYS_JRHistory_DC("Check 'Job Responsibility History' option under 'Print' menu."),
	SYS_JRHistory_AC("'Job Responsibility' submenu under 'Print' menu should be selected"),
	SYS_JRHistory_AR("'Job Responsibility' submenu under 'Print' menu is getting selected"),
	SYS_JRHistory_SS("'Job Responsibility History' checkbox"),

	SubmitwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Global Profile: Registration Initiation'."),
	SubmitwithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Global Profile: Registration Initiation'."),

	Esign_ProceedGlobileProfile_AC(
			"'Global Profile  Registration Initiated Role: (Role)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedGlobileProfile_AR(
			"'Global Profile  Registration Initiated Role: (Role)' confirmation message is getting displayed with 'Done' button.</div>"),

	GlobalProfile_Menu("Click on 'Global Profile' submenu."),
	GlobalProfile_AuditTrails_Menu_AC(
			"'User Product / Module Assignment Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
					+ " The screen should be displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
					+ "<div><b>*</b>"
					+ " Screen should be displayed with 'Role', 'Category', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
					+ "<div><b>*</b>"
					+ " The screen should contain the list of all registered 'Global Profile' Roles</div>"),
	GlobalProfile_AuditTrails_Menu_AR(
			"'User Product / Module Assignment Audit Trails' screen is getting displayed.</div>" + "<div><b>*</b>"
					+ " The screen is getting displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
					+ "<div><b>*</b>"
					+ " Screen is getting displayed with 'User Name', 'Employee ID', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
					+ "<div><b>*</b>" + " The screen contains the list of all registered 'Global Profile' Roles</div>"),
	GlobalProfile_AuditTrails_Menu_SS("'Global Profile Audit Trails'"),

	Like_Role_DC("Enter the above registered 'Role' in search field."), Like_Role_SS("'Role' search field"),

	AT_SearchBy_AC(
			"Option to search with 'Top 250 Records, Role, Category and Initiated Between' should be available."),
	AT_SearchBy_AR(
			"Option to search with 'Top 250 Records, Role, Category and Initiated Between' are available.</div>"),

	SearchBy_Role_DC("Select 'Role'."), SearchBy_Role_SS("Select 'Role'."),

	Click_GP_for_AuditTrails_DC("Click on the above registered 'Role'."),
	Click_GP_for_AuditTrails_AC(
			"'Global Profile-Audit Trails: Revision No.: 0 - Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during registration of 'Global Profile'.<div>"
					+ "<div><b>*</b> The 'Final Status' should be displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b> The 'Events' section should display only the 'Initiated' transaction with 'Username, Date& Time and Remarks/Reasons' details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' and the 'No. of  Approvals Completed both should be read as ‘0’.<div/>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_GP_for_AuditTrails_AR(
			"'Global Profile-Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during registration of 'Global Profile'.<div>"
					+ "<div><b>*</b> The 'Final Status' is getting displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b> The 'Events' section is displaying only the 'Initiated' transaction with 'Username, Date& Time and Remarks/Reasons' details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' and the 'No. of  Approvals Completed both reads as ‘0’.<div/>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_GP_for_AuditTrails_SS("'Global Profile-Audit Trails."),

	Close_AuditTrails_GP_AC("'Global Profile Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_GP_AR("'Global Profile Audit Trails' screen is getting displayed.</div>"),

	;

	private final String SetGlobleProfileStrings;

	SetGlobleProfileStrings(String SetGlobleProfileStrings) {

		this.SetGlobleProfileStrings = SetGlobleProfileStrings;

	}

	public String getSetGlobleProfileStrings() {
		return SetGlobleProfileStrings;
	}

}
