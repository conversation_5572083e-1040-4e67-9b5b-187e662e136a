package com.Automation.learniqCRITICALScenarios.PreRequisites310;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;

public class Pre_Requsite_ResetPassword_EPIQ extends OQActionEngine {

	public Pre_Requsite_ResetPassword_EPIQ() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	@Test(priority = 0, enabled = true)
	public void ResetPasswordEPIQ() {

		test = extent.createTest("Pre Requisite: 6.0 Reset Login Passoword")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 6.0 Reset Login Passoword");

		epiclogin.loginToEPIQforResetPassword(ConfigsReader.getPropValue("company"));

		UserReg.userReg_resetPasswordEpiq(ConfigsReader.getPropValue("SSONewUserRegPassword"));

	}

}
