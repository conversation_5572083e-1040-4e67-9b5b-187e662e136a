package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.GroupStrings;
import com.Automation.Strings.SubGroupStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.QuestionBankStrings;
import com.Automation.Strings.RecordAttendanceStrings;
import com.Automation.Strings.SetGlobleProfileStrings;
import com.Automation.Strings.SubGroupStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Strings.TrainingScheduleStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;

public class SYS_SubGroup_StatusChange extends OQActionEngine {

	Properties prop;
	public static String SubGroupval = "";
	public static String Descriptionval = "";
	public static String JobResponsibilityval = "";

	public static String ModSubGroupval = "";
	public static String ModDescriptionval = "";
	public static String ModJobResponsibilityval = "";

	public static String Initiator = "";
	public static String Approver = "";
	public static String SubgroupUC = "";

	public static String getSubGroupval() {
		return SubGroupval;
	}

	public static void setSubGroupval(String subGroupval) {
		SubGroupval = subGroupval;
	}

	public static String getModSubGroupval() {
		return ModSubGroupval;
	}

	public static void setModSubGroupval(String modsubGroupval) {
		ModSubGroupval = modsubGroupval;
	}

	public static String getSubgroupUC() {
		return SubgroupUC;
	}

	public static void setSubgroupUC(String subGroupUC) {
		SubgroupUC = subGroupUC;
	}

	public static String getDescriptionval() {
		return Descriptionval;
	}

	public static void setDescriptionval(String descriptionval) {
		Descriptionval = descriptionval;
	}

	public static String getModDescriptionval() {
		return ModDescriptionval;
	}

	public static void setModDescriptionval(String moddescriptionval) {
		ModDescriptionval = moddescriptionval;
	}

	public static String getJobResponsibilityval() {
		return JobResponsibilityval;
	}

	public static void setJobResponsibilityval(String jobResponsibilityval) {
		JobResponsibilityval = jobResponsibilityval;
	}

	public static String getModJobResponsibilityval() {
		return ModJobResponsibilityval;
	}

	public static void setModJobResponsibilityval(String modjobResponsibilityval) {
		ModJobResponsibilityval = modjobResponsibilityval;
	}

	public static String getInitiatorName() {
		return Initiator;
	}

	public static void setInitiatorName(String InitiatorName) {
		Initiator = InitiatorName;
	}

	public static String getApproverName() {
		return Approver;
	}

	public String getinitiator() {
		return initiator;
	}

	public void setinitiator(String InitiatiorName) {
		initiator = InitiatiorName;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Re-Initiation')]")
	WebElement reinitiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approveMenu;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN07_SUBMEN16']")
	WebElement approveSubgroupMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;
	@FindBy(xpath = "(//ul//a[text()='Configuration Audit Trails'])[3]")
	WebElement ConfigurationauditTrailsMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Configure')]")
	WebElement configurationMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN16")
	WebElement subGroupConfig;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN16")
	WebElement subgroupCnfgMenu;
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationText2;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN16")
	WebElement subgroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN109_SUBMEN16")
	WebElement reniatesubgroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN16")
	WebElement subgroupAuditTrails;
//	@FindBy(xpath = "//ul[@class='inner-sub'][preceding-sibling::a[text()='Audit Trails']]//li//a[text()='Subgroup']")
//	WebElement subgroupAuditTrails;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Subgroup Name')]")
	WebElement searchBySubgroupNameDropdown;
	@FindBy(id = "Description")
	WebElement subgroupNameLike;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//input[@id='Subgroup_SubgrpDesc']")
	WebElement subGroupNameTxt;
	@FindBy(xpath = "//input[@id='Subgroup_Description']")
	WebElement descriptionText;
	@FindBy(xpath = "//textarea[@id='Subgroup_JDSC']")
	WebElement jobResponsibilityTxt;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDropdown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[1]")
	WebElement searchSel3;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(id = "Config_Remarks")
	WebElement r1emarks;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Subgroup Name']//following-sibling::span")
	WebElement auditSubgroupName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Unique Code')]//following-sibling::span")
	WebElement auditUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Description')]//following-sibling::span")
	WebElement auditDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span")
	WebElement auditJobResponsibility;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'1 -')]")
	WebElement revisionNotransValueCompareTRN;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']")
	WebElement auditCompareReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditCompareReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditCompareReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditCompareReturnTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']")
	WebElement auditCompareDropTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditCompareDropTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditCompareDropTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditCompareDropTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']")
	WebElement auditCompareReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditCompareReinitateTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditCompareReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditCompareReinitateTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditCompareApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareApproveTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;

	@FindBy(xpath = "//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus1;

	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNReurnedFinalStatus;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ModApprovalValue;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtModify-results']//child::li")
	WebElement clickModApprovalCount;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditCompareTRNUniqueCode;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement initiatedBy;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement initiatedOn;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement revNo;
	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;

	@FindBy(xpath = "//input[@id='SelectedDecision_3']")
	WebElement returnRadioBtn;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approvetext;

	@FindBy(xpath = "//label[text()='Return']")
	WebElement returntext;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement verifyApprovedRemarks;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Subgroup Name')]")
	WebElement searchBySubgroupNameDropdown1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditCompareTRNLevel1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditCompareTRNRole1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement iniatiatedby;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement iniatiatedon;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement revno;
	@FindBy(xpath = "//span[@id='MainTitle']")
	WebElement subroupsreen;
	@FindBy(xpath = "//span[@id='SubTitle']")
	WebElement subroupsreen1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title pb-0 pt-2']")
	WebElement subroupauditsreen;
	@FindBy(xpath = "//span[text()='Subgroup']")
	WebElement subroupauditsreen1;
	@FindBy(xpath = "//span[text()='Audit Trails']")
	WebElement subroupauditsreen2;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']//li//a[text()='Subgroup']")
	WebElement configurationSubGroupAuditTrails;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callEsignAtInitiaiton;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callEsignAtApproval;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtRegApprCheckBox;
	@FindBy(id = "txtESignPassword")
	WebElement esignPWDTextBox;

	@FindBy(id = "Submit_Esign")
	WebElement proceedBtn;

	@FindBy(xpath = "//textarea[@id='Subgroup_Remarks']")
	WebElement reiniateRemarksReasonTextbox;

	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement returnRemarksReasonTextbox;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::span")
	WebElement remarksandreasons;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement remarksandreasonsapprove;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement remarksandreasonsapprovemain;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Modify']")
	WebElement modifymenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN16")
	WebElement modifysubgroupmenu;

	@FindBy(xpath = "//td[text()='No data available in table']")
	WebElement nodisplaydata;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='RI Transfer']")
	WebElement ritransfermenu;

	@FindBy(xpath = "//span[text()='Add Item']")
	WebElement additem;

	@FindBy(xpath = "//input[@type='search']")
	WebElement searchby;

	@FindBy(xpath = "//button[@id='Transfer_selectBtn']")
	WebElement radiobutton;

	@FindBy(xpath = "//div[text()='Re-initiation Task Transfer (Reg.)']")
	WebElement reiniatetasktransfer;

	@FindBy(xpath = "//div[text()='Re-initiation Task Transfer (Mod.)']")
	WebElement reiniatemodtasktransfer;

	@FindBy(xpath = "//div[text()='Registration']")
	WebElement reiniatetasktransferregistration;

	@FindBy(xpath = "//button[@id='AuditEventModal_View']")
	WebElement ritransferproceed;

	@FindBy(xpath = "//h6[text()='Final Status : Transferred']")
	WebElement ritransferfinalstatus;
	@FindBy(xpath = "//p[@id='approve-name-0']")
	WebElement ritransferusername;
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[1]")
	WebElement ritransferinitiateddate;
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[2]")
	WebElement ritransferremarks;

	@FindBy(xpath = "//h6[@id='status_heading']")
	WebElement ritransferusernamestatus;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal2;

	@FindBy(xpath = "//p[@id='status-message-1-0']")
	WebElement auditCompareTRNAintActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[1]")
	WebElement auditCompareTRNintActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[2]")
	WebElement auditCompareTRNintDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[3]")
	WebElement auditCompareintRemarksVal2;

	@FindBy(xpath = "//input[@type='radio']")
	WebElement arButton;

	@FindBy(id = "Transfer_selectBtn")
	WebElement addButton;

	@FindBy(id = "TransferUserPopUpBtn")
	WebElement transwerAddItems;

	@FindBy(id = "TMS_System Manager_User Groups_MODMEN21_SUBMEN16")
	WebElement ritransfersubgroup;

	@FindBy(xpath = "//span[@id='approve-status-span-old']")
	WebElement transfrom;

	@FindBy(xpath = "//span[@id='approve-status-span-new']")
	WebElement transTo;

	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;

	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approveLabelText;

	@FindBy(xpath = "//input[@id='SelectedDecision_4']")
	WebElement dropRadioBtn;

	@FindBy(id = "TMS_System Manager_User Groups_MEN129_SUBMEN16")
	WebElement statuschangesubgroup;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Status Change')]")
	WebElement statuschange;

	@FindBy(xpath = "//button[text()='Inactive']")
	WebElement statuschangeinactve;

	@FindBy(xpath = "//div[text()='Registration']")
	WebElement regmod;

	@FindBy(xpath = "//div[text()='Modification']")
	WebElement modmod;

	@FindBy(xpath = "//div[@data-target='#MainTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleMainTRN;
	@FindBy(xpath = "//div[@data-target='#MainTRN']//span[contains(text(),'1 -')]")
	WebElement revisionNoValueMainTRN;

	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditMainTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditMainTRNApprovalComVal;

	@FindBy(xpath = "(//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span)[2]")
	WebElement auditMainritransTRNApprovalReqVal;
	@FindBy(xpath = "(//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span)[2]")
	WebElement auditMainritransTRNApprovalComVal;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']")
	WebElement auditMainApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditMainApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditMainApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditMainApproveTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditMainTRNFinalStatus;

	@FindBy(xpath = "(//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading'])[2]")
	WebElement auditMaintransTRNFinalStatus;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement modMainremarksandreasonsapprove;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Subgroup Name']//following-sibling::span")
	WebElement auditmodSubgroupName;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Unique Code')]//following-sibling::span")
	WebElement auditmodUniqueCode;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Description')]//following-sibling::span")
	WebElement auditmodDescription;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span")
	WebElement auditmodJobResponsibility;

	@FindBy(xpath = "(//div[@id='MainTRN']//label[text()='Subgroup Name']//following-sibling::span)[2]")
	WebElement auditmodriSubgroupName;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Unique Code')]//following-sibling::span)[2]")
	WebElement auditmodriUniqueCode;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Description')]//following-sibling::span)[2]")
	WebElement auditmodriDescription;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span)[2]")
	WebElement auditmodriJobResponsibility;

	@FindBy(xpath = "//button[text()='Modification']")
	WebElement modificationtab;
	@FindBy(xpath = "//tr[@class='click-row odd']")
	WebElement statusactiveRecord;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']")
	WebElement auditMainReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditMainReinitateTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditMainReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditMainReinitateTRNRemarksVal1;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditMainTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditMainTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditMainTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal2;

	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]")
	WebElement auditMainTRNAintActionValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[1]")
	WebElement auditMainTRNintActionByValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[2]")
	WebElement auditMainTRNintDateTimeValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[3]")
	WebElement auditMainintRemarksVal2;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']")
	WebElement auditMainReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditMainReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditMainReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditMainReturnTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']")
	WebElement auditMainTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditMainTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditMainTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal1;

	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3])[2]")
	WebElement auditMainritransTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']")
	WebElement auditMainDropTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditMainDropTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditMainDropTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditMainDropTRNRemarksVal1;

	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated'])[2]")
	WebElement auditMainriTRNActionValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1])[2]")
	WebElement auditMainriTRNActionByValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2])[2]")
	WebElement auditMainriTRNDateTimeValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3])[2]")
	WebElement auditMainriTRNRemarksVal1;

	@FindBy(xpath = "//button[text()='Active']")
	WebElement statuschangeactve;

	@FindBy(xpath = "//button[text()='Status Change']")
	WebElement statusChangeTabMenu;

	@FindBy(xpath = "//span[contains(text(),'Active')]")
	WebElement activeLabel;

	@FindBy(xpath = "//span[contains(text(),'Inactive')]")
	WebElement inActiveLabel;

	@FindBy(id = "Remarks")
	WebElement ReInitiateremarks;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForStatusDropdown;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtStatusChange-results']//child::li")
	WebElement clickStatusApprovalCount;

	@FindBy(xpath = "//div[text()='Status Change']")
	WebElement statusmod;

	@FindBy(xpath = "(//div[text()='Status Change'])[2]")
	WebElement statuschangemod;

	/**
	 * This method is for SubGroup Configuration
	 *
	 */

	public void SubgroupRegistrationApproval_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configurationMenu,
				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configurationMenu, subgroupCnfgMenu,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(subgroupCnfgMenu, SubGroupStrings.SubgroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "Call E-sign At: Approval Registration");
		click2(noOfAprReqForRegDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void SubgroupStatusChangeApproval_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configurationMenu,
				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configurationMenu, subgroupCnfgMenu,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(subgroupCnfgMenu, SubGroupStrings.SubgroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtSTCAprCheckBox, "Call E-sign At: Approval Registration");
// 
		click2(noOfAprReqForStatusDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ModApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("StatusChangeapprovalcount"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickStatusApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		JavascriptExecutor js3 = (JavascriptExecutor) driver;
		js3.executeScript("arguments[0].scrollIntoView();", remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		highLightElement(driver, confirmationText, "ConfirmationText", test);
		click2(confirmationDone, CommonStrings.Click_DoneatConfig_DC.getCommonStrings(),
				SubGroupStrings.Click_DoneatSubGroupConfig_AC.getSubGroupStrings(),
				SubGroupStrings.Click_DoneatSubGroupConfig_AR.getSubGroupStrings(),
				CommonStrings.Click_DoneatConfig_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void SubgroupModificationApproval_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configurationMenu,
				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configurationMenu, subgroupCnfgMenu,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(subgroupCnfgMenu, SubGroupStrings.SubgroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);
		SelectRadioBtnAndCheckbox(driver, eSignAtModInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtModAprCheckBox, "Call E-sign At: Approval Registration");
		click2(noOfAprReqForModDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ModApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("ModificationApprovalCount"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickModApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void subgroup_Registration(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(3);
			System.out.println("Generated S Value is: " + s);
		}

		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		setSubGroupval(SubGroupval = testData.get("SubGroupNameValue") + s);
		setDescriptionval(Descriptionval = testData.get("DescriptionValue") + s);
		setJobResponsibilityval(JobResponsibilityval = testData.get("JobResponsibilityValue") + s);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(initiateMenu, CommonStrings.SYS_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_SS.getCommonStrings());
		click2(subgroup, SubGroupStrings.SubGroup_Initiate_DC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AR.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		sendKeys2(subGroupNameTxt, SubGroupStrings.Subgroup_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Subgroup_SS.getSubGroupStrings());

		sendKeys2(descriptionText, SubGroupStrings.description_DC.getSubGroupStrings(), Descriptionval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.description_SS.getSubGroupStrings());

		sendKeys2(jobResponsibilityTxt, SubGroupStrings.JobRes_DC.getSubGroupStrings(), JobResponsibilityval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.JobRes_SS.getSubGroupStrings());
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
//		getInitiatorFirstNameLastName(initiatorName);
//		setinitiator(initiator = initiatorName.getAttribute("value"));
		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_REGISTRATION_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void subgroup_Registration_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(auditTrailPageColumn1, SubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		// verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks
		// and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_0,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
//
	}

	// Subgroup Return
	public void Subgroup_StatusChange_activetoInactive(HashMap<String, String> testData) {

//				String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(statuschange, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(statuschangesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statuschangeactve, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");

		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		TimeUtil.shortWait();
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasonsMod"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.STATUSCHANGE_SUBGROUP_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.STATUSCHANGE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_StatusChange_activetoInactive_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statusmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");

		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
//
	}

	// Subgroup Approval
	public void Subgroup_statuschange_drop(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(auditTrailPageColumn1, SubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		// verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks
		// and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		scrollToViewElement(approvetext);

		click2(dropRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("DropRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_STATUSCHANGE_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.GROUPDROPREMAKS;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.DROPSTATUSCHANGE_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_statuschange_drop_auditrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(auditTrailPageColumn1, SubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statusmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		scrollToViewElement(auditCompareTRNRemarksVal1);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		scrollToViewElement(auditCompareDropTRNActionValue);
		verifyExactCaption(auditCompareDropTRNActionValue, Constants.DROP_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareDropTRNActionByValue);
		verifyExactCaption(auditCompareDropTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareDropTRNDateTimeValue);
		scrollToViewElement(auditCompareDropTRNRemarksVal1);
		verifyExactCaption(auditCompareDropTRNRemarksVal1, Constants.DROP_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		scrollToViewElement(auditCompareTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_DROPPED);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Return
	public void Subgroup_Modification_StatusChange_inactivetoactive(HashMap<String, String> testData) {

//				String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(statuschange, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(statuschangesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statuschangeinactve, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");

		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		TimeUtil.shortWait();
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasonsMod"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.STATUSCHANGE_SUBGROUP_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.STATUSCHANGE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void subgroup_StatusChangeiancattoactAuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statusmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
//
	}

	public void subgroup_Modification_afterapproval(HashMap<String, String> testData) {

		// String SubGroupval="query";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(modifymenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(modifysubgroupmenu, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);

		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, statusactiveRecord, "Subgroup Registration Intiation", test);

		switchToDefaultContent(driver);

	}

	public void subgroup_Modification(HashMap<String, String> testData) {

		// String SubGroupval="query";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(modifymenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(modifysubgroupmenu, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);

		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		verifyExactCaption(nodisplaydata, Constants.nodataavilable, "no data avilable");
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Approval
	public void Subgroup_statuschange_Approve(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_STATUSCHANGE_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.GROUPAPPROVEREMAKS;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.STATUSCHANGE_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_statuschange_Approve_auditrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statusmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		scrollToViewElement(auditCompareTRNRemarksVal1);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		scrollToViewElement(auditCompareApproveTRNActionValue);
		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareApproveTRNActionByValue);
		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		scrollToViewElement(auditCompareApproveTRNRemarksVal1);
		verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.APPROVE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		scrollToViewElement(auditCompareTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		scrollToViewElement(activeLabel);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;

		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Return
	public void Subgroup_StatusChange_InactivetoActive(HashMap<String, String> testData) {

//					String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(statuschange, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(statuschangesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statuschangeinactve, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		TimeUtil.shortWait();
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasonsMod"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.STATUSCHANGE_SUBGROUP_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.STATUSCHANGE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_StatusChange_InactivetoActive_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statuschangemod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");

		scrollToViewElement(inActiveLabel);
		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");
		scrollToViewElement(auditCompareTRNApprovalReqVal);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
		//
	}

	// Subgroup Approval
	public void Subgroup_statuschange_afterapprove_drop(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		// subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");
		scrollToViewElement(approvetext);

		click2(dropRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("DropRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_STATUSCHANGE_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.GROUPDROPREMAKS;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.DROPSTATUSCHANGE_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_statuschange_afterapprove_drop_auditrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());

		sendKeys2(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.mediumWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(statuschangemod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();

		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		scrollToViewElement(auditCompareTRNRemarksVal1);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.INITIATESTATUSCHANGE, "Remarks");

		scrollToViewElement(auditCompareDropTRNActionValue);
		verifyExactCaption(auditCompareDropTRNActionValue, Constants.DROP_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareDropTRNActionByValue);
		verifyExactCaption(auditCompareDropTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareDropTRNDateTimeValue);
		scrollToViewElement(auditCompareDropTRNRemarksVal1);
		verifyExactCaption(auditCompareDropTRNRemarksVal1, Constants.DROP_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		scrollToViewElement(auditCompareTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_DROPPED);
		scrollToViewElement(activeLabel);
		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

}