package com.Automation.learniqCRITICALScenarios.InterimCountMismatchCases;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Offline RE type Session without assessment for scheduled course and
 * make atleast one employee should be pending at record document reading and
 * one should be qualified by viewing Individual employee report at each
 * transaction starting from course session.
 */

public class INTERIM_RE_OFF_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/INTERIM_RE_OFF_WITHOUT_ASSESSMENT.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/INTERIM_RE_OFFLINE.xlsx";

	public INTERIM_RE_OFF_WITHOUT_ASSESSMENT() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");
	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}
		Initiate_Course.Refresher_Course_Registration(testData);
		Logout.signOutPage();
	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");
		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();
		CSRReport.TBPCSRReport();
		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		verifyCSScreen.courseSession_Offline_DocumentReading(testData);
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");
		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();
		CSRReport.TBPCSRReport();
		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
//		Logout.signOutPage();
	}

	// Test Method for Record Document Reading

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void RecordDocumentReading(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Record Document Reading").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Document Reading");
		}
		RecordDR.NewRecordDocumentReading();
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Screen_After_RecordDocumentReading(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}
		verifyCSScreen.checkCourseSessionAfterRespondDocumentReading();
	}

	@Test(priority = 6, dataProvider = "CourseSession", enabled = true)
	public void checkCSR_After_Keeping_Employees_In_Different_States(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Report after Different states")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Course Session Report after Different states");
		}
		CSRReport.TBPCSR_InterimCourse_Report_AfterRespond_Record_DocumentReading_Without_Assessment();
	}


}
