package com.Automation.Strings;

public enum CourseStrings {

	// Course Configuration

	CourseSubMenu_DC("Click 'Course' submenu."), CourseSubMenu_SS("'Course' submenu"),

	CourseConfig_DC("Click on 'Course' submenu."),
	CourseConfig_AC("'Course Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and No of Approvals Required should be available.</div>"),
	CourseConfig_AR("'Course Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and No of Approvals Required are available.</div>"),
	CourseConfig_SS("'Course' submenu"),

	Click_DoneatCourseConfig_AC(
			"'Course Configuration" + "</br>" + "Registration' screen" + "</br>" + "should be displayed."),
	Click_DoneatCourseConfig_AR("'Course Configuration Registration' screen is getting displayed.</div>"),

	// Course Registration

	CourseRegistrationScreen_DC("Click 'Course' menu."),
	CourseRegistrationScreen_AC("'Course Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Course Name, Unique Code, Description, Course Type, Training Type, Self-Study' fields.</div>"
			+ "<div><b>*</b> 'Unique Code' should be system generated.</div>"
			+ "<div><b>*</b> By default, 'Course Type' should be selected as 'One Time'.</div>"
			+ "<div><b>*</b> The option to search with 'Category Name', 'Subject Name' & Topic Name' should be available with 'Fetch Records' hyperlink under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen should contain 'Recent 10 Topics, Reset' and 'Show All Topics of Listed Subjects' options with hyperlinks under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen should contain 'Category', 'Subject', 'Available Topics & 'Selected Topics' list columns under 'Select Topics' section.</div>"
			+ "<div><b>*</b> All the registered Category(s) at the time of Topic registration in both Plant1 & Master plant(if any) should be listed in 'Category' list column under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen should display a path with details such as 'Course Particulars, Link Documents, Propose GTP and Preview'.</div>"
			+ "<div><b>*</b> The screen should contain 'View Courses' and 'Next' buttons.</div>"
			+ "<div><b>*</b> Under 'View Courses', registered & active records of 'Courses' should be displayed if any.</div>"),
	CourseRegistrationScreen_AR("'Course Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen contains 'Course Name, Unique Code, Description, Course Type, Training Type, Self-Study' fields.</div>"
			+ "<div><b>*</b> 'Unique Code' is system generated.</div>"
			+ "<div><b>*</b> By default, 'Course Type' is selected as 'One Time'.</div>"
			+ "<div><b>*</b> The option to search with 'Category Name', 'Subject Name' & Topic Name' are available with 'Fetch Records' hyperlink under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen contains 'Recent 10 Topics, Reset' and 'Show All Topics of Listed Subjects' options with hyperlinks under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen contains 'Category', 'Subject', 'Available Topics & 'Selected Topics' list columns under 'Select Topics' section.</div>"
			+ "<div><b>*</b> All the registered Category(s) at the time of Topic registration in both Plant1 & Master plant(if any) are listed in 'Category' list column under 'Select Topics' section.</div>"
			+ "<div><b>*</b> The screen displays a path with details such as 'Course Particulars, Link Documents, Propose GTP and Preview'.</div>"
			+ "<div><b>*</b> The screen contains 'View Courses' and 'Next' buttons.</div>"
			+ "<div><b>*</b> Under 'View Courses', registered & active records of 'Courses' are displayed if any.</div>"),
	CourseRegistrationScreen_SS("'Course Registration Initiation'"),

	CourseName_DC("Enter the value less than or equals to 250 characters in 'Course Name' field."),
	CourseName_AC("Entered value should be displayed.</div>"), CourseName_AR("'Course Name'.</div>"),
	CourseName_SS("'Course Name'"),

	CourseDescription_DC("Enter the value less than or equals to 250 characters in 'Description' field."),
	CourseDescription_AC("<div><b>*</b> Entered value should be displayed.</div>"),
	CourseDescription_AR("'Description'.</div>"), CourseDescription_SS("'Description'"),

	TrainingTypeDropDown_DC("Click drop-down list present for 'Training Type' field."),
	TrainingTypeDropDown_AC(
			"Option to select the 'cGMP Training, Safety Training, Technical Training, Induction Training and On Job Training' should be available.</div>"),
	TrainingTypeDropDown_AR(
			"Option to select the 'cGMP Training, Safety Training, Technical Training, Induction Training and On Job Training' are available.</div>"),
	TrainingTypeDropDown_SS("'Training Type' field"),

	TrainingTypeLike_DC("Enter the 'Technical Training'."), OJTTrainingTypeLike_DC("Enter the 'On Job Training'."),

	TrainingTypeLike_AC("Option to select the 'Technical Training' should be displayed."),

	TrainingTypeLike_AR("'Training Type'.</div>"), TrainingTypeLike_SS("'Training Type'"),

	TechnicalTraining_Select_DC("Select the 'Technical Training' for 'Training Type' field."),
	OJTTechnicalTraining_Select_DC("Select the 'On Job Training' for 'Training Type' field."),
	TechnicalTraining_Select_AC("Selected value should be displayed for 'Training Type' field</div>"),
	TechnicalTraining_Select_AR("Selected value is getting displayed for 'Training Type' field</div>"),
	TechnicalTraining_Select_SS("'Technical Training'"), OJTTechnicalTraining_Select_SS("'On Job Training'"),

	SelfStudyNo_DC("Select  the 'Self-study' as 'No'"),
	SelfStudyNo_AC("Selected value should be accepted or 'Self-Study' field.</div>"),
	SelfStudyNo_AR("Selected value is getting accepted for 'Self-Study' field.</div>"), SelfStudyNo_SS("'Self-Study'"),

	SearchByTopicName_DC("Select 'Topic Name'."), SearchByTopicName_AC("Selected value should be accepted</div>"),
	SearchByTopicName_AR("Selected value is getting accepted."), SearchByTopicName_SS("'Topic Name'"),

	TopicNameLike_DC("Enter the above registered topic name."),
	TopicNameLike_AC("<div><b>*</b> Entered value should be displayed.</div>"), TopicNameLike_AR("'Topic Name'</div>"),
	TopicNameLike_SS("'TopicName'"),

	FetchRecords_DC("Click on 'Fetch Records' hyperlink."),
	FetchRecords_AC("Records should be displayed based on the search criteria in 'Available Topics' column.</div>"),
	FetchRecords_AR("Records are getting displayed based on the search criteria in 'Available Topics' column.</div>"),
	FetchRecords_SS("'Fetch Records'"),

	AvailableTopic_DC("Select the above registered topic in 'Available Topics' column."),
	AvailableTopic_AC(
			"Selected topic should be moved to 'Selected  Topics' column from 'Available Topics' list column.</div>"),
	AvailableTopic_AR(
			"Selected topic is getting moved to 'Selected  Topics' column from 'Available Topics' list column.</div>"),
	AvailableTopic_SS("'Selected Topics'"),

	NextButtonCR_DC("Click on 'Next' button."),
	NextButtonCR_AC(
			"Selected Topic(s) with 'Topic Name (Topic Unique Code)' should be displayed under 'Selected Topics'.</div>"
					+ "<div><b>*</b> 'Document Name, Next Review Date, Version No. and Select All' columns should be displayed for listed all the Topic(s).</div>"
					+ "<div><b>*</b> The option to select the listed documents should be available.</div>"
					+ "<div><b>*</b> The option to preview the document should be available.</div>"
					+ "<div><b>*</b> 'No Documents Linked to This Topic' message should be displayed to the 'Topics' for which no documents are linked (if any).</div>"
					+ "<div><b>*</b> The screen should contain 'Back' and 'Next' buttons.</div>"),
	NextButtonCR_AR(
			"Selected Topic(s) with 'Topic Name (Topic Unique Code)' are getting displayed under 'Selected Topics'.</div>"
					+ "<div><b>*</b> 'Document Name, Next Review Date, Version No. and Select All' columns are displayed for listed all the Topic(s).</div>"
					+ "<div><b>*</b> The option to select the listed documents is available.</div>"
					+ "<div><b>*</b> The option to preview the document is available.</div>"
					+ "<div><b>*</b> 'No Documents Linked to This Topic' message is getting displayed to the 'Topics' for which no documents are linked (if any).</div>"
					+ "<div><b>*</b> The screen contains 'Back' and 'Next' buttons.</div>"),
	NextButtonCR_SS("'Next' button"),

	SelectAllDoc_DC("Click 'Select All' check box against required topic."),
	SelectAllDoc_AC("All the documents should be selected against the respective topic."),
	SelectAllDoc_AR("All the documents are selected against the respective topic."),
	SelectAllDoc_SS("'Select All' check box"),

	NextButton1_AC(
			"The option to search with 'Group Name' & 'Subgroup Name' should be available with 'Fetch Records' hyperlink under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen should contain 'Recent 10 Subgroups' and 'Reset' options with hyperlink under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen should contain 'Groups', 'Available Subgroups' & 'Selected Subgroups' list columns under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> List of Group(s) which are registered and in active state should be displayed in 'Groups' list column under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen should contain 'Approval for Candidature' with options like 'By Approval Group, By Immediate Supervisor, Not Required'.</div>"
					+ "<div><b>*</b> The screen should contain 'Back' and 'Preview' buttons.</div>"),
	NextButton1_AR(
			"The option to search with 'Group Name' & 'Subgroup Name' are available with 'Fetch Records' hyperlink under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen contains 'Recent 10 Subgroups' and 'Reset' options with hyperlink under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen contains 'Groups', 'Available Subgroups' & 'Selected Subgroups' list columns under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> List of Group(s) which are registered and in active state are getting displayed in 'Groups' list column under 'Group Training Plan' section.</div>"
					+ "<div><b>*</b> The screen contains 'Approval for Candidature' with options like 'By Approval Group, By Immediate Supervisor, Not Required'.</div>"
					+ "<div><b>*</b> The screen contains 'Back' and 'Preview' buttons.</div>"),
	NextButton1_SS("'Next' button."),

	SearchBySubgroupName_DC("Select 'Subgroup Name' search option."),
	SearchBySubgroupName_AC("Selected value should be accepted.</div>"),
	SearchBySubgroupName_AR("Selected value is gettting accepted.</div>"),
	SearchBySubgroupName_SS("'Subgroup Name' search option"),

	SubgroupNameLike_DC("Enter the required 'Subgroup Name'."),
	SubgroupNameLike_AC("Entered value should be displayed.</div>"),
	SubgroupNameLike_AR("Entered value is getting displayed.</div>"), SubgroupNameLike_SS("'Subgroup Name'"),

	SubgrpName_FetchRcrds_DC("Click on 'FetchRecords' hyperlink"),
	SubgrpName_FetchRcrds_AC(
			"Records should be displayed based on the search criteria in 'Available Subgroups' column.</div>"),
	SubgrpName_FetchRcrds_AR(
			"Records are getting displayed based on the search criteria in 'Available Subgroups' column.</div>"),
	SubgrpName_FetchRcrds_SS("'Fetch Records'"),

	AvailableSubgrp_DC("Click on required subgroup from 'Available Subgroup' list column."),
	AvailableSubgrp_AC(
			"Selected subgroup should be moved from 'Available Subgroups' list column to 'Selected Subgroups' list column.</div>"),
	AvailableSubgrp_AR(
			"Selected subgroup is moved from 'Available Subgroups' list column to 'Selected Subgroups' list column.</div>"),
	AvailableSubgrp_SS("'Available Subgroups' list column."),

	ApprovalforCandidature_DC("Select Approval for Candidature as 'Not Required'"),
	ApprovalforCandidature_AC("Selected value should be accepted</div>"),
	ApprovalforCandidature_AR("Selected value is getting accepted.</div>"),
	ApprovalforCandidature_SS("''Approval for Candidature'"),

	Preview_DC("Click on 'Preview' button."),
	Preview_AC(
			"The screen should contain 'Course Name', 'Unique Code', 'Description', 'Course Type', 'Self-Study', 'Training Type', 'Selected Topics', 'Selected Subgroups List' and 'Approval for Candidature' details in read only format.</div>"
					+ "<div><b>*</b> The screen should contain 'View Existing', 'Back' and 'Submit' buttons.</div>"
					+ "<div><b>*</b> Under 'View Existing', registered & active state records of 'Course' should be displayed if any.</div>"),

	Preview_AR(
			"The screen contains 'Course Name', 'Unique Code', 'Description', 'Course Type', 'Self-Study', 'Training Type', 'Selected Topics', 'Selected Subgroups List' and 'Approval for Candidature' details in read only format.</div>"
					+ "<div><b>*</b> The screen contains 'View Existing', 'Back' and 'Submit' buttons.</div>"
					+ "<div><b>*</b> Under 'View Existing', registered & active state records of 'Course' are displayed if any.</div>"),
	Preview_SS("'Preview' button"),

	SubmitCoursewithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Course: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> should be displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' should be available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>*</b> 'Proceed' button should be displayed in disabled mode.</div>"),
	SubmitCoursewithEsign_AR(
			"'Meaning of This Electronic Signature' is getting displayed as 'Course: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' is available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>*</b> 'Proceed' button is getting displayed in disabled mode.</div>"),

	Esign_ProceedCourse_AC(
			"'Course  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedCourse_AR(
			"'Course  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	SearchByCourse_Dropdown_DC("Click on 'Search By' dropdown."),
	SearchByCourse_Dropdown_AC(
			"Option to search with 'Top 250 Records, Course Name, Unique' Code', Initiated Between should be displayed.</div>"),
	SearchByCourse_Dropdown_AR(
			"Option to search with 'Top 250 Records, Course Name, Unique' Code', Initiated Between is getting displayed.</div>"),
	SearchByCourse_Dropdown_SS("'Search By' dropdown"),

	Select_CoursName_DC("Select 'Course Name'."), Select_CoursName_AC("Selection should be accepted."),
	Select_CoursName_AR("Selection is getting accepted."), Select_CoursName_SS("'Course Name'."),

	// Course Audit trails

	CourseAudittrails_DC("Click on 'Course' menu."),
	CourseAudittrails_AC("'Course Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Course Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Top 250 Records', 'Course Name', 'Unique Code', 'Initiated Between' should be available.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.</div>"
			+ "<div><b>*</b> The screen should contain a list of all the registered Courses in current plant.</div>"
			+ "<div><b>*</b> Courses which are registered in Master plant/ any other plant should not be displayed (if any).</div>"),
	CourseAudittrails_AR("'Course Audit Trails' is getting be displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Course Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Top 250 Records', 'Course Name', 'Unique Code', 'Initiated Between' are available.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is displayed as '20'.</div>"
			+ "<div><b>*</b> The screen contains a list of all the registered Courses in current plant.</div>"
			+ "<div><b>*</b> Courses which are registered in Master plant/ any other plant are not be displayed (if any).</div>"),
	CourseAudittrails_SS("'Course Audit Trails' screen."),

	Like_CourseName_DC("Enter the above registered 'Course Name' in 'Like' field."),
	Like_CourseName_AC("<div><b>*</b> Entered value should be displayed.</div>"),
	Like_CourseName_AR("<div><b>*</b> Entered value is getting displayed.</div>"), Like_CourseName_SS("Course Name"),

	Close_AuditTrails_Course_AC("'Course Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_Course_AR("'Course Audit trails' screen  is getting displayed.</div>"),

	Click_Course_for_AuditTrails_DC("Click on the above registered 'Course' Name'."),
	Click_Course_for_AuditTrails_AC(
			"'Course - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
					+"<div><b>*</b> The screen should contain the details of Course details entered/selected during registration.</div>"
					+"<div><b>*</b> 'Final Status' should be displayed as 'Initiated'.</div>"
					+"<div><b>*</b> Also, the 'No. of Approvals Required' should be read as '1' and the 'No. of Approvals Completed' should be read as '0'.</div>"
					+"<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_Course_for_AuditTrails_AR(
			"'Course - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen contains the details of Course details entered/selected during registration.</div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Initiated'.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' reads as '1' and the 'No. of Approvals Completed' reads as '0'.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_Course_for_AuditTrails_SS("'Course' -  Audit Trails screen."),

	RetakeAssessmentNo_DC("Click 'Retake Assessment' Radio Button"),
	RetakeAssessmentNo_AC("Selected value should be accepted"),
	RetakeAssessmentNo_AR("'No', Selected value is accepted"),
	RetakeAssessmentNo_SS("'Retake Assessment' Radio Button"),

	RefresherCourseType_DC("Click 'Refresher Training Required' Radio Button"),
	RefresherCourseType_AC("Selected value should be accepted"),
	RefresherCourseType_AR("'Refresher Training Required' Selected value is accepted"),
	RefresherCourseType_SS("'Refresher Training Required' Course Type"),

	Course_ConfigAudit_AC("'Course Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Course_ConfigAudit_AR("'Course Configuration Audit Trails' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),

	Click_Course_for_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_Course_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Course_for_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Course_for_ConfigAuditTrails_SS("'Transactions'"),

	Click_Config_Proceed_AC(
			"'Course Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Course Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Course Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Course Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Course Configuration - Audit Trails'."),

	Close_ConfigAuditTrails_Course_AC("'Course Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_Course_AR("'Course Configuration Audit Trails' screen is getting displayed.</div>"),

	RefresherTrainingTypeDropDown_DC("Click drop-down list present for 'Training Type' field."),
	RefresherTrainingTypeDropDown_AC(
			"Option to select the 'cGMP Training, Safety Training, Technical Training' should be available.</div>"),
	RefresherTrainingTypeDropDown_AR(
			"Option to select the 'cGMP Training, Safety Training, Technical Training' are available.</div>"),
	RefresherTrainingTypeDropDown_SS("'Training Type' field"),

	CourseApproveMenu_AC("'Course Approval Tasks' screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details should be displayed.<div>"
			+ "<div><b>*</b> All the Courses whose registration request is to be approved by the current user should be listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen should contain 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen should contain 'Course Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.<div>"),
	CourseApproveMenu_AR("'Course Approval Tasks' screen is getting displayed."
			+ "<div><b>*</b> Screen contains 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details are getting displayed.<div>"
			+ "<div><b>*</b> All the Courses whose registration request is to be approved by the current user is listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen contains 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen contains 'Course Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is getting displayed as '20'.<div>"),

	SearchBy_Course_AC(
			"Option to search with 'Top 250 Records, Course Name, Unique Code ,Initiated Between' should be available.</div>"),
	SearchBy_Course_AR(
			"Option to search with 'Top 250 Records, Course Name, Unique Code ,Initiated Between' are available.</div>"),

	Select_CourseName_DC("Select 'Course Name'."), Select_CourseName_SS("'Course Name'."),

	Click_Course_for_Approve_DC("Click on the above registered 'Group Name'."),
	Click_Course_for_Approve_AC(" 'Course Registration Approval' screen should be displayed</div>" + "<div><b>*</b>"
			+ " The screen should display the details of the Course entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' should be displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section should display the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' should be reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options should be available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' should be available.</div>"),
	Click_Course_for_Approve_AR(" 'Course Registration Approval' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with details of the Course entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' is getting displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section is displaying the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options are available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' are available.</div>"),
	Click_Course_for_Approve_SS("'Course' menu"),

	// approve radio button
	CourseApproval_DC("Select 'Decision' as 'Approve'."),
	CourseApproval_AC("Selected option should be accepted for 'Decision' field.</div>"),
	CourseApproval_AR("Selected option is getting accepted for 'Decision' field.</div>"),
	CourseApproval_SS("'Approve'"),

	Submit_Course_Approval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Course: Registration Approval: Approve'.</div>"),
	Submit_Course_Approval_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Course: Registration Approval: Approve'.</div>"),
	Submit_Course_Approval_SS("'E-Sign window'"),

	Esign_ProceedCourse_Approval_AC(
			"'Course Registration Approved Unique Code:(Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedCourse_Approval_AR(
			"'Course Registration Approved Unique Code:(Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Select_CourseUniqueCode_DC("Select 'Unique Code'."), Select_CourseUniqueCode_SS("'Unique Code'."),

	Like_CourseUniqueCode_DC("Enter the Unique Code of the above registered Course in 'Like' field."),

	Click_Course_for_Approve_AuditTrails_AC(
			"'Course - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should display the details of the Course entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section should be updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be as '1' and the 'No. of Approvals Completed' should be read as '1'.<div>"
					+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),
	Click_Course_for_Approve_AuditTrails_AR(
			"'Course - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is displaying the details of the Course entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section is updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' is '1' and the 'No. of Approvals Completed' reads as '1'.<div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),
	MasterCoursePreview_AC(
			"The screen should contain 'Course Name', 'Unique Code', 'Description', 'Course Type', 'Self-Study', 'Training Type' and 'Selected Topics' details in read only format.</div>"
					+ "<div><b>*</b> The screen should contain 'View Existing', 'Back' and 'Submit' buttons.</div>"
					+ "<div><b>*</b> Under 'View Existing', registered & active state records of 'Course' should be displayed if any.</div>"),

	MasterCoursePreview_AR(
			"The screen contains 'Course Name', 'Unique Code', 'Description', 'Course Type', 'Self-Study', 'Training Type' and 'Selected Topics' details in read only format.</div>"
					+ "<div><b>*</b> The screen contains 'View Existing', 'Back' and 'Submit' buttons.</div>"
					+ "<div><b>*</b> Under 'View Existing', registered & active state records of 'Course' are displayed if any.</div>"),

	;

	private final String courseStrings;

	CourseStrings(String courseStrings) {

		this.courseStrings = courseStrings;

	}

	public String getCourseStrings() {
		return courseStrings;
	}

}
