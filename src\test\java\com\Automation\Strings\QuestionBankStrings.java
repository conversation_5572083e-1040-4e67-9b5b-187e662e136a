package com.Automation.Strings;

public enum QuestionBankStrings {

	// Question Bank Submenu

	QB_Config_DC("Click on 'Question Bank' submenu."),
	QB_Config_AC("'Question Bank Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	QB_Config_AR("'Question Bank Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	QB_Config_SS("'Configuration'"),
//Question Bank Configuration 
	Click_DoneatQBConfig_AC("'Question Bank Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatQBConfig_AR("'Question Bank Configuration Registration' screen is getting displayed.</div>"),

	PrepareQB_AC("'Question Bank' screen should be displayed.</div>"
			+ "<div><b>* </b>List of topics for which the question bank needs to be prepared should be displayed.</div>"),

	PrepareQB_AR("'Question Bank' screen is getting displayed.</div>"
			+ "<div><b>* </b>List of topics for which the question bank needs to be prepared is/are getting displayed.</div>"),

	PrepareQB_SS("'Question Bank'"),

	// Search By

	SearchBy_AC(
			"Option to search with 'Top 250 Records, Topic Name, Topic Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_AR(
			"Option to search with 'Top 250 Records, Topic Name, Topic Unique Code, Initiated Between' are available.</div>"),

	// Topic Name

	Like_TopicName_DC("Enter the 'Topic Name' which is linked to the above registred course in 'Like' text box."),

	// Click Topic name

	click_Topic_DC("Click on the topic which is linked for the above registred course.</div>"),
	click_Topic_AC("'Question Bank Registration Initiation' screen should be displayed.</div>"

			+ "<div><b>*</b> Selected Topic particulars like ‘Topic Name’, ‘Topic Unique Code’, ‘Unique Code’, ‘Category Name’ and ‘Subject Name’ should be displayed against the respective fields in read only format.</div>"
			+ "<div><b>*</b> Unique Code’ field value should be displayed with extension of ‘-QB’ of respective Topic unique Code. </div>"
			+ "<div><b>*</b> The screen should contain ‘Total No. of Questions’, ‘Multiple Choice’, ‘Fill In The Blank(s)’, ‘True or False’ and ‘Essay’ fields with ‘0’ value by default.</div>"
			+ "<div><b>*</b> The screen should contain ‘View Existing’, and ‘Submit’ buttons.</div>"),

	click_Topic_AR("'Question Bank Registration Initiation' screen is getting displayed.</div>"

			+ "<div><b>*</b> Selected Topic particulars like ‘Topic Name’, ‘Topic Unique Code’, ‘Unique Code’, ‘Category Name’ and ‘Subject Name’ are getting displayed against the respective fields in read only format.</div>"
			+ "<div><b>*</b> Unique Code’ field value should be displayed with extension of ‘-QB’ of respective Topic unique Code. </div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘Total No. of Questions’, ‘Multiple Choice’, ‘Fill In The Blank(s)’, ‘True or False’ and ‘Essay’ fields with ‘0’ value by default.</div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘View Existing’, and ‘Submit’ buttons.</div>"),
	click_Topic_SS("'Question Bank Registration '"),

	// Questions Count entering

	MCTypeCount_DC(
			"Enter the value greater than '1' and less than '40' in 'Multiple Choice' textbox and click on the screen"),
	MCTypeCount_AC(
			"Number of questions shoud be displayed under 'Mutliple Choice' section based on the entered value."),
	MCTypeCount_AR(
			"Number of questions are getting be displayed under 'Mutliple Choice' section based on the entered value."),
	MCTypeCount_SS("'Mutliple Choice'"),

	FBTypeCount_DC(
			"Enter the value greater than '1' and less than '40' in 'Fill In The Blanks' textbox and click on the screen."),
	FBTypeCount_AC(
			"Number of questions shoud be displayed under 'Fill In The Blanks' section based on the entered value."),
	FBTypeCount_AR(
			"Number of questions are getting be displayed under 'Fill In The Blanks' section based on the entered value."),
	FBTypeCount_SS("'Mutliple Choice'"),

	TFTypeCount_DC(
			"Enter the value greater than '1' and less than '40' in 'True or False' textbox and click on the screen."),
	TFTypeCount_AC("Number of questions shoud be displayed under 'True or False' section based on the entered value."),
	TFTypeCount_AR(
			"Number of questions are getting be displayed under 'True or False' section based on the entered value."),
	TFTypeCount_SS("'True or False'"),

	EssayTypeCount_DC(
			"Enter the value greater than '1' and less than '40' in 'True or False' textbox and click on the screen."),
	EssayTypeCount_AC(
			"Number of questions shoud be displayed under 'True or False' section based on the entered value."),
	EssayTypeCount_AR(
			"Number of questions are getting be displayed under 'True or False' section based on the entered value."),
	EssayTypeCount_SS("'True or False'"),

	// Questions, Answer and Marks related Strings

	EnterMC_Question_DC("Enter the required question in 'Question 1' under 'Mutliple Choice' section."),
	EnterFB_Question_DC("Enter the required question in 'Question 1' under 'Fill In The Blanks' section."),
	EnterTF_Question_DC("Enter the required question in 'Question 1' under 'True or False' section."),
	EnterEssay_Question_DC("Enter the required question in 'Question 1' under 'Essay' section."),

	EnterMC_Question_SS("'Question 1'"),

	EnterMC_Option_a_DC("Enter the value for the option 'a'."), EnterMC_Option_a_SS("Option 'a'"),

	EnterMC_Option_b_DC("Enter the value for the option 'b'."), EnterMC_Option_b_SS("Option 'b'"),

	EnterMC_Option_c_DC("Enter the value for the option 'c'."), EnterMC_Option_c_SS("Option 'c'"),

	EnterMC_Option_d_DC("Enter the value for the option 'c'."), EnterMC_Option_d_SS("Option 'd'"),

	Select_MCAnswer_b_DC("Select the option 'b' as answer."),

	Select_MCAnswer_b_SS("Option 'b'"),

	Enter_Marks__DC("Enter the required marks in the 'Marks' field."),

	Enter_Marks__SS("'Marks'"),

	EnterFB_ESS_ANS_DC("Enter the required answer in 'Ans' field."), EnterFB_ESS_ANS_SS("'Ans'"),

	Select_True_DC("Select 'True' as answer."), Select_True_SS("'True'"),

	// Submit

	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Question Bank:Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Question Bank:Registration Initiation'.</div>"),

	// Esign Proceed

	Esign_QBProceed_AC(
			"'Question Bank Registration Initiated Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_QBProceed_AR(
			"'Question Bank Registration Initiated Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	// Audit trails list screen

	click_QBAuditTrails_AC(
			"'Question Bank Audit Trails' screen should be displayed and list of topics for which the Question Bank is prepared should be displayed.<div>"),

	click_QBAuditTrails_AR(
			"'Question Bank Audit Trails' screen is getting displayed and list of topics for which the Question Bank is prepared are getting displayed.<div>"),

	click_QBAuditTrails_SS("'Question Bank Audit Trails'"),

	// Search By

	SearchBy_Audit_AC(
			"Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_Audit_AR(
			"Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between' are available.</div>"),

//Topic Name at AT
	Like_TopicName_AuditTrails_DC("Ente the topic name for which the Question Bank is registred."),

	click_QB_AuditTrails_DC("Click on the topic for which the Question Bank is registered.</div>"),
	click_QB_AuditTrails_AC(
			"'Question Bank-Audit Trails: Revision No.:Title 0-Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> Screen should contain Topic Name, Topic Unique Code, Unique Code, Category Name, Subject Name, Total Active Questions details accurately.</div>"
					+ "<div><b>*</b>Under 'Active Questions' section all the questions should be displayed and all the details should be read only format.</div>"),

	click_QB_AuditTrails_AR(
			"'Question Bank-Audit Trails: Revision No.:Title 0-Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> Screen  contains Topic Name, Topic Unique Code, Unique Code, Category Name, Subject Name, Total Active Questions details accurately.</div>"
					+ "<div><b>*</b>Under 'Active Questions' section all the questions are getting displayed and all the details are in read only format.</div>"),
	click_QB_AuditTrails_SS("'Audit trails'"),

	// Question Bank configuration Audit trails

	QB_ConfigAudit_DC("Click on 'Question Bank' submenu."),
	QB_ConfigAudit_AC("'Question Bank Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	QB_ConfigAudit_AR("'Question Bank Configuration Audit Trails' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	QB_ConfigAudit_SS("'Question Bank Configuration Audit Trails'"),

	// Approve QB Tasks screen
	QB_Approve_AC("'Question Bank Approval Tasks' screen should be displayed."
			+ "<div><b>* </b>Screen should contain ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Topic Name’, ‘Unique Code’, ‘Initiated By’, and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>List of Topic Name(s) whose Question Bank registration request is to be approved by the current login user should be listed and available for approval under the ‘Registration’ tab.</div>"
			+ "<div><b>* </b>•	Question Bank(s) which are registered in Master plant / any other plant(s) should not be displayed (if any).</div>"),
	QB_Approve_AR("'Question Bank Approval Tasks' screen is getting displayed."
			+ "<div><b>* </b>Screen is getting displayed with ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details are getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Topic Name’, ‘Unique Code’, ‘Initiated By’, and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>List of Topic Name(s) whose Question Bank registration request is to be approved by the current login user are listed and available for approval under the ‘Registration’ tab.</div>"
			+ "<div><b>* </b>•	Question Bank(s) which are registered in Master plant / any other plant(s) are not getting displayed (if any).</div>"),

	// Approve Esign
	Esign_Approve_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Question Bank: Registration Approval: Approve'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name (Employee ID)> should be displayed for the ‘Signed By’ field.</div>"
					+ "<div><b>* </b>The option to enter ‘Password’ should be available.</div>"
					+ "<div><b>* </b>'Cancel' and ‘Proceed’ button should be available.</div>"
					+ "<div><b>* </b>'Proceed' button should be displayed in disabled mode.</div>"),
	Esign_Approve_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Question Bank: Registration Approval: Approve'.</div>"
					+ "<div><b>* </b>Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the ‘Signed By’ field.</div>"
					+ "<div><b>* </b>The option to enter ‘Password’ is available.</div>"
					+ "<div><b>* </b>'Cancel' and ‘Proceed’ button are available.</div>"
					+ "<div><b>* </b>'Proceed’ button is getting displayed in disabled mode.</div>"),

	// Approved Confirmation Messages
	Esign_ProceedTrainingSche_AC(
			"'Training Schedule  Registration Initiated Unique Code:(Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedTrainingSche_AR(
			"'Training Schedule  Registration Initiated Unique Code:(Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	// Click on Topic name for Approval

	Click_TopicforApproval_DC(
			"Click on the above topic name for which the Question Paper Registration Approval is required"),
	Click_TopicforApproval_AC("'Question Bank - Registration Approval’ screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain the details of the Question Bank entered/ selected during registration.</div>"
			+ "<div><b>* </b>'Final Status' should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section should contain the ‘Initiated’ transaction with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
			+ "<div><b>* </b>'Approve, Return & Drop’ options should be available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>'Drop' option should be in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter / select required ‘Remark(s)/ Reason(s)’ should be available.</div>"),
	Click_TopicforApproval_AR("'Question Bank - Registration Approval’ screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen should is getting displayed with the details of the Question Bank entered/ selected during registration.</div>"
			+ "<div><b>* </b>'Final Status' is getting displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section is getting displayed with the ‘Initiated’ transaction with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
			+ "<div><b>* </b>'Approve, Return & Drop’ options are available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>'Drop' option is getting displayed in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter / select required ‘Remark(s)/ Reason(s)’ is available.</div>"),
	Click_TopicforApproval_SS("'Question Bank - Registration Approval’"),

	// Enter Topic after Approval

	Like_TopicNameAfterApprovalAT_DC("Ente the topic name for which the Question Bank registration was approved."),

	// Click on Topic Name Before Approval for Audit trails

	Click_TopicBeforeApproval_AC(
			"Question Bank - Audit Trails: Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the Question Bank entered/ selected during registration.</div>"
					+ "<div><b>* </b>'Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section should contain ‘Initiated’  transaction with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both should be read as '1' and '0' respectively.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
	Click_TopicBeforeApproval_AR(
			"Question Bank - Audit Trails: Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displayed with details of the Question Bank entered/ selected during registration.</div>"
					+ "<div><b>* </b>'Final Status’ is getting displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section is getting displayed with ‘Initiated’  transaction with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both are read as '1' and '0' respectively.</div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),

	// Click on Topic name after Approval for Audit Trails

	Click_TopicAfterApproval_DC("Click on the above topic whose Question Bank registration was approved."),
	Click_TopicAfterApproval_AC(
			"Question Bank - Audit Trails: Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the Question Bank entered/ selected during registration.</div>"
					+ "<div><b>* </b>'Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>* </b>The ‘Events’ section should contain ‘Initiated’ and ‘Approved’ transaction’s with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both should be read as 1.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format..</div>"),
	Click_TopicAfterApproval_AR(
			"Question Bank - Audit Trails: Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displayed with the details of the Question Bank entered/ selected during registration.</div>"
					+ "<div><b>* </b>'Final Status’ is getting displayed as ‘Approved’.</div>"
					+ "<div><b>* </b>The ‘Events’ section is contained with ‘Initiated’ and ‘Approved’ transaction’s with ‘Username, Date & Time and Remark(s) / Reason(s)’  details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both are read as 1.</div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),

	// Config Audit Trails window
		Click_Config_Proceed_AC(
				"'Question Bank Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
						+ "<div><b>* </b>The screen should be displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
						+ "<div><b>* </b>The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
						+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
		Click_Config_Proceed_AR(
				"'Question Bank Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
						+ "<div><b>* </b>The screen is getting displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
						+ "<div><b>* </b>The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
						+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),
		Click_Config_Proceed_SS("'Question Bank Configuration - Audit Trails'."),
		
		Enter_UniqueCode_DC("Enter the 'Unique Code' of the above registred QuestionBank"),
	
	;

	private final String QBank;

	QuestionBankStrings(String Qbank) {

		this.QBank = Qbank;

	}

	public String getQBStrings() {
		return QBank;
	}

}
