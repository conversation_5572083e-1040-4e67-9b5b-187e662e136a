package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Stream;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.Reports;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_CSRReport extends OQActionEngine {

	public static String[] columnData1Before;
	public static String[] columnData2Before;
	public static String[] columnData1After;
	public static String[] columnData2After;
	public static String[] combinedArrayBefore;
	public static String[] combinedArrayAfter;
	public static int tobePlannedinsideCountAfter;
	public static int tobePlannedinsideCountBefore;
	public static int toBePlannedOutSideCountBefore;
	public static int toBePlannedOutSideCountAfter;
	public static int qualifiedCountDisplayedAtCourseSessionReport;
	public static int toBeRetrainedCountAtCSReport;
	public static int TotalPendingCount;
	public static String[] toBeRetrainedData;
	public static String[] QualifiedEmployeesData;
	public static String[] RetakeEvaluation;
	public static int QualifiedCountInsideCourseSessionReport;
	public static int AfterRecordAttendanceToBePlannedCountOutside;
	public static int AfterRecordAttendnaceSkippedCountOurside;
	public static int AfterRecordAttendanceAbsentOutsideCount;
	public static String[] ToBePlannedDataAfterRA1;
	public static String[] ToBePlannedDataAfterRA2;
	public static String[] SkippedData;
	public static int skippedCount;
	public static String[] AbsentData;
	public static int AbsentInsideCount;
	public static String[] ActualQualifiedCount;
	public static int totalTrainessCount;
	public static int totalPendingCount;
	public static int QualifiedActiveCount;

	public static int ToBePlannedCountAfterRespondDocumentReading;
	public static String[] ToBePlanned_Data_AfterRespondDocumentReading;
	public static int retakeCount;
	public static String[] retakeData;
	public static String[] plannedDataAfterSession;
	public static String[] TotalPendingData;
	public static int plnnnedCountAfterSession;
	public static String[] QualifiedData;
	public static String[] QualifiedActiveData;
	Properties prop;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Reports']")
	WebElement courseManagerReportsMenu;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Reports_LEARNIQ1543']")
	WebElement CSRReportmENU;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[2]//input")
	WebElement courseSessionNameNullCheckBox;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[1]//input")
	WebElement courseSessionNameTextBox;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl00']")
	WebElement viewReport;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement employeeName;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")
	WebElement TBPCount;

	@FindBy(xpath = "//table[1]/tbody[1]/tr/td[1]/div[1]/a[1]/div[1]")
	WebElement CourseNamehyperlink;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement CourseNamehyperlink2;
	@FindBy(xpath = "(//div[text()='Training Certificate'])[2]")
	WebElement ClassroomTypeTrainingCertificate;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]/div/table/tbody/tr/td[2]/span/input")
	WebElement CourseCheckBox;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]/div/table/tbody/tr/td[1]/input")
	WebElement CourseNameTextBox;
	@FindBy(xpath = "//tr[@valign='top']/td/div/a//div[contains(text(),'Training Certificate')]")
	WebElement RETrainingCertificate;

	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")
	WebElement ToBePlanned;
	@FindBy(xpath = "//button[normalize-space(text())='Back']")
	WebElement BackButton;

	@FindBy(xpath = "//table/tbody/tr[3]/td[5]/div/a/div/div/div/span")
	WebElement Skipped;

	@FindBy(xpath = "//table/tbody/tr[3]/td[6]/div/a/div/div/div/span")
	WebElement Absent;

	@FindBy(xpath = "//table/tbody/tr[3]/td[8]/div/a/div/div/div/span")
	WebElement Qualified;

	@FindBy(xpath = "//table/tbody/tr[3]/td[10]/div/a/div/div/div/span")
	WebElement TotalPending;

	@FindBy(xpath = "//table/tbody/tr[3]/td[9]/div/a/div/div/div/span")
	WebElement QualifiedActive;

	@FindBy(xpath = "//table/tbody/tr[3]/td[7]/div/a/div/div/div/span")
	WebElement ToBeRetrained;

	@FindBy(xpath = "//table/tbody/tr[3]/td[11]/div/div")
	WebElement TotalTrainees;

	@FindBy(xpath = "//table/tbody/tr[3]/td[12]/div/a/div/div/div/span")
	WebElement Retake;

	@FindBy(xpath = "//table/tbody/tr[3]/td[4]/div/a/div/div/div/span")
	WebElement planned;

	public CM_CSRReport() {
		PageFactory.initElements(driver, this);
	}

	public void TBPCSRReport() {

		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		clickForReports(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());

		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		TimeUtil.mediumWait();
		JavascriptExecutor js = (JavascriptExecutor) driver;
		TimeUtil.mediumWait();
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click2(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TotalPendingData = null;
		TimeUtil.longwait();
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));

		String text = element.getText();

		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {

			toBePlannedOutSideCountBefore = Integer.parseInt(text);

		}
		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {

			toBePlannedOutSideCountAfter = Integer.parseInt(text);

		}
		click2(element, "Cliok on to br planned count", "", "", "");
		TimeUtil.longwait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {
			columnData1Before = new String[44];

			for (int j = 2; j <= rows.size() - 1; j++) {

				WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
				WebElement div = cell.findElement(By.xpath(".//div/div"));

				columnData1Before[j - 2] = div.getText();

			}

		} else if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {

			columnData1After = new String[44];

			for (int j = 2; j <= rows.size() - 1; j++) {

				WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
				WebElement div = cell.findElement(By.xpath(".//div/div"));

				columnData1After[j - 2] = div.getText();

			}
		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {
					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				} else if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {
					columnData2After = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2After[j - 2] = div.getText();

					}
				}
			}
		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {

			blankMethod(columnData1Before, columnData2Before);
		}

		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {

			blankMethod(columnData1After, columnData2After);
		}

		if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {

			switchToDefaultContent(driver);
			switchToBodyFrame(driver);
			TimeUtil.mediumWait();
			waitForElementVisibile(BackButton);
			BackButton.click();
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			enterToSSRSShadowRoot();
			convertString_To_Integer(planned.getText());
			waitForElementVisibile(planned);
			click2(planned, "", "", "", "");
			TimeUtil.mediumWait();
			plnnnedCountAfterSession = stringToInt;
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			WebElement plannedtable = driver.findElement(
					By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
			List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

			plannedDataAfterSession = new String[44];

			for (int j = 2; j <= plannedrows.size() - 1; j++) {

				WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
				WebElement div = cell.findElement(By.xpath(".//div/div"));

				plannedDataAfterSession[j - 2] = div.getText();

			}

			plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null)
					.toArray(String[]::new);

			switchToDefaultContent(driver);
			switchToBodyFrame(driver);
			TimeUtil.mediumWait();
			waitForElementVisibile(BackButton);
			BackButton.click();
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			enterToSSRSShadowRoot();
			convertString_To_Integer(planned.getText());
			// TotalPendingCount = stringToInt;
			// TotalPendingData= new String[44];

		}

		switchToDefaultContent(driver);

	}

	public void blankMethod(String[] array1, String[] array2) {

		if (array2 == null) {

			String[] result1 = Arrays.stream(array1).filter(s -> s != null) // Filter out null values
					.toArray(String[]::new);

			if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {
				combinedArrayBefore = new String[result1.length];
				System.arraycopy(result1, 0, combinedArrayBefore, 0, result1.length);

				// Copy elements of the second array
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountBefore = combinedArrayBefore.length;
			}

			else if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {
				combinedArrayAfter = new String[result1.length];
				System.arraycopy(result1, 0, combinedArrayAfter, 0, result1.length);

				// Copy elements of the second array
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountAfter = combinedArrayAfter.length;
			}

			else {
				combinedArrayBefore = new String[result1.length];
				System.arraycopy(result1, 0, combinedArrayBefore, 0, result1.length);

				// Copy elements of the second array
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountBefore = combinedArrayBefore.length;

			}

		} else {

			String[] result1 = Arrays.stream(array1).filter(s -> s != null) // Filter out null values
					.toArray(String[]::new);
			String[] result2 = Arrays.stream(array2).filter(s -> s != null) // Filter out null values
					.toArray(String[]::new);

			if (CM_VerifyCourseSessionScreen.SessionStatus.equals("BeforeSession")) {
				combinedArrayBefore = new String[result1.length + result2.length];
				System.arraycopy(result1, 0, combinedArrayBefore, 0, result1.length);

				// Copy elements of the second array
				System.arraycopy(result2, 0, combinedArrayBefore, result1.length, result2.length);
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountBefore = combinedArrayBefore.length;
			}

			else if (CM_VerifyCourseSessionScreen.SessionStatus.equals("AfterSession")) {
				combinedArrayAfter = new String[result1.length + result2.length];
				System.arraycopy(result1, 0, combinedArrayAfter, 0, result1.length);

				// Copy elements of the second array
				System.arraycopy(result2, 0, combinedArrayAfter, result1.length, result2.length);
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountAfter = combinedArrayAfter.length;
			}

			else {
				combinedArrayBefore = new String[result1.length + result2.length];
				System.arraycopy(result1, 0, combinedArrayBefore, 0, result1.length);

				// Copy elements of the second array
				System.arraycopy(result2, 0, combinedArrayBefore, result1.length, result2.length);
				// System.out.println(Arrays.toString(combinedArray));
				tobePlannedinsideCountBefore = combinedArrayBefore.length;

			}
		}
	}

	public void OpenCourseSessionReport() {

		String CourseName = CM_Course.getCourse();

		// String CourseName = "CRSNewULSZ";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();

	}

	public void QualifiedCount() {

		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[8]/div/a/div/div/div/span"));
		String text = element.getText();
		qualifiedCountDisplayedAtCourseSessionReport = Integer.parseInt(text);
		element.click();
		TimeUtil.mediumWait();
		WebElement table = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		// int employeecount = rows.size() - 2;
		String[] RawQualifiedEmployeesData = new String[withoutdec + 1];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list.toArray(new String[0]);
		QualifiedCountInsideCourseSessionReport = QualifiedEmployeesData.length;

		switchToDefaultContent(driver);

	}

	public void skipped_Absent_Qualified_AfterRecordAttendance() {

		OpenCourseSessionReport();
		convertString_To_Integer(ToBePlanned.getText());
		AfterRecordAttendanceToBePlannedCountOutside = stringToInt;
		ToBePlanned.click();

		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;
		ToBePlannedDataAfterRA1 = new String[100];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			ToBePlannedDataAfterRA1[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				ToBePlannedDataAfterRA2 = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					ToBePlannedDataAfterRA2[j - 2] = div.getText();

				}

			}
		}

		TimeUtil.mediumWait();
		blankMethod(ToBePlannedDataAfterRA1, ToBePlannedDataAfterRA2);
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);
		skippedCount = SkippedData.length;
		System.out.println("Skipped Inside Count at CSR Report after record Attedance: " + skippedCount);
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;
		System.out.println("Absent Outside Count at CSR Report after record Attedance: "
				+ AfterRecordAttendanceAbsentOutsideCount);

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;
		System.out.println("Absent Inside Count at CSR Report after record Attedance: " + AbsentInsideCount);

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		System.out.println("Qualified Outside Count at CSR Report after record Attedance: "
				+ qualifiedCountDisplayedAtCourseSessionReport);
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);
		QualifiedCountInsideCourseSessionReport = QualifiedEmployeesData.length;
		System.out.println("Qualified Inside Count at CSR Report after record Attedance: "
				+ QualifiedCountInsideCourseSessionReport);
		switchToDefaultContent(driver);

	}

	public void TBPCSRReport_AfterRespondDocumentReading_Without_Assessment() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));
//	WebElement elementcsrtext = wait.until(ExpectedConditions
//				.visibilityOfElementLocated(By.xpath("//div[text()='Planned User']")));
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click2(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		// WebElement element =
		// driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employees in different states",
				"To planned Count in CSR after keeping employees in different states");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				columnData2Before = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					columnData2Before[j - 2] = div.getText();

				}

			}
		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR, combinedArrayBefore,
				"To planned Data in Course Session Screen after keeping employees in different states",
				"To planned Data in Course Session Screen after keeping employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR after keeping employees in different states",
				"Inside To Be Planned Count in CSR after keeping employees in different states");
		TimeUtil.mediumWait();
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressUsers, plannedDataAfterSession,
				"Actual Inprogress users", "Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keeping employees in different states");

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count after in CSR after keeping employees in different states",
				"Inside Qualified Count after in CSR after keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				combinedArrayBefore.length + QualifiedEmployeesData.length
						+ CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of To Be Planned, Planned and Qualified");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + +CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Planned");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_After_Course_Retraining_DocumentReading_Without_Assessment() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		String[] combineUnsche_Schedu_ToBePlannedData = Stream
				.concat(Arrays.stream(CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab),
						Arrays.stream(CM_VerifyCourseSessionScreen.columnDataBeforeSession))
				.toArray(String[]::new);

//		String[] combineUnsche_Schedu_ToBePlannedData = Arrays.copyOf(
//				CM_VerifyCourseSessionScreen.columnDataBeforeSession,
//				CM_VerifyCourseSessionScreen.columnDataBeforeSession.length
//						+ CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab.length);
//		System.arraycopy(CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab, 0,
//				combineUnsche_Schedu_ToBePlannedData, CM_VerifyCourseSessionScreen.columnDataBeforeSession.length,
//				CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab.length);
		compareCount(combineUnsche_Schedu_ToBePlannedData.length, ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen(Unsch and Sche) After Course Retraining",
				"To be planned count is CSR after Course Retraining");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				columnData2Before = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					columnData2Before[j - 2] = div.getText();

				}

			}
		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		toBePlannedCountData(combineUnsche_Schedu_ToBePlannedData, combinedArrayBefore,
				"To Be Planned Data from Unschedule and Schedule tabs After Course Retraining",
				"To Be Planned Data in CSR After Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After Course Retraining",
				"Inside To Be Planned Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length,
				"Outisde Total Trainees Count in CSR After Course Retraining", "Sum of all columns excluding Retake");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount, combinedArrayBefore.length, "Outside Total Pending Count at CSR",
				"To Be Planned Count");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_RE_SystemEval_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR, combinedArrayBefore,
				"To Be Planned Data in Course Session After Diferent States",
				"To Be Planned Data in CSR After Different States");
		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After Different States",
				"Inside To Be Planned Count in CSR After Different States");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressExcluding_Retake, plannedDataAfterSession,
				"Actual Inprogress users excluding Retake",
				"Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Planned Count in CSR After Keeping employees in different states",
				"Inside To Be Planned Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"Actual To Be Retrained Data After keeping employees in different states",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		convertString_To_Integer(Retake.getText());
		waitForElementVisibile(Retake);
		Retake.click();
		retakeCount = stringToInt;
		TimeUtil.mediumWait();

		WebElement Retaketable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
		int retakewithoutdec = retakerows.size();
		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];

		for (int j = 2; j <= retakerows.size() - 1; j++) {

			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RetakeEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));

		// Remove null values
		list5.removeIf(item -> item == null);

		// Convert back to array if needed
		retakeData = list5.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.retakeData.length, retakeCount,
				"Retake Count at Course Session screen after keeping employee in different states",
				"Retake Count at CSR after keeping employee in different states");

		compareCount(retakeCount, retakeData.length,
				"Outiside Retake Count in CSR After Keeping employees in different states",
				"Inside Retake Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData, "Actual Retake Data",
				"Retake Data in CSR Report after keping employees in different states");

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + QualifiedEmployeesData.length
						+ plannedDataAfterSession.length + retakeData.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Planned, o Be Retrained, Qualified ,Retake and In Progress users ");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.longwait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + plannedDataAfterSession.length + toBeRetrainedData.length
						+ retakeData.length,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Planned, retake and to be retrained");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);

	}

	// Interim Course Session Report

	public void Interim_TBPCSRReport_RE_SystemEval_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR, combinedArrayBefore,
				"To Be Planned Data in Course Session After Respond/Record DR",
				"To Be Planned Data in CSR After Respond/Record DR");
		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After Respond/Record vDR",
				"Inside To Be Planned Count in CSR After Respond/Record DR");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressExcluding_Retake, plannedDataAfterSession,
				"Actual Inprogress users excluding Retake",
				"Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Planned Count in CSR After Keeping employees in different states",
				"Inside To Be Planned Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"Actual To Be Retrained Data After keeping employees in different states",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		convertString_To_Integer(Retake.getText());
		waitForElementVisibile(Retake);
		Retake.click();
		retakeCount = stringToInt;
		TimeUtil.mediumWait();

		WebElement Retaketable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
		int retakewithoutdec = retakerows.size();
		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];

		for (int j = 2; j <= retakerows.size() - 1; j++) {

			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RetakeEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));

		// Remove null values
		list5.removeIf(item -> item == null);

		// Convert back to array if needed
		retakeData = list5.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.RetakeCountBeforeSGP, retakeCount,
				"Retake Count at Course Session screen after keeping employee in different states",
				"Retake Count at CSR after keeping employee in different states");

		compareCount(retakeCount, retakeData.length,
				"Outiside Retake Count in CSR After Keeping employees in different states",
				"Inside Retake Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData, "Actual Retake Data",
				"Retake Data in CSR Report after keping employees in different states");

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + QualifiedEmployeesData.length
						+ plannedDataAfterSession.length + retakeData.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Planned, o Be Retrained, Qualified ,Retake and In Progress users ");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.longwait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + plannedDataAfterSession.length + toBeRetrainedData.length
						+ retakeData.length,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Planned, retake and to be retrained");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);

	}

	public void DocumentReading_Sys_Evaluation_TBPCSRReport_After_Course_Retraining_() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		String[] combineUnsche_Schedu_ToBePlannedData = Arrays.copyOf(
				CM_VerifyCourseSessionScreen.columnDataBeforeSession,
				CM_VerifyCourseSessionScreen.columnDataBeforeSession.length
						+ CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab, 0,
				combineUnsche_Schedu_ToBePlannedData, CM_VerifyCourseSessionScreen.columnDataBeforeSession.length,
				CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab.length);
		compareCount(
				CM_VerifyCourseSessionScreen.To_Be_Planned_Data_After_Selecting_Subgroup_AfterCourseRetraining.length,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen(Unsch and Sche) After Course Retraining",
				"To be planned count is CSR after Course Retraining");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				columnData2Before = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					columnData2Before[j - 2] = div.getText();

				}

			}
		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		toBePlannedCountData(
				CM_VerifyCourseSessionScreen.To_Be_Planned_Data_After_Selecting_Subgroup_AfterCourseRetraining,
				combinedArrayBefore, "To Be Planned Data After Course Retraining",
				"To Be Planned Data in CSR After Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After Course Retraining",
				"Inside To Be Planned Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
				"To Be Retrained Data After Course Retraining in Course Session Screen",
				"To Be Planned Data in CSR After Course Retraining");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outisde To Be Planned Count in CSR After Course Retraining",
				"Inside To Be Planned Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length + +toBeRetrainedData.length,
				"Outisde Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned and To BE Retrained");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount, combinedArrayBefore.length + toBeRetrainedData.length,
				"Outside Total Pending Count at CSR", "To Be Planned Count + To Be Retrained count");

		switchToDefaultContent(driver);
	}

	public void compareEmployeeDataBefore_Sesion_At_CSRData() {

		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore, CM_CSRReport.tobePlannedinsideCountBefore,
				"Outside To Be Planned Count at Course Session Report Before Any Session",
				"Inside To Be Planned Count at CourseSession Report Before Any Session");

		// Data between Course Session Screen and Course Session Report Before Session
		// for To be Planned Count

		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataBeforeSession, CM_CSRReport.combinedArrayBefore,
				"CourseSession To be Planned Count Before", "Course Session Report To be Plannd Count Before Session");
	}

	public void compare_EmployeeData_After_Session_At_CSRReport() {

		checkCommonElements(CM_CSRReport.combinedArrayAfter, CM_VerifyCourseSessionScreen.selectedEmployees,
				"Employees in To Be planned After Session", "Selected Employees");

		// Compare the To Be planned Count in Course Session Report with Previous Count
		// minus Selected Candidates
		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore - CM_VerifyCourseSessionScreen.SessionProposedFor,
				CM_CSRReport.toBePlannedOutSideCountAfter,
				"Course session Report before minus Selected employees Count",
				"After Course Session To be Planned Count");

		// Compare Course Session Data with Course Session Report Data After Session
		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataAfterSession, CM_CSRReport.combinedArrayAfter,
				"CourseSession To be Planned Data After Session",
				"Course Session Report To be Plannd Data After Session");

		compareCount(CM_VerifyCourseSessionScreen.SessionProposedFor, CM_CSRReport.plnnnedCountAfterSession,
				"Actual Planned Count", "Planned Count displayed in CSR after Course Session");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.selectedEmployees, CM_CSRReport.plannedDataAfterSession,
				"Actual Planned Data", "Planned Data displyed in CSR after proposing session");
		compareCount(CM_CSRReport.plnnnedCountAfterSession, CM_CSRReport.plannedDataAfterSession.length,
				"Outside Planned Count in CSR Report", "Inside Planned Count in CSR Report");
	}

	public void TBPCSRReport_IT_WithAssesment_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());

		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));

		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];
		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData, combinedArrayBefore,
				"To Be Planned Data in Course Session After Keeping employees in different states",
				"To Be Planned Data in CSR After keepi ng employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After keeping employees in different states",
				"Inside To Be Planned Count in CSR After keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressExcluding_Retake, plannedDataAfterSession,
				"Actual Inprogress users excluding Retake",
				"Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData, "Actual Skipped Data",
				"Skipped Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after keeping employes in different states",
				"Absent Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR After Keeping employees in different states",
				"Inside Absent Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData, "Actual Absent Data",
				"Absent Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedCountBeforeSGP, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after keeping employes in different states",
				"To Be Retrained Count in CSR Report after keeping employes in different states");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR After Keeping employees in different states",
				"Inside To Be Retrained Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"Actual To Be Retrained Data After keeping employees in different states",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count in CSR After Keeping employees in different states",
				"Inside Qualified Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length, qualifiedCountDisplayedAtCourseSessionReport,
				"Actual Qualified Data", "Qualified Count in CSR After Keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length
						+ QualifiedEmployeesData.length + CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Planned, Skipped, Absent, To Be Retrained, Qualified and Retake  ");

//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
		convertString_To_Integer(Retake.getText());
		waitForElementVisibile(Retake);
		Retake.click();
		retakeCount = stringToInt;
		TimeUtil.mediumWait();

		WebElement Retaketable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
		int retakewithoutdec = retakerows.size();
		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];

		for (int j = 2; j <= retakerows.size() - 1; j++) {

			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RetakeEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));

		// Remove null values
		list5.removeIf(item -> item == null);

		// Convert back to array if needed
		retakeData = list5.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.retakeData.length, retakeCount,
				"Retake Count at Course Session screen after keeping employee in different states",
				"Retake Count at CSR after keeping employee in different states");

		compareCount(retakeCount, retakeData.length,
				"Outiside Retake Count in CSR After Keeping employees in different states",
				"Inside Retake Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData, "Actual Retake Data",
				"Retake Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + plnnnedCountAfterSession + +AfterRecordAttendnaceSkippedCountOurside
						+ AfterRecordAttendanceAbsentOutsideCount + toBeRetrainedCountAtCSReport + retakeCount,
				"Outside Total Pending Count at CSR",
				"Sum of To Be Planned, Planned, Skipped, Absent, To be Retrained and Retake");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");
		switchToDefaultContent(driver);
	}

	/*
	 * public void TBPCSRReport_IT_WithAssesment_After_CourseRetraining() {
	 * CM_VerifyCourseSessionScreen.SessionStatus = ""; String CourseName =
	 * CM_Course.getCourse();
	 * 
	 * // CourseName = "AutomationCourseLZRW"; click2(menu,
	 * CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_SS.getCommonStrings()); TimeUtil.shortWait();
	 * click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AR.getCommonStrings(),
	 * CommonStrings.CM_Menus_SS.getCommonStrings());
	 * scrollToViewElement(courseManagerReportsMenu);
	 * click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(),
	 * Reports.ClickReports_AC.getReports(), Reports.ClickReports_AR.getReports(),
	 * Reports.ClickReports_SS.getReports()); scrollToViewElement(CSRReportmENU);
	 * click2(CSRReportmENU, Reports.click_IER_DC.getReports(),
	 * Reports.click_IER_AC.getReports(), Reports.click_IER_AR.getReports(),
	 * Reports.click_IER_SS.getReports()); TimeUtil.mediumWait();
	 * TimeUtil.mediumWait(); switchToBodyFrame(driver); String strt =
	 * "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')"
	 * ; JavascriptExecutor js = (JavascriptExecutor) driver; WebElement element3 =
	 * (WebElement) js.executeScript(strt); driver.switchTo().frame(element3);
	 * 
	 * WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));
	 * 
	 * // Wait for the element with the text "Course Sessions Report (Complete)"
	 * WebElement elementcsrtext =
	 * wait.until(ExpectedConditions.visibilityOfElementLocated(By.
	 * xpath("//div[text()='Course Sessions Report (Complete)']")));
	 * 
	 * WebElement button = driver.findElement( By.xpath(
	 * "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"
	 * )); waitForElementVisibile(button); clickAndWaitforNextElement(button,
	 * CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
	 * Reports.Click_ToggleBtnIER_AC.getReports(),
	 * Reports.Click_ToggleBtnIER_AR.getReports(),
	 * Reports.Click_ToggleBtn_SS.getReports()); click2(CourseCheckBox,
	 * Reports.UnselectEmployeeID_IER_DC.getReports(),
	 * Reports.UnselectEmployeeID_IER_AC.getReports(),
	 * Reports.UnselectEmployeeID_IER_AR.getReports(),
	 * Reports.UnselectEmployeeID_IER_SS.getReports()); TimeUtil.shortWait();
	 * sendKeys2(CourseNameTextBox,
	 * Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
	 * CommonStrings.sendKeys_AC.getCommonStrings(),
	 * CommonStrings.sendKeys_AR.getCommonStrings(),
	 * Reports.EmployeeName_SS.getReports()); click3(viewReport,
	 * Reports.ClickViewReport_DC.getReports(),
	 * Reports.ClickViewReport_AC.getReports(),
	 * Reports.ClickViewReport_AR.getReports(),
	 * Reports.ClickViewReport_SS.getReports()); WebElement element =
	 * wait.until(ExpectedConditions .visibilityOfElementLocated(By.xpath(
	 * "//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"))); String text =
	 * element.getText();
	 * 
	 * ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);
	 * 
	 * element.click(); TimeUtil.mediumWait(); TimeUtil.mediumWait();
	 * 
	 * WebElement table = driver.findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); List<WebElement> rows = table.findElements(By.tagName("tr")); int
	 * withoutdec = rows.size(); int employeecount = rows.size() - 2;
	 * 
	 * columnData1Before = new String[44];
	 * 
	 * for (int j = 2; j <= rows.size() - 1; j++) {
	 * 
	 * WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
	 * WebElement div = cell.findElement(By.xpath(".//div/div"));
	 * 
	 * columnData1Before[j - 2] = div.getText();
	 * 
	 * }
	 * 
	 * WebElement getPages = driver .findElement(By.xpath(
	 * "//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
	 * String getPagesCount = getPages.getText(); int getPagesCountNumeric =
	 * Integer.parseInt(getPagesCount); if (getPagesCountNumeric > 1) { for (int i =
	 * 1; i <= getPagesCountNumeric; i++) { WebElement nextpage =
	 * driver.findElement(By.xpath("//div[@title='Next Page']")); String value =
	 * nextpage.getAttribute("style"); if (!value.equals("display: none;")) {
	 * TimeUtil.mediumWait(); WebElement newNext = driver.findElement(By.xpath(
	 * "//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"
	 * )); TimeUtil.mediumWait(); newNext.click(); TimeUtil.mediumWait(); WebElement
	 * table2 = driver.findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); List<WebElement> rows2 = table2.findElements(By.tagName("tr")); int
	 * withoutdec2 = rows2.size(); int employeecount2 = rows2.size() - 2;
	 * 
	 * columnData2Before = new String[44];
	 * 
	 * for (int j = 2; j <= rows2.size() - 1; j++) {
	 * 
	 * WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
	 * WebElement div = cell.findElement(By.xpath(".//div/div"));
	 * 
	 * columnData2Before[j - 2] = div.getText();
	 * 
	 * }
	 * 
	 * } }
	 * 
	 * } else { columnData2Before = null;
	 * 
	 * } TimeUtil.mediumWait(); blankMethod(columnData1Before, columnData2Before);
	 * 
	 * // To Be Planned Data After keeping Employees in Diferent states in Course //
	 * Session Vs Course Session Report compareCount(CM_VerifyCourseSessionScreen.
	 * ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
	 * ToBePlannedCountAfterRespondDocumentReading,
	 * "To planned Count in Course Session Screen after Course Retraining",
	 * "To planned Count in CSR after Course Retraining");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.
	 * ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection, combinedArrayBefore,
	 * "To planned Data in Course Session Screen after Course Retraining",
	 * "To planned Data in CSR after Course Retraining");
	 * 
	 * compareCount(ToBePlannedCountAfterRespondDocumentReading,
	 * combinedArrayBefore.length,
	 * "Outisde To Be Planned Count in CSR  after Course Retraining",
	 * "Inside To Be Planned Count in CSR  after Course Retraining");
	 * 
	 * switchToDefaultContent(driver); switchToBodyFrame(driver);
	 * TimeUtil.mediumWait(); waitForElementVisibile(BackButton);
	 * BackButton.click(); TimeUtil.mediumWait(); enterToSSRSShadowRoot();
	 * convertString_To_Integer(Skipped.getText()); waitForElementVisibile(Skipped);
	 * Skipped.click(); AfterRecordAttendnaceSkippedCountOurside = stringToInt;
	 * TimeUtil.mediumWait();
	 * 
	 * WebElement SkippedTable = driver .findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); List<WebElement> skippedrows =
	 * SkippedTable.findElements(By.tagName("tr")); int Skippedwithoutdec =
	 * skippedrows.size(); // int employeecount = rows.size() - 2; String[]
	 * RawSkippedEmployeesData = new String[Skippedwithoutdec + 1]; for (int j = 2;
	 * j <= skippedrows.size() - 1; j++) { WebElement cell =
	 * skippedrows.get(j).findElements(By.tagName("td")).get(1); WebElement div =
	 * cell.findElement(By.xpath(".//div/div"));
	 * 
	 * RawSkippedEmployeesData[j - 2] = div.getText();
	 * 
	 * } ArrayList<String> list = new
	 * ArrayList<>(Arrays.asList(RawSkippedEmployeesData));
	 * 
	 * // Remove null values list.removeIf(item -> item == null);
	 * 
	 * // Convert back to array if needed SkippedData = list.toArray(new String[0]);
	 * 
	 * compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.
	 * length, AfterRecordAttendnaceSkippedCountOurside,
	 * "Skipped Count in Course Session Screen after keeping employes in different states"
	 * , "Skipped Count in CSR Report after keeping employes in different states");
	 * 
	 * compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
	 * "Outiside Skipped Count in CSR After Keeping employees in different states",
	 * "Inside Skipped Count in CSR After Keeping employees in different states");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.
	 * afterSelectingSubgroupSkippedData, SkippedData,
	 * "Skipped Data at Course Session after Course Retraining",
	 * "Skipped Data at CSR after Course Retraining");
	 * 
	 * switchToDefaultContent(driver); switchToBodyFrame(driver);
	 * waitForElementVisibile(BackButton);
	 * 
	 * BackButton.click(); TimeUtil.mediumWait();
	 * 
	 * enterToSSRSShadowRoot();
	 * 
	 * convertString_To_Integer(Absent.getText()); waitForElementVisibile(Absent);
	 * Absent.click(); AfterRecordAttendanceAbsentOutsideCount = stringToInt;
	 * 
	 * TimeUtil.mediumWait();
	 * 
	 * WebElement AbsentTable = driver .findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
	 * int Absentwithoutdec = Absentrows.size(); String[] RawAbsentEmployeesData =
	 * new String[Absentwithoutdec + 1]; for (int j = 2; j <= Absentrows.size() - 1;
	 * j++) { WebElement cell =
	 * Absentrows.get(j).findElements(By.tagName("td")).get(1); WebElement div =
	 * cell.findElement(By.xpath(".//div/div"));
	 * 
	 * RawAbsentEmployeesData[j - 2] = div.getText();
	 * 
	 * } ArrayList<String> list2 = new
	 * ArrayList<>(Arrays.asList(RawAbsentEmployeesData));
	 * 
	 * // Remove null values list2.removeIf(item -> item == null);
	 * 
	 * // Convert back to array if needed AbsentData = list2.toArray(new String[0]);
	 * AbsentInsideCount = AbsentData.length;
	 * 
	 * compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.
	 * length, AfterRecordAttendanceAbsentOutsideCount,
	 * "Absent Count in Course Session Screen after Course Retraining",
	 * "Absent Count in CSR Screen after Course Retraining");
	 * 
	 * compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
	 * "Outiside Absent Count in CSR after Course Retraining",
	 * "Inside Absent Count in CSR after Course Retraining");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.
	 * afterSelectingSubgroupAbsentData, AbsentData,
	 * "Absent Data in Course Session Screen after Course Retraining",
	 * "Absent Data in CSR Report after Course Retraining");
	 * 
	 * switchToDefaultContent(driver); switchToBodyFrame(driver);
	 * TimeUtil.mediumWait(); waitForElementVisibile(BackButton);
	 * BackButton.click(); TimeUtil.mediumWait(); enterToSSRSShadowRoot();
	 * convertString_To_Integer(ToBeRetrained.getText());
	 * waitForElementVisibile(ToBeRetrained); click2(ToBeRetrained, "", "", "", "");
	 * toBeRetrainedCountAtCSReport = stringToInt; TimeUtil.mediumWait();
	 * 
	 * WebElement TBRTable = driver .findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr")); int
	 * tBRwithoutdec = TBRRows.size(); // int employeecount = rows.size() - 2;
	 * String[] RawTBRData = new String[tBRwithoutdec + 1];
	 * 
	 * for (int j = 2; j <= TBRRows.size() - 1; j++) {
	 * 
	 * WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
	 * WebElement div = cell.findElement(By.xpath(".//div/div"));
	 * 
	 * RawTBRData[j - 2] = div.getText();
	 * 
	 * } ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));
	 * 
	 * // Remove null values list4.removeIf(item -> item == null); toBeRetrainedData
	 * = list4.toArray(new String[0]);
	 * 
	 * compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length,
	 * toBeRetrainedCountAtCSReport,
	 * "To Be Retrained Count in Course Session Screen after Course Retraining",
	 * "To Be Retrained Count in CSR Report after Course Retraining");
	 * 
	 * compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
	 * "Outiside To Be Retrained Count in CSR after Course Retraining",
	 * "Inside To Be Retrained Count in CSR after Course Retraining");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP,
	 * toBeRetrainedData,
	 * "To Be Retrained Data displayed at Course Session after Course Retraining",
	 * "To Be Retrained Data in CSR Reportafter Course Retraining");
	 * 
	 * // switchToDefaultContent(driver); // switchToBodyFrame(driver); //
	 * TimeUtil.mediumWait(); // waitForElementVisibile(BackButton); //
	 * BackButton.click(); // TimeUtil.mediumWait(); // enterToSSRSShadowRoot(); //
	 * convertString_To_Integer(Qualified.getText()); //
	 * waitForElementVisibile(Qualified); // Qualified.click(); //
	 * qualifiedCountDisplayedAtCourseSessionReport = stringToInt; //
	 * TimeUtil.mediumWait(); // // WebElement Qualifiedtable = driver //
	 * .findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); // List<WebElement> Qualifiedrows =
	 * Qualifiedtable.findElements(By.tagName("tr")); // int Qualifiedwithoutdec =
	 * Qualifiedrows.size(); // String[] RawQualifiedEmployeesData = new
	 * String[Qualifiedwithoutdec + 1]; // // for (int j = 2; j <=
	 * Qualifiedrows.size() - 1; j++) { // // WebElement cell =
	 * Qualifiedrows.get(j).findElements(By.tagName("td")).get(1); // WebElement div
	 * = cell.findElement(By.xpath(".//div/div")); // // RawQualifiedEmployeesData[j
	 * - 2] = div.getText(); // // } // ArrayList<String> list3 = new
	 * ArrayList<>(Arrays.asList(RawQualifiedEmployeesData)); // // // Remove null
	 * values // list3.removeIf(item -> item == null); // // // Convert back to
	 * array if needed // QualifiedEmployeesData = list3.toArray(new String[0]); //
	 * // // compareCount(qualifiedCountDisplayedAtCourseSessionReport,
	 * QualifiedEmployeesData.length, //
	 * "Outiside Qualified Count in CSR After Course Retraining", //
	 * "Inside Qualified Count in CSR After Course Retraining"); // //
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData,
	 * QualifiedEmployeesData, // "Actual Qualified Count",
	 * "Qualified Count in CSR Report After Course Retraining"); // //
	 * compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,
	 * qualifiedCountDisplayedAtCourseSessionReport, // "Actual Qualified Data", //
	 * "Qualified Count in CSR After Course Retraining");
	 * 
	 * switchToDefaultContent(driver); switchToBodyFrame(driver);
	 * TimeUtil.mediumWait(); waitForElementVisibile(BackButton);
	 * BackButton.click(); TimeUtil.mediumWait(); enterToSSRSShadowRoot();
	 * waitForElementVisibile(TotalTrainees);
	 * convertString_To_Integer(TotalTrainees.getText().trim()); totalTrainessCount
	 * = stringToInt;
	 * 
	 * compareCount(totalTrainessCount, toBeRetrainedData.length +
	 * combinedArrayBefore.length + SkippedData.length + AbsentData.length,
	 * "Total Trainees Count in CSR After Course Retraining",
	 * "Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");
	 * 
	 * // convertString_To_Integer(Retake.getText()); //
	 * waitForElementVisibile(Retake); // Retake.click(); // retakeCount =
	 * stringToInt; // TimeUtil.mediumWait(); // // WebElement Retaketable = driver
	 * // .findElement(By.xpath(
	 * "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"
	 * )); // List<WebElement> retakerows =
	 * Retaketable.findElements(By.tagName("tr")); // int retakewithoutdec =
	 * retakerows.size(); // String[] RetakeEmployeesData = new
	 * String[retakewithoutdec + 1]; // // for (int j = 2; j <= retakerows.size() -
	 * 1; j++) { // // WebElement cell =
	 * retakerows.get(j).findElements(By.tagName("td")).get(1); // WebElement div =
	 * cell.findElement(By.xpath(".//div/div")); // // RetakeEmployeesData[j - 2] =
	 * div.getText(); // // } // ArrayList<String> list5 = new
	 * ArrayList<>(Arrays.asList(RetakeEmployeesData)); // // // Remove null values
	 * // list5.removeIf(item -> item == null); // // // Convert back to array if
	 * needed // retakeData = list5.toArray(new String[0]); // //
	 * compareCount(CM_VerifyCourseSessionScreen.retakeData.length,retakeCount, //
	 * "Retake Count at Course Session screen after keeping employee in different states"
	 * , // "Retake Count at CSR after keeping employee in different states"); // //
	 * // compareCount(retakeCount, retakeData.length, //
	 * "Outiside Retake Count in CSR After Keeping employees in different states",
	 * // "Inside Retake Count in CSR After Keeping employees in different states");
	 * // // toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData,
	 * retakeData, // "Actual Retake Data",
	 * "Retake Data in CSR Report after keping employees in different states");
	 * switchToDefaultContent(driver); }
	 */

//	public void TBPCSRReport_IT_WithAssesment_After_CourseRetraining() {
//		CM_VerifyCourseSessionScreen.SessionStatus = "";
//		String CourseName = CM_Course.getCourse();
//
//		// CourseName = "AutomationCourseLZRW";
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		scrollToViewElement(courseManagerReportsMenu);
//		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
//				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
//		scrollToViewElement(CSRReportmENU);
//		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
//				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		switchToBodyFrame(driver);
//		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		WebElement element3 = (WebElement) js.executeScript(strt);
//		driver.switchTo().frame(element3);
//
//		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));
//
//		// Wait for the element with the text "Course Sessions Report (Complete)"
//		WebElement elementcsrtext = wait.until(ExpectedConditions
//				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));
//
//		WebElement button = driver.findElement(
//				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
//		waitForElementVisibile(button);
//		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
//				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
//				Reports.Click_ToggleBtn_SS.getReports());
//		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
//				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
//				Reports.UnselectEmployeeID_IER_SS.getReports());
//		TimeUtil.shortWait();
//		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				Reports.EmployeeName_SS.getReports());
//		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
//				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
//		WebElement element = wait.until(ExpectedConditions
//				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
//		String text = element.getText();
//
//		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);
//
//		element.click();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//
//		WebElement table = driver.findElement(By.xpath(
//				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//		int withoutdec = rows.size();
//		int employeecount = rows.size() - 2;
//
//		columnData1Before = new String[44];
//
//		for (int j = 2; j <= rows.size() - 1; j++) {
//
//			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			columnData1Before[j - 2] = div.getText();
//
//		}
//
//		WebElement getPages = driver
//				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
//		String getPagesCount = getPages.getText();
//		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
//		if (getPagesCountNumeric > 1) {
//			for (int i = 1; i <= getPagesCountNumeric; i++) {
//				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
//				String value = nextpage.getAttribute("style");
//				if (!value.equals("display: none;")) {
//					TimeUtil.mediumWait();
//					WebElement newNext = driver.findElement(By.xpath(
//							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
//					TimeUtil.mediumWait();
//					newNext.click();
//					TimeUtil.mediumWait();
//					WebElement table2 = driver.findElement(By.xpath(
//							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
//					int withoutdec2 = rows2.size();
//					int employeecount2 = rows2.size() - 2;
//
//					columnData2Before = new String[44];
//
//					for (int j = 2; j <= rows2.size() - 1; j++) {
//
//						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
//						WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//						columnData2Before[j - 2] = div.getText();
//
//					}
//
//				}
//			}
//
//		} else {
//			columnData2Before = null;
//
//		}
//		TimeUtil.mediumWait();
//		blankMethod(columnData1Before, columnData2Before);
//
//		// To Be Planned Data After keeping Employees in Diferent states in Course
//		// Session Vs Course Session Report
//		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
//				ToBePlannedCountAfterRespondDocumentReading,
//				"To planned Count in Course Session Screen after Course Retraining",
//				"To planned Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
//				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
//				"To planned Data in CSR after Course Retraining");
//
//		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
//				"Outisde To Be Planned Count in CSR  after Course Retraining",
//				"Inside To Be Planned Count in CSR  after Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(Skipped.getText());
//		waitForElementVisibile(Skipped);
//		Skipped.click();
//		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement SkippedTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
//		int Skippedwithoutdec = skippedrows.size();
//		// int employeecount = rows.size() - 2;
//		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
//		for (int j = 2; j <= skippedrows.size() - 1; j++) {
//			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawSkippedEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));
//
//		// Remove null values
//		list.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		SkippedData = list.toArray(new String[0]);
//
//		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.length,
//				AfterRecordAttendnaceSkippedCountOurside,
//				"Skipped Count in Course Session Screen after keeping employes in different states",
//				"Skipped Count in CSR Report after keeping employes in different states");
//
//		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
//				"Outiside Skipped Count in CSR After Keeping employees in different states",
//				"Inside Skipped Count in CSR After Keeping employees in different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData, SkippedData,
//				"Skipped Data at Course Session after Course Retraining",
//				"Skipped Data at CSR after Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		waitForElementVisibile(BackButton);
//
//		BackButton.click();
//		TimeUtil.mediumWait();
//
//		enterToSSRSShadowRoot();
//
//		convertString_To_Integer(Absent.getText());
//		waitForElementVisibile(Absent);
//		Absent.click();
//		AfterRecordAttendanceAbsentOutsideCount = stringToInt;
//
//		TimeUtil.mediumWait();
//
//		WebElement AbsentTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
//		int Absentwithoutdec = Absentrows.size();
//		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
//		for (int j = 2; j <= Absentrows.size() - 1; j++) {
//			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawAbsentEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));
//
//		// Remove null values
//		list2.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		AbsentData = list2.toArray(new String[0]);
//		AbsentInsideCount = AbsentData.length;
//
//		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.length,
//				AfterRecordAttendanceAbsentOutsideCount,
//				"Absent Count in Course Session Screen after Course Retraining",
//				"Absent Count in CSR Screen after Course Retraining");
//
//		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
//				"Outiside Absent Count in CSR after Course Retraining",
//				"Inside Absent Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData, AbsentData,
//				"Absent Data in Course Session Screen after Course Retraining",
//				"Absent Data in CSR Report after Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(ToBeRetrained.getText());
//		waitForElementVisibile(ToBeRetrained);
//		click2(ToBeRetrained, "", "", "", "");
//		toBeRetrainedCountAtCSReport = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement TBRTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
//		int tBRwithoutdec = TBRRows.size();
//		// int employeecount = rows.size() - 2;
//		String[] RawTBRData = new String[tBRwithoutdec + 1];
//
//		for (int j = 2; j <= TBRRows.size() - 1; j++) {
//
//			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawTBRData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));
//
//		// Remove null values
//		list4.removeIf(item -> item == null);
//		toBeRetrainedData = list4.toArray(new String[0]);
//
//		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,
//				"To Be Retrained Count in Course Session Screen after Course Retraining",
//				"To Be Retrained Count in CSR Report after Course Retraining");
//
//		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
//				"Outiside To Be Retrained Count in CSR after Course Retraining",
//				"Inside To Be Retrained Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
//				"To Be Retrained Data displayed at Course Session after Course Retraining",
//				"To Be Retrained Data in CSR Reportafter Course Retraining");
//
////		switchToDefaultContent(driver);
////		switchToBodyFrame(driver);
////		TimeUtil.mediumWait();
////		waitForElementVisibile(BackButton);
////		BackButton.click();
////		TimeUtil.mediumWait();
////		enterToSSRSShadowRoot();
////		convertString_To_Integer(Qualified.getText());
////		waitForElementVisibile(Qualified);
////		Qualified.click();
////		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
////		TimeUtil.mediumWait();
////
////		WebElement Qualifiedtable = driver
////				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
////		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
////		int Qualifiedwithoutdec = Qualifiedrows.size();
////		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
////
////		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
////
////			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
////			WebElement div = cell.findElement(By.xpath(".//div/div"));
////
////			RawQualifiedEmployeesData[j - 2] = div.getText();
////
////		}
////		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
////
////		// Remove null values
////		list3.removeIf(item -> item == null);
////
////		// Convert back to array if needed
////		QualifiedEmployeesData = list3.toArray(new String[0]);
////
////		
////		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
////				"Outiside Qualified Count in CSR After Course Retraining",
////				"Inside Qualified Count in CSR After Course Retraining");
////
////		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
////				"Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");
////		
////		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,
////				"Actual Qualified Data",
////				"Qualified Count in CSR After Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		waitForElementVisibile(TotalTrainees);
//		convertString_To_Integer(TotalTrainees.getText().trim());
//		totalTrainessCount = stringToInt;
//
//		compareCount(totalTrainessCount,
//				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length,
//				"Total Trainees Count in CSR After Course Retraining",
//				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");
//
//		switchToDefaultContent(driver);
//	}
	
	
	public void TBPCSRReport_IT_WithAssesment_After_CourseRetraining() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
//		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
//				ToBePlannedCountAfterRespondDocumentReading,
//				"To planned Count in Course Session Screen after Course Retraining",
//				"To planned Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
//				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
//				"To planned Data in CSR after Course Retraining");
//
//		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
//				"Outisde To Be Planned Count in CSR  after Course Retraining",
//				"Inside To Be Planned Count in CSR  after Course Retraining");

		
		
		
		
		
		
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length + 1,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		String[] combined = Stream.concat(
				Arrays.stream(
						CM_VerifyCourseSessionScreen.To_Be_Planned_Data_After_Selecting_Subgroup_AfterCourseRetraining),
				Arrays.stream(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection))
				.toArray(String[]::new);

		toBePlannedCountData(combined, combinedArrayBefore,
				"To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");
		
		
		
		
		
		
		
		
		
		
		
		
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.length,
				AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData, SkippedData,
				"Skipped Data at Course Session after Course Retraining",
				"Skipped Data at CSR after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.length,
				AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after Course Retraining",
				"Absent Count in CSR Screen after Course Retraining");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR after Course Retraining",
				"Inside Absent Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData, AbsentData,
				"Absent Data in Course Session Screen after Course Retraining",
				"Absent Data in CSR Report after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after Course Retraining",
				"To Be Retrained Count in CSR Report after Course Retraining");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR after Course Retraining",
				"Inside To Be Retrained Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
				"To Be Retrained Data displayed at Course Session after Course Retraining",
				"To Be Retrained Data in CSR Reportafter Course Retraining");

//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(Qualified.getText());
//		waitForElementVisibile(Qualified);
//		Qualified.click();
//		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement Qualifiedtable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
//		int Qualifiedwithoutdec = Qualifiedrows.size();
//		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
//
//		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
//
//			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawQualifiedEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
//
//		// Remove null values
//		list3.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		QualifiedEmployeesData = list3.toArray(new String[0]);
//
//		
//		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
//				"Outiside Qualified Count in CSR After Course Retraining",
//				"Inside Qualified Count in CSR After Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
//				"Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");
//		
//		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,
//				"Actual Qualified Data",
//				"Qualified Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_Unsch_IT_OffflineWithoutAssesment_Verbal_After_CourseRetraining() {

		CM_VerifyCourseSessionScreen.SessionStatus = "";

		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),

				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),

				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),

				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(courseManagerReportsMenu);

		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),

				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());

		scrollToViewElement(CSRReportmENU);

		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),

				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();

		switchToBodyFrame(driver);

		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";

		JavascriptExecutor js = (JavascriptExecutor) driver;

		WebElement element3 = (WebElement) js.executeScript(strt);

		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"

		WebElement elementcsrtext = wait.until(ExpectedConditions

				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(

				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));

		waitForElementVisibile(button);

		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),

				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),

				Reports.Click_ToggleBtn_SS.getReports());

		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),

				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),

				Reports.UnselectEmployeeID_IER_SS.getReports());

		TimeUtil.shortWait();

		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,

				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),

				Reports.EmployeeName_SS.getReports());

		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),

				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());

		WebElement element = wait.until(ExpectedConditions

				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));

		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(

				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

		List<WebElement> rows = table.findElements(By.tagName("tr"));

		int withoutdec = rows.size();

		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);

			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver

				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));

		String getPagesCount = getPages.getText();

		int getPagesCountNumeric = Integer.parseInt(getPagesCount);

		if (getPagesCountNumeric > 1) {

			for (int i = 1; i <= getPagesCountNumeric; i++) {

				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));

				String value = nextpage.getAttribute("style");

				if (!value.equals("display: none;")) {

					TimeUtil.mediumWait();

					WebElement newNext = driver.findElement(By.xpath(

							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));

					TimeUtil.mediumWait();

					newNext.click();

					TimeUtil.mediumWait();

					WebElement table2 = driver.findElement(By.xpath(

							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));

					int withoutdec2 = rows2.size();

					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);

						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}

			}

		} else {

			columnData2Before = null;

		}

		TimeUtil.mediumWait();

		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course

		// Session Vs Course Session Report

		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,

				ToBePlannedCountAfterRespondDocumentReading,

				"To planned Count in Course Session Screen after Course Retraining",

				"To planned Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,

				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",

				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,

				"Outisde To Be Planned Count in CSR  after Course Retraining",

				"Inside To Be Planned Count in CSR  after Course Retraining");

		switchToDefaultContent(driver);

		switchToBodyFrame(driver);

		TimeUtil.mediumWait();

		waitForElementVisibile(BackButton);

		BackButton.click();

		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Skipped.getText());

		waitForElementVisibile(Skipped);

		Skipped.click();

		AfterRecordAttendnaceSkippedCountOurside = stringToInt;

		TimeUtil.mediumWait();

		WebElement SkippedTable = driver

				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));

		int Skippedwithoutdec = skippedrows.size();

		// int employeecount = rows.size() - 2;

		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];

		for (int j = 2; j <= skippedrows.size() - 1; j++) {

			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);

			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}

		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values

		list.removeIf(item -> item == null);

		// Convert back to array if needed

		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.length,

				AfterRecordAttendnaceSkippedCountOurside,

				"Skipped Count in Course Session Screen after keeping employes in different states",

				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,

				"Outiside Skipped Count in CSR After Keeping employees in different states",

				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData, SkippedData,

				"Skipped Data at Course Session after Course Retraining",

				"Skipped Data at CSR after Course Retraining");

		switchToDefaultContent(driver);

		switchToBodyFrame(driver);

		waitForElementVisibile(BackButton);

		BackButton.click();

		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());

		waitForElementVisibile(Absent);

		Absent.click();

		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver

				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));

		int Absentwithoutdec = Absentrows.size();

		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];

		for (int j = 2; j <= Absentrows.size() - 1; j++) {

			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);

			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}

		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values

		list2.removeIf(item -> item == null);

		// Convert back to array if needed

		AbsentData = list2.toArray(new String[0]);

		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.length,

				AfterRecordAttendanceAbsentOutsideCount,

				"Absent Count in Course Session Screen after Course Retraining",

				"Absent Count in CSR Screen after Course Retraining");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,

				"Outiside Absent Count in CSR after Course Retraining",

				"Inside Absent Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData, AbsentData,

				"Absent Data in Course Session Screen after Course Retraining",

				"Absent Data in CSR Report after Course Retraining");

		switchToDefaultContent(driver);

		switchToBodyFrame(driver);

		TimeUtil.mediumWait();

		waitForElementVisibile(BackButton);

		BackButton.click();

		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(ToBeRetrained.getText());

		waitForElementVisibile(ToBeRetrained);

		click2(ToBeRetrained, "", "", "", "");

		toBeRetrainedCountAtCSReport = stringToInt;

		TimeUtil.mediumWait();

		WebElement TBRTable = driver

				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));

		int tBRwithoutdec = TBRRows.size();

		// int employeecount = rows.size() - 2;

		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);

			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}

		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values

		list4.removeIf(item -> item == null);

		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,

				"To Be Retrained Count in Course Session Screen after Course Retraining",

				"To Be Retrained Count in CSR Report after Course Retraining");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,

				"Outiside To Be Retrained Count in CSR after Course Retraining",

				"Inside To Be Retrained Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,

				"To Be Retrained Data displayed at Course Session after Course Retraining",

				"To Be Retrained Data in CSR Reportafter Course Retraining");

//      switchToDefaultContent(driver);

//      switchToBodyFrame(driver);

//      TimeUtil.mediumWait();

//      waitForElementVisibile(BackButton);

//      BackButton.click();

//      TimeUtil.mediumWait();

//      enterToSSRSShadowRoot();

//      convertString_To_Integer(Qualified.getText());

//      waitForElementVisibile(Qualified);

//      Qualified.click();

//      qualifiedCountDisplayedAtCourseSessionReport = stringToInt;

//      TimeUtil.mediumWait();

//

//      WebElement Qualifiedtable = driver

//              .findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));

//      List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));

//      int Qualifiedwithoutdec = Qualifiedrows.size();

//      String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

//

//      for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

//

//          WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);

//          WebElement div = cell.findElement(By.xpath(".//div/div"));

//

//          RawQualifiedEmployeesData[j - 2] = div.getText();

//

//      }

//      ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

//

//      // Remove null values

//      list3.removeIf(item -> item == null);

//

//      // Convert back to array if needed

//      QualifiedEmployeesData = list3.toArray(new String[0]);

//

//      

//      compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,

//              "Outiside Qualified Count in CSR After Course Retraining",

//              "Inside Qualified Count in CSR After Course Retraining");

//

//      toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,

//              "Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");

//      

//      compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,

//              "Actual Qualified Data",

//              "Qualified Count in CSR After Course Retraining");

		switchToDefaultContent(driver);

		switchToBodyFrame(driver);

		TimeUtil.mediumWait();

		waitForElementVisibile(BackButton);

		BackButton.click();

		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		waitForElementVisibile(TotalTrainees);

		convertString_To_Integer(TotalTrainees.getText().trim());

		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,

				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length,

				"Total Trainees Count in CSR After Course Retraining",

				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

		switchToDefaultContent(driver);

	}

	public void TBPCSRReport_IT_WithAssesment_After_CourseRetraining_Sch() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length + 1,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		String[] combined = Stream.concat(
				Arrays.stream(
						CM_VerifyCourseSessionScreen.To_Be_Planned_Data_After_Selecting_Subgroup_AfterCourseRetraining),
				Arrays.stream(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection))
				.toArray(String[]::new);

		toBePlannedCountData(combined, combinedArrayBefore,
				"To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.length,
				AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData, SkippedData,
				"Skipped Data at Course Session after Course Retraining",
				"Skipped Data at CSR after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.length,
				AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after Course Retraining",
				"Absent Count in CSR Screen after Course Retraining");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR after Course Retraining",
				"Inside Absent Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData, AbsentData,
				"Absent Data in Course Session Screen after Course Retraining",
				"Absent Data in CSR Report after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after Course Retraining",
				"To Be Retrained Count in CSR Report after Course Retraining");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR after Course Retraining",
				"Inside To Be Retrained Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
				"To Be Retrained Data displayed at Course Session after Course Retraining",
				"To Be Retrained Data in CSR Reportafter Course Retraining");

//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(Qualified.getText());
//		waitForElementVisibile(Qualified);
//		Qualified.click();
//		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement Qualifiedtable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
//		int Qualifiedwithoutdec = Qualifiedrows.size();
//		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
//
//		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
//
//			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawQualifiedEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
//
//		// Remove null values
//		list3.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		QualifiedEmployeesData = list3.toArray(new String[0]);
//
//		
//		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
//				"Outiside Qualified Count in CSR After Course Retraining",
//				"Inside Qualified Count in CSR After Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
//				"Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");
//		
//		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,
//				"Actual Qualified Data",
//				"Qualified Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

//		convertString_To_Integer(Retake.getText());
//		waitForElementVisibile(Retake);
//		Retake.click();
//		retakeCount = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement Retaketable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
//		int retakewithoutdec = retakerows.size();
//		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];
//
//		for (int j = 2; j <= retakerows.size() - 1; j++) {
//
//			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RetakeEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));
//
//		// Remove null values
//		list5.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		retakeData = list5.toArray(new String[0]);
//		
//		compareCount(CM_VerifyCourseSessionScreen.retakeData.length,retakeCount,
//				"Retake Count at Course Session screen after keeping employee in different states",
//				"Retake Count at CSR after keeping employee in different states");
//
//		
//		compareCount(retakeCount, retakeData.length,
//				"Outiside Retake Count in CSR After Keeping employees in different states",
//				"Inside Retake Count in CSR After Keeping employees in different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData,
//				"Actual Retake Data", "Retake Data in CSR Report after keping employees in different states");
		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_IT_Offline_WithAssesment_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}
		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				ToBePlannedCountAfterRespondDocumentReading,
				"To Be Planned count is displayed on the Course Session screen after assigning employees to different states",
				"To be planned count is displayed in the CSR report after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData, combinedArrayBefore,
				"To be planned data is displayed in the Course Session after assigning employees to different states",
				"To be planned data is displayed in the CSR after assigning employees to different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"To be planned outside count is displayed in the CSR after assigning employees to different states",
				"To be planned inside count is displayed in the CSR after assigning employees to different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
				"Skipped count is displayed on the Course Session screen after assigning employees to different states",
				"Skipped count is displayed in the CSR report after assigning employees to different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Skipped outside count is displayed in the CSR after assigning employees to different states",
				"Skipped inside count is displayed in the CSR after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData, "Actual Skipped Data",
				"Skipped data is displayed in the CSR report after assigning employees to different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
				"Absent count is displayed on the Course Session screen after assigning employees to different states",
				"Absent count is displayed in the CSR report after assigning employees to different states");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Absent outside count is displayed in the CSR after assigning employees to different states",
				"Absent inside count is displayed in the CSR after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData, "Actual Absent Data",
				"Absent data is displayed in the CSR report after assigning employees to different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedCountBeforeSGP, toBeRetrainedCountAtCSReport,
				"To be retrained count is displayed on the Course Session screen after assigning employees to different states",
				"To be retrained count is displayed in the CSR report after assigning employees to different states");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"To be retrained outside count is displayed in the CSR after assigning employees to different states",
				"To be retrained inside count is displayed in the CSR after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"To be retrained data is displayed on the Course Session screen after assigning employees to different states",
				"To be retrained data is displayed in the CSR report after assigning employees to different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Qualified outside count is displayed in the CSR after assigning employees to different states",
				"Qualified inside count is displayed in the CSR after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"The actual qualified count",
				"Qualified count is displayed in the CSR report after assigning employees to different states");

		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length, qualifiedCountDisplayedAtCourseSessionReport,
				"The actual qualified data",
				"Qualified count is displayed in the CSR after assigning employees to different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length
						+ QualifiedEmployeesData.length,
				"total trainees  outside  count is displayed in the CSR after course retraining",
				"The sum of total to be planned, to be retrained, skipped, absent, and qualified");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_IT_WithOut_Assesment_Evaulation_Verbal_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData, combinedArrayBefore,
				"To Be Planned Data in Course Session After Keeping employees in different states",
				"To Be Planned Data in CSR After keepi ng employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After keeping employees in different states",
				"Inside To Be Planned Count in CSR After keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData, "Actual Skipped Data",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after keeping employes in different states",
				"Absent Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR After Keeping employees in different states",
				"Inside Absent Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData, "Actual Absent Data",
				"Absent Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedCountBeforeSGP, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after keeping employes in different states",
				"To Be Retrained Count in CSR Report after keeping employes in different states");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR After Keeping employees in different states",
				"Inside To Be Retrained Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"Actual To Be Retrained Data After keeping employees in different states",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count in CSR After Keeping employees in different states",
				"Inside Qualified Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length, qualifiedCountDisplayedAtCourseSessionReport,
				"Actual Qualified Data", "Qualified Count in CSR After Keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;
		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + SkippedData.length + AbsentData.length
						+ QualifiedEmployeesData.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Skipped, Absent, To Be Retrained, Qualified and Retake  ");
		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_RE_Without_Assesment_After_CourseRetraining() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		// WebElement element =
		// driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}
		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount, combinedArrayBefore.length, "Outside Total Pending Count at CSR",
				"To Be Planned Count");

		switchToDefaultContent(driver);
	}

	public void verify_CSR_After_Course_Retraining_For_Schedule_Course_RE_Without_Asessment() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}
		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_IT_WithOut_Assesment_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));

		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData, combinedArrayBefore,
				"To Be Planned Data in Course Session After Keeping employees in different states",
				"To Be Planned Data in CSR After keepi ng employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After keeping employees in different states",
				"Inside To Be Planned Count in CSR After keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
//		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
//				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData, "Actual Skipped Data",
				"Skipped Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after keeping employes in different states",
				"Absent Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR After Keeping employees in different states",
				"Inside Absent Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData, "Actual Absent Data",
				"Absent Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count in CSR After Keeping employees in different states",
				"Inside Qualified Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		compareCount(CM_VerifyCourseSessionScreen.QualifiedUser.length, qualifiedCountDisplayedAtCourseSessionReport,
				"Actual Qualified Data", "Qualified Count in CSR After Keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;
		compareCount(totalTrainessCount,
				combinedArrayBefore.length + SkippedData.length + AbsentData.length + QualifiedEmployeesData.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Skipped, Absent,  Qualified ");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + +AfterRecordAttendnaceSkippedCountOurside
						+ AfterRecordAttendanceAbsentOutsideCount,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Skipped, Absent");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_IT_WithOutAssesment_After_CourseRetraining() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData.length,
				AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after keeping employes in different states",
				"Skipped Count in CSR Report after keeping employes in different states");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR After Keeping employees in different states",
				"Inside Skipped Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupSkippedData, SkippedData,
				"Skipped Data at Course Session after Course Retraining",
				"Skipped Data at CSR after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData.length,
				AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after Course Retraining",
				"Absent Count in CSR Screen after Course Retraining");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR after Course Retraining",
				"Inside Absent Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.afterSelectingSubgroupAbsentData, AbsentData,
				"Absent Data in Course Session Screen after Course Retraining",
				"Absent Data in CSR Report after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length + SkippedData.length + AbsentData.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

		switchToDefaultContent(driver);
	}

//	public void TBPCSRReport_After_Course_Retraining_IT_WITH_Assessmen() {
//		CM_VerifyCourseSessionScreen.SessionStatus = "";
//		String CourseName = CM_Course.getCourse();
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		scrollToViewElement(courseManagerReportsMenu);
//		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
//				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
//		scrollToViewElement(CSRReportmENU);
//		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
//				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		switchToBodyFrame(driver);
//		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		WebElement element3 = (WebElement) js.executeScript(strt);
//		driver.switchTo().frame(element3);
//		WebElement button = driver.findElement(
//				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
//		waitForElementVisibile(button);
//		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
//				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
//				Reports.Click_ToggleBtn_SS.getReports());
//		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
//				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
//				Reports.UnselectEmployeeID_IER_SS.getReports());
//		TimeUtil.shortWait();
//		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				Reports.EmployeeName_SS.getReports());
//		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
//				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
//		TimeUtil.shortWait();
//
//		TimeUtil.mediumWait();
//		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
//		String text = element.getText();
//
//		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);
//
//		String[] combineUnsche_Schedu_ToBePlannedData = Stream
//				.concat(Arrays.stream(CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab),
//						Arrays.stream(
//								CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection))
//				.toArray(String[]::new);
//		compareCount(combineUnsche_Schedu_ToBePlannedData.length, ToBePlannedCountAfterRespondDocumentReading,
//				"To planned Count in Course Session Screen(Unsch and Sche) After Course Retraining",
//				"To be planned count is CSR after Course Retraining");
//
//		element.click();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//
//		WebElement table = driver.findElement(By.xpath(
//				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//		int withoutdec = rows.size();
//		int employeecount = rows.size() - 2;
//
//		columnData1Before = new String[44];
//
//		for (int j = 2; j <= rows.size() - 1; j++) {
//
//			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			columnData1Before[j - 2] = div.getText();
//
//		}
//
//		WebElement getPages = driver
//				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
//		String getPagesCount = getPages.getText();
//		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
//		TimeUtil.mediumWait();
//
//		for (int i = 1; i <= getPagesCountNumeric; i++) {
//			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
//			String value = nextpage.getAttribute("style");
//			TimeUtil.mediumWait();
//			if (!value.equals("display: none;")) {
//				TimeUtil.mediumWait();
//				WebElement newNext = driver.findElement(By.xpath(
//						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
//				TimeUtil.mediumWait();
//				newNext.click();
//				TimeUtil.mediumWait();
//				WebElement table2 = driver.findElement(By.xpath(
//						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
//				int withoutdec2 = rows2.size();
//				int employeecount2 = rows2.size() - 2;
//
//				columnData2Before = new String[44];
//
//				for (int j = 2; j <= rows2.size() - 1; j++) {
//
//					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
//					WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//					columnData2Before[j - 2] = div.getText();
//
//				}
//
//			}
//		}
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		blankMethod(columnData1Before, columnData2Before);
//		toBePlannedCountData(combineUnsche_Schedu_ToBePlannedData, combinedArrayBefore,
//				"To Be Planned Data from Unschedule and Schedule tabs After Course Retraining",
//				"To Be Planned Data in CSR After Course Retraining");
//
//		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
//				"Outisde To Be Planned Count in CSR After Course Retraining",
//				"Inside To Be Planned Count in CSR After Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(Skipped.getText());
//		waitForElementVisibile(Skipped);
//		Skipped.click();
//		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
////		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
////				+ AfterRecordAttendnaceSkippedCountOurside);
//		TimeUtil.mediumWait();
//
//		WebElement SkippedTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
//		int Skippedwithoutdec = skippedrows.size();
//		// int employeecount = rows.size() - 2;
//		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
//		for (int j = 2; j <= skippedrows.size() - 1; j++) {
//			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawSkippedEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));
//
//		// Remove null values
//		list.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		SkippedData = list.toArray(new String[0]);
//
//		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
//				"Skipped Count in Course Session Screen after Course Retraining",
//				"Skipped Count in CSR Report after Course Retraining");
//
//		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
//				"Outiside Skipped Count in CSR after Course Retraining",
//				"Inside Skipped Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
//				"Skipped Data at Course Session after Course Retraining",
//				"Skipped Data in CSR Report after Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		waitForElementVisibile(BackButton);
//
//		BackButton.click();
//		TimeUtil.mediumWait();
//
//		enterToSSRSShadowRoot();
//
//		convertString_To_Integer(Absent.getText());
//		waitForElementVisibile(Absent);
//		Absent.click();
//		AfterRecordAttendanceAbsentOutsideCount = stringToInt;
//
//		TimeUtil.mediumWait();
//
//		WebElement AbsentTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
//		int Absentwithoutdec = Absentrows.size();
//		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
//		for (int j = 2; j <= Absentrows.size() - 1; j++) {
//			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawAbsentEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));
//
//		// Remove null values
//		list2.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		AbsentData = list2.toArray(new String[0]);
//		AbsentInsideCount = AbsentData.length;
//
//		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
//				"Absent Count in Course Session Screen after Course Retraining",
//				"Absent Count in CSR Report after Course Retraining");
//
//		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
//				"Outiside Absent Count in CSR after Course Retraining",
//				"Inside Absent Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
//				"Absent Data at Course Session after Course Retraining",
//				"Absent Data in CSR Report after Course Retraining");
//
////		switchToDefaultContent(driver);
////		switchToBodyFrame(driver);
////		TimeUtil.mediumWait();
////		waitForElementVisibile(BackButton);
////		BackButton.click();
////		TimeUtil.mediumWait();
////		enterToSSRSShadowRoot();
////		convertString_To_Integer(Qualified.getText());
////		waitForElementVisibile(Qualified);
////		Qualified.click();
////		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
////		TimeUtil.mediumWait();
////
////		WebElement Qualifiedtable = driver
////				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
////		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
////		int Qualifiedwithoutdec = Qualifiedrows.size();
////		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
////
////		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
////
////			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
////			WebElement div = cell.findElement(By.xpath(".//div/div"));
////
////			RawQualifiedEmployeesData[j - 2] = div.getText();
////
////		}
////		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
////
////		// Remove null values
////		list3.removeIf(item -> item == null);
////
////		// Convert back to array if needed
////		QualifiedEmployeesData = list3.toArray(new String[0]);
////
////		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
////				"Outiside Qualified Count in CSR after Course Retraining.",
////				"Inside Qualified Count in CSR after Course Retraining.");
////
////		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, QualifiedEmployeesData,
////				"Actual Qualified Count", "Qualified Count in CSR Report after Course Retraining");
////
////		compareCount(CM_VerifyCourseSessionScreen.QualifiedUser.length, qualifiedCountDisplayedAtCourseSessionReport,
////				"Actual Qualified Data", "Qualified Count in CSR after Course Retraining");
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
////		
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		waitForElementVisibile(TotalTrainees);
//		convertString_To_Integer(TotalTrainees.getText().trim());
//		totalTrainessCount = stringToInt;
//
//		compareCount(totalTrainessCount, combinedArrayBefore.length + SkippedData.length + AbsentData.length,
//				"Outisde Total Trainees Count in CSR After Course Retraining", "Sum of all columns excluding Retake");
//
//		switchToDefaultContent(driver);
//	}

	public void TBPCSRReport_After_Course_Retraining_IT_WITH_Assessmen() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		String[] combineUnsche_Schedu_ToBePlannedData = Stream
				.concat(Arrays.stream(CM_VerifyCourseSessionScreen.toBePlanned_Data_after_Crs_retaining_Unscheduledtab),
						Arrays.stream(
								CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection))
				.toArray(String[]::new);
		compareCount(combineUnsche_Schedu_ToBePlannedData.length, ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen(Unsch and Sche) After Course Retraining",
				"To be planned count is CSR after Course Retraining");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				columnData2Before = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					columnData2Before[j - 2] = div.getText();

				}

			}
		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		toBePlannedCountData(combineUnsche_Schedu_ToBePlannedData, combinedArrayBefore,
				"To Be Planned Data from Unschedule and Schedule tabs After Course Retraining",
				"To Be Planned Data in CSR After Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After Course Retraining",
				"Inside To Be Planned Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Skipped.getText());
		waitForElementVisibile(Skipped);
		Skipped.click();
		AfterRecordAttendnaceSkippedCountOurside = stringToInt;
//		System.out.println("Skipped Outside Count at CSR Report after record Attedance: "
//				+ AfterRecordAttendnaceSkippedCountOurside);
		TimeUtil.mediumWait();

		WebElement SkippedTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> skippedrows = SkippedTable.findElements(By.tagName("tr"));
		int Skippedwithoutdec = skippedrows.size();
		// int employeecount = rows.size() - 2;
		String[] RawSkippedEmployeesData = new String[Skippedwithoutdec + 1];
		for (int j = 2; j <= skippedrows.size() - 1; j++) {
			WebElement cell = skippedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawSkippedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list = new ArrayList<>(Arrays.asList(RawSkippedEmployeesData));

		// Remove null values
		list.removeIf(item -> item == null);

		// Convert back to array if needed
		SkippedData = list.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, AfterRecordAttendnaceSkippedCountOurside,
				"Skipped Count in Course Session Screen after Course Retraining",
				"Skipped Count in CSR Report after Course Retraining");

		compareCount(AfterRecordAttendnaceSkippedCountOurside, SkippedData.length,
				"Outiside Skipped Count in CSR after Course Retraining",
				"Inside Skipped Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
				"Skipped Data at Course Session after Course Retraining",
				"Skipped Data in CSR Report after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		waitForElementVisibile(BackButton);

		BackButton.click();
		TimeUtil.mediumWait();

		enterToSSRSShadowRoot();

		convertString_To_Integer(Absent.getText());
		waitForElementVisibile(Absent);
		Absent.click();
		AfterRecordAttendanceAbsentOutsideCount = stringToInt;

		TimeUtil.mediumWait();

		WebElement AbsentTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Absentrows = AbsentTable.findElements(By.tagName("tr"));
		int Absentwithoutdec = Absentrows.size();
		String[] RawAbsentEmployeesData = new String[Absentwithoutdec + 1];
		for (int j = 2; j <= Absentrows.size() - 1; j++) {
			WebElement cell = Absentrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawAbsentEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list2 = new ArrayList<>(Arrays.asList(RawAbsentEmployeesData));

		// Remove null values
		list2.removeIf(item -> item == null);

		// Convert back to array if needed
		AbsentData = list2.toArray(new String[0]);
		AbsentInsideCount = AbsentData.length;

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, AfterRecordAttendanceAbsentOutsideCount,
				"Absent Count in Course Session Screen after Course Retraining",
				"Absent Count in CSR Report after Course Retraining");

		compareCount(AfterRecordAttendanceAbsentOutsideCount, AbsentData.length,
				"Outiside Absent Count in CSR after Course Retraining",
				"Inside Absent Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
				"Absent Data at Course Session after Course Retraining",
				"Absent Data in CSR Report after Course Retraining");

//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(Qualified.getText());
//		waitForElementVisibile(Qualified);
//		Qualified.click();
//		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement Qualifiedtable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
//		int Qualifiedwithoutdec = Qualifiedrows.size();
//		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
//
//		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
//
//			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawQualifiedEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
//
//		// Remove null values
//		list3.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		QualifiedEmployeesData = list3.toArray(new String[0]);
//
//		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
//				"Outiside Qualified Count in CSR after Course Retraining.",
//				"Inside Qualified Count in CSR after Course Retraining.");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, QualifiedEmployeesData,
//				"Actual Qualified Count", "Qualified Count in CSR Report after Course Retraining");
//
//		compareCount(CM_VerifyCourseSessionScreen.QualifiedUser.length, qualifiedCountDisplayedAtCourseSessionReport,
//				"Actual Qualified Data", "Qualified Count in CSR after Course Retraining");
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		
//		

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, combinedArrayBefore.length + SkippedData.length + AbsentData.length,
				"Outisde Total Trainees Count in CSR After Course Retraining", "Sum of all columns excluding Retake");

		switchToDefaultContent(driver);
	}

	public void TBPCSRReport_RE_WithAssesment_After_keping_Emplpoyees_In_Diff_States() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				TimeUtil.mediumWait();
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employes in different states",
				"To planned Count in CSR Report after keeping employes in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData, combinedArrayBefore,
				"To Be Planned Data in Course Session After Keeping employees in different states",
				"To Be Planned Data in CSR After keepi ng employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR After keeping employees in different states",
				"Inside To Be Planned Count in CSR After keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressExcluding_Retake, plannedDataAfterSession,
				"Actual Inprogress users excluding Retake",
				"Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedCountBeforeSGP, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after keeping employes in different states",
				"To Be Retrained Count in CSR Report after keeping employes in different states");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR After Keeping employees in different states",
				"Inside To Be Retrained Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedData, toBeRetrainedData,
				"Actual To Be Retrained Data After keeping employees in different states",
				"To Be Retrained Data in CSR Report after keping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));

		// Remove null values
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count in CSR After Keeping employees in different states",
				"Inside Qualified Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keping employees in different states");

		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length, qualifiedCountDisplayedAtCourseSessionReport,
				"Actual Qualified Data", "Qualified Count in CSR After Keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				toBeRetrainedData.length + combinedArrayBefore.length + QualifiedEmployeesData.length
						+ CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of Total To Be Planned, Planned, Skipped, Absent, To Be Retrained, Qualified and Retake  ");

//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
		convertString_To_Integer(Retake.getText());
		waitForElementVisibile(Retake);
		Retake.click();
		retakeCount = stringToInt;
		TimeUtil.mediumWait();

		WebElement Retaketable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
		int retakewithoutdec = retakerows.size();
		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];

		for (int j = 2; j <= retakerows.size() - 1; j++) {

			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RetakeEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));

		// Remove null values
		list5.removeIf(item -> item == null);

		// Convert back to array if needed
		retakeData = list5.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.retakeData.length, retakeCount,
				"Retake Count at Course Session screen after keeping employee in different states",
				"Retake Count at CSR after keeping employee in different states");

		compareCount(retakeCount, retakeData.length,
				"Outiside Retake Count in CSR After Keeping employees in different states",
				"Inside Retake Count in CSR After Keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData, "Actual Retake Data",
				"Retake Data in CSR Report after keping employees in different states");
		switchToDefaultContent(driver);

		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.longwait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(
				totalPendingCount, combinedArrayBefore.length + plannedDataAfterSession.length
						+ +toBeRetrainedData.length + retakeData.length,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Planned");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);

	}

//	public void TBPCSRReport_RE_WithAssesment_After_CourseRetraining() {
//		CM_VerifyCourseSessionScreen.SessionStatus = "";
//		String CourseName = CM_Course.getCourse();
//
//		// CourseName = "AutomationCourseLZRW";
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		scrollToViewElement(courseManagerReportsMenu);
//		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
//				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
//		scrollToViewElement(CSRReportmENU);
//		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
//				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		switchToBodyFrame(driver);
//		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		WebElement element3 = (WebElement) js.executeScript(strt);
//		driver.switchTo().frame(element3);
//		WebElement button = driver.findElement(
//				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
//		waitForElementVisibile(button);
//		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
//				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
//				Reports.Click_ToggleBtn_SS.getReports());
//		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
//				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
//				Reports.UnselectEmployeeID_IER_SS.getReports());
//		TimeUtil.shortWait();
//		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				Reports.EmployeeName_SS.getReports());
//		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
//				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
//		TimeUtil.shortWait();
//
//		TimeUtil.mediumWait();
//		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
//		String text = element.getText();
//
//		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);
//
//		element.click();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//
//		WebElement table = driver.findElement(By.xpath(
//				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//		int withoutdec = rows.size();
//		int employeecount = rows.size() - 2;
//
//		columnData1Before = new String[44];
//
//		for (int j = 2; j <= rows.size() - 1; j++) {
//
//			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			columnData1Before[j - 2] = div.getText();
//
//		}
//
//		WebElement getPages = driver
//				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
//		String getPagesCount = getPages.getText();
//		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
//		if (getPagesCountNumeric > 1) {
//			for (int i = 1; i <= getPagesCountNumeric; i++) {
//				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
//				String value = nextpage.getAttribute("style");
//				if (!value.equals("display: none;")) {
//					TimeUtil.mediumWait();
//					WebElement newNext = driver.findElement(By.xpath(
//							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
//					TimeUtil.mediumWait();
//					newNext.click();
//					TimeUtil.mediumWait();
//					WebElement table2 = driver.findElement(By.xpath(
//							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
//					int withoutdec2 = rows2.size();
//					int employeecount2 = rows2.size() - 2;
//
//					columnData2Before = new String[44];
//
//					for (int j = 2; j <= rows2.size() - 1; j++) {
//
//						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
//						WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//						columnData2Before[j - 2] = div.getText();
//
//					}
//
//				}
//			}
//
//		} else {
//			columnData2Before = null;
//
//		}
//		TimeUtil.mediumWait();
//		blankMethod(columnData1Before, columnData2Before);
//
//		// To Be Planned Data After keeping Employees in Diferent states in Course
//		// Session Vs Course Session Report
//		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
//				ToBePlannedCountAfterRespondDocumentReading,
//				"To planned Count in Course Session Screen after Course Retraining",
//				"To planned Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
//				combinedArrayBefore, "To planned Data in Course Session Screen after Course Retraining",
//				"To planned Data in CSR after Course Retraining");
//
//		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
//				"Outisde To Be Planned Count in CSR  after Course Retraining",
//				"Inside To Be Planned Count in CSR  after Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		convertString_To_Integer(ToBeRetrained.getText());
//		waitForElementVisibile(ToBeRetrained);
//		click2(ToBeRetrained, "", "", "", "");
//		toBeRetrainedCountAtCSReport = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement TBRTable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
//		int tBRwithoutdec = TBRRows.size();
//		// int employeecount = rows.size() - 2;
//		String[] RawTBRData = new String[tBRwithoutdec + 1];
//
//		for (int j = 2; j <= TBRRows.size() - 1; j++) {
//
//			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RawTBRData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));
//
//		// Remove null values
//		list4.removeIf(item -> item == null);
//		toBeRetrainedData = list4.toArray(new String[0]);
//
//		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,
//				"To Be Retrained Count in Course Session Screen after Course Retraining",
//				"To Be Retrained Count in CSR Report after Course Retraining");
//
//		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
//				"Outiside To Be Retrained Count in CSR after Course Retraining",
//				"Inside To Be Retrained Count in CSR after Course Retraining");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
//				"To Be Retrained Data displayed at Course Session after Course Retraining",
//				"To Be Retrained Data in CSR Reportafter Course Retraining");
//
////		switchToDefaultContent(driver);
////		switchToBodyFrame(driver);
////		TimeUtil.mediumWait();
////		waitForElementVisibile(BackButton);
////		BackButton.click();
////		TimeUtil.mediumWait();
////		enterToSSRSShadowRoot();
////		convertString_To_Integer(Qualified.getText());
////		waitForElementVisibile(Qualified);
////		Qualified.click();
////		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
////		TimeUtil.mediumWait();
////
////		WebElement Qualifiedtable = driver
////				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
////		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
////		int Qualifiedwithoutdec = Qualifiedrows.size();
////		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
////
////		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
////
////			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
////			WebElement div = cell.findElement(By.xpath(".//div/div"));
////
////			RawQualifiedEmployeesData[j - 2] = div.getText();
////
////		}
////		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
////
////		// Remove null values
////		list3.removeIf(item -> item == null);
////
////		// Convert back to array if needed
////		QualifiedEmployeesData = list3.toArray(new String[0]);
////
////		
////		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
////				"Outiside Qualified Count in CSR After Course Retraining",
////				"Inside Qualified Count in CSR After Course Retraining");
////
////		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
////				"Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");
////		
////		compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,
////				"Actual Qualified Data",
////				"Qualified Count in CSR After Course Retraining");
//
//		switchToDefaultContent(driver);
//		switchToBodyFrame(driver);
//		TimeUtil.mediumWait();
//		waitForElementVisibile(BackButton);
//		BackButton.click();
//		TimeUtil.mediumWait();
//		enterToSSRSShadowRoot();
//		waitForElementVisibile(TotalTrainees);
//		convertString_To_Integer(TotalTrainees.getText().trim());
//		totalTrainessCount = stringToInt;
//
////		compareCount(totalTrainessCount, toBeRetrainedData.length + combinedArrayBefore.length,
////				"Total Trainees Count in CSR After Course Retraining",
////				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");
//
//		compareCount(totalTrainessCount,
//				combinedArrayBefore.length + CM_VerifyCourseSessionScreen.InprogressUsers.length
//						+ toBeRetrainedData.length + retakeData.length,
//				"Outisde Total Trainees Count in CSR After Course Retraining",
//				"Sum of Total To Be Planned and To BE Retrained");
//
//		waitForElementVisibile(TotalPending);
//		convertString_To_Integer(TotalPending.getText().trim());
//		totalPendingCount = stringToInt;
//		TotalPending.click();
//		TimeUtil.mediumWait();
//		TimeUtil.mediumWait();
//		TotalPendingData = new String[44];
//		TimeUtil.mediumWait();
//
//		TimeUtil.mediumWait();
//		WebElement totalPendingtable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));
//
//		TotalPendingData = new String[44];
//
//		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {
//
//			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			TotalPendingData[j - 2] = div.getText();
//
//		}
//
//		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);
//
//		compareCount(totalPendingCount, combinedArrayBefore.length, "Outside Total Pending Count at CSR",
//				"To Be Planned Count");
//
//		switchToDefaultContent(driver);
//	}

	public void TBPCSR_InterimCourse_Report_AfterRespond_Record_DocumentReading_Without_Assessment() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
//	TimeUtil.mediumWait();
//	TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(5));

		// Wait for the element with the text "Course Sessions Report (Complete)"
		WebElement elementcsrtext = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[text()='Course Sessions Report (Complete)']")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click2(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		WebElement element = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span")));
		// WebElement element =
		// driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after keeping employees in different states",
				"To planned Count in CSR after keeping employees in different states");

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		TimeUtil.mediumWait();

		for (int i = 1; i <= getPagesCountNumeric; i++) {
			WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
			String value = nextpage.getAttribute("style");
			TimeUtil.mediumWait();
			if (!value.equals("display: none;")) {
				TimeUtil.mediumWait();
				WebElement newNext = driver.findElement(By.xpath(
						"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
				TimeUtil.mediumWait();
				newNext.click();
				TimeUtil.mediumWait();
				WebElement table2 = driver.findElement(By.xpath(
						"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
				List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
				int withoutdec2 = rows2.size();
				int employeecount2 = rows2.size() - 2;

				columnData2Before = new String[44];

				for (int j = 2; j <= rows2.size() - 1; j++) {

					WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
					WebElement div = cell.findElement(By.xpath(".//div/div"));

					columnData2Before[j - 2] = div.getText();

				}

			}
		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR, combinedArrayBefore,
				"To planned Data in Course Session Screen after keeping employees in different states",
				"To planned Data in Course Session Screen after keeping employees in different states");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR after keeping employees in different states",
				"Inside To Be Planned Count in CSR after keeping employees in different states");
		TimeUtil.mediumWait();
		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(planned.getText());
		waitForElementVisibile(planned);
		click2(planned, "", "", "", "");
		plnnnedCountAfterSession = stringToInt;

		TimeUtil.mediumWait();
		WebElement plannedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> plannedrows = plannedtable.findElements(By.tagName("tr"));

		plannedDataAfterSession = new String[44];

		for (int j = 2; j <= plannedrows.size() - 1; j++) {

			WebElement cell = plannedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			plannedDataAfterSession[j - 2] = div.getText();

		}

		plannedDataAfterSession = Arrays.stream(plannedDataAfterSession).filter(s -> s != null).toArray(String[]::new);

		compareCount(plnnnedCountAfterSession, plannedDataAfterSession.length,
				"Outisde Planned Count in CSR After keeping employees in different states",
				"Inside Planned Count in CSR After keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InprogressUsers, plannedDataAfterSession,
				"Actual Inprogress users", "Planned Data displayed after keeping employees in different state");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(Qualified.getText());
		waitForElementVisibile(Qualified);
		Qualified.click();
		qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement Qualifiedtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
		int Qualifiedwithoutdec = Qualifiedrows.size();
		String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];

		for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {

			WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawQualifiedEmployeesData[j - 2] = div.getText();

		}
		ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
		list3.removeIf(item -> item == null);

		// Convert back to array if needed
		QualifiedEmployeesData = list3.toArray(new String[0]);
		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, QualifiedEmployeesData,
				"Actual Qualified Count", "Qualified Count in CSR Report after keeping employees in different states");

		compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
				"Outiside Qualified Count after in CSR after keeping employees in different states",
				"Inside Qualified Count after in CSR after keeping employees in different states");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount,
				combinedArrayBefore.length + QualifiedEmployeesData.length
						+ CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outisde Total Trainees Count in CSR After Keeping employees in different states",
				"Sum of To Be Planned, Planned and Qualified");

		waitForElementVisibile(TotalPending);
		convertString_To_Integer(TotalPending.getText().trim());
		totalPendingCount = stringToInt;
		TotalPending.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TotalPendingData = new String[44];
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement totalPendingtable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> totalPendingrows = totalPendingtable.findElements(By.tagName("tr"));

		TotalPendingData = new String[44];

		for (int j = 2; j <= totalPendingrows.size() - 1; j++) {

			WebElement cell = totalPendingrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			TotalPendingData[j - 2] = div.getText();

		}

		TotalPendingData = Arrays.stream(TotalPendingData).filter(s -> s != null).toArray(String[]::new);

		compareCount(totalPendingCount,
				combinedArrayBefore.length + +CM_VerifyCourseSessionScreen.InprogressUsers.length,
				"Outside Total Pending Count at CSR", "Sum of To Be Planned, Planned");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();

		waitForElementVisibile(QualifiedActive);
		convertString_To_Integer(QualifiedActive.getText().trim());
		QualifiedActiveCount = stringToInt;
		QualifiedActive.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		QualifiedActiveData = new String[44];
		// QualifiedData

		TimeUtil.mediumWait();

		TimeUtil.mediumWait();
		WebElement QualifiedActivetable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> QualifiedActivegrows = QualifiedActivetable.findElements(By.tagName("tr"));

		QualifiedActiveData = new String[44];

		for (int j = 2; j <= QualifiedActivegrows.size() - 1; j++) {

			WebElement cell = QualifiedActivegrows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			QualifiedActiveData[j - 2] = div.getText();

		}

		QualifiedActiveData = Arrays.stream(QualifiedActiveData).filter(s -> s != null).toArray(String[]::new);

		toBePlannedCountData(QualifiedEmployeesData, QualifiedActiveData, "Qualified Data", "Qualified Active Data");

		compareCount(QualifiedActiveCount, QualifiedActiveData.length, "Outside Qualified Active Count at CSR",
				"Inside Qualified Active Count at CSR");

		switchToDefaultContent(driver);
	}

//		convertString_To_Integer(Retake.getText());
//		waitForElementVisibile(Retake);
//		Retake.click();
//		retakeCount = stringToInt;
//		TimeUtil.mediumWait();
//
//		WebElement Retaketable = driver
//				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//		List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
//		int retakewithoutdec = retakerows.size();
//		String[] RetakeEmployeesData = new String[retakewithoutdec + 1];
//
//		for (int j = 2; j <= retakerows.size() - 1; j++) {
//
//			WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
//			WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//			RetakeEmployeesData[j - 2] = div.getText();
//
//		}
//		ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));
//
//		// Remove null values
//		list5.removeIf(item -> item == null);
//
//		// Convert back to array if needed
//		retakeData = list5.toArray(new String[0]);
//		
//		compareCount(CM_VerifyCourseSessionScreen.retakeData.length,retakeCount,
//				"Retake Count at Course Session screen after keeping employee in different states",
//				"Retake Count at CSR after keeping employee in different states");
//
//		
//		compareCount(retakeCount, retakeData.length,
//				"Outiside Retake Count in CSR After Keeping employees in different states",
//				"Inside Retake Count in CSR After Keeping employees in different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData,
//				"Actual Retake Data", "Retake Data in CSR Report after keping employees in different states");

	public void TBPCSRReport_RE_WithAssesment_After_CourseRetraining() {
		CM_VerifyCourseSessionScreen.SessionStatus = "";
		String CourseName = CM_Course.getCourse();

		// CourseName = "AutomationCourseLZRW";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(CSRReportmENU);
		click2(CSRReportmENU, Reports.click_IER_DC.getReports(), Reports.click_IER_AC.getReports(),
				Reports.click_IER_AR.getReports(), Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, CourseCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(CourseCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(CourseNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.shortWait();

		TimeUtil.mediumWait();
		WebElement element = driver.findElement(By.xpath("//table/tbody/tr[3]/td[3]/div/a/div/div/div/span"));
		String text = element.getText();

		ToBePlannedCountAfterRespondDocumentReading = Integer.parseInt(text);

		element.click();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath(
				"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		int withoutdec = rows.size();
		int employeecount = rows.size() - 2;

		columnData1Before = new String[44];

		for (int j = 2; j <= rows.size() - 1; j++) {

			WebElement cell = rows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			columnData1Before[j - 2] = div.getText();

		}

		WebElement getPages = driver
				.findElement(By.xpath("//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[9]/span"));
		String getPagesCount = getPages.getText();
		int getPagesCountNumeric = Integer.parseInt(getPagesCount);
		if (getPagesCountNumeric > 1) {
			for (int i = 1; i <= getPagesCountNumeric; i++) {
				WebElement nextpage = driver.findElement(By.xpath("//div[@title='Next Page']"));
				String value = nextpage.getAttribute("style");
				if (!value.equals("display: none;")) {
					TimeUtil.mediumWait();
					WebElement newNext = driver.findElement(By.xpath(
							"//table/tbody/tr[4]/td/div[1]/div/div[1]/table/tbody/tr/td[11]/div/div[1]/table/tbody/tr/td/span"));
					TimeUtil.mediumWait();
					newNext.click();
					TimeUtil.mediumWait();
					WebElement table2 = driver.findElement(By.xpath(
							"//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr/td[2]//table/tbody | //table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
					List<WebElement> rows2 = table2.findElements(By.tagName("tr"));
					int withoutdec2 = rows2.size();
					int employeecount2 = rows2.size() - 2;

					columnData2Before = new String[44];

					for (int j = 2; j <= rows2.size() - 1; j++) {

						WebElement cell = rows2.get(j).findElements(By.tagName("td")).get(1);
						WebElement div = cell.findElement(By.xpath(".//div/div"));

						columnData2Before[j - 2] = div.getText();

					}

				}
			}

		} else {
			columnData2Before = null;

		}
		TimeUtil.mediumWait();
		blankMethod(columnData1Before, columnData2Before);

		// To Be Planned Data After keeping Employees in Diferent states in Course
		// Session Vs Course Session Report
		compareCount(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length + 1,
				ToBePlannedCountAfterRespondDocumentReading,
				"To planned Count in Course Session Screen after Course Retraining",
				"To planned Count in CSR after Course Retraining");

		String[] combined = Stream.concat(
				Arrays.stream(
						CM_VerifyCourseSessionScreen.To_Be_Planned_Data_After_Selecting_Subgroup_AfterCourseRetraining),
				Arrays.stream(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection))
				.toArray(String[]::new);

		toBePlannedCountData(combined, combinedArrayBefore,
				"To planned Data in Course Session Screen after Course Retraining",
				"To planned Data in CSR after Course Retraining");

		compareCount(ToBePlannedCountAfterRespondDocumentReading, combinedArrayBefore.length,
				"Outisde To Be Planned Count in CSR  after Course Retraining",
				"Inside To Be Planned Count in CSR  after Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		convertString_To_Integer(ToBeRetrained.getText());
		waitForElementVisibile(ToBeRetrained);
		click2(ToBeRetrained, "", "", "", "");
		toBeRetrainedCountAtCSReport = stringToInt;
		TimeUtil.mediumWait();

		WebElement TBRTable = driver
				.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
		List<WebElement> TBRRows = TBRTable.findElements(By.tagName("tr"));
		int tBRwithoutdec = TBRRows.size();
		// int employeecount = rows.size() - 2;
		String[] RawTBRData = new String[tBRwithoutdec + 1];

		for (int j = 2; j <= TBRRows.size() - 1; j++) {

			WebElement cell = TBRRows.get(j).findElements(By.tagName("td")).get(1);
			WebElement div = cell.findElement(By.xpath(".//div/div"));

			RawTBRData[j - 2] = div.getText();

		}
		ArrayList<String> list4 = new ArrayList<>(Arrays.asList(RawTBRData));

		// Remove null values
		list4.removeIf(item -> item == null);
		toBeRetrainedData = list4.toArray(new String[0]);

		compareCount(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP.length, toBeRetrainedCountAtCSReport,
				"To Be Retrained Count in Course Session Screen after Course Retraining",
				"To Be Retrained Count in CSR Report after Course Retraining");

		compareCount(toBeRetrainedCountAtCSReport, toBeRetrainedData.length,
				"Outiside To Be Retrained Count in CSR after Course Retraining",
				"Inside To Be Retrained Count in CSR after Course Retraining");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP, toBeRetrainedData,
				"To Be Retrained Data displayed at Course Session after Course Retraining",
				"To Be Retrained Data in CSR Reportafter Course Retraining");

//	switchToDefaultContent(driver);
//	switchToBodyFrame(driver);
//	TimeUtil.mediumWait();
//	waitForElementVisibile(BackButton);
//	BackButton.click();
//	TimeUtil.mediumWait();
//	enterToSSRSShadowRoot();
//	convertString_To_Integer(Qualified.getText());
//	waitForElementVisibile(Qualified);
//	Qualified.click();
//	qualifiedCountDisplayedAtCourseSessionReport = stringToInt;
//	TimeUtil.mediumWait();
//
//	WebElement Qualifiedtable = driver
//			.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//	List<WebElement> Qualifiedrows = Qualifiedtable.findElements(By.tagName("tr"));
//	int Qualifiedwithoutdec = Qualifiedrows.size();
//	String[] RawQualifiedEmployeesData = new String[Qualifiedwithoutdec + 1];
//
//	for (int j = 2; j <= Qualifiedrows.size() - 1; j++) {
//
//		WebElement cell = Qualifiedrows.get(j).findElements(By.tagName("td")).get(1);
//		WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//		RawQualifiedEmployeesData[j - 2] = div.getText();
//
//	}
//	ArrayList<String> list3 = new ArrayList<>(Arrays.asList(RawQualifiedEmployeesData));
//
//	// Remove null values
//	list3.removeIf(item -> item == null);
//
//	// Convert back to array if needed
//	QualifiedEmployeesData = list3.toArray(new String[0]);
//
//	
//	compareCount(qualifiedCountDisplayedAtCourseSessionReport, QualifiedEmployeesData.length,
//			"Outiside Qualified Count in CSR After Course Retraining",
//			"Inside Qualified Count in CSR After Course Retraining");
//
//	toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, QualifiedEmployeesData,
//			"Actual Qualified Count", "Qualified Count in CSR Report After Course Retraining");
//	
//	compareCount(CM_VerifyCourseSessionScreen.QualifiedData.length,qualifiedCountDisplayedAtCourseSessionReport,
//			"Actual Qualified Data",
//			"Qualified Count in CSR After Course Retraining");

		switchToDefaultContent(driver);
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		waitForElementVisibile(BackButton);
		BackButton.click();
		TimeUtil.mediumWait();
		enterToSSRSShadowRoot();
		waitForElementVisibile(TotalTrainees);
		convertString_To_Integer(TotalTrainees.getText().trim());
		totalTrainessCount = stringToInt;

		compareCount(totalTrainessCount, toBeRetrainedData.length + combinedArrayBefore.length,
				"Total Trainees Count in CSR After Course Retraining",
				"Sum of Total To Be Planned, Skipped, Absent and To Be Retrained");

//	convertString_To_Integer(Retake.getText());
//	waitForElementVisibile(Retake);
//	Retake.click();
//	retakeCount = stringToInt;
//	TimeUtil.mediumWait();
//
//	WebElement Retaketable = driver
//			.findElement(By.xpath("//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]//table/tbody"));
//	List<WebElement> retakerows = Retaketable.findElements(By.tagName("tr"));
//	int retakewithoutdec = retakerows.size();
//	String[] RetakeEmployeesData = new String[retakewithoutdec + 1];
//
//	for (int j = 2; j <= retakerows.size() - 1; j++) {
//
//		WebElement cell = retakerows.get(j).findElements(By.tagName("td")).get(1);
//		WebElement div = cell.findElement(By.xpath(".//div/div"));
//
//		RetakeEmployeesData[j - 2] = div.getText();
//
//	}
//	ArrayList<String> list5 = new ArrayList<>(Arrays.asList(RetakeEmployeesData));
//
//	// Remove null values
//	list5.removeIf(item -> item == null);
//
//	// Convert back to array if needed
//	retakeData = list5.toArray(new String[0]);
//	
//	compareCount(CM_VerifyCourseSessionScreen.retakeData.length,retakeCount,
//			"Retake Count at Course Session screen after keeping employee in different states",
//			"Retake Count at CSR after keeping employee in different states");
//
//	
//	compareCount(retakeCount, retakeData.length,
//			"Outiside Retake Count in CSR After Keeping employees in different states",
//			"Inside Retake Count in CSR After Keeping employees in different states");
//
//	toBePlannedCountData(CM_VerifyCourseSessionScreen.retakeData, retakeData,
//			"Actual Retake Data", "Retake Data in CSR Report after keping employees in different states");
		switchToDefaultContent(driver);
	}
}
