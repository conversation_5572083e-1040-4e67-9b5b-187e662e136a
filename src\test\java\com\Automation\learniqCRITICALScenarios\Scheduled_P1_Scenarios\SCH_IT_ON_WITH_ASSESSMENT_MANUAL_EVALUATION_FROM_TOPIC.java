package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_QuestionPaper;
import com.Automation.learniqObjects.CM_SelfNomination;

/**
 * Verify Online IT Session with assessment for scheduled course and make
 * at-least one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, qualified, To Be Retrained with manual
 * evaluation and by viewing Individual employee report at each transaction
 * starting from course session, also add at least 2 additional users and make
 * sure that should be qualified and to be retrained and view IER for those
 * employees.
 */

public class SCH_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION_FROM_TOPIC extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SCH_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION_FROM_TOPIC.xlsx";

	public SCH_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION_FROM_TOPIC() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Configuration
		ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");
	 
		@DataProvider(name = "TopicReg")
		public Object[][] getTopicData() throws Exception {
			Object[][] obj = new Object[topicData.getRowCount()][1];
			for (int i = 1; i <= topicData.getRowCount(); i++) {
				HashMap<String, String> testData = topicData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 0, dataProvider = "TopicReg", enabled = true)
		public void Topic_Registration(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
	 
				test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Topic Registration");
			}
	 
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
	 
			epiclogin.plant1();
			;
	 
			InitiateTopic.topic_Registration(testData);
	 
//				Logout.signOutPage();
	 
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			//
//				epiclogin.plant1();
			//
//				InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
			//
//				Logout.signOutPage();
	 
		}
	 
		// Test Method for Course Configuration, Registration, Approve with
		// AuditTrails----------------------------------------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");
	 
		@DataProvider(name = "CourseRegistrationData")
		public Object[][] getCourseData() throws Exception {
			Object[][] obj = new Object[CourseData.getRowCount()][1];
			for (int i = 1; i <= CourseData.getRowCount(); i++) {
				HashMap<String, String> testData = CourseData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 1, dataProvider = "CourseRegistrationData", enabled = true)
		public void Course_Registration(HashMap<String, String> testData) {
	 
			if (isReportedRequired == true) {
	 
				test = extent.createTest("Course Registration")
	 
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	 
						.assignCategory("Course Registration");
			}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
	 
//				epiclogin.masterPlant();
			//
//				Initiate_Course.courseConfiguration_Reg(testData);
	 
//				epiclogin.plant1();
	 
			Initiate_Course.Course_Registration(testData);
	 
//				Logout.signOutPage();
	 
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			//
//				epiclogin.plant1();
			//
//				Initiate_Course.course_Approval_AuditTrials_Yes(testData);
			//
//				Logout.signOutPage();
	 
		}
	 
		// Test Method for TrainingSchedule Modification
		ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");
	 
		@DataProvider(name = "modifyTrainingSchedule")
		public Object[][] getModifyTrainingSchedule() throws Exception {
			Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
			for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
				HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 2, dataProvider = "modifyTrainingSchedule", enabled = true)
		public void TrainingSchedule_Modification(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
	 
				test = extent.createTest("TrainingSchedule Modification")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("TrainingSchedule Modification");
			}
//								epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//										ConfigsReader.getPropValue("EpicUserPWD"));
//								epiclogin.plant1();
//								TrainingShcedule.trainingScheduleConfiguration(testData);
	 
			TrainingShcedule.modifyTrainingScheduled(testData);
	 
//								TrainingShcedule.trainingScheduleAuditTrail();
//								Logout.signOutPage();
//								epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//										ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//								epiclogin.plant1();
//								TrainingShcedule.approveModifyTrainingScheduled(testData);
//								TrainingShcedule.trainingScheduleAuditTrail();
//								Logout.signOutPage();
		}
	 
		// Test Method for Trainer Configuration, Modification, Approve with
		// Audit Trails
	 
		ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");
	 
		@DataProvider(name = "TrainerModification")
		public Object[][] getTrainerData() throws Exception {
			Object[][] obj = new Object[TrainerMod.getRowCount()][1];
			for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
				HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
		public void Trainer_Modification(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("Trainer Modification")
	 
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	 
						.assignCategory("Trainer Modification");
			}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));
//					epiclogin.plant1();
//					trainer.TrainerModificationConfigurations(testData);
			trainer.trainer_Modification_AuditTrails(testData);
//					Logout.signOutPage();
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//					epiclogin.plant1();
//					trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//					Logout.signOutPage();
		}
	 
		// Test Method for CourseSession Configuration, Registration, Approve with
		// AuditTrails
	 
		ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "SessionRegAppr");
	 
		@DataProvider(name = "CourseSession")
		public Object[][] getCourseSessionData() throws Exception {
			Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
			for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
				HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
		public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
	 
			if (isReportedRequired == true) {
				test = extent.createTest("CourseSession Registration")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("CourseSession Registration");
			}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
			//
//				epiclogin.plant1();
			//
//				CourseSession.courseSessionConfiguration(testData);
	 
			CourseSession.courseSession_Online_WithExam_ManualEval_WithoutPrequisiteTrainee(testData);
	 
//				CourseSession.courseSessionAuditTrails();
	 
//				if (isReportedRequired == true) {
//				test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//						.assignCategory("Individual Employee Report for Course Session Under Approval");
//				}
//				IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//						Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL, Constants.ITType);
			//
//				Logout.signOutPage();
			//
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//				epiclogin.plant1();
			//
//				CourseSession.courseSession_Approve(testData);
//				if (isReportedRequired == true) {
//				test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//						.assignCategory("Individual Employee Report for Course Session Proposed Status");
//				}
//				IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//						Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED, Constants.ITType);
			Logout.signOutPage();
	 
		}
	 
		// Test Method for Respond CourseInvitaion
		// (Accept)---------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated CourseInvData = new ExcelUtilUpdated(ExcelPath, "CourseInvitiationAccept");
	 
		@DataProvider(name = "AcceptCourseInvitation")
		public Object[][] getCIacceptData() throws Exception {
			Object[][] obj = new Object[CourseInvData.getRowCount()][1];
			for (int i = 1; i <= CourseInvData.getRowCount(); i++) {
				HashMap<String, String> testData = CourseInvData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
	 
		}
	 
		@Test(priority = 6, dataProvider = "AcceptCourseInvitation", enabled = true)
		public void Respond_CourseInvitaion_Accept(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond CourseInvitaion (Accept)")
	 
						.assignAuthor(CM_CourseSession.CIAcceptTraineeID)
	 
						.assignCategory("Respond CourseInvitaion (Accept)");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.CIAcceptTraineeID,
					CM_CourseSession.CIAcceptTraineePsw);
	 
			epiclogin.plant1();
	 
			CourseInvitation.respondCourseInvitation(testData);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report After CourseInvitation Accept")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report After CourseInvitation Accept");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_CIACCEPT,
					Constants.ITType);
	 
			Logout.signOutPage();
		}
	 
		// Test Method for Respond
		// CourseInvitaion(Reject)---------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated CIData = new ExcelUtilUpdated(ExcelPath, "CourseInvitionReject");
	 
		@DataProvider(name = "RejectCourseInvition")
		public Object[][] getCI_Reject_Data() throws Exception {
			Object[][] obj = new Object[CIData.getRowCount()][1];
			for (int i = 1; i <= CIData.getRowCount(); i++) {
				HashMap<String, String> testData = CIData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 7, dataProvider = "RejectCourseInvition", enabled = true)
		public void Respond_CourseInvitaion_Reject(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond CourseInvitaion (Reject)")
	 
						.assignAuthor(CM_CourseSession.CIRejectTraineeID)
	 
						.assignCategory("Respond CourseInvitaion (Reject)");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.CIRejectTraineeID,
					CM_CourseSession.CIRejectTraineePsw);
	 
			epiclogin.plant1();
	 
			CourseInvitation.respondCourseInvitationReject(testData);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report After CourseInvitation Reject")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report After CourseInvitation Reject");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIRejectEmployeeID, Constants.EMPLOYEESTATUS_AS_CIREJECT,
					Constants.ITType);
	 
			Logout.signOutPage();
		}
	 
		// Test Method for SelfNomination Configuration, Registration, Approve with
		// AuditTrails---------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated SelfNominated = new ExcelUtilUpdated(ExcelPath, "SelfNominationRegApproval");
	 
		@DataProvider(name = "SelfNominationReg")
		public Object[][] getSelfNominationData() throws Exception {
			Object[][] obj = new Object[SelfNominated.getRowCount()][1];
			for (int i = 1; i <= SelfNominated.getRowCount(); i++) {
				HashMap<String, String> testData = SelfNominated.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 8, dataProvider = "SelfNominationReg", enabled = true)
		public void SelfNomination_Registration(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("SelfNomination Registration")
	 
						.assignAuthor(CM_SelfNomination.selfNominatedTraineeID)
	 
						.assignCategory("SelfNomination Registration");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("selfNominatedTraineeID"),
					testData.get("selfNominatedTraineePsw"));
	 
			epiclogin.plant1();
	 
			// SelfNominate.selfNominationConfiguration(testData);
	 
			SelfNominate.selfNomination_Registration(testData);
	 
//					Logout.signOutPage();
			//
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			//
//					epiclogin.plant1();
			//
//					SelfNominate.SelfNominationRegistrationApproval_With_AuditTrails(testData);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report After SelfNomination")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report After SelfNomination");
			}
			IERReport.individualEmployeeReport(CM_SelfNomination.selfNominatedEmployeeID,
					Constants.EMPLOYEESTATUS_AS_SELFNOMINATED, Constants.ITType);
			Logout.signOutPage();
	 
		}
	 
		// Test Method for BatchFormation Configuration, Registration with
		// AuditTrails---------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");
	 
		@DataProvider(name = "batchFormation")
		public Object[][] getBatchFormation() throws Exception {
			Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
			for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
				HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 9, dataProvider = "batchFormation", enabled = true)
		public void BatchFormation_Registration(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("BatchFormation Registration")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("BatchFormation Registration");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
	 
			epiclogin.plant1();
	 
			// BatchFormation.batchFormationConfiguration(testData);
	 
			BatchFormation.batchFormation_Online_NotResponded_Responded_SelfNominatedUsers(testData);
	 
			// BatchFormation.proposeBatchFormationAuditTrail();
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report For Employee not selected in batch")
						.assignAuthor(CM_CourseSession.SkippedEmployeeID)
						.assignCategory("Individual Employee Report For Employee not selected in batch");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID,
					Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED, Constants.ITType);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report For Employee selected in batch")
						.assignAuthor(CM_CourseSession.SkippedEmployeeID)
						.assignCategory("Individual Employee Report For Employee selected in batch");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
					Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);
	 
		}
	 
		// Test Method for QuestionBank Configuration, Registration, Approve with
		// AuditTrails-------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");
	 
		@DataProvider(name = "QuestionBank")
		public Object[][] getdocument_preparation_RequestFlow() throws Exception {
			Object[][] obj = new Object[QBData.getRowCount()][1];
			for (int i = 1; i <= QBData.getRowCount(); i++) {
				HashMap<String, String> testData = QBData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 10, dataProvider = "QuestionBank")
		public void QuestionBank_Registration(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("QuestionBank Registration");
			}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));
//					epiclogin.masterPlant();
//					PrepareQB.QBRegistrationApproval_Configuration(testData);
//					epiclogin.navigateTolearnIQPlant();
	 
			PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
	 
//					Logout.signOutPage();
			//
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			//
//					epiclogin.plant1();
			//
//					PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);
			//
//					Logout.signOutPage();
	 
		}
	 
		// Test Method for QuestionPaper Registration
		// -------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");
	 
		@DataProvider(name = "QuestionPaperData")
		public Object[][] getQuestionPaperDataRE() throws Exception {
			Object[][] obj = new Object[QPReg.getRowCount()][1];
			for (int i = 1; i <= QPReg.getRowCount(); i++) {
				HashMap<String, String> testData = QPReg.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 11, dataProvider = "QuestionPaperData", enabled = true)
		public void QuestionPaper_Registration(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("QuestionPaper Registration")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("QuestionPaper Registration");
			}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
			//
//				epiclogin.plant1();
	 
			questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);
	 
//				Logout.signOutPage();
	 
		}
	 
		// Test Method for
		// RecordAttendance-------------------------------------------------------------------------------------------
	 
		ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");
	 
		@DataProvider(name = "recordAttendance")
		public Object[][] getRecordAttendance() throws Exception {
			Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
			for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
				HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	 
		@Test(priority = 12, dataProvider = "recordAttendance", enabled = true)
		public void recordAttendance(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("RecordAttendance");
			}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));
			//
//				epiclogin.plant1();
	 
			RecordAttendance.recordAttendance_OnlineSession_2AdditionalUsers(testData);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Skipped Trainee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report of Skipped Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID, Constants.EMPLOYEESTATUS_AS_SKIPPED,
					Constants.ITType);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Absent Trainee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report of Absent Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.AbsentEmployeeID, Constants.EMPLOYEESTATUS_AS_ABSENT,
					Constants.ITType);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Present Trainee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report of Present Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_PRESENT,
					Constants.ITType);
			Logout.signOutPage();
	 
		}
	 
		// Test Method for RespondQuestionPaper
		// (Qualify)-------------------------------------------------------------------------------------------
	 
		@Test(priority = 13, enabled = true)
		public void Respond_QuestionPaper_Qualify() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Question paper (Qualify)")
						.assignAuthor(CM_CourseSession.CIAcceptTraineeID)
						.assignCategory("Respond Question paper (Qualify)");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.CIAcceptTraineeID,
					CM_CourseSession.CIAcceptTraineePsw);
			epiclogin.plant1();
			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
					CM_CourseSession.CIAcceptTraineePsw);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Response Submitted Trainee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report of Response Submitted Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
					Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED, Constants.ITType);
			Logout.signOutPage();
		}
	 
		// Test Method for RespondQuestionPaper
		// (AdditionalUser
		// Qualify)-------------------------------------------------------------------------------------------
	 
//			@Test(priority = 14, enabled = true)
//			public void Respond_QuestionPaper_AdditionalUser_Qualify() {
		//
//				test = extent.createTest("Respond Question paper (AdditionalUser Qualify)")
		//
//						.assignAuthor(CM_CourseSession.AdditionalUSerQulaifiedID)
		//
//						.assignCategory("Respond Question paper (AdditionalUser Qualify)");
		//
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getAddUserQID(),
//						CM_CourseSession.AdditionalUSerQulaifiedPsw);
		//
//				epiclogin.plant1();
		//
//				RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
//						CM_CourseSession.AdditionalUSerQulaifiedPsw);
		//
//				Logout.signOutPage();
		//
//				n = 0;
//				screenshotCounter = 0;
//			}
	 
		// Test Method for RespondQuestionPaper
		// (ToBeRetrained)-------------------------------------------------------------------------------------------
	 
		@Test(priority = 15, enabled = true)
		public void Respond_QuestionPaper_ToBeRetrained() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Question paper (ToBeRetrained)")
						.assignAuthor(CM_CourseSession.ToBeRetrainedTraineeID)
						.assignCategory("Respond Question paper (ToBeRetrained)");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					CM_CourseSession.getToBeRetrainedTraineeID(), CM_CourseSession.ToBeRetrainedTraineePsw);
			epiclogin.plant1();
			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
					CM_CourseSession.ToBeRetrainedTraineePsw);
			Logout.signOutPage();
		}
	 
		// Test Method for RespondQuestionPaper
		// (AdditionalUser
		// ToBeRetrained)-------------------------------------------------------------------------------------------
	 
//			@Test(priority = 16, enabled = true)
//			public void Respond_QuestionPaper_AdditionalUser_ToBeRetrained() {
//				test = extent.createTest("Respond Question paper (Additional User ToBeRetrained)")
//						.assignAuthor(CM_CourseSession.AdditionalUSerTRBID)
//						.assignCategory("Respond Question paper (Additional User ToBeRetrained)");
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.AdditionalUSerTRBID,
//						CM_CourseSession.AdditionalUSerTRBPsw);
//				epiclogin.plant1();
//				RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
//						CM_CourseSession.AdditionalUSerTRBPsw);
//				Logout.signOutPage();
//				n = 0;
//				screenshotCounter = 0;
//			}
	 
		// Test Method for
		// EvaluateAnswerPaper-------------------------------------------------------------------------------------------
	 
		@Test(priority = 17, enabled = true)
		public void evaluate_AnswerPaper() {
			if (isReportedRequired == true) {
				test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
						.assignCategory("Evaluate Answer Paper");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
					CM_QuestionPaper.EvaluatorPassword);
	 
			epiclogin.plant1();
	 
			Evaluate.manualEvaluation();
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Qualified Trainee")
						.assignAuthor(CM_QuestionPaper.EvaluatorUserID)
						.assignCategory("Individual Employee Report of Qualified Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
					Constants.ITType);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of ToBeRetrained Trainee")
						.assignAuthor(CM_QuestionPaper.EvaluatorUserID)
						.assignCategory("Individual Employee Report of ToBeRetrained Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedEmployeeID,
					Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType);
	 
//					test = extent.createTest("Check Trainees at Course Session Screen")
//							.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//							.assignCategory("Check Trainees at Course Session Screen");
			//
//					CourseSession.verify_Employees_At_Coursesession();
			//
			Logout.signOutPage();
			//
//					n = 0;
//					screenshotCounter = 0;
		}
	 
		@AfterTest
		public void afterTest() {
			extent.flush();
			MyScreenRecorder.stopRecording();
			driver.quit();
		}
	}
	 
	 