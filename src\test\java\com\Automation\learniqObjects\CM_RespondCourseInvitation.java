package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import com.Automation.learniqObjects.CM_Course;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseInvitationStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_RespondCourseInvitation extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[8]//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respondMenu;
	@FindBy(id = "TMS_Course Manager_Respond_MEN21")
	WebElement courseInvitation;
	@FindBy(id = "BtnSes")
	WebElement selectSession;

	@FindBy(id = "CMSESSIONS_Remarks")
	WebElement remarks;

	@FindBy(xpath = "//textarea[@class='form-control caliber-textarea']")
	WebElement remarksReasons;

	@FindBy(xpath = "//button[@id='btnSubmit'and@class='caliber-button-primary']")
	WebElement submit;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;

	@FindBy(xpath = "//input[@class='form-control caliber-textbox']")
	WebElement searchField1;

	@FindBy(xpath = "//span[@id='btnClientSearch']/i")
	WebElement searchField;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//span[@id='btnTtlRcrds']")
	WebElement recordCount;
	@FindBy(xpath = "//div[@class='toast toast-info']/button")
	WebElement recordCountClose;

	@FindBy(xpath = "//*[@id=\"ListTab_filter\"]/label/input")
	WebElement textBox;

	@FindBy(xpath = "//*[@id=\"ListTab\"]/tbody/tr/td[1]")
	WebElement courseSelect;

	@FindBy(xpath = "//a[@id='cfnMsg_Next']")
	WebElement done;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText1;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;

	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	@FindBy(id = "SelectedDecision_1")
	WebElement reject;

	@FindBy(xpath = "/html/body/form/section/section/div/div/div/div/div/div/div[2]/div[2]/div[11]/div/div/div/div/div/div[2]")
	WebElement reject320;

	public CM_RespondCourseInvitation() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * This method is to respond course Invitation
	 */
	public void respondCourseInvitation(HashMap<String, String> testdata) {

		String CourseNameValue = CM_Course.getCourse();
//		String CourseNameValue = "Courseafgv";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(respondMenu, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());

		click2(courseInvitation, CourseInvitationStrings.CIMenu_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_SS.getCourseInvitationStrings());
		switchToBodyFrame(driver);
		click2(searchField, CourseInvitationStrings.SearchBy_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_SS.getCourseInvitationStrings());

		TimeUtil.shortWait();
		sendKeys2(textBox, CourseInvitationStrings.SearchBy2_DC.getCourseInvitationStrings(), CourseNameValue,
				CourseInvitationStrings.SearchBy2_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_SS.getCourseInvitationStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		click2(courseSelect, CourseInvitationStrings.SearchBy1_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_SS.getCourseInvitationStrings());
		click2(selectSession, CourseInvitationStrings.UserSelectionScreenNextBtn_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_SS.getCourseInvitationStrings());
		scrollToViewElement(remarks);
		sendKeys2(remarks, CourseInvitationStrings.remarks_DC.getCourseInvitationStrings(), testdata.get("remarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseInvitationStrings.remarks_SS.getCourseInvitationStrings());
		click2(submit, CourseInvitationStrings.Submit_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_SS.getCourseInvitationStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);
	}

	/**
	 * This method is to respond course Invitation
	 */
	public void respondCourseInvitationReject(HashMap<String, String> testdata) {

		String CourseNameValue = CM_Course.getCourse();
//		String CourseNameValue = "Courseafgv";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(respondMenu, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());

		click2(courseInvitation, CourseInvitationStrings.CIMenu_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.CIMenu_SS.getCourseInvitationStrings());
		switchToBodyFrame(driver);
		click2(searchField, CourseInvitationStrings.SearchBy_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_SS.getCourseInvitationStrings());

		TimeUtil.shortWait();
		sendKeys2(textBox, CourseInvitationStrings.SearchBy2_DC.getCourseInvitationStrings(), CourseNameValue,
				CourseInvitationStrings.SearchBy2_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_SS.getCourseInvitationStrings());

		click2(courseSelect, CourseInvitationStrings.SearchBy1_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy1_SS.getCourseInvitationStrings());
		click2(selectSession, CourseInvitationStrings.UserSelectionScreenNextBtn_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.UserSelectionScreenNextBtn_SS.getCourseInvitationStrings());

		TimeUtil.mediumWait();
		waitForElementVisibile(reject);
		scrollToViewElement(reject);

		click2(reject, CourseInvitationStrings.Select_Reject_DC.getCourseInvitationStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseInvitationStrings.Select_Reject_SS.getCourseInvitationStrings());

		sendKeys2(remarks, CourseInvitationStrings.remarks_DC.getCourseInvitationStrings(), testdata.get("Remarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseInvitationStrings.remarks_SS.getCourseInvitationStrings());
		click2(submit, CourseInvitationStrings.Submit_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.Submit_SS.getCourseInvitationStrings());

		TimeUtil.shortWait();

		highLightElement(driver, confirmationText, "Confirmation Message", test);

		switchToDefaultContent(driver);
	}

}
