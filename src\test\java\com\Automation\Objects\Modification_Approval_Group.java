
package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;

public class Modification_Approval_Group extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";

	public Modification_Approval_Group() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration",enabled = false)
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration1")
	public Object[][] getGroupDatamod() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration1")
	public void groupRegistrationMOd(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData20 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApproveMOD1")
	public Object[][] getGroupVerifyDatamod() throws Exception {
		Object[][] obj = new Object[topicData20.getRowCount()][1];
		for (int i = 1; i <= topicData20.getRowCount(); i++) {
			HashMap<String, String> testData = topicData20.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 3, dataProvider = "groupApproveMOD1")
	public void groupRegistrationApproveMOD(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationModApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course")
	public Object[][] getGroupModData() throws Exception {
		Object[][] obj = new Object[topicData2.getRowCount()][1];
		for (int i = 1; i <= topicData2.getRowCount(); i++) {
			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "course")
	public void groupModificationMod(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Modification Flow.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Modification Flow.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprove1")
	public Object[][] getGroupVerifyData() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */

	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 5, dataProvider = "groupApprove1")
	public void groupRegistrationApprove1(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Approval Flow and ensure its update in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Modification Approval Flow and ensure its update in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.groupModificatioApproval(testData);
		group_Modification.groupModificationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData4 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification")
	public Object[][] getGroupverifyModData() throws Exception {
		Object[][] obj = new Object[topicData4.getRowCount()][1];
		for (int i = 1; i <= topicData4.getRowCount(); i++) {
			HashMap<String, String> testData = topicData4.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "verifyModification")
	public void VerifyModification(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the modified and approved Group is available for further modification at Group Modification screen.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the modified and approved Group is available for further modification at Group Modification screen.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.verifiModification(testData);
		Logout.signOutPage();
	}

//	--------------------------------------------------------Return---------------------------------

	ExcelUtilUpdated topicData5 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration2")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData5.getRowCount()][1];
		for (int i = 1; i <= topicData5.getRowCount(); i++) {
			HashMap<String, String> testData = topicData5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 7, dataProvider = "groupRegistration2")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData21 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprovemod2")
	public Object[][] getGroupMODAPPData() throws Exception {
		Object[][] obj = new Object[topicData21.getRowCount()][1];
		for (int i = 1; i <= topicData21.getRowCount(); i++) {
			HashMap<String, String> testData = topicData21.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 8, dataProvider = "groupApprovemod2")
	public void groupRegistrationApprovemod2(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Approval Flow and ensure its update in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Registration Approval Flow and ensure its update in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData6 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course1")
	public Object[][] getGroupReturnData() throws Exception {
		Object[][] obj = new Object[topicData6.getRowCount()][1];
		for (int i = 1; i <= topicData6.getRowCount(); i++) {
			HashMap<String, String> testData = topicData6.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "course1")
	public void groupModification2(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Modification Flow.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Modification Flow.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData7 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn1")
	public Object[][] getGroupReturnnData() throws Exception {
		Object[][] obj = new Object[topicData7.getRowCount()][1];
		for (int i = 1; i <= topicData7.getRowCount(); i++) {
			HashMap<String, String> testData = topicData7.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 10, dataProvider = "groupReturn1")
	public void groupRegistrationReturn(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Return Flow and ensure its update in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Modification Return Flow and ensure its update in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_Returns(testData);
		group_Modification.modificationgroup_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData8 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate1")
	public Object[][] getGroupReInitiateData() throws Exception {
		Object[][] obj = new Object[topicData8.getRowCount()][1];
		for (int i = 1; i <= topicData8.getRowCount(); i++) {
			HashMap<String, String> testData = topicData8.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 11, dataProvider = "groupReinitiate1")
	public void groupRegistrationReInitiate(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Re-initiation Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the Group Modification Re-initiation Flow and ensure its update in the Audit Trails.");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.modificationgroupReInitiate(testData);
		group_Modification.modificationgroupReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData9 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1Approval1")
	public Object[][] getGroupApproveData() throws Exception {
		Object[][] obj = new Object[topicData9.getRowCount()][1];
		for (int i = 1; i <= topicData9.getRowCount(); i++) {
			HashMap<String, String> testData = topicData9.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 12, dataProvider = "groupRegistration1Approval1")
	public void groupRegistrationApprove2(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Re-initiation Approval Flow and ensure its update in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Modification Re-initiation Approval Flow and ensure its update in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_Approval(testData);
		group_Modification.modificationgroup_ApprovalWithAuditTrials_Yes(testData);

		Logout.signOutPage();
	}

//	-------------------------------------------------RI Transwer---------------------------------------

	ExcelUtilUpdated topicData10 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration3")
	public Object[][] getGroupRITRSData() throws Exception {
		Object[][] obj = new Object[topicData10.getRowCount()][1];
		for (int i = 1; i <= topicData10.getRowCount(); i++) {
			HashMap<String, String> testData = topicData10.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 13, dataProvider = "groupRegistration3")
	public void groupRegistrationRI(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData23 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApproveMOd4")
	public Object[][] getGroupAppMod4Data() throws Exception {
		Object[][] obj = new Object[topicData23.getRowCount()][1];
		for (int i = 1; i <= topicData23.getRowCount(); i++) {
			HashMap<String, String> testData = topicData23.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 14, dataProvider = "groupApproveMOd4")
	public void groupRegistrationApproveMod5(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData11 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course2")
	public Object[][] getGroupReturnDataMod() throws Exception {
		Object[][] obj = new Object[topicData11.getRowCount()][1];
		for (int i = 1; i <= topicData11.getRowCount(); i++) {
			HashMap<String, String> testData = topicData11.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 15, dataProvider = "course2")
	public void groupModification(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Modification Flow.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Modification Flow.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData12 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn")
	public Object[][] getGroupReturnnDataRI() throws Exception {
		Object[][] obj = new Object[topicData12.getRowCount()][1];
		for (int i = 1; i <= topicData12.getRowCount(); i++) {
			HashMap<String, String> testData = topicData12.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 16, dataProvider = "groupReturn")
	public void groupRegistrationReturnRI(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Return Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Modification Return Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_Returns(testData);
		group_Modification.modificationgroup_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData13 = new ExcelUtilUpdated(ExcelPath, "Sheet8");

	@DataProvider(name = "riTranswer")
	public Object[][] getGroupRItranswerData() throws Exception {
		Object[][] obj = new Object[topicData13.getRowCount()][1];
		for (int i = 1; i <= topicData13.getRowCount(); i++) {
			HashMap<String, String> testData = topicData13.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 17, dataProvider = "riTranswer")
	public void groupRITranswer(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps checks the Group Modification Ri-Transfer Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps checks the Group Modification Ri-Transfer Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.modificationgroupRITranswaer(testData);
		group_Modification.modificationgroupRITranswaerWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData14 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate2")
	public Object[][] getGroupReInitiateDataAgain() throws Exception {
		Object[][] obj = new Object[topicData14.getRowCount()][1];
		for (int i = 1; i <= topicData14.getRowCount(); i++) {
			HashMap<String, String> testData = topicData14.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 18, dataProvider = "groupReinitiate2")
	public void groupRegistrationReInitiateAgain(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Re-initiation Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the Group Modification Re-initiation Flow and ensure its update in the Audit Trails.");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupTRSID"),
				ConfigsReader.getPropValue("GroupTRSPwd"));
		
		epiclogin.plant1();

		group_Modification.modificationgroupRITRInitiate(testData);
		group_Modification.modificationgroupRIReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData15 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1Approval2")
	public Object[][] getGroupApproveDataApp() throws Exception {
		Object[][] obj = new Object[topicData15.getRowCount()][1];
		for (int i = 1; i <= topicData15.getRowCount(); i++) {
			HashMap<String, String> testData = topicData15.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 19, dataProvider = "groupRegistration1Approval2")
	public void groupRegistrationApproveAP(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Modification Re-initiation Approval Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Modification Re-initiation Approval Flow and ensure its update in the Audit Trails.");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_TranswerApproval(testData);
		group_Modification.modificationgroup_TranswerApprovalWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

//	-----------------------------------------------------Drop--------------------------------------

	ExcelUtilUpdated topicData16 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration4")
	public Object[][] getGroupDataDP() throws Exception {
		Object[][] obj = new Object[topicData16.getRowCount()][1];
		for (int i = 1; i <= topicData16.getRowCount(); i++) {
			HashMap<String, String> testData = topicData16.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 20, dataProvider = "groupRegistration4")
	public void groupRegistrationDP(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData25 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprovemod5")
	public Object[][] getGroupMod5Data() throws Exception {
		Object[][] obj = new Object[topicData25.getRowCount()][1];
		for (int i = 1; i <= topicData25.getRowCount(); i++) {
			HashMap<String, String> testData = topicData25.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 21, dataProvider = "groupApprovemod5")
	public void groupRegistrationApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData17 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course3")
	public Object[][] getGroupReturnDataDP() throws Exception {
		Object[][] obj = new Object[topicData17.getRowCount()][1];
		for (int i = 1; i <= topicData17.getRowCount(); i++) {
			HashMap<String, String> testData = topicData17.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 22, dataProvider = "course3")
	public void groupModificationDP(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Modification Flow.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Modification Flow.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData18 = new ExcelUtilUpdated(ExcelPath, "Drop");

	@DataProvider(name = "groupDrop")
	public Object[][] getGroupVerifyDataDP() throws Exception {
		Object[][] obj = new Object[topicData18.getRowCount()][1];
		for (int i = 1; i <= topicData18.getRowCount(); i++) {
			HashMap<String, String> testData = topicData18.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */

	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 23, dataProvider = "groupDrop")
	public void groupRegistrationCDrop(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps checks the Group Modification Drop Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps checks the Group Modification Drop Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.groupModificationDrop(testData);
		group_Modification.groupModationDropWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData19 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification2")
	public Object[][] getGroupApproveDataDP() throws Exception {
		Object[][] obj = new Object[topicData19.getRowCount()][1];
		for (int i = 1; i <= topicData19.getRowCount(); i++) {
			HashMap<String, String> testData = topicData19.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 24, dataProvider = "verifyModification2")
	public void VerifyModificationAgain(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the modified and dropped Group is available for further modification at Group Modification screen.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the modified and dropped Group is available for further modification at Group Modification screen.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.verifygroupModification(testData);
		Logout.signOutPage();
	}

	@AfterTest

	public void afterTest() {

		extent.flush();
		MyScreenRecorder.stopRecording();

		driver.quit();
	}
}
