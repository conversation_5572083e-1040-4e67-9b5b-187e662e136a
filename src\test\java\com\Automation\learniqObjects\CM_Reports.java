package com.Automation.learniqObjects;

import java.time.Duration;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.SearchContext;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.Reports;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Reports extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Reports']")
	WebElement courseManagerReportsMenu;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Reports_MEN50']")
	WebElement feedbackReport;
//
//	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Missed Question Analysis Log']")
//	WebElement mqa_Report;
//	
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Reports_MEN73']")
	WebElement mqa_Report;
	
	
	

	@FindBy(xpath = "//span[@class='searchHighlighting']")
	WebElement highlight;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[2]//input")
	WebElement employeeIDCheckBox;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[1]//input")
	WebElement employeeIDTextBox;

	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl00']")
	WebElement viewReport;

	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[2]/div[1]/a[1]/div[1]")
	WebElement batchNameHyperLink;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr[2]//td[2]//select")
	WebElement Type;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr[2]//td[2]//select//option[2]")
	WebElement completedList;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[2]//input")
	WebElement batchNameCheckBox;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[1]//input")
	WebElement batchNameTextBox;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[2]//input")
	WebElement employeeNameCheckBox;

	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[1]//input")
	WebElement employeeNameTextBox;

	@FindBy(xpath = "//div[text()='Total Records Count']")
	WebElement totalRecordCount;

	@FindBy(xpath = "//table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[3]/div/a/div/div/div/span")
	WebElement FeedBackHyperLink;

	@FindBy(xpath = "//span[@class='searchHighlighting']//parent::div/parent::div//parent::td//parent::tr/td[4]")
	WebElement totaltrainees;

	public void feedbackReport() {

		waitForElementVisibile(menu);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(courseManagerReportsMenu);

		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());

		TimeUtil.shortWait();

		scrollToViewElement(feedbackReport);

		click2(feedbackReport, Reports.CLICK_FEEdBACK_REPORT_DC.getReports(),
				Reports.CLICK_FEEdBACK_REPORT_AC.getReports(), Reports.CLICK_FEEdBACK_REPORT_AR.getReports(),
				Reports.CLICK_FEEdBACK_REPORT_SS.getReports());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		// authenticationPopup(ConfigsReader.getPropValue("systemPassword"));

		switchToBodyFrame(driver);

		// This Element is inside single shadow DOM.

		SearchContext shadow = driver.findElement(By.tagName("ssrs-reportviewer")).getShadowRoot();

		TimeUtil.mediumWait();

		WebElement iframe = shadow.findElement(By.cssSelector("iframe[scrolling='no']"));

		driver.switchTo().frame(iframe);

		WebElement toggleIcon = driver.findElement(By.xpath("//input[@id='ReportViewerControl_ToggleParam_img']"));

		click2(toggleIcon, Reports.Click_ToggleBtn_DC.getReports(), Reports.Click_ToggleBtnIER_AC.getReports(),
				Reports.Click_ToggleBtnIER_AR.getReports(), Reports.Click_ToggleBtn_SS.getReports());

		WebElement TrainerNameCheckbox = driver.findElement(By.xpath(
				"//span[text()='Trainer Name']/ancestor::td[@class='ParamLabelCell']/following-sibling::td//input[@id = 'ReportViewerControl_ctl04_ctl03_cbNull' and @type='checkbox']"));

		click2(TrainerNameCheckbox, Reports.UNSELECT_TRAINER_NAME_DC.getReports(),
				Reports.UNSELECT_TRAINER_NAME_AC.getReports(), Reports.UNSELECT_TRAINER_NAME_AR.getReports(),
				Reports.UNSELECT_TRAINER_NAME_SS.getReports());

		TimeUtil.mediumWait();
		WebElement trainerNameTextbox = driver.findElement(By.xpath(
				"//span[text()='Trainer Name']/ancestor::td[@class='ParamLabelCell']/following-sibling::td//input[@id = 'ReportViewerControl_ctl04_ctl03_txtValue']"));
		TimeUtil.mediumWait();
		sendKeys2(trainerNameTextbox, Reports.TRAINER_NAME_DC.getReports(), CM_Trainer.TrainerName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.TRAINER_NAME_SS.getReports());

//		WebElement viewReport = driver.findElement(By.id("ReportViewerControl_ctl04_ctl00"));
//
//		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
//				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		// WebElement totalRecordCount =
		// driver.findElement(By.xpath("//div[text()='Total Records Count']"));

		waitForElementVisibile(totalRecordCount);
		scrollToViewElement(totalRecordCount);
		TimeUtil.mediumWait();
		WebElement trainerNameHyperlink = driver
				.findElement(By.xpath("//table/tbody/tr/td//span[text()='" + CM_Trainer.TrainerName + "']"));
		click2(trainerNameHyperlink, Reports.CLICK_TRAINER_NAME_DC.getReports(), Reports.TRAINER_NAME_AC.getReports(),
				Reports.TRAINER_NAME_AC.getReports(), Reports.TRAINER_NAME_SS.getReports());

		TimeUtil.longwait();

		WebElement findTextbox = driver.findElement(By.xpath("//input[@title='Find Text in Report']"));

		scrollToViewElement(findTextbox);

		sendKeys2(findTextbox, Reports.COURSE_SESSION_DC.getReports(),
				// "CRSNewRQRF",
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), Reports.COURSE_SESSION_SS.getReports());

		WebElement search = driver.findElement(By.id("ReportViewerControl_ctl05_ctl03_ctl01_ctl00"));

		click2(search, "", "", "", "");

		waitForElementVisibile(highlight);

		click2(totaltrainees, "Click on Trainees Count Hypelink", "Feedbacks Should be displayed",
				"Feedbacks are getting displayed", "Feedback");
		// totaltrainees.click();

		TimeUtil.mediumWait();
		click2(FeedBackHyperLink, "Click on FeedBack Tremplate Name", "Enter Feedback Should be displayed",
				"Enter Feedback is getting displayed", "Feedback");
		driver.navigate().refresh();
		switchToDefaultContent(driver);
//		WebElement courseSessionName = driver
//				.findElement(By.xpath("//table[@id='ReportViewerControl_fixedTable']//div[contains(text(), '"
//						+ CM_BatchFormation.courseName + "')]"));

//		WebElement totalNumberTrainees = driver.findElement(By.xpath("//div[contains(text(), '"
//				+ CM_BatchFormation.courseName + "')]/parent::div/parent::td/following-sibling::td[3]//a"));
//
////		scrollToViewElement(courseSessionName);
//
//		TimeUtil.shortWait();
//
//		click2(totalNumberTrainees, "", "", "", "");
//
//		TimeUtil.longwait();
//
//		WebElement employeeName = driver.findElement(By.xpath(
//				"((//div[text()='Feedback']/parent::div/parent::td/parent::*)[2]/following-sibling::tr/td//div[text()])[1]"));
//
//		scrollToViewElement(employeeName);
//
//		WebElement employeeFeedbackHyperlink = driver.findElement(By.xpath(
//				"((//div[text()='Feedback']/parent::div/parent::td/parent::*)[2]/following-sibling::tr/td//div[text()])[1]/parent::div/parent::td/parent::tr/td[3]//a"));
//
//		click2(employeeFeedbackHyperlink, "", "", "", "");
//
//		TimeUtil.longwait();
//
//		switchToDefaultContent(driver);
	}

	public void missedQuestionAnalysisLog_Report(String EmployeeName, String EmployeeStatus) {

		String CourseName = CM_Course.getCourse();

//		String CourseName = "CRSNewNFZT";

		waitForElementVisibile(menu);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		TimeUtil.shortWait();
		scrollToViewElement(mqa_Report);
		click2(mqa_Report, Reports.CLICK_MQA_REPORT_DC.getReports(), Reports.CLICK_MQA_REPORT_AC.getReports(),
				Reports.CLICK_MQA_REPORT_AR.getReports(), Reports.CLICK_MQA_REPORT_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		TimeUtil.longwait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, employeeIDCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(employeeNameCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.mediumWait();
		sendKeys2(employeeNameTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(),
				CM_CourseSession.mqaEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), Reports.EmployeeName_SS.getReports());
		TimeUtil.mediumWait();

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(6));
		// Wait for the element with the specified text to be visible
		WebElement textElement = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//input[@id='ReportViewerControl_ctl04_ctl00']")));

		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		if (EmployeeStatus.equals("Completed List")) {
			click2(Type, Reports.SelectType_DC.getReports(), Reports.SelectType_AC.getReports(),
					Reports.SelectType_AR.getReports(), Reports.SelectType_SS.getReports());
			click2(completedList, Reports.SelectCompletedList_DC.getReports(),
					Reports.SelectCompletedList_AC.getReports(), Reports.SelectCompletedList_AR.getReports(),
					Reports.SelectCompletedList_SS.getReports());

			WebDriverWait wait1 = new WebDriverWait(driver, Duration.ofMinutes(6));
			// Wait for the element with the specified text to be visible
			WebElement textElement1 = wait1.until(ExpectedConditions
					.visibilityOfElementLocated(By.xpath("//input[@id='ReportViewerControl_ctl04_ctl00']")));

			click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
					Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
			TimeUtil.shortWait();
		}
		click2(batchNameCheckBox, Reports.Unselect_CS_Name_DC.getReports(), Reports.Unselect_CS_Name_AC.getReports(),
				Reports.Unselect_CS_Name_AR.getReports(), Reports.Unselect_CS_Name_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(batchNameTextBox, Reports.CS_Name_DC.getReports(), CourseName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.CS_Name_Like_SS.getReports());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		click2(batchNameHyperLink, Reports.Click_CS_hyperlink_DC.getReports(), Reports.ClickBatchName_AC.getReports(),
				Reports.ClickBatchName_AR.getReports(), Reports.CS1_Name_Like_SS.getReports());
		TimeUtil.mediumWait();

		switchToDefaultContent(driver);

	}

}
