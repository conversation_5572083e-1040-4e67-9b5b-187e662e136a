package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.InterimGTPStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.PageTitles;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_InterimGTP extends OQActionEngine {

	public static String IGTPName = "";

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']/preceding-sibling::a[text()='Configure']")
	WebElement configMenu;
	@FindBy(id = "TMS_Course Manager_Configure_MEN57")
	WebElement ConfigGTPMenu;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Propose_MEN67']")
	WebElement ProposeinterimGTPMenu;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Name']")
	WebElement searchByCourseName;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeMenu;
	@FindBy(id = "CourseName")
	WebElement courseName;
	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[2]")
	WebElement searchByNewDropdown;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(xpath = "//input[@id='InterimGTP_GtpDesc']")
	WebElement interimGTPName;
	@FindBy(xpath = "//input[@id='InterimGTP_Description']")
	WebElement description;
	@FindBy(xpath = "//input[@value='2']")
	WebElement subgroupBtn;
	@FindBy(xpath = "//input[@id='CMCourseGTP_TreeVC_SearchTxt']")
	WebElement groupSearchTextBox;
	@FindBy(linkText = "Fetch Records")
	WebElement fetchClick1;
	@FindBy(xpath = "//input[@name='SearchType' and @value='3']")
	WebElement clickUsernameRadio;
	@FindBy(id = "CMCourseGTP_TreeVC_SearchTxt")
	WebElement userNameValue;
	@FindBy(xpath = "//ul[@id='CMCourseGTP_AvailUsers_ul']/li")
	WebElement selectavailableUsers;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//div[text()='Number of Approvals Required for Initiation']")
	WebElement noOfApprovalsReqForInitiationHeading;

	@FindBy(xpath = "//input[@aria-controls= 'select2-InterimGTP_AppSubGrpsMul0-results']")
	WebElement LineofApproverValue;
	@FindBy(xpath = "//span[@class='select2-search select2-search--dropdown']//following-sibling::span")
	WebElement SelectLineofApproverValue;
	@FindBy(id = "select2-InterimGTP_AppSubGrpsMul0-container")
	WebElement LineofApprover;

	public void interimGTPConfiguration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());

		click2(ConfigGTPMenu, InterimGTPStrings.GTP_DC.getInterimGTPStrings(),
				InterimGTPStrings.GTP_Config_AC.getInterimGTPStrings(),
				InterimGTPStrings.GTP_Config_AR.getInterimGTPStrings(),
				InterimGTPStrings.GTP_Config_SS.getInterimGTPStrings());

		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtInitAprCheckBox, "Approval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());
		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		scrollToViewElement(remarks);
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("configureRemarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void ProposeInterim_GTP(HashMap<String, String> testData) {

		click2(menu, PageTitles.LearnIQ_ICON_DC.getTitle(), PageTitles.LearnIQ_ICON_AC.getTitle(),
				PageTitles.LearnIQ_ICON_AR.getTitle(), PageTitles.LearnIQ_ICON_SS.getTitle());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(ProposeinterimGTPMenu, InterimGTPStrings.InterimGTP_DC.getInterimGTPStrings(),
				InterimGTPStrings.Propopse_IGTP_AC.getInterimGTPStrings(),
				InterimGTPStrings.Propopse_IGTP_AR.getInterimGTPStrings(),
				InterimGTPStrings.Propopse_IGTP_SS.getInterimGTPStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				InterimGTPStrings.SearchBy_AC.getInterimGTPStrings(),
				InterimGTPStrings.SearchBy_AR.getInterimGTPStrings(),
				InterimGTPStrings.SearchBy_SS.getInterimGTPStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, InterimGTPStrings.SearchBy_CourseName_DC.getInterimGTPStrings(),
				InterimGTPStrings.SearchBy_CourseName_AC.getInterimGTPStrings(),
				InterimGTPStrings.SearchBy_CourseName_AR.getInterimGTPStrings(),
				InterimGTPStrings.SearchBy_CourseName_SS.getInterimGTPStrings());
		sendKeys2(courseName, InterimGTPStrings.Like_CourseName_DC.getInterimGTPStrings(), CM_Course.Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseSessionStrings.Like_CourseName_SS.getCourseSessionStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, InterimGTPStrings.Click_Req_Course_DC.getInterimGTPStrings(),
				InterimGTPStrings.Click_Req_Course_AC.getInterimGTPStrings(),
				InterimGTPStrings.Click_Req_Course_AR.getInterimGTPStrings(),
				InterimGTPStrings.Click_Req_Course_SS.getInterimGTPStrings());

		IGTPName = CM_Course.Course + testData.get("GTPName");
		sendKeys2(interimGTPName, InterimGTPStrings.Enter_IGTPName_DC.getInterimGTPStrings(), IGTPName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				InterimGTPStrings.Enter_IGTPName_SS.getInterimGTPStrings());
		sendKeys2(description, InterimGTPStrings.Enter_Desc_DC.getInterimGTPStrings(), IGTPName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				InterimGTPStrings.Enter_Desc_SS.getInterimGTPStrings());
		click2(clickUsernameRadio, "", "", "", "");
		String Users = testData.get("UserNames");
		String[] searchStrings = Users.split(",");
		for (int i = 0; i < searchStrings.length; i++) {
			String user = searchStrings[i];
			sendKeys2(userNameValue, InterimGTPStrings.Enter_User_Name.getInterimGTPStrings(),
					user.trim() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					InterimGTPStrings.Select_username_SS.getInterimGTPStrings());
			click2(fetchClick1, InterimGTPStrings.Click_FetchRecords_DC.getInterimGTPStrings(),
					InterimGTPStrings.Click_FetchRecords_AC.getInterimGTPStrings(),
					InterimGTPStrings.Click_FetchRecords_AR.getInterimGTPStrings(),
					InterimGTPStrings.Click_FetchRecords_SS.getInterimGTPStrings());
			TimeUtil.shortWait();
			try {
				click2(selectavailableUsers, "", "", "", "");
			}

			catch (Exception e) {
				System.out.println(user.trim() + "  user not found at propose Interim GTP");
			}
		}

		try {

//			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {
//
//				scrollToViewElement(noOfApprovalsReqForInitiationHeading);
			if (LineofApprover.isDisplayed()) {

				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
						CommonStrings.sendKeys_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
				TimeUtil.shortWait();
				;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.Submit_Course_Approval_AC.getCourseStrings(),
				CourseStrings.Submit_Course_Approval_AR.getCourseStrings(),
				CourseStrings.Submit_Course_Approval_SS.getCourseStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}

}