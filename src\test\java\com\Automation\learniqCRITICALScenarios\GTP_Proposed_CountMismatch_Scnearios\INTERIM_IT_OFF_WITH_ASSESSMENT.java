package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_BatchFormation;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_RecordAttendance;
import com.Automation.learniqObjects.CM_SelfNomination;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class INTERIM_IT_OFF_WITH_ASSESSMENT extends OQActionEngine {


	String ExcelPath = "./learnIQTestData/Interim_P1_TestData/INTERIM_IT_OFF_WITH_ASSESSMENT.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/INTERIM_IT_OFFLINE_W_O_EXAM.xlsx";

	public INTERIM_IT_OFF_WITH_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Registration

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);
	}

	// Test Method for Course Registration
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Cours_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
		Initiate_Course.Refresher_Course_Registration(testData);
	}

	// Test Method for Trainer Modification
	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}

		trainer.trainer_Modification_AuditTrails(testData);

	}

	ExcelUtilUpdated GTPData = new ExcelUtilUpdated(ExcelPath, "ProposeIGTP");

	@DataProvider(name = "ProposeIGTP")
	public Object[][] getGTPData() throws Exception {
		Object[][] obj = new Object[GTPData.getRowCount()][1];
		for (int i = 1; i <= GTPData.getRowCount(); i++) {
			HashMap<String, String> testData = GTPData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "ProposeIGTP", enabled = true)
	public void proposeigtp(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Propose IGTP")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Propose IGTP");
		}
		InterimGTP.ProposeInterim_GTP(testData);
		TimeUtil.longwait();
	}

	// Test Method for CourseSession Registration
	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("Propose Course Session").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Propose Course Session");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		verifyCSScreen.courseSession_Offline_WithExam_New(testData);

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();

	}

	// Test Method for BatchFormation Registration
	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Batch Formation").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Batch Formation");
		}

		BatchFormation.batchFormation_Offline_Users_CountMisMatch(testData);

	}

	// Test Method for QuestionBank Registration
	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionBank", enabled = true)
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
		// Logout.signOutPage();
	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with Manual Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with Manual Evaluation");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOffline, Constants.ITType);

	}
	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}

		RecordAttendance.recordAttendance_OfflineSession_CountMismatch_New(testData);

		driver.navigate().refresh();
	}

	// Test Method for
	// RecordMarks-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordMarksData = new ExcelUtilUpdated(ExcelPath, "Record Marks");

	@DataProvider(name = "Record Marks")
	public Object[][] get_RecordMarks() throws Exception {
		Object[][] obj = new Object[RecordMarksData.getRowCount()][1];
		for (int i = 1; i <= RecordMarksData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordMarksData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 10, dataProvider = "Record Marks", enabled = true)
	public void recordMarks(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Record Marks").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Marks");
		}

		RecordMarks.RecordMarks(testData);

	}

	@Test(priority = 11, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Screen after keping employees in different states")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Course Session Screen after keping employees in different states");
		}
		verifyCSScreen.checkCourseSession_IT_Offline_With_Assessment_After_Keeping_Employees_In_Different_States();

	}

	@Test(priority = 12, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Report Screen after keping employees in different states")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Course Session Report Screen after keping employees in different states");
		}
		CSRReport.TBPCSRReport_IT_Offline_WithAssesment_After_keping_Emplpoyees_In_Diff_States();

	}
}