package com.Automation.Strings;

public enum SubgroupAssignmentStrings {

	SubGroupAssignMenu_DC("Click on 'Subgroup Assignment' submenu."), SubGroupAssign_SS("'Subgroup Assignment'"),
	SubGroupAssign_Config_AC(
			"'Subgroup Assignment Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
					+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	SubGroupAssign_Config_AR("'Subgroup Assignment Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	SubGroupAssign_Config_SS("'Subgroup Assignment'"),

	Click_DoneatSubGroupAssignConfig_AC(
			"'Subgroup Assignment Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatSubGroupAssignConfig_AR(
			"'Subgroup Assignment Configuration Registration' screen is getting displayed.</div>"),

	SubGroupAssign_Modify_AC("'Subgroup Assignment  Modification' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Employee Name, Unique Code, Initiated By, Initiated On, Revision No.' fields.</div>"),
	SubGroupAssign_Modify_AR("'Subgroup Assignment  Modification' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Employee Name, Unique Code, Initiated By, Initiated On, Revision No.' fields.</div>"),

	SearchBy_SubgrpAssignMod_AC(
			"Option to search with 'Top 250 Records, Employee Name, Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_SubgrpAssignMod_AR(
			"Option to search with 'Top 250 Records, Employee Name, Unique Code, Initiated Between' are available.</div>"),

	Select_EmployeeName_DC("Select 'Employee Name'."), EmployeeName_SS("'Employee Name'."),

	Like_EmployeeName_DC("Enter the 'Employee Name' in 'Like' field."),

	Click_EmpName_For_SubgrpAssignMod_DC("Click on the displayed 'Employee Name' record"),
	Click_EmpName_For_SubgrpAssignMod_AC(
			"'Subgroup Assignment Modification Initiation' screen should be displayed.</div>"
					+ "<div><b>*</b> Screen should contain 'Employee Name, Select Subgroup, Remark(s) / Reason(s) fields.<div>"),
	Click_EmpName_For_SubgrpAssignMod_AR(
			"'Subgroup Assignment Modification Initiation' screen is getting displayed.</div>"
					+ "<div><b>*</b> Screen is getting displayed with 'Employee Name, Select Subgroup, Remark(s) / Reason(s) fields.<div>"),
	Click_EmpName_For_SubgrpAssignMod_SS("'Subgroup' - Audit Trails."),

	SearchBy_SubgroupName_DC("Enter the above registered subgroup name in 'Search By' field."),
	SearchBy_SubgroupName_SS("'Search By'"),

	Click_FetchRecords_DC("Click on 'Fetch Records' hyperlink"),
	Click_FetchRecords_AC("Records should be displayed based on the search criteria in 'Subgroup' column.</div>"),
	Click_FetchRecords_AR("Records are getting displayed based on the search criteria in 'Subgroup' column."),
	Click_FetchRecords_SS("'Fetch Records'"),

	Click_DisplayedSubgrp_Record_DC("Click on displayed 'Subgroup' record"),
	Click_DisplayedSubgrp_Record_AC("Selected Subgroup should be moved to 'Selected  Subgroups' column."),
	Click_DisplayedSubgrp_Record_AR("Selected Subgroup is getting moved to 'Selected  Subgroups' column."),
	Click_DisplayedSubgrp_Record_SS("'Selected Subgroups'"),

	Click_DoneatSubgrpAssignMod_AC("'Subgroup Assignment Modification Initiation' screen should be displayed.</div>"),
	Click_DoneatSubgrpAssignMod_AR("'Subgroup Assignment Modification Initiation' screen is getting displayed.</div>"),

	Esign_ProceedSubGroupName_AC(
			"'Subgroup Assignment Change Request Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedSubGroupName_AR(
			"'Subgroup Assignment Change Request Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	// Registration
//Subgroup assignment
	SubGroupAssignmMenu_DC("Click on 'Subgroup Assignment' submenu."),

	SubGroupAssignm_AC("‘Subgroup Assignment - Registration Initiation’ screen should be displayed.</div>"
			+ "<div><b>* </b>The option to search with ‘Employee Name, Subgroup should be available.</div>"
			+ "<div><b>* </b>The screen should contain ‘Fetch Records’, ‘Add All Employees’, ‘Clear All Employees’, ‘Add All Subgroups’, ‘Clear All Subgroups’, hyperlinks.</div>"
			+ "<div><b>* </b>The screen should contain ‘Employee Name’, ‘Selected Employees’, ‘Subgroup’ and ‘Selected Subgroups’ tabs.</div>"
			+ "<div><b>* </b>Active users who are assigned to current login plant all should be displayed under ‘Employee Name’ tab.</div>"
			+ "<div><b>* </b>All the registered and active subgroups in the current login plant should be displayed under ‘Subgroup’ tab.</div>"),
	SubGroupAssignm_AR("‘Subgroup Assignment - Registration Initiation’ screen is getting displayed.</div>"
			+ "<div><b>* </b>The option to search with ‘Employee Name, Subgroup is available.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Fetch Records’, ‘Add All Employees’, ‘Clear All Employees’, ‘Add All Subgroups’, ‘Clear All Subgroups’, hyperlinks.</div>"
			+ "<div><b>* </b>The screen is getting displaying with ‘Employee Name’, ‘Selected Employees’, ‘Subgroup’ and ‘Selected Subgroups’ tabs.</div>"
			+ "<div><b>* </b>Active users who are assigned to current login plant are getting displayed under ‘Employee Name’ tab.</div>"
			+ "<div><b>* </b>All the registered and active subgroups in the current login plant are getting displayed under ‘Subgroup’ tab.</div>"),
	SubGroupAssignm_SS("'Subgroup Assignment Registration Initiation'"),
//Approve Subgroup Assignment Menu
	ApproveSubGroupAssignmMenu_DC("Click on 'Subgroup Assignment' submenu."),

	ApproveSubGroupAssignmMenu_AC("‘Subgroup Assignment  Approval Tasks’ screen should be displayed.</div>"
			+ "<div><b>* </b>Screen should contain ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Employee Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details should be displayed.</div>"
			+ "<div><b>* </b>All the Employee Names whose Subgroup Assignment registration request is to be approved by the current login user should be listed and available for approval.</div>"),
	ApproveSubGroupAssignmMenu_AR("‘Subgroup Assignment  Approval Tasks’ screen is getting displayed.</div>"
			+ "<div><b>* </b>Screen is getting displayed with ‘Registration and Modification’ tabs.</div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen is contianed with ‘Employee Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>By default, the ‘Registration’ tab details are getting displayed.</div>"
			+ "<div><b>* </b>All the Employee Names whose Subgroup Assignment registration request is to be approved by the current login user are getting listed and are available for approval.</div>"),
	ApproveSubGroupAssignmMenu_SS("'Subgroup Assignment Approval Tasks'"),

	// employee radio button
	SubGroupAssignmEmployeeselectMenu_DC("Select Employee Name radio button."),

	SubGroupAssignmEmployeeselectMenu_SS("'Employee Name'"),
//enter employee name
	SubGroupAssignmEmployee_DC("Enter the above registered username."),

	SubGroupAssignmEmployee_SS("'Subgroup Assignment'"),

	// subgroup assignment registration page
	SubGroupAssignmEmployee_Config_AC(
			"'Subgroup Assignment Registration Initiation' screen should be displayed.</div>"),
	SubGroupAssignmEmployee_Config_AR(
			"'Subgroup Assignment Registration Initiation' screen is getting displayed</div>"),
	SubGroupAssignmEmployee_Config_SS("'Subgroup Assignment'"),
//fetch records
	fetchrecords_DC("Click on 'Fetch Records'."), fetchrecords_SS("'Fetch Records'"),
//	select employee name under employee column
	SubGroupAssignmaddEmployee_DC("Select the above registered 'Employee Name' under 'Employee Name' column"),
	SubGroupAssignmaddEmployee_AC("Selected Employee  should be moved to 'Selected Employees' column.</div>"),
	SubGroupAssignmaddEmployee_AR("'Selected Employee is getting  moved to 'Selected Employees' column</div>"),
	SubGroupAssignmaddEmployee_SS("'Selected Employees'"),

	// enter the required subgroup

	SubGroupAssignmsubgroup_DC("Enter the required 'Subgroup Name'"),
	SubGroupAssignmsubgroupReg_DC("Enter the above registered 'Subgroup Name'"),
//click subgroup radio button
	SubGroupAssignmsubgroupselectMenu_DC("Select 'Subgroup' radio button."),

	SubGroupAssignmsubgroupselectMenu_SS("Subgroup'"),
//fetch records
	fetchrecordssubgroup_DC("Click on 'Fetch Records'."),
//select required subgroup name
	SubGroupAssignmaddsubgroup_DC("Select the required  'Subgroup Name'"),
	SubGroupAssignmaddsubgroup_AC("Selected 'Subgroup'  should be moved to 'Selected Subgroups' column.</div>"),
	SubGroupAssignmaddsubgroup_AR("'Selected 'Subgroup'  is getting  moved to 'Selected Subgroups' column</div>"),
	SubGroupAssignmaddsubgroup_SS("'Selected Subgroups '"),
//e sign -proceed
	Esign_SubProceed_AC(
			"'Subgroup Assignment: Registration  Initiated Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_SubProceed_AR(
			"'Subgroup Assignment: Registration  Initiated Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),
//E Sign - Approved Proceed
	Esign_APRProceed_AC(
			"'Subgroup Assignment: Registration Approved Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_APRProceed_AR(
			"'Subgroup Assignment: Registration Approved Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	// esign
	Esign_sub_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Subgroup Assignment: Registration Initiation'.</div>"),
	Esign_sub_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Subgroup Assignment: Registration Initiation'.</div>"),
	Submitsub_SS("'E-Sign window'"),

	// Approve Esign
	Esign_Approve_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Subgroup Assignment: Registration Approval:Approve'.</div>"),
	Esign_Approve_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Subgroup Assignment: Registration Approval:Approve'.</div>"),

	// Audit trails
	SubGroupAssignAudittrails_DC("Click on 'Subgroup Assignment' submenu."),
	SubGroupAssignAudittrails_AC("'Subgroup Assignment Audit Trails' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Employee Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>*" + " </b>The screen should contain a list of all the registered subgroup assignments.</div>"),
	SubGroupAssignAudittrails_AR("'Subgroup Assignment Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is contained with ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen is contained with ‘Employee Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>*" + " </b>The screen is contained with list of all the registered subgroup assignments.</div>"),
	SubGroupAssignAudittrails_SS("'Subgroup Assignment Audit Trails' screen."),

	// Click latest modified Modification tab

	Click_LastesModTab_DC("Click on the latest Modification Revision No.:(No) Tab."),
	Click_LastesModTab_AC("'Selected tab should be displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button should be enabled.<div>"),
	Click_LastesModTab_AR("'Selected tab is getting displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button is getting enabled.<div>"),

	// Click Proceed Button

	Click_Proceed_AC("'Trainer - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
			+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),

	Click_Proceed_AR("'Trainer - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
			+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),

	Click_ModifiedEmployee_for_AuditTrails_SS("'Transactions'"),

	Click_Employee_for_AuditTrails_SS("'Subgroup Assignment- Audit Trails'."),

	// Search By

	SearchBy_AC(
			"The option to search with ‘Top 250 Records’, ‘Employee Name’, ‘Unique Code’ and ‘Initiated Between’ should be available.</div>"),

	SearchBy_AR(
			"The option to search with ‘Top 250 Records’, ‘Employee Name’, ‘Unique Code’ and ‘Initiated Between’ is available.</div>"),

	// Click Employee Name

	Click_Employee_AT_DC(
			"Click on the above employee name for whom the subgroup assignment registration is completed."),
	Click_Employee_APRAT_DC(
			"Click on the above employee name for whom the subgroup assignment registration is approved."),
	Click_Employee_APRAT_AC(
			"‘Subgroup Assignment - Audit Trails  Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>‘Final Status’ should be displayed as ’Approved’.</div>"
					+ "<div><b>* </b>The ‘Events’ section should contain ‘Initiated and Approved' transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘1’.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
	Click_Employee_APRAT_AR(
			"‘Subgroup Assignment - Audit Trails  Revision No.: 0 - Registration’ screen is getting displayed.</div>"
					+ "<div><b>* </b>‘Final Status’ is getting displayed as ’Approved’.</div>"
					+ "<div><b>* </b>The ‘Events’ section is contained with ‘Initiated and Approved' transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is reading as ‘1’ and the ‘No. of Approvals Completed’ is reading as ‘1’.</div>"
					+ "<div><b>* </b>All the particulars ar getting displayed in read only format.</div>"),

	Click_Employee_AT_AC(
			"‘Subgroup Assignment - Audit Trails  Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details entered at the time of registration.</div>"
					+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section should contain the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘0’.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
	Click_Employee_AT_AR(
			"‘Subgroup Assignment - Audit Trails  Revision No.: 0 - Registration’ screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details entered at the time of registration.</div>"
					+ "<div><b>* </b>‘Final Status’ is getting displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section is contained with only the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is getting read as ‘1’ and the ‘No. of Approvals Completed’ is getting read as ‘0’.</div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),
	Click_Employee_AT_SS("'Subgroup Assignment - Audit Trails'"),

	// Click Employee Name Approval Screen

	Click_Employee_APR_AC("‘Subgroup Assignment Registration Approval’ screen should be displayed.</div>"
			+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section should contain only the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ should be read as 1 and the ‘No. of Approvals Completed’ should be read as 0.</div>"
			+ "<div><b>* </b>Approve, Return & Drop’ options should be available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>‘Drop’ radio button should be displayed in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter/ select ‘Remark(s) / Reason(s)’ should be available.</div>"),
	Click_Employee_APR_AR("‘Subgroup Assignment Registration Approval’ screen is getting displayed.</div>"
			+ "<div><b>* </b>‘Final Status’ is getting displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section is getting displayed with only the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ is getting read as 1 and the ‘No. of Approvals Completed’  read as 0.</div>"
			+ "<div><b>* </b>Approve, Return & Drop’ options are available for ‘Decision’ field.</div>"
			+ "<div><b>* </b>‘Drop’ radio button is getting displayed in disabled mode.</div>"
			+ "<div><b>* </b>The option to enter/ select ‘Remark(s) / Reason(s)’ is available.</div>"),
	Click_Employee_APR_SS("'Subgroup Assignment Registration Approval'"),

	// Close Audit Trails

	Close_AuditTrailsTopic_AC("'Subgroup Assignment Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsTopic_AR("'Subgroup Assignment Audit Trails' screen is getting displayed.</div>"),

	// SubGroup configuration Audit trails

	SubgroupAssignment_ConfigAudit_DC("Click on 'Subgroup Assignment' submenu."),
	SubgroupAssignment_ConfigAudit_AC(
			"'Subgroup Assignment Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
					+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	SubgroupAssignment_ConfigAudit_AR(
			"'Subgroup Assignment Configuration Audit Trails' screen is getting displayed</div>" + "<div><b>*</b>"
					+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	SubgroupAssignment_ConfigAudit_SS("'Subgroup Assignment Configuration Audit Trails'"),

	Click_SubgroupAssign_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_SubgroupAssign_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should be displayed with Registration and Modification tabs with Revsion numbers.</div>"
			+ "<div><b>* </b>The screen should be displayed with 'Proceed' button.</div>"
			+ "<div><b>* </b>Proceed button should be displayed in disabled mode.</div>"),
	Click_SubgroupAssign_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with Registration and Modification tabs with Revsion numbers.</div>"
			+ "<div><b>* </b>The screen is getting displayed with 'Proceed' button.</div>"
			+ "<div><b>* </b>Proceed button is getting displayed in disabled mode.</div>"),
	Click_SubgroupAssign_ConfigAuditTrails_SS("'Transactions'."),

	// Config Audit Trails window
	Click_Config_Proceed_AC(
			"'Subgroup Assignment Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should be displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>* </b>The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>* </b>All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Subgroup Assignment Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displayed with the details entered/selected during Registration of 'Groups Configuration'.<div>"
					+ "<div><b>* </b>The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>* </b>All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Subgroup Assignment Configuration - Audit Trails'."),

	// Close Audit Trails

	Close_ConfigAuditTrails_SubgroupAssign_AC(
			"'Subgroup Assignment Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_SubgroupAssign_AR(
			"'Subgroup Assignment Configuration Audit Trails' screen is getting displayed.</div>");

	private final String subgroupAssignmentStrings;

	SubgroupAssignmentStrings(String subgroupAssignmentStrings) {

		this.subgroupAssignmentStrings = subgroupAssignmentStrings;

	}

	public String getSubgroupAssignmentStrings() {
		return subgroupAssignmentStrings;
	}

}
