package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;

public class Registration_RITranswer_Group extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	public Registration_RITranswer_Group() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration", enabled = false)
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Registration With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Group Registration With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

//	@Test(priority = 3)
//	public void VerifyModification() {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Group Registration With AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Group Registration With AuditTrails");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		Group.verifiModification();
//		Logout.signOutPage();
//	}

	ExcelUtilUpdated topicData5 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn")
	public Object[][] getGroupReturnnData() throws Exception {
		Object[][] obj = new Object[topicData5.getRowCount()][1];
		for (int i = 1; i <= topicData5.getRowCount(); i++) {
			HashMap<String, String> testData = topicData5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 4, dataProvider = "groupReturn")
	public void groupRegistrationReturn(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Return With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Return With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.group_Returns(testData);
		Group.group_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Sheet8");

	@DataProvider(name = "riTranswer")
	public Object[][] getGroupRItranswerData() throws Exception {
		Object[][] obj = new Object[topicData2.getRowCount()][1];
		for (int i = 1; i <= topicData2.getRowCount(); i++) {
			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 5, dataProvider = "riTranswer")
	public void groupRITranswer(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Return With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Return With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRITranswaer(testData);
		Group.groupRITranswaerWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate")
	public Object[][] getGroupReInitiateData() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 6, dataProvider = "groupReinitiate")
	public void groupRegistrationReInitiate(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Re-Initiate With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Group Re-Initiate With AuditTrails");
		}

	
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRITRInitiate(testData);
		Group.groupRIReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

//	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");
//
//	@DataProvider(name = "groupReinitiate")
//	public Object[][] getGroupReInitiateData() throws Exception {
//		Object[][] obj = new Object[topicData3.getRowCount()][1];
//		for (int i = 1; i <= topicData3.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	/**
//	 * Method is for Group Re-Initiate
//	 * 
//	 */
//	// ---------------------------------------------------------------------------------------------------------------------------
//	@Test(priority = 7, dataProvider = "groupReinitiate")
//	public void groupRegistrationReInitiate(HashMap<String, String> testData) {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Group Re-Initiate With AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Group Re-Initiate With AuditTrails");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		Group.verifyGroupReInitiate(testData);
////		Group.groupReInitiateWithAuditTrails_Yes(testData);
//		Logout.signOutPage();
//	}

	ExcelUtilUpdated topicData4 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1Approval")
	public Object[][] getGroupApproveData() throws Exception {
		Object[][] obj = new Object[topicData4.getRowCount()][1];
		for (int i = 1; i <= topicData4.getRowCount(); i++) {
			HashMap<String, String> testData = topicData4.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * @throws Throwable 
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 8, dataProvider = "groupRegistration1Approval")
	public void groupRegistrationApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Approvals With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Approvals With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupTRSID"),
				ConfigsReader.getPropValue("GroupTRSPwd"));
		epiclogin.plant1();

		Group.group_TranswerApproval(testData);
		Group.group_TranswerApprovalWithAuditTrials_Yes(testData);

		Logout.signOutPage();
	}
//
//	@Test(priority = 9)
//	public void VerifyModificationAgain() {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Group Registration With AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Group Registration With AuditTrails");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		Group.verifiModificationReAgain();
//		Logout.signOutPage();
//	}

	@AfterTest

	public void afterTest() {

		extent.flush();
		MyScreenRecorder.stopRecording();

		driver.quit();
	}
}
