package com.Automation.Strings;

public enum BatchFormationStrings {

	// Batch Formation submenu

	BatchForMenu_DC("Click on 'Batch Formation' submenu."), BatchForMenu_SS("'Batch Formation'"),

	BatchFormation_Config_AC("'Batch Formation Configuration Registration' screen should be displayed</div>"
			+ "<div><b>*</b> Option to select the E-Sign at Registration and 'No of Approvals Required' should be available in disabled mode.</div>"),
	BatchFormation_Config_AR("'Batch Formation Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b>  E-Sign is getting selected at Registration  and 'No of Approvals Required' is  displayed in disabled mode.</div>"),
	BatchFormation_Config_SS("'Configuration'"),

	// BatchFormation configuration Audit trails

	BatchFormation_ConfigAudit_DC("Click on 'Batch Formation' submenu."),
	BatchFormation_ConfigAudit_AC("'Batch Formation Configuration Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	BatchFormation_ConfigAudit_AR("'Batch Formation Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	BatchFormation_ConfigAudit_SS("'Batch Formation' submenu"),

	// BatchFormation configuration Audit trails
	Click_BatchFormationfor_ConfigAuditTrails_DC("Click on the above registered 'Unique Code'."),
	Click_BatchFormationfor_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_BatchFormationfor_ConfigAuditTrails_AR("'Transactions' screen is getting.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_BatchFormationfor_ConfigAuditTrails_SS("'Batch Formation Configuration' - Audit Trails."),

	// configuration proceed
	Click_BatchFormationConfig_Proceed_AC(
			"'Batch Formation Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Batch Formation Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_BatchFormationConfig_Proceed_AR(
			"'Batch Formation Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Batch Formation Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_BatchFormationConfig_Proceed_SS("'Batch Formation Configuration - Audit Trails'."),

	Close_BatchFormationConfigAuditTrails_AC(
			"'Batch Formation Configuration Audit Trails' screen should be displayed.</div>"),
	Close_BatchFormationConfigAuditTrails_AR(
			"'Batch Formation Configuration Audit Trails' screen is getting displayed.</div>"),

//Search By

	SearchBy_AC(
			"Option to search with 'Top 250 Records, Course Session Name and Initiated Between' should be displayed.</div>"),
	SearchBy_AR(
			"Option to search with 'Top 250 Records, Course Session Name and Initiated Between' are getting displayed.</div>"),

	// Propose Batch Formation

	Propose_BatchFor_AC("'Batch Formation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Course Session Name, Session Type, Training Type and Initiated On' details.</div>"),
	Propose_BatchFor_AR("'Batch Formation' screen are getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Course Session Name, Session Type, Training Type and Initiated On' details.</div>"),
	Propose_BatchFor_SS("'Batch Formation' screen"),

	BatchForConfig_SS("'Configuration'"),

	Click_DoneatBatchForConfig_AC("'Batch Formation Configuration Registration' screen should be displayed.</div>"),

	Click_DoneatBatchForConfig_AR("'Batch Formation Configuration Registration' screen is getting displayed.</div>"),

	BatchForMenu_AC("'Batch Formation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Course Session Name, Session Type, Training Type, Session Initiated On' details.</div>"),
	BatchForMenu_AR("'Batch Formation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Course Session Name, Session Type, Training Type, Session Initiated On' details accurately.</div>"),

	// Search By Course Session Name

	SearchBy_CourseSessionName_DC("Select 'Course Session Name'."),
	SearchBy_CourseSessionName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseSessionName_AR("Selection is getting accepted.</div>"),
	SearchBy_CourseSessionName_SS("'Course Session Name'"),

	// Enter the Course Session Name for self nomination

	Like_CourseSessNameself_DC(
			"Enter the above registered 'Course Session Name' for which the Self Nomination is proposed in 'Like' field."),
	Like_CourseSessNameself_SS("Course Session Name"),

	// Enter the Course Session Name for Not Responded Candidates list

	Like_CourseSessName_DC("Enter the above registered 'Course Session Name' in 'Like' field."),
	Like_CourseSessName_SS("Course Session Name"), Like_BatchName_SS("Batch Formation Name"),

	RegisteredCourseSess_DC("Click on above registered Course Session."),
	RegisteredCourseSess_AC("Batch Formation Registration Initiation screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the Session Details should be displayed.</div>"),
	RegisteredCourseSess_AR("Batch Formation Registration Initiation screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the Session Details is getting displayed.</div>"),
	RegisteredCourseSess_SS("'Batch Formation'"),

	// Enter the Batch formation name

	Like_BFName_DC("Enter the above registered 'Batch Formation Name' in 'Like' field."),

	// Not Responded Candidates list

	NotResCan_SelectAll_DC("Click on 'Select All' checkbox."), NotResCan_SelectAll_AC("Selection  should be accepted."),
	NotResCan_SelectAll_AR("Selection is getting accepted"), NotResCan_SelectAll_SS("'SelectAll'"),

	// Responded Candidates list

	ResCan_SelectAll_DC("Click on 'Select All' checkbox under 'Responded Candidates' section"),
	ResCan_SelectAll_AC("Selection should be accepted."), ResCan_SelectAll_AR("Selection is getting accepted"),
	ResCan_SelectAll_SS("'SelectAll'"),

	// Self-Nominated Candidates list

	SelfCan_SelectAll_DC("Click on 'Select All' checkbox under 'Self-Nominated Candidates' section"),
	SelfCan_SelectAll_AC("Selection should be accepted."), SelfCan_SelectAll_AR("Selection is getting accepted"),
	SelfCan_SelectAll_SS("'SelectAll'"),

	// Offline Candididate List

	Offline_SelectAll_DC("Click on 'Select All' checkbox under 'Off-Line Candidates' section"),

	// Esign Proceed

	Esign_ProceedBatchFor_AC(
			"'Batch Formation Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedBatchFor_AR(
			"'Batch Formation  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	// Bach Formation audit trails list screen
	BatchFor_AuditTrails_Menu_AC("'Batch Formation Audit Trails' screen should be displayed.<div>"),
	BatchFor_AuditTrails_Menu_AR("'Batch Formation Audit trails' screen is getting displayed.</div>"),
	BatchFor_AuditTrails_Menu_SS("'Batch Formation Audit Trails'"),

	// Audit trails search by

	AT_SearchBy_AC(
			"Option to search with 'Top 250 Records, Batch Formation Name, Unique Code and Initiated Between' should be available."),
	AT_SearchBy_AR(
			"Option to search with 'Top 250 Records, Batch Formation Name, Unique Code and Initiated Between' are available.</div>"),
//Search by Batch Formation Name 
	SearchBy_BatchFormationName_DC("Select 'Batch Formation Name'."),
	SearchBy_BatchFormationName_AC("Selection should be accepted.<div>"),
	SearchBy_BatchFormationName_AR("Selection is getting accepted.<div>"),
	SearchBy_BatchFormationName_SS("'Batch Formation Name'."),
//search by course session Name
	SearchBy_CoursSessionName_DC("Select 'Course Session Name'"),
	SearchBy_CoursSessionName_AC("Selection should be accepted.<div>"),
	SearchBy_CoursSessionName_AR("Selection is getting accepted.<div>"),
	SearchBy_CoursSessionName_SS("'Course Session Name'."),

	Click_BF_for_AuditTrails_DC("Click on the above registered 'Batch Formation Name'."),
	Click_BF_for_AuditTrails_AC(
			"'Batch Formation -Audit Trails: Revision No.: 0 - Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> All the entered/selected at registration details should be displayed in read only mode.<div>"),
	Click_BF_for_AuditTrails_AR(
			"'Batch Formation -Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the entered/selected at registration details are getting displayed in read only mode.<div>"),

	Click_BF_for_AuditTrails_SS("'Batch Formation Audit Trails."),

	// Submit Button

	Submit_DC("Click on 'Submit' button."),
	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Batch Formation: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Batch Formation: Registration Initiation'.</div>"),
	Submit_SS("'E-Sign window'"),

	// Select One by One

	Select_OneUser_DC("Select the required under 'Non Responded Candidated' section'"),
	Select_OneUser2_DC("Similarly select one more user."),
	Select_OneUser3_DC("Similarly select one more user and make sure that atleast one user should be skipped."),

	// Search By BatchFormation Name

	SearchBy_BatchFormation_DC("Select 'Batch Formation Name'."),
	SearchBy_BatchFormation_AC("Selection should be accepted.<div>"),
	SearchBy_BatchFormation_AR("Selection is getting accepted.<div>"),
	SearchBy_BatchFormation_SS("'Batch Formation Name'."),

	BatchFormationRegistrationScreen_DC("Click on 'Batch Formation' menu."),
	BatchFormationRegistrationScreen_AC("‘Batch Formation’ screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain ‘Search this Page’, ‘Advanced Search’ and Total Records Count’ icons.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records’, ‘Course Session Name and Initiated Between’ should be available.</div>"
			+ "<div><b>*</b>The screen should contain ‘Course Session Name, Session Type, Training Type and session initiated On’ columns.</div>"
			+ "<div><b>*</b>List of Classroom type Course Session Name(s), which are registered with/ without an assessment, whether On-Line or Off-Line, for which Batch Formation needs to be registered.</div>"),

	BatchFormationRegistrationScreen_AR("‘Batch Formation’ screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains ‘Search this Page’, ‘Advanced Search’ and Total Records Count’ icons.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records’, ‘Course Session Name and Initiated Between’ are available.</div>"
			+ "<div><b>*</b>The screen  contains ‘Course Session Name, Session Type, Training Type and session initiated On’ columns.</div>"
			+ "<div><b>*</b>List of Classroom type Course Session Name(s), which are registered with/ without an assessment, whether On-Line or Off-Line, for which Batch Formation needs are  registered.</div>"),

	BatchFormationRegistrationScreen_SS("'Batch Formation'"),

	BatchFormationRegisteredCourseSess_DC("Click on 'Batch Formation' menu."),
	BatchFormationRegisteredCourseSess_AC("‘Batch Formation Registration Initiation’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Course Name’, ‘Batch Strength’, ‘Training Method’, ‘Assessment Required’, ‘Evaluation Required’, ‘Feedback Required’, ‘Total Number of Trainees’ and ‘Session Information Management’ details under ‘Course Session Details’ section..</div>"
			+ "<div><b>*</b>The screen should contain ‘Course Session Name’, ‘Start Date’, ‘End Date’, ‘Start Time’, ‘End Time’, ‘Total Hours’, ‘Trainer Name(s)’ and ‘Venue Name(s)’ details under ‘Session Details’ section..</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Response’, ‘Remarks’, ‘Self-Nominated On’ and ‘Select All’ columns under ‘Self-Nominated Candidates (Count No.) section.</div>"
			+ "<div><b>*</b>As of now, ‘No data available in table’ message should be displayed under ‘Responded Candidates(Count No.)’ and ‘Self-Nominated Candidates(Count No.)’ sections.</div>"
			+ "<div><b>*</b> List of Employee Name(s) who were selected at the time of Course Session Registration and not yet responded the course invitation of respective batch should be displayed under ‘Not Responded Candidates (Count No.)’ section..</div>"
			+ "<div><b>*</b> The option to select the listed Employee(s) should be available.</div>"
			+ "<div><b>*</b>The screen should contain ‘View Existing’ and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered Batch Formation records is getting  displayed (if any).</div>"),
	BatchFormationRegisteredCourseSess_AR(
			"‘Batch Formation Registration Initiation’ screen is getting  displayed.</div>"
					+ "<div><b>*</b>The screen  contains ‘Course Name’, ‘Batch Strength’, ‘Training Method’, ‘Assessment Required’, ‘Evaluation Required’, ‘Feedback Required’, ‘Total Number of Trainees’ and ‘Session Information Management’ details under ‘Course Session Details’ section.</div>"
					+ "<div><b>*</b>The screen  contains ‘Course Session Name’, ‘Start Date’, ‘End Date’, ‘Start Time’, ‘End Time’, ‘Total Hours’, ‘Trainer Name(s)’ and ‘Venue Name(s)’ details under ‘Session Details’ section..</div>"
					+ "<div><b>*</b>The screen contains ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Response’, ‘Remarks’, ‘Self-Nominated On’ and ‘Select All’ columns under ‘Self-Nominated Candidates (Count No.) section.</div>"
					+ "<div><b>*</b>As of now, ‘No data available in table’ message should be displayed under ‘Responded Candidates(Count No.)’ and ‘Self-Nominated Candidates(Count No.)’ sections.</div>"
					+ "<div><b>*</b> List of Employee Name(s) who were selected at the time of Course Session Registration and not yet responded the course invitation of respective batch is  displayed under ‘Not Responded Candidates (Count No.)’ section.</div>"
					+ "<div><b>*</b> The option to select the listed Employee(s) are available.</div>"
					+ "<div><b>*</b>The screen contains ‘View Existing’ and ‘Submit’ buttons.</div>"
					+ "<div><b>*</b>Under ‘View Existing’, registered Batch Formation records is  displayed (if any).</div>"),

	BatchFormationRegisteredCourseSess_SS("'Batch Formation Registration Initiation'"),

	click_BatchFormationAuditTrails_DC("Click on 'Batch Formation' submenu."),

	click_BatchFormationAuditTrails_SS("'Batch Formation Audit Trials'"),

	click_BatchFormationAuditTrails_AC("‘Batch Formation Audit Trails’ screen should be displayed..</div>"
			+ "<div><b>*</b>The screen should contain ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Batch Formation Name, Unique Code, Initiated Between  should be available.</div>"
			+ "<div><b>*</b> The screen should contain ‘Batch Formation Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns..</div>"
			+ "<div><b>*</b> By default, ‘Records Per Page’ should be displayed as ‘20’.</div>"

			+ "<div><b>*</b>The screen should contain a list of all the Batch Name(s) for which registration is completed.</div>"
			+ "<div><b>*</b> The records of proposed course sessions with the document reading training method should also be displayed in the Batch Formation Audit Trails (if any).</div>"),

	click_BatchFormationAuditTrails_AR("‘Batch Formation Audit Trails’ screen is  getting displayed..</div>"
			+ "<div><b>*</b>The screen  contains ‘Search this Page’, Advanced Search’ & ‘Total Records Count’ icons..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Batch Formation Name, Unique Code, Initiated Between  are available.</div>"
			+ "<div><b>*</b> The screen contains ‘Batch Formation Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>*</b> By default, ‘Records Per Page’ is getting displayed as ‘20’.</div>"

			+ "<div><b>*</b>The screen contains a list of all the Batch Name(s) for which registration is completed.</div>"
			+ "<div><b>*</b> The records of proposed course sessions with the document reading training method is also displayed in the Batch Formation Audit Trails (if any).</div>"),

	click_BatchFormationAuditTrailsdisplayrecord_AC(
			"‘Batch Formation -Audit Trails: - Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the details entered/ selected at the time of Batch Formation registration.</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain only the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘0’.</div>"

					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	click_BatchFormationAuditTrailsdisplayrecord_AR(
			"‘Batch Formation -Audit Trails: - Revision No.: 0 - Registration’ screen is getting  displayed.</div>"
					+ "<div><b>*</b>The screen contains the details entered/ selected at the time of Batch Formation registration.</div>"
					+ "<div><b>*</b>‘Final Status’ is  displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b> The ‘Events’ section contains only the Registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both are read as ‘0’.</div>"

					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>");

	private final String batchFormationStrings;

	BatchFormationStrings(String batchFormationStrings) {

		this.batchFormationStrings = batchFormationStrings;

	}

	public String getBatchFormationStrings() {
		return batchFormationStrings;
	}

}
