package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Course extends OQActionEngine {

	Properties prop;
	public static String Course = "";
	public static String subgroupName = "";
	public static String Trainingtype = "";
	public static boolean retakeAssessmentFeildOpted;
	public static String subgroupUniqueCode = "";
	public static String CourseDescription = "";
	public static String ScheduleType = "";
	public static String CoursePurpose = "";

	public static String getCourseDescription() {
		return CourseDescription;
	}

	public static void setCourseDescription(String courseDescription) {
		CourseDescription = courseDescription;
	}

	public static String getSubgroupUniqueCode() {
		return subgroupUniqueCode;
	}

	public static void setSubgroupUniqueCode(String subgroupUniqueCode) {
		CM_Course.subgroupUniqueCode = subgroupUniqueCode;
	}

	public static String Approver = "";

	public static String getTrainingtype() {
		return Trainingtype;
	}

	public static void setTrainingtype(String trainingtype) {
		Trainingtype = trainingtype;
	}

	public static String getSubgroupName() {
		return subgroupName;
	}

	public static void setSubgroupName(String subgroupName) {
		CM_Course.subgroupName = subgroupName;
	}

	public static String getCourse() {
		return Course;
	}

	public static void setCourse(String course) {
		Course = course;
	}

	public static String getApproverName() {
		return Approver;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	public boolean getRetakeAssessmentFeildOpted() {
		return retakeAssessmentFeildOpted;
	}

	public static String getSCHType() {
		return ScheduleType;
	}

	public static void setSCHType(String SchType) {
		ScheduleType = SchType;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[9]/a[1]")
	WebElement configMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[2]//a[contains(@class,'sub-menu')][contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(id = "TMS_Course Manager_Configure_MEN20")
	WebElement configCourseMenu;
	@FindBy(id = "TMS_Course Manager_Initiate_MEN20")
	WebElement courseMenu;
	@FindBy(xpath = "//input[@id='CMCourse_CourseDesc']")
	WebElement courseNameTxtFld;
	@FindBy(xpath = "//input[@id='CMCourse_Description']")
	WebElement descriptionText;
	@FindBy(xpath = "//input[@id='SelfStudyReqRD_1']")
	WebElement selfStudyNo;
	@FindBy(xpath = "//input[@id='RetakeReqRD_1']")
	WebElement retakeNo;
	@FindBy(xpath = "//input[@id='RetakeReqRD_0']")
	WebElement retakeYes;
	@FindBy(xpath = "//div[1]/span[2]/span[1]/span[1]/span[1]")
	WebElement trainingType;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement trainingTypeSearchField;
	@FindBy(xpath = "//ul[@id='select2-CMCourse_TrainingType-results']/li[1]")
	WebElement trainingTypeOption;
	@FindBy(xpath = "//div[@class='conrol-column col-sm-12 p-2 border rounded']//input[@value='3']")
	WebElement topicRadioButton;
	@FindBy(xpath = "//input[@id='CMCourseTopic_TreeVC_SearchTxt']")
	WebElement searchField;
	@FindBy(xpath = "//input[@id='CMCourseGTP_TreeVC_SearchTxt']")
	WebElement subGroupSearchField;
	@FindBy(xpath = "//a[@onclick='CMCourseTopicTreeVc.SearchRecords()']")
	WebElement fetchRecords;
	@FindBy(xpath = "//a[@onclick='CMCourseGTPTreeVc.SearchRecords()']")
	WebElement subGroupFetchRecords;
	@FindBy(xpath = "//ul[@id='CMCourse_AvailableTopics_ul']/li[1]")
	WebElement availableTopic;
	@FindBy(xpath = "//div[@id='pagediv1']//button[@class='caliber-button-primary nextTab']")
	WebElement nextNew;
	@FindBy(xpath = "//div[@id='pagediv3']//button[@class='caliber-button-primary nextTab']")
	WebElement preview;
	@FindBy(xpath = "//div[@id='pagediv2']//button[@class='caliber-button-primary nextTab']")
	WebElement masterCoursePreview;
	@FindBy(xpath = "//thead/tr[1]/th[3]/input[1]")
	WebElement selectAllCheckBox;
	@FindBy(xpath = "//button[@id='NextBtn2']")
	WebElement nextNew1;
	@FindBy(xpath = "//div[@id='pagediv3']//div[@class='conrol-column col-sm-12 p-2 border rounded']//input[@value='2']")
	WebElement subGroupRadioButton;
	@FindBy(xpath = "//ul[@id='CMCourseGTP_AvailSubgrps_ul']/li[1]")
	WebElement availableSubGroup;
	@FindBy(id = "SelSgpAppTypeRD_2")
	WebElement notRequired;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropDown;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDropDown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDropDown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[1]")
	WebElement searchSel3;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//div[@class='sub-page-layout-footer-actions btmSubmitDiv']//button[@id='btnSubmit']")
	WebElement masterCourseSubmit;
	@FindBy(xpath = "//input[@id='CourseTypeRD_1']")
	WebElement refresherCourse;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//button[@id='btnModal_CMCourse_Assignment']")
	WebElement assignment;
	@FindBy(xpath = "//input[@id='CMCourse_Assignment_FindTxt']")
	WebElement assignmentFindTxt;
	@FindBy(xpath = "//input[@id='CMCourse_Assignment_DisplayBtn']")
	WebElement applyBtn;
	@FindBy(xpath = "//table[@id='multipopupfilter2']/tbody/tr/td//button[@class='CMCourse_AssignmentAddRemoveBtn btn btn-sm  notselected SelectAll AsmntCheck']")
	WebElement addBtn;
	@FindBy(xpath = "//button[@id='CMCourse_Assignment_selectBtn']")
	WebElement addBtn2;
	@FindBy(xpath = "//span[@class='select2-search select2-search--dropdown']//input")
	WebElement txtEnter;
	@FindBy(xpath = "//ul[@id='select2-CMCourse_TrainingType-results']/li")
	WebElement trainingSelect;
	@FindBy(id = "SelfStudyReqRD_1")
	WebElement selfStudy;
	@FindBy(xpath = "//input[@id='SelfStudyReqRD_1']//following-sibling::label")
	WebElement selfStudyNew;
	@FindBy(xpath = "//input[@class='radio'][3]")
	WebElement topicRadio;
	@FindBy(xpath = "//label[text()='Topic Name']")
	WebElement topicRadioNew;
	@FindBy(id = "CMCourseTopic_TreeVC_SearchTxt")
	WebElement findText;
	@FindBy(id = "SearchBtn")
	WebElement fetchClick;
	@FindBy(xpath = "//ul[@id='CMCourse_AvailableTopics_ul']/li[1]")
	WebElement topicSelect1;
	@FindBy(xpath = "//button[@class='caliber-button-primary nextTab']")
	WebElement nextButton;
	@FindBy(xpath = "//th[normalize-space()='Select All']/input")
	WebElement docCheckbox;
	@FindBy(xpath = "//*[contains(text(),'Select All')]//input")
	WebElement docCheckbox1;
	@FindBy(id = "RetakeReqRD_1")
	WebElement retakeAssessment;
	@FindBy(id = "RetakeReqRD_0")
	WebElement retakeAssessmentYes;

	@FindBy(xpath = "//input[@id='RetakeReqRD_1']//following-sibling::label")
	WebElement retakeAssessmentNew;
	@FindBy(id = "NextBtn2")
	WebElement nextBtn11;
	@FindBy(xpath = "//span[text()='Subgroup Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement subGrp;
	@FindBy(xpath = "//label[text()='Subgroup Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement subGrpNew;
	@FindBy(id = "CMCourseGTP_TreeVC_SearchTxt")
	WebElement subGroupSelect;
	@FindBy(linkText = "Fetch Records")
	WebElement fetchClick1;
	@FindBy(xpath = "//ul[@id='CMCourseGTP_AvailSubgrps_ul']/li")
	WebElement subGrpSelect;
	@FindBy(id = "SelSgpAppTypeRD_2")
	WebElement approvals;
	@FindBy(xpath = "//*[@id=\"pagediv3\"]/div[3]/div/div/div/div/button[1]")
	WebElement previewButton;
	@FindBy(id = "btnSubmit")
	WebElement finalSubmit;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']//li//a[text()='Course']")
	WebElement aduitTrailsCourse;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Name')]")
	WebElement searchByCourseNameDropdown;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement courseNameLike;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Course Name']//following-sibling::span")
	WebElement auditCompareTRNCourseName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']//following-sibling::span")
	WebElement auditCompareTRNUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Description']//following-sibling::span")
	WebElement auditCompareTRNDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Course Type']//following-sibling::span")
	WebElement auditCompareTRNCourseType;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Training Type']//following-sibling::span")
	WebElement auditCompareTRNTrainingType;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='External Training']//following-sibling::span")
	WebElement auditCompareTRNExternalTraining;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Self-Study']//following-sibling::span")
	WebElement auditCompareTRNSelfStudy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Retake Assessment Required']//following-sibling::span")
	WebElement auditCompareTRNRetakeAssessmentRequired;
	@FindBy(xpath = "//label[contains(text(),'Selected Documents')]")
	WebElement auditCompareTRNSelectedDocuments;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Assignment(s)']//following-sibling::span")
	WebElement auditCompareTRNAssignments;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement auditCompareTRNDocumentName;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Code']/parent::tr/parent::thead//following-sibling::tbody//td[2]")
	WebElement auditCompareTRNDocumentCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Category']/parent::tr/parent::thead//following-sibling::tbody//td[3]")
	WebElement auditCompareTRNTopicName;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Category']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement auditCompareTRNTopicCategory;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Category']/parent::tr/parent::thead//following-sibling::tbody//td[2]")
	WebElement auditCompareTRNTopicSubject;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement auditCompareTRNDocument1;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[2]//a")
	WebElement auditCompareTRNDocument1hyper;
	@FindBy(xpath = "//div[@id='PreviewModal']//span[@id='PreviewCloseBtn']")
	WebElement documentReadClose;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Subgroup Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement auditCompareTRNSubgroupName;
	@FindBy(xpath = "//label[text()='Approval for Candidature']//following-sibling::span")
	WebElement auditCompareTRNApprovalForCandidature;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Assignment(s)']//following-sibling::span")
	WebElement auditCompareTRNAssignments1;

	@FindBy(id = "txtESignPassword")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//ul[@id='select2-CMCourse_TrainingType-results']/li[6]")
	WebElement trainingSelect1;
	@FindBy(xpath = "//*[@id='RetakeDiv']/div/div/label")
	WebElement RetakeOpt;
	@FindBy(xpath = "//*[@id='tpcDocsDiv']/div/div[2]/div/table/tbody/tr/td[1]/a")
	WebElement DocName;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//label[text()='Refresher Training Required']")
	WebElement refresherType;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement esignWindowTitle;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configuration Audit Trails']//li//a[text()='Course']")
	WebElement configurationCourseAuditTrails;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Approve']//following-sibling::ul//li//a[text()='Course']")
	WebElement approveCourseMenu;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[contains(text(),'Course Name')]")
	WebElement approveCourse_SearchBy;
	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationApprovalAction;
	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;
	@FindBy(xpath = "//ul[@id='CMCourseGTP_SelSgps_ul']//li[1]")
	WebElement selectedSubgroup;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approveLabelText;
	@FindBy(xpath = "//ul[@id='CMCourse_SelTopics_ul']//li[1]")
	WebElement selectedTopic;
	@FindBy(xpath = "//div[@id='CompareTRN']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[3]//a")
	WebElement approverPageDocumentCode;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Course Name']")
	WebElement previewPageCourseNameTitle;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Course Name']//following-sibling::div//span")
	WebElement previewPageCourseName;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Unique Code']//following-sibling::div//span")
	WebElement previewPageUniqueCode;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Description']//following-sibling::div//span")
	WebElement previewPageDescription;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Course Type']//following-sibling::div//span")
	WebElement previewPageCourseType;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='External Training']//following-sibling::div//span")
	WebElement previewPageExternalTraining;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Self-Study']//following-sibling::div//span")
	WebElement previewPageSelfStudy;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Training Type']//following-sibling::div//span")
	WebElement previewPageTrainingType;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Assignment(s)']//following-sibling::div//span")
	WebElement previewPageAssignments;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Selected Topics']//following-sibling::div//h6")
	WebElement previewPageSelectedTopicsText;
	@FindBy(xpath = "//div[@id='pagediv4']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]//a")
	WebElement previewPageDocumentName;
	@FindBy(xpath = "//div[@id='pagediv4']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[2]")
	WebElement previewPageDocumentReviewDate;
	@FindBy(xpath = "//div[@id='pagediv4']//th[text()='Document Name']/parent::tr/parent::thead//following-sibling::tbody//td[3]")
	WebElement previewPageDocumentVersionNo;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Retake Assessment Required']//following-sibling::div//span")
	WebElement previewPageRetakeAssessmentRequired;
	@FindBy(xpath = "//div[@id='pagediv4']//th[text()='Subgroup Name']/parent::tr/parent::thead//following-sibling::tbody//td[1]")
	WebElement previewPageSubgroupName;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Approval for Candidature']//following-sibling::div//span")
	WebElement previewPageApprovalForCandidature;
	@FindBy(xpath = "//div[@id='pagediv2']//label[text()='Selected Topics']//following-sibling::div//h6")
	WebElement linkDocumentsPageselectedTopicsText;
	@FindBy(xpath = "//div[@id='pagediv4']//label[text()='Retake Assessment Required']")
	WebElement previewPageRetakeAssessmentRequiredTitle;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callAtEsignInitiationLabel;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callAtEsignApprovalLabel;
	@FindBy(xpath = "//table[@class='table linkDocSelect']//td[2]")
	WebElement linkDocumentsPageDocumentReviewDate;
	@FindBy(xpath = "//table[@class='table linkDocSelect']//td[3]")
	WebElement linkDocumentsPageDocumentVersionNum;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByCourseUniqueCodeDropdown;
	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniqueCodeLike;
	@FindBy(xpath = "//div[@id='pagediv2']//button[contains(text(),'Preview')]")
	WebElement masterCoursePreviewBtn;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;

	public CM_Course() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * This method is for Course Configuration
	 * 
	 */
	public void courseConfiguration_Reg(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(configCourseMenu, CourseStrings.CourseConfig_DC.getCourseStrings(),
				CourseStrings.CourseConfig_AC.getCourseStrings(), CourseStrings.CourseConfig_AR.getCourseStrings(),
				CourseStrings.CourseConfig_SS.getCourseStrings());
		switchToBodyFrame(driver);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "RegistrationApproval");
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());
		waitForElementVisibile(ApprovalValue);
		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("CourseApprovalRequired"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), "Remarks",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		highLightElement(driver, confirmationText, "ConfirmationText", test);
		click2(confirmationDone, CommonStrings.Click_DoneatConfig_DC.getCommonStrings(),
				TopicStrings.Click_DoneatTopicConfig_AC.getTopicStrings(),
				TopicStrings.Click_DoneatTopicConfig_AR.getTopicStrings(),
				CommonStrings.Click_DoneatConfig_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}

	public void Course_Registration(HashMap<String, String> testData) {
		String s = "";
		// String initiator= SYS_Subgroup.getInitiatorName();
		// String initiator = "Shyam2.shyam2(shyam2)";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			System.out.println("Generated S Value is: " + s);
		}
		// String CentralRetakeValue = SetCentralConfig.getCetralRetakeAssessment();
//		String CentralRetakeValue = "No";
		// String ScheduledsubgroupName = SYS_Subgroup.getSubGroupval();
//		String ScheduledsubgroupName = "ATSCSubgroupZYG";
		setSubgroupName(subgroupName = testData.get("SubgroupNamevalue"));
		setSCHType(ScheduleType = testData.get("ScheduleType"));

//		String DocumentName = CM_Topic.getDocumentName();
//		// String DocumentName ="object";
		setCourse(Course = testData.get("CourseName") + s);

//		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingSelect, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(selfStudy, CourseStrings.SelfStudyNo_DC.getCourseStrings(),
				CourseStrings.SelfStudyNo_AC.getCourseStrings(), CourseStrings.SelfStudyNo_AR.getCourseStrings(),
				CourseStrings.SelfStudyNo_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		// }

		// String TopicNameVal = "TopicLEIQVQWB";
		String TopicNameVal = CM_Topic.getTopic();

		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		TimeUtil.mediumWait();
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		// TimeUtil.shortWait();
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());
		List<WebElement> radioButtons = driver.findElements(By.xpath("//div[@id='RetakeDiv']//input[@type='radio']"));
		if (!radioButtons.isEmpty()) {
			retakeAssessmentFeildOpted = true;
			click2(retakeAssessment, CourseStrings.RetakeAssessmentNo_DC.getCourseStrings(),
					CourseStrings.RetakeAssessmentNo_AC.getCourseStrings(),
					CourseStrings.RetakeAssessmentNo_AR.getCourseStrings(),
					CourseStrings.RetakeAssessmentNo_SS.getCourseStrings());
		}

		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		if (ScheduleType.equals("Interim")) {
			clickandHandleAlert(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		}

		// if(testData.get("CoursePurpose")=="Interim") {

//			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
//					CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
//			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
//					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
//					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
//					CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		else {
			click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
			sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
					testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseStrings.SubgroupNameLike_SS.getCourseStrings());
			TimeUtil.shortWait();
			click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
			TimeUtil.mediumWait();
			click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
					CourseStrings.AvailableSubgrp_SS.getCourseStrings());
			TimeUtil.shortWait();
			click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		}
		// TimeUtil.shortWait();
		// String initiator = initiatorName.getAttribute("value");
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		saveUniqueCode(driver, confirmationText);
		waitForElementVisibile(confirmationText);
		saveCourseUniqueCode(driver, confirmationText);

		// TimeUtil.shortWait();
		switchToDefaultContent(driver);

		// AuditTrails
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		// TimeUtil.shortWait();
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		// TimeUtil.shortWait();
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		// TimeUtil.shortWait();
//		click2(aduitTrailsCourse, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		// TimeUtil.shortWait();
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		// TimeUtil.shortWait();
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), Course + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		// TimeUtil.shortWait();
//		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		// TimeUtil.shortWait();
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		// TimeUtil.shortWait();
//
//		driver.switchTo().frame(0);
//		// TimeUtil.shortWait();
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//
//		switchToDefaultContent(driver);

	}

	public void course_Approval_AuditTrials_Yes(HashMap<String, String> testData) {

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(approve, CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_AC.getCommonStrings(), CommonStrings.CM_ApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveCourseMenu, CourseStrings.CourseSubMenu_DC.getCourseStrings(),
				CourseStrings.CourseApproveMenu_AC.getCourseStrings(),
				CourseStrings.CourseApproveMenu_AR.getCourseStrings(),
				CourseStrings.CourseSubMenu_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				CourseStrings.SearchBy_Course_AC.getCourseStrings(),
				CourseStrings.SearchBy_Course_AR.getCourseStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(approveCourse_SearchBy, CourseStrings.Select_CourseName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CourseUniqueCode_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CourseStrings.Select_CourseName_SS.getCourseStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_Approve_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_Approve_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_Approve_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_Approve_SS.getCourseStrings());
		scrollToViewElement(approveRadioBtn);
		click2(approveRadioBtn, CourseStrings.CourseApproval_DC.getCourseStrings(),
				CourseStrings.CourseApproval_AC.getCourseStrings(), CourseStrings.CourseApproval_AR.getCourseStrings(),
				CourseStrings.CourseApproval_SS.getCourseStrings());
		sendKeys2(groupApproveRemarks, CommonStrings.Approve_Remarks_DC.getCommonStrings(),
				testData.get("ApproverRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approve_Remarks_SS.getCommonStrings());
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.Submit_Course_Approval_AC.getCourseStrings(),
				CourseStrings.Submit_Course_Approval_AR.getCourseStrings(),
				CourseStrings.Submit_Course_Approval_SS.getCourseStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

		// AuditTrails

//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		click2(aduitTrailsCourse, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		highLightElement(driver, highLightScreenTitle, "Audit Trails screen", test);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		// TimeUtil.shortWait();
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), Course + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_Approve_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_Approve_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		driver.switchTo().frame(0);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_Icon_SS.getCommonStrings());
//		switchToDefaultContent(driver);

	}

	public void Refresher_Course_Registration(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		setSubgroupName(subgroupName = testData.get("SubgroupNamevalue"));
		setSCHType(ScheduleType = testData.get("ScheduleType"));

//		String DocumentName = CM_Topic.getDocumentName();
//		// String DocumentName ="object";
		setCourse(Course = testData.get("CourseName") + s);

//		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		//
		click2(refresherCourse, "", "", "", "");

		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingSelect, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());
		click2(selfStudy, CourseStrings.SelfStudyNo_DC.getCourseStrings(),
				CourseStrings.SelfStudyNo_AC.getCourseStrings(), CourseStrings.SelfStudyNo_AR.getCourseStrings(),
				CourseStrings.SelfStudyNo_SS.getCourseStrings());
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		// }

		// String TopicNameVal = "TopicLEIQVQWB";
		String TopicNameVal = CM_Topic.getTopic();

		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		// TimeUtil.shortWait();
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		// TimeUtil.shortWait();
		waitForElementVisibile(topicSelect1);
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		// TimeUtil.shortWait();
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());
		List<WebElement> radioButtons = driver.findElements(By.xpath("//div[@id='RetakeDiv']//input[@type='radio']"));
		if (!radioButtons.isEmpty()) {
			retakeAssessmentFeildOpted = true;
			click2(retakeAssessmentYes, "", "", "", "");
		}

		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		if (ScheduleType.equals("Interim")) {
			clickandHandleAlert(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		} else {
			click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
			sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
					testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseStrings.SubgroupNameLike_SS.getCourseStrings());
			TimeUtil.shortWait();
			click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
			TimeUtil.mediumWait();
			click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
					CourseStrings.AvailableSubgrp_SS.getCourseStrings());
			TimeUtil.mediumWait();
			click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		}
		// TimeUtil.shortWait();
		// String initiator = initiatorName.getAttribute("value");
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		waitForElementVisibile(confirmationText);

		saveUniqueCode(driver, confirmationText);
		switchToDefaultContent(driver);

	}

	public void Refresher_Course_Registration_With_Out_GTP(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		setSubgroupName(subgroupName = testData.get("SubgroupNamevalue"));
		setSCHType(ScheduleType = testData.get("ScheduleType"));

//		String DocumentName = CM_Topic.getDocumentName();
//		// String DocumentName ="object";
		setCourse(Course = testData.get("CourseName") + s);

//		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		//
		click2(refresherCourse, "", "", "", "");

		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingSelect, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());
		click2(selfStudy, CourseStrings.SelfStudyNo_DC.getCourseStrings(),
				CourseStrings.SelfStudyNo_AC.getCourseStrings(), CourseStrings.SelfStudyNo_AR.getCourseStrings(),
				CourseStrings.SelfStudyNo_SS.getCourseStrings());
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		// }

		// String TopicNameVal = "TopicLEIQVQWB";
		String TopicNameVal = CM_Topic.getTopic();

		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		// TimeUtil.shortWait();
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		// TimeUtil.shortWait();
		waitForElementVisibile(topicSelect1);
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		// TimeUtil.shortWait();
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());
		List<WebElement> radioButtons = driver.findElements(By.xpath("//div[@id='RetakeDiv']//input[@type='radio']"));
		if (!radioButtons.isEmpty()) {
			retakeAssessmentFeildOpted = true;
			click2(retakeAssessmentYes, "", "", "", "");
		}

		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		if (ScheduleType.equals("Interim")) {
			clickandHandleAlert(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		} else {
			click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
					CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
			sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
					testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CourseStrings.SubgroupNameLike_SS.getCourseStrings());
			TimeUtil.shortWait();
			click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
					CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
			TimeUtil.mediumWait();
			click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
					CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
					CourseStrings.AvailableSubgrp_SS.getCourseStrings());
			TimeUtil.mediumWait();
			click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
					CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());
			click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
					CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		}
		// TimeUtil.shortWait();
		// String initiator = initiatorName.getAttribute("value");
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		waitForElementVisibile(confirmationText);

		saveUniqueCode(driver, confirmationText);
		switchToDefaultContent(driver);

	}

}
