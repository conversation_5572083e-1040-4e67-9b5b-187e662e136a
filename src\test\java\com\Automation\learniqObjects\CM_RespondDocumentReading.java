package com.Automation.learniqObjects;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.RespondDRStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_RespondDocumentReading extends OQActionEngine {
	public static String Sessionname ="";
	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[8]//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respondMenu;

	@FindBy(id = "TMS_Course Manager_Respond_MEN32")
	WebElement documentReading;

	@FindBy(id = "learniqPreviewCloseBtn")
	WebElement previewCloseBtn;

	@FindBy(id = "txtESignPassword")
	WebElement eSign1;

	@FindBy(id = "Submit_Esign")
	WebElement eSignProceed;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[8]")
	WebElement respond;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Respond']/li[4]")
	WebElement resDocRead;

	@FindBy(xpath = "//div[@id='ListTab_filter']/label/input")
	WebElement search;

	@FindBy(xpath = "//input[@class='caliber-labeled-option Cmptclass']")
	WebElement completed;

	@FindBy(xpath = "//table[@id=\"ListTab\"]/tbody/tr/td[1]")
	WebElement displayedDoc;

	@FindBy(id = "btnSubmit")
	WebElement submit;

	@FindBy(xpath = "//*[contains(text(), 'Click To Read')]")
	WebElement clickToReadBtn;

	@FindBy(xpath = "//a[@id='cfnMsg_Next']")
	WebElement done;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;

	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//tbody/tr/td[text()='No data available in table']")
	WebElement noDataAvailable;
	
	@FindBy(xpath = "//input[@class='caliber-labeled-option']")
	WebElement inProgress;
	
	@FindBy(id = "MandatoryCrs")
	WebElement MandatoryCrs;
	
	@FindBy(id = "OpenCrs")
	WebElement OpenCrs;

	
	@FindBy(xpath = "//label[text()='Course Name']/following-sibling::input")
	WebElement VerifyCourseValue;

	public void respondDocReading(String esignPsw) {
		/* By using getter method getting course name */

		String CourseNameValue = CM_Course.getCourse();

		//String CourseNameValue = "Course_1609";
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(resDocRead, RespondDRStrings.DRMenu_DC.getRespondDRStrings(),
				RespondDRStrings.DRMenu_AC.getRespondDRStrings(), RespondDRStrings.DRMenu_AR.getRespondDRStrings(),
				RespondDRStrings.DRMenu_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CourseNameValue,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());
		TimeUtil.shortWait();
		click2(displayedDoc, RespondDRStrings.Click_CourseSession_DC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AR.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_SS.getRespondDRStrings());
		waitForElementVisibile(clickToReadBtn);
		click2(clickToReadBtn, RespondDRStrings.Click_To_Read_DC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AR.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_SS.getRespondDRStrings());
		TimeUtil.shortWait();
		driver.switchTo().parentFrame();
		TimeUtil.shortWait();
		click2(previewCloseBtn, RespondDRStrings.Preview_Close_DC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AR.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		click2(completed, RespondDRStrings.Select_Completed_DC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AR.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_SS.getRespondDRStrings());
		click2(submit, RespondDRStrings.Submit_Button_DC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AR.getRespondDRStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());
		
		
		
		sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), esignPsw,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}
	public void verifyRespondDocReading() {
		/* By using getter method getting course name */

		String CourseNameValue = CM_Course.getCourse();

//		String CourseNameValue = "AutomationCourseZYPL";

		TimeUtil.shortWait();

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());

		click2(resDocRead, RespondDRStrings.DRMenu_DC.getRespondDRStrings(),
				RespondDRStrings.DRMenu_AC.getRespondDRStrings(), RespondDRStrings.DRMenu_AR.getRespondDRStrings(),
				RespondDRStrings.DRMenu_SS.getRespondDRStrings());

		switchToBodyFrame(driver);

		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CourseNameValue,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());

		highlightEle2(noDataAvailable);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
	
	public void respondDocReading_Mandatory_Self_Study_Course( String Status, String Password) {
		/* By using getter method getting course name */


		// String CourseNameValue = "CRSNewORPQ";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(respond);
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(resDocRead, RespondDRStrings.DRMenu_DC.getRespondDRStrings(),
				RespondDRStrings.DRMenu_AC.getRespondDRStrings(), RespondDRStrings.DRMenu_AR.getRespondDRStrings(),
				RespondDRStrings.DRMenu_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		click2(MandatoryCrs, "Click on Mandatory Courses tab", "", "", "");
		TimeUtil.mediumWait();
		waitForElementVisibile(search);
		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CM_SelfStudyCourse.Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		click2(displayedDoc, RespondDRStrings.Click_CourseSession_DC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AR.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		WebElement ele = driver.findElement(By.xpath("//label[text()='Course Session Name']/following-sibling::input"));
		Sessionname = ele.getAttribute("value").trim();
		waitForElementVisibile(clickToReadBtn);
		click2(clickToReadBtn, RespondDRStrings.Click_To_Read_DC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AR.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_SS.getRespondDRStrings());
		TimeUtil.shortWait();
		driver.switchTo().parentFrame();
		TimeUtil.shortWait();
		click2(previewCloseBtn, RespondDRStrings.Preview_Close_DC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AR.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		
		 if(Status.equals("Completed")){
		click2(completed, RespondDRStrings.Select_Completed_DC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AR.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_SS.getRespondDRStrings());
		}
		 else {
			 click2(inProgress, "Select InProgress",
						RespondDRStrings.Select_Completed_AC.getRespondDRStrings(),
						RespondDRStrings.Select_Completed_AR.getRespondDRStrings(),
						RespondDRStrings.Select_Completed_SS.getRespondDRStrings());
			 
		 }
		click2(submit, RespondDRStrings.Submit_Button_DC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AR.getRespondDRStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());
		
		sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), Password,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}
	
	public void respondDocReading_Self_Study_Course(String Status, String Password) {
		waitForElementVisibile(search);
		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CM_SelfStudyCourse.Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		click2(displayedDoc, RespondDRStrings.Click_CourseSession_DC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AR.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		WebElement ele = driver.findElement(By.xpath("//label[text()='Course Session Name']/following-sibling::input"));
		Sessionname = ele.getAttribute("value").trim();
		waitForElementVisibile(clickToReadBtn);
		click2(clickToReadBtn, RespondDRStrings.Click_To_Read_DC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AC.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_AR.getRespondDRStrings(),
				RespondDRStrings.Click_To_Read_SS.getRespondDRStrings());
		TimeUtil.shortWait();
		driver.switchTo().parentFrame();
		TimeUtil.shortWait();
		click2(previewCloseBtn, RespondDRStrings.Preview_Close_DC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AC.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_AR.getRespondDRStrings(),
				RespondDRStrings.Preview_Close_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		
		 if(Status.equals("Completed")){
		click2(completed, RespondDRStrings.Select_Completed_DC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AC.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_AR.getRespondDRStrings(),
				RespondDRStrings.Select_Completed_SS.getRespondDRStrings());
		}
		 else {
			 click2(inProgress, "Select InProgress",
						RespondDRStrings.Select_Completed_AC.getRespondDRStrings(),
						RespondDRStrings.Select_Completed_AR.getRespondDRStrings(),
						RespondDRStrings.Select_Completed_SS.getRespondDRStrings());
			 
		 }
		click2(submit, RespondDRStrings.Submit_Button_DC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AC.getRespondDRStrings(),
				RespondDRStrings.RespondDR_Esign_AR.getRespondDRStrings(),
				CommonStrings.Submit_Button_Esign_SS.getCommonStrings());
		
		sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(), Password,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}
	
	public void check_CourseName_At_respondDocReading() {
		/* By using getter method getting course name */


		//String CourseNameValue = "Course_1609";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(resDocRead, RespondDRStrings.DRMenu_DC.getRespondDRStrings(),
				RespondDRStrings.DRMenu_AC.getRespondDRStrings(), RespondDRStrings.DRMenu_AR.getRespondDRStrings(),
				RespondDRStrings.DRMenu_SS.getRespondDRStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(OpenCrs);
		click2(OpenCrs, "Click on Open Self Study Courses tab", "", "", "");
		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CM_SelfStudyCourse.Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		verifyExactCaption(displayedDoc, "No data available in table", "Old Course Name should not exist");

		sendKeys2(search, RespondDRStrings.Enter_CSName_RSPDR_DC.getRespondDRStrings(), CM_SelfStudyCourse.ModifiedCourse,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondDRStrings.Enter_CSName_RSPDR_SS.getRespondDRStrings());
		TimeUtil.mediumWait();
		verifyExactCaption(displayedDoc, CM_SelfStudyCourse.ModifiedCourse, "Modified Course Name");
		click2(displayedDoc, RespondDRStrings.Click_CourseSession_DC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AC.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_AR.getRespondDRStrings(),
				RespondDRStrings.Click_CourseSession_SS.getRespondDRStrings());
		waitForElementVisibile(clickToReadBtn);
		verifyExactValueInFeild(VerifyCourseValue, CM_SelfStudyCourse.ModifiedCourse, "Modified Course Name");
		switchToDefaultContent(driver);
	}
	
	
	
	
	
	
	
	

}


