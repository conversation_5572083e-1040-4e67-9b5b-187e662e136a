package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.GroupStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class SYS_Group_StatusChange extends OQActionEngine {

	Properties prop;
	public static String RegGroupName1 = "";
	public static String RegDescription1 = "";

	public static String RegGroupName2 = "";
	public static String RegDescription2 = "";

	public static String RegGroupName3 = "";
	public static String RegDescription3 = "";
	public static String Initiator = "";
	public static String Approver = "";
	public static String GroupUC = "";

	public static String getRegGroupName() {
		return RegGroupName1;
	}

	public static void setRegGroupName(String regGroupName) {
		RegGroupName1 = regGroupName;
	}

	public static String getRegDescription() {
		return RegDescription1;
	}

	public static void setRegDescription(String regDescription) {
		RegDescription1 = regDescription;
	}

	public static String getInitiatorName() {
		return Initiator;
	}

	public static void setInitiatorName(String InitiatorName) {
		Initiator = InitiatorName;
	}

	public static String getApproverName() {
		return Approver;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	public static String getGroupUC() {
		return GroupUC;
	}

	public static void setGroupUC(String GroupUniqueCode) {
		GroupUC = GroupUniqueCode;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Configure')]")
	WebElement configurationMenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN08")
	WebElement groupCnfgMenu;

	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtInitCheckBox;

	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDropdown;

	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForStatusDropdown;

	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDropdown;

	@FindBy(id = "Config_Remarks")
	WebElement r1emarks;

	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[2]")
	WebElement select1ForInitDropdown;

	@FindBy(id = "Config_Remarks")
	WebElement remarks;

	@FindBy(id = "btnSubmit")
	WebElement submitButton;

	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;

	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;

	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;

	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;

	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ModApprovalValue;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtModify-results']//child::li")
	WebElement clickModApprovalCount;

	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtStatusChange-results']//child::li")
	WebElement clickStatusApprovalCount;

//	 ConfigureRemarks  configureConfirmation Zero
	public void group_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(configurationMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(groupCnfgMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Config_AC.getGroupStrings(), GroupStrings.Group_Config_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);

//	Registration	

		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(noOfAprReqForRegDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

//	modification	

		SelectRadioBtnAndCheckbox(driver, eSignAtModInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtModAprCheckBox, "Call E-sign At: Approval Registration");
		click2(noOfAprReqForModDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ModApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("ModificationApprovalCount"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickModApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

//		Status Change 
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtSTCAprCheckBox, "Call E-sign At: Approval Registration");

		click2(noOfAprReqForStatusDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ModApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("StatusChangeApp"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickStatusApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		JavascriptExecutor js3 = (JavascriptExecutor) driver;
		js3.executeScript("arguments[0].scrollIntoView();", remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		highLightElement(driver, confirmationText, "ConfirmationText", test);
		click2(confirmationDone, CommonStrings.Click_DoneatConfig_DC.getCommonStrings(),
				GroupStrings.Click_DoneatGroupConfig_AC.getGroupStrings(),
				GroupStrings.Click_DoneatGroupConfig_AR.getGroupStrings(),
				CommonStrings.Click_DoneatConfig_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();
	}

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Status Change')]")
	WebElement statusChyangeMenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN129_SUBMEN08")
	WebElement statusChangeGroup;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;

	@FindBy(xpath = "//span[@role='presentation']")
	WebElement searchByNewFilter;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Group')]")
	WebElement searchByGroupNameDropdown;

	@FindBy(id = "Description")
	WebElement groupNameLike;

	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[contains(text(),'Group Name')]")
	WebElement approveGroup_SearchBy;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;

	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;

	@FindBy(id = "Groups_GrpDesc")
	WebElement groupNameTxt;

	@FindBy(id = "Groups_Description")
	WebElement descriptionTxt;

	@FindBy(id = "Remarks")
	WebElement ReInitiateremarks;

	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[2]")
	WebElement revisionNoValueModCompareTRN;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Unique Code']//following-sibling::span")
	WebElement auditModUniqueCode;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Group Name']//following-sibling::span")
	WebElement auditModGroupName;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Description']//following-sibling::span")
	WebElement auditModDescription;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Subgroup Name(s)')]//following-sibling::span")
	WebElement auditModSubgroupName;

	@FindBy(xpath = "//button[text()='Status Change']")
	WebElement statusChangeTabMenu;

	@FindBy(xpath = "//button[text()='Inactive']")
	WebElement inActiveTabMenu;

	@FindBy(xpath = "//span[contains(text(),'Active')]")
	WebElement activeLabel;

	@FindBy(xpath = "//span[contains(text(),'Inactive')]")
	WebElement inActiveLabel;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;

	public void groupStatusChange(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(statusChyangeMenu, statusChangeGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(statusChangeGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), SYS_Group.RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(auditTrailPageColumn1, SYS_Group.RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueModCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		TimeUtil.shortWait();
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasonsMod"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.STATUSCHANGE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN08")
	WebElement groupAuditTrails;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;

	@FindBy(xpath = "//span[@class='audit-revision-region-value']")
	WebElement revisionNoValueCompareTRN;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']//following-sibling::span")
	WebElement auditUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Group Name']//following-sibling::span")
	WebElement auditGroupName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Description']//following-sibling::span")
	WebElement auditDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Subgroup Name(s)')]//following-sibling::span")
	WebElement auditSubgroupName;

	@FindBy(xpath = "(//div[@id='CompareTRN']//h6[@class='status_heading'])")
	WebElement auditCompareTRNFinalStatus;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionModInitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByModinitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeModinitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksModinitiateVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNModReqVal;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNModComVal;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;

	@FindBy(xpath = "//div[text()='Modification'] ")
	WebElement transwerRecord;

	@FindBy(xpath = "//div[text()='Status Change'] ")
	WebElement statusChangeRecord;

	@FindBy(xpath = "(//div[text()='Status Change']) [2]")
	WebElement statusChangeRecordInActive;

	@FindBy(xpath = "(//div[text()='Status Change']) [3]")
	WebElement statusChangeRecordInActiveA;

	@FindBy(id = "AuditEventModal_View")
	WebElement proceedButton;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']")
	WebElement auditCompareTRNActionDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditCompareTRNAppRemarksDropVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareTRNActionApproveValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeModApproveValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeApproveValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareTRNAppAppRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareTRNAppRemarksVal1;

	public void groupStatusChangeWithAuditTrails(HashMap<String, String> testData) {

		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);

		click2(statusChangeRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
	
	
	public void groupStatusChangeWithAuditTrailsADA(HashMap<String, String> testData) {

		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);

		click2(statusChangeRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}


	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Approve']//following-sibling::ul//li//a[text()='Group']")
	WebElement approveGroupMenu;

	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;

	@FindBy(xpath = "//input[@id='SelectedDecision_4']")
	WebElement dropRadioBtn;

	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;

	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationApprovalAction;

	public void groupStatusChangeDrop(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, SYS_Group.RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");

//		verifyExactCaption(auditTrailPageColumn3, initiator, "Initiated By");

		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueModCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		TimeUtil.shortWait();

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", dropRadioBtn);
		highLightElement(driver, dropRadioBtn, "Group Approval", test);
		clickAndWaitforNextElement(dropRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("RemarksReasonsAP"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_STATUSCHANGE_TEXT_ESIGN;
		String ApprovalAction = Constants.DROP_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.GROUP_STATUSCHANGEDROP_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void groupStatusChangeDropWithAuditTrails(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		click2(statusChangeRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_DROPPED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionDropValue,
				auditCompareApproveTRNActionByDropValue, auditCompareTRNDateTimeDropValue,
				auditCompareTRNAppRemarksDropVal1, Constants.DROP_ACTIONVAL, initiator, Constants.GROUPDROPREMAKS);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Modify')]")
	WebElement modifyMenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN08")
	WebElement modifyGroup;

	@FindBy(xpath = "//td[text()='No data available in table']")
	WebElement nodataAvailabel;

	public void verifiModificationGroup(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(modifyMenu, modifyGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(modifyGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

//		highlightEle2(nodataAvailabel);
	}

	public void groupStatusChangeApprove(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, SYS_Group.RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");

//		verifyExactCaption(auditTrailPageColumn3, initiator, "Initiated By");

		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueModCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		TimeUtil.shortWait();

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, approveRadioBtn, "Group Approval", test);
		clickAndWaitforNextElement(approveRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("RemarksReasonsAP"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_STATUSCHANGE_TEXT_ESIGN;
		String ApprovalAction = Constants.APPROVAL_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.STATUSCHANGE_APPROVE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void groupStatusChangeApproveWithAuditTrails(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		click2(statusChangeRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionApproveValue,
				auditCompareApproveTRNActionByValue, auditCompareTRNDateTimeModApproveValue,
				auditCompareTRNAppAppRemarksVal1, Constants.APPROVE_ACTIONVAL, initiator, Constants.GROUPAPPROVEREMAKS);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupStatusChangeInActive(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(statusChyangeMenu, statusChangeGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(statusChangeGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(inActiveTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.longwait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), SYS_Group.RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, SYS_Group.RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueModCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		String statusINAc = Constants.STATUSCHANGE_INACTIVE;
		verifyExactCaption(inActiveLabel, statusINAc, "Subgroup Name");

		String statusAc = Constants.STATUSCHANGE_ACTIVE;
		verifyExactCaption(activeLabel, statusAc, "Subgroup Name");

		TimeUtil.shortWait();
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasonsMod"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.STATUSCHANGE_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupStatusChangeInActiveWithAuditTrails(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);

		click2(statusChangeRecordInActive, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupStatusChangeInActiveDrop(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, SYS_Group.RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");

//		verifyExactCaption(auditTrailPageColumn3, initiator, "Initiated By");

		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueModCompareTRN, Constants.REVISION_NUM_0STC, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		TimeUtil.shortWait();

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionModInitiateValue,
				auditCompareTRNActionByModinitiateValue, auditCompareTRNDateTimeModinitiateValue,
				auditCompareTRNRemarksModinitiateVal1, Constants.INITIATE_ACTIONVAL, initiator,
				Constants.statusRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", dropRadioBtn);
		highLightElement(driver, dropRadioBtn, "Group Approval", test);
		clickAndWaitforNextElement(dropRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("RemarksReasonsAP"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_STATUSCHANGE_TEXT_ESIGN;
		String ApprovalAction = Constants.DROP_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.GROUP_STATUSCHANGEDROP_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void groupStatusChangeInActiveDropWithAuditTrails(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				SYS_Group.RegGroupName1 + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		click2(statusChangeRecordInActiveA, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0STC, " 1 - Modification");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, SYS_Group.RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, SYS_Group.RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_DROPPED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionDropValue,
				auditCompareApproveTRNActionByDropValue, auditCompareTRNDateTimeDropValue,
				auditCompareTRNAppRemarksDropVal1, Constants.DROP_ACTIONVAL, initiator, Constants.GROUPDROPREMAKS);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNModReqVal, auditCompareTRNModComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
}
