package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.RoleStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Strings.UserProducAssignStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Strings.UserRegistrationStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;

public class SSO_UserRegistration extends OQActionEngine {

	public static String FirstName = "";
	public static String LastName = "";
	public static String UserID = "";
	public static String EmployeeID = "";
	public static String EmployeeName = "";
	public static String designationValue = "";
	public static String departmentName = "";

	public static String getDesignationValue() {
		return designationValue;
	}

	public static void setDesignationValue(String designationValue) {
		SSO_UserRegistration.designationValue = designationValue;
	}

	public static String getEmployeeName() {
		return EmployeeName;
	}

	public static void setEmployeeName(String employeeName) {
		EmployeeName = employeeName;
	}

	public static String getFirstName() {
		return FirstName;
	}

	public static void setFirstName(String firstname) {
		FirstName = firstname;
	}

	public static String getLastName() {
		return LastName;
	}

	public static void setLastName(String lastname) {
		LastName = lastname;
	}

	public static String getUserID() {
		return UserID;
	}

	public static void setUserID(String userID) {
		UserID = userID;
		
	}

	public static String getEmployeeID() {
		return EmployeeID;
	}

	public static void setEmployeeID(String employeeID) {
		EmployeeID = employeeID;
	}
	
	

	Properties prop;
	@FindBy(xpath = "//ul[1]/li[3]/a[1]//span[contains(text(),'Identity Manager')]")
	WebElement identityManager;
	@FindBy(xpath = "//ul[1]/li[3]/ul[@class='sub-menu']/li[1]/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(xpath = "//ul[1]/li[3]/ul[@class='sub-menu']/li[3]/a[contains(text(),'Audit Trails')]")
	WebElement auditTrailsMenu;
	@FindBy(id = "USERREG")
	WebElement userRegistration;
	@FindBy(id = "USERREGAT")
	WebElement userRegistrationAuditTrails;
	@FindBy(xpath = "//button[@id='btnModal_UserRegistration_Designation']/span")
	WebElement designation;
	@FindBy(xpath = "(//span[@class='select2-selection__arrow'])[1]")
	WebElement designationdropdown;
	@FindBy(xpath = "(//span[@class='select2-selection__arrow'])[2]")
	WebElement reportingTodropdown;
	@FindBy(xpath = "//ul[@id='select2-UserRegistration_Designation_selectDdl-results']/li[2]")
	WebElement selectdesignation;
	@FindBy(xpath = "//ul[@id='select2-UserRegistration_ReportingTo_selectDdl-results']/li[2]")
	WebElement selectreportingtoEmployeeId;
	@FindBy(id = "UserRegistration_Designation_FindTxt")
	WebElement findTxt;
	@FindBy(id = "UserRegistration_Designation_DisplayBtn")
	WebElement applyTxt;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]//input[1]")
	WebElement designationRB;
	@FindBy(id = "UserRegistration_Designation_selectBtn")
	WebElement addBtn;
	@FindBy(id = "UserRegistration_FirstName")
	WebElement firstName;
	@FindBy(id = "UserRegistration_LastName")
	WebElement lastName;
	@FindBy(id = "UserRegistration_UserCode")
	WebElement userID;
	@FindBy(id = "UserRegistration_EmployeeID")
	WebElement employeeID;
	@FindBy(id = "UserRegistration_TemporaryPassword")
	WebElement temporaryPassword;
	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submitBtn;
	@FindBy(id = "txtESignPassword")
	WebElement eSign;
	@FindBy(id = "Submit_Esign")
	WebElement eSignProceed;
	@FindBy(xpath = "//input[@id='RblDesc_1']/following-sibling::label[1]")
	WebElement confirm;
	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement remarks;
	@FindBy(xpath = "//input[@id='CurrentPassword']")
	WebElement changeCurrentPWD;
	@FindBy(xpath = "//input[@id='NewPassword']")
	WebElement changeNewPWD;
	@FindBy(xpath = "//input[@id='ConfirmPassword']")
	WebElement changeRetypePWD;
	@FindBy(xpath = "//a[@id='logoutBtn']")
	WebElement signOut;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(id = "UserRegistration_EmailID")
	WebElement enterEmail;
	@FindBy(xpath = "//button[@class='toast-close-button']")
	WebElement closealert;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='0']")
	WebElement inductionTraningNo1;
	// @FindBy(xpath = "//input[@id='btnConfirm']")
	@FindBy(xpath = "//input[@class='radioCls margin-right-10 caliber-labeled-option']")
	WebElement reportingToRadioBtn;
	@FindBy(id = "UserRegistration_ReportingTo_selectBtn")
	WebElement reportingToAddBtn;
	@FindBy(xpath = "//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submitBtn310;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//span[@title='Advanced Search']")
	WebElement searchfilter;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'User Name')]")
	WebElement searchByUserNameDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'User ID')]")
	WebElement searchByUserIDDropdown;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Employee ID']//following-sibling::span")
	WebElement auditCompareEmplyoeeID;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Initiated Between')]")
	WebElement searchByInitiatedBetweenDropdown;
	@FindBy(id = "select2-SearchType-container")
	WebElement like;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//*[@id='select2-SearchType-container']")
	WebElement searchByNew;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//label[contains(text(),'Email ID')]//following-sibling::span")
	WebElement auditCompareEmailID;



	@FindBy(id = "SrhUniqueCode")
	WebElement userNameLike;

	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;

	@FindBy(xpath = "//label[@for='ExpiryDTReq_1']")
	WebElement AutoDeactivationNo;
	
	@FindBy(id = "ExpiryDTReq_1")
	WebElement AutoDeactivationNoBtn;
	@FindBy(xpath = "//td[text()='Initiated']")
	WebElement auditCompareDepartment;
	private String depart1;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw_;
	@FindBy(xpath = "//label[contains(text(),'Confirm')]")
	WebElement confirmDecision;

	@FindBy(xpath = "//input[@id='CurrentPassword']")
	WebElement CurrentPasword;

	@FindBy(xpath = "//input[@id='NewPassword']")
	WebElement NewPassword;

	@FindBy(id = "ConfirmPassword")
	WebElement ConfirmPassword;
	@FindBy(xpath = "//a[@id='Logout']")
	WebElement Logout;

	public void sso_UserRegistration(HashMap<String, String> testData) {

	String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		setFirstName(FirstName = testData.get("firstnameval") + s);
		setLastName(LastName = testData.get("lastnameval") + s);
		setEmployeeName(EmployeeName = FirstName + "." + LastName);
		setUserID(UserID = testData.get("UserIDval") + s);
		setEmployeeID(EmployeeID = testData.get("EmployeeIDval") + s);
		setDesignationValue(designationValue = testData.get("Designationval"));
		String userID1 = UserID + "@gmail.com";
		
	waitForElementVisibile(identityManager);
		clickAndWaitforNextElement(identityManager, initiateMenu,
				UserRegistrationStrings.Click_IM_DC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_IM_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_IM_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_IM_SS.getUserRegistrationStrings());
		clickAndWaitforNextElement(initiateMenu, userRegistration, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userRegistration, UserRegistrationStrings.Click_UserRegistration_310DC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_UserRegistration_310AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_UserRegistration_310AR.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_UserRegistrationSS.getUserRegistrationStrings());
		switchToBodyFrame(driver);
		click2(designation, UserRegistrationStrings.Click_ADDItemDesig_DC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_ADDItemDesig_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_ADDItemDesig_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_ADDItemDesig_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(designationdropdown, selectdesignation,
				UserRegistrationStrings.Designationdropdown_DC.getUserRegistrationStrings(),
				UserRegistrationStrings.Designationdropdown_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Designationdropdown_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.Designationdropdown_SS.getUserRegistrationStrings());
		clickAndWaitforNextElement(selectdesignation, findTxt,
				UserRegistrationStrings.SelectDesignation_DC.getUserRegistrationStrings(),
				UserRegistrationStrings.SelectDesignation_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.SelectDesignation_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.SelectDesignation_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
		sendKeys2(findTxt, UserRegistrationStrings.Enter_Designation_DC.getUserRegistrationStrings(),
				testData.get("Designationval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_Designation_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
		click2(applyTxt, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(designationRB, addBtn,
				UserRegistrationStrings.Select_Desg_DC.getUserRegistrationStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
				UserRegistrationStrings.Enter_DesgCode_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(addBtn, firstName, CommonStrings.Click_Add_DC.getCommonStrings(),
				UserRegistrationStrings.AddDesig_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.AddDesig_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.Click_ADDItemDesig_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
		sendKeys2(firstName, UserRegistrationStrings.Enter_FN_DC.getUserRegistrationStrings(), FirstName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_FN_SS.getUserRegistrationStrings());
		sendKeys2(lastName, UserRegistrationStrings.Enter_LN_DC.getUserRegistrationStrings(), LastName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_LN_SS.getUserRegistrationStrings());
		sendKeys2(userID, UserRegistrationStrings.Enter_UID_DC.getUserRegistrationStrings(), UserID,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_UID_SS.getUserRegistrationStrings());
		sendKeys2(employeeID, UserRegistrationStrings.Enter_EID_DC.getUserRegistrationStrings(), EmployeeID,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_EID_SS.getUserRegistrationStrings());
		sendKeys2(enterEmail, UserRegistrationStrings.Enter_Email_DC.getUserRegistrationStrings(), userID1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserRegistrationStrings.Enter_EMail_SS.getUserRegistrationStrings());
		click2(AutoDeactivationNoBtn, "Select Auto Deactivation as 'No'", "", "", "");
		saveTempPassword(driver, temporaryPassword, test);
		clickAndWaitforNextElement(submitBtn310, esign_psw,CommonStrings.Submit_Button_DC.getCommonStrings(),
				UserRegistrationStrings.Esign_UserReg_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Esign_UserReg_AR.getUserRegistrationStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.mediumWait();
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("SSOPassword"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				UserRegistrationStrings.Esign_Proceed_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Esign_Proceed_AR.getUserRegistrationStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		String MenuName = Constants.MENUNAME_AS_USERREGISTRATION;
		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		switchToDefaultContent(driver);
		TimeUtil.shortWait();
//		clickAndWaitforNextElement(auditTrailsMenu, userRegistrationAuditTrails,
//				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(userRegistrationAuditTrails,
//				UserRegistrationStrings.SSO_AudittrailsUserRegistrationMenu_DC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SSO_AudittrailsUserRegistrationMenu_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SSO_AudittrailsUserRegistrationMenu_AR.getUserRegistrationStrings(),
//				UserRegistrationStrings.SSO_AudittrailsUserRegistrationMenu_SS.getUserRegistrationStrings());
//		TimeUtil.shortWait();
//		switchToBodyFrame(driver);
//		TimeUtil.shortWait();
//		searchfilter.click();
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(searchByNew, searchByUserIDDropdown,
//				UserRegistrationStrings.SearchBy_UserName_DC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserName_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserName_AR.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserName_SS.getUserRegistrationStrings());
//		clickAndWaitforNextElement(searchByUserIDDropdown, userNameLike,
//				UserRegistrationStrings.SearchBy_UserID_DC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserID_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserID_AR.getUserRegistrationStrings(),
//				UserRegistrationStrings.SearchBy_UserID_SS.getUserRegistrationStrings());
//		TimeUtil.shortWait();
//		userNameLike.clear();
//		sendKeys2(userNameLike, UserRegistrationStrings.Like_UserID_DC.getUserRegistrationStrings(), UserID+testData.get("percentage"),
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				UserRegistrationStrings.Like_UserID_SS.getUserRegistrationStrings());
//		TimeUtil.shortWait();
//		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(displayedRecord,
//				UserRegistrationStrings.Click_UserRegistration_for_AuditTrails_DC.getUserRegistrationStrings(),
//				UserRegistrationStrings.Click_UserRegistration_for_AuditTrails_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.Click_UserRegistration_for_AuditTrails_AR.getUserRegistrationStrings(),
//				UserRegistrationStrings.Click_UserRegistration_for_AuditTrails_SS.getUserRegistrationStrings());
//		driver.switchTo().frame(0);
//		scrollToViewElement(revisionNoTitleCompareTRN);
//		scrollToViewElement(auditCompareEmailID);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				UserRegistrationStrings.Close_AuditTrails_UserRegistration_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.Close_AuditTrails_UserRegistration_AR.getUserRegistrationStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		TimeUtil.shortWait();
	//	switchToDefaultContent(driver);

	}

	public void userReg_resetPasswordEpiq(String SSOPassword) {
		String TempPsw = OQActionEngine.tempPwd;

		// String TempPsw = "hz769IK";
		// switchToDefaultContent(driver);
		// switchToBodyFrame(driver);
		waitForElementVisibile(confirmDecision);
		clickAndWaitforNextElement(confirmDecision, remarks,
				UserRegistrationStrings.Decision_Confirm_DC.getUserRegistrationStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				UserRegistrationStrings.Decision_Confirm_SS.getUserRegistrationStrings());
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), "Confirm Registration",
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(submitBtn, CurrentPasword, CommonStrings.Submit_Button_DC.getCommonStrings(),
				UserRegistrationStrings.UserConfirm_Submit_310_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.UserConfirm_Submit_310_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.UserConfirm_Submit_310_SS.getUserRegistrationStrings());
		TimeUtil.shortWait();
//		sendKeys2(eSign, CommonStrings.Password_DC.getCommonStrings(), TempPsw,
//				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//				CommonStrings.Password_SS.getCommonStrings());
//		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//				UserRegistrationStrings.Proceed_UserConfirmation_AC.getUserRegistrationStrings(),
//				UserRegistrationStrings.Proceed_UserConfirmation_AR.getUserRegistrationStrings(),
//				CommonStrings.Esign_Proceed_SS.getCommonStrings());
//		waitForElementVisibile(CurrentPasword);
		sendKeys2(CurrentPasword, UserRegistrationStrings.CurrentPassword_DC.getUserRegistrationStrings(), TempPsw,
				UserRegistrationStrings.CurrentPassword_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.CurrentPassword_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.CurrentPassword_SS.getUserRegistrationStrings());
		sendKeys2(NewPassword, UserRegistrationStrings.NewPassword_DC.getUserRegistrationStrings(), SSOPassword,
				UserRegistrationStrings.NewPassword_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.NewPassword_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.NewPassword_SS.getUserRegistrationStrings());
		System.out.println("newpasswordenteredis:" + ConfigsReader.getPropValue("pwd"));
		sendKeys2(ConfirmPassword, UserRegistrationStrings.ConmfirmPassword_DC.getUserRegistrationStrings(),
				SSOPassword, UserRegistrationStrings.ConmfirmPassword_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.ConmfirmPassword_AR.getUserRegistrationStrings(),
				UserRegistrationStrings.ConmfirmPassword_SS.getUserRegistrationStrings());
		clickAndWaitforNextElement(submitBtn, Logout, CommonStrings.Submit_Button_DC.getCommonStrings(),
				UserRegistrationStrings.Submit_Button_AC.getUserRegistrationStrings(),
				UserRegistrationStrings.Submit_Button_AR.getUserRegistrationStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());

		click2(Logout, CommonStrings.LogOut_icon_DC.getCommonStrings(),
				CommonStrings.SignOut_icon_AC.getCommonStrings(), CommonStrings.SignOut_icon_AR.getCommonStrings(),
				CommonStrings.SignOut_icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.longwait();
		TimeUtil.longwait();
	}

}
