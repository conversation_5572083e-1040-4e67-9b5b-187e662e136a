package com.Automation.Utils;

import org.testng.ISuite;
import org.testng.ISuiteListener;

public class <PERSON><PERSON>ist<PERSON> implements ISuiteListener {
	@Override
	public void onStart(ISuite suite) {
		if (ConfigsReader.getPropValue("ScreenRecording").equalsIgnoreCase("YES")) {

			MyScreenRecorder.startRecording(suite.getName());
		}

	}

	@Override
	public void onFinish(ISuite suite) {
		MyScreenRecorder.stopRecording();
	}
}
