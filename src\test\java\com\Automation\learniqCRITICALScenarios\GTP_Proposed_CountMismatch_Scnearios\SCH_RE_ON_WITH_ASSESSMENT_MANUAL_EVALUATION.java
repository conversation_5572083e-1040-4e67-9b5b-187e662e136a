package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_QuestionPaper;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online RE type Session with assessment for scheduled course and make
 * at least one employee qualified, To Be Retrained with manual evaluation and
 * by viewing Individual employee report at each transaction starting from
 * course session.
 */

public class SCH_RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION extends OQActionEngine {

	public SCH_RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	String ExcelPath = "./learnIQTestData/SGTestData/SCH_RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/UNSCH_RE_MANUAL_EVAL.xlsx";

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic  Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "TrainingschedModify");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Modification");
		}

		TrainingShcedule.modifyTrainingScheduled(testData);

		Logout.signOutPage();
	}
	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {

		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		verifyCSScreen.courseSession_ON_RE_manualEvaluation(testData);

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();

	}
	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "QuestionBank")
	public void QuestionBank_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank  Registration");
		}

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPRegManual");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with Manual Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with Manual Evaluation");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.REType);

		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user
	// Qualified and view IER

	@Test(priority = 7, enabled = true)
	public void qualifiedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user Qualified");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQualifiedTraineeID(), CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType,
				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 8, enabled = true)
	public void toBeRetrainedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained")
					.assignAuthor(CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID(),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		Logout.signOutPage();
	}

	@Test(priority = 9, enabled = true)
	public void toRetakePending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To retake pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To retake pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_VerifyCourseSessionScreen.retakeUserID,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.retakeEmployeePassword);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 10, enabled = true)
	public void toRetakePendingEvaluationPending() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Respond DocumentReading, Respond QuestionPaper and make user To retake pending evaluation pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper and make user To retake pending evaluation pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 11, enabled = true)
	public void evaluate_AnswerPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
					.assignCategory("Evaluate Answer Paper");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
				CM_QuestionPaper.EvaluatorPassword);

		epiclogin.plant1();

		Evaluate.manualEvaluation();
		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 12, enabled = true)
	public void toBeRetrainedEmp() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained")
					.assignAuthor(CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID(),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		Logout.signOutPage();
	}

	@Test(priority = 13, enabled = true)
	public void evaluate_AnswerPaper_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
					.assignCategory("Evaluate Answer Paper");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
				CM_QuestionPaper.EvaluatorPassword);

		epiclogin.plant1();

		Evaluate.manualEvaluation();
		Logout.signOutPage();
	}

	@Test(priority = 14, enabled = true)
	public void toRetakePendingEvaluationPending_AfterRetake() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 15, enabled = true)
	public void EvaluationPending_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user Evaluation Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user Evaluation Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.Eval_Pending_EmployeePassword);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 16, enabled = true)
	public void respond_QP_Pending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user QP Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user QP Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQPPendingUserID(), CM_VerifyCourseSessionScreen.QPPendingUserPsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QPPendingUserPsw);

	}

	@Test(priority = 17, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session screen before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session screen before course retraining");
		}
		verifyCSScreen.checkCourseSession_RE_With_Assessment_SystemEval_After_Keeping_Employees_In_Different_States();
	}

	@Test(priority = 18, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session report screen before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session report screen before course retraining");
		}
		CSRReport.TBPCSRReport_RE_WithAssesment_After_keping_Emplpoyees_In_Diff_States();
	}

	@Test(priority = 19, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("courseRetraining").assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("courseRetraining");
		}
		CourseRetraining.Scheduled_course_Retraining_RE_Online_With_Assessment_();

	}

	@Test(priority = 20, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session screen after course retraining in Unscheduled Tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session screen after course retraining in Unscheduled Tab");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_();
	}

	@Test(priority = 21, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining1() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session screen after course retraining in Scheduled Tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session screen after course retraining in Scheduled Tab");
		}
		verifyCSScreen.scheduled_RE_online_With_Assessment_VerifyCourseSession_Screen_After_CourseRetraining();
	}

	@Test(priority = 22, enabled = true)
	public void verifyCSR_AfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Report screen after course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session Report screen after course retraining");
		}
		CSRReport.TBPCSRReport_RE_WithAssesment_After_CourseRetraining();

		Logout.signOutPage();

	}
}
