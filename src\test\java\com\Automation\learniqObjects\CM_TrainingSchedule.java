package com.Automation.learniqObjects;

import java.time.LocalDate;
import java.time.Month;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Strings.TrainingScheduleStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.Markup;
import com.aventstack.extentreports.markuputils.MarkupHelper;

public class CM_TrainingSchedule extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[14]/a[1]")
	WebElement configMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']//li//a[text()='Training Schedule']")
	WebElement configureTrainingSchedule;

	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement esignModInitiation;

	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement esignModApproval;

	@FindBy(xpath = "//span[@id='select2-Config_NarAtModify-container']/parent::span//span[@class='select2-selection__arrow']")
	WebElement approvalModDropdown;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement searchText;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtModify-results']//child::li")
	WebElement approvalModValue;

	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']")
	WebElement remarksReasonField;

	@FindBy(id = "Config_Remarks")
	WebElement remarks;

	@FindBy(id = "btnSubmit")
	WebElement submitBtn;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//div[text()='Number of Approvals Required for Initiation']")
	WebElement noOfApprovalsReqForInitiationHeading;

	@FindBy(xpath = "//input[@aria-controls= 'select2-TrainingSchedule_AppSubGrpsMul0-results']")
	WebElement LineofApproverValue;
	@FindBy(xpath = "//span[@class='select2-search select2-search--dropdown']//following-sibling::span")
	WebElement SelectLineofApproverValue;
	@FindBy(id = "select2-TrainingSchedule_AppSubGrpsMul0-container")
	WebElement LineofApprover;

	String trainingScheduleModValue = "";

	public void trainingScheduleConfiguration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		scrollToViewElement(configMenu);

		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		scrollToViewElement(configureTrainingSchedule);

		TimeUtil.shortWait();

		click2(configureTrainingSchedule,
				TrainingScheduleStrings.TrainingScheduleSubMenu_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleConfig_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleConfig_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleConfig_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		switchToBodyFrame(driver);

		SelectRadioBtnAndCheckbox(driver, esignModInitiation, "Initiation Modification");

		SelectRadioBtnAndCheckbox(driver, esignModApproval, "Approval Modification");

		TimeUtil.shortWait();

		click2(approvalModDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(searchText, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalsRequired"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(approvalModValue, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.One_Approvals_AC.getCommonStrings(), CommonStrings.One_Approvals_AR.getCommonStrings(),
				CommonStrings.One_Approvals_SS.getCommonStrings());

		scrollToViewElement(remarksReasonField);

		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		scrollToViewElement(submitBtn);

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());

		TimeUtil.shortWait();

		highLightElement(driver, confirmationText, "Confirmation Message", test);

		switchToDefaultContent(driver);
	}

	// Modify Training Schedule

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li//a[text()='Modify']")
	WebElement modifyMenu;

//	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Modify']/li//a[text()='Training Schedule']")
//	WebElement trainingScheduledMenu;

	@FindBy(xpath = "//span[@class='select2-selection__arrow']")
	WebElement searchByDropdown;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[text()='Training Schedule Name']")
	WebElement trainingScheduledName;

	@FindBy(id = "Description")
	WebElement searchTextbox;

	@FindBy(id = "displayBtn")
	WebElement applyBtn;

	@FindBy(xpath = "(//table[@id='ListTab']/tbody/tr/td[@class='text-ellipsis'])[1]")
	WebElement trainingScheduledNameRecord;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr")
	List<WebElement> trainingScheduleModData;

	@FindBy(xpath = "//span[@id='select2-TrainingSchedule_AppSubGrpsMul0-container']/parent::span//span[@class='select2-selection__arrow']")
	WebElement groupSubgroupDropdown;

	@FindBy(xpath = "//ul[@id='select2-TrainingSchedule_AppSubGrpsMul0-results']/li[text()='Approvers Group/Approvers Subgroup']")
	WebElement approverGroup_ApproverSubgroup;

	@FindBy(id = "TrainingSchedule_Remarks")
	WebElement remarksReasonTextbox;

	@FindBy(id = "txtESignPassword")
	WebElement esignPWDTextBox;

	@FindBy(id = "Submit_Esign")
	WebElement proceedBtn;
	
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Modify_MEN143']")
	WebElement trainingScheduledMenu;
 

	public void modifyTrainingScheduled(HashMap<String, String> testData) {
		
		
		waitForElementVisibile(menu);
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(modifyMenu, TrainingScheduleStrings.MODIFY_MENU.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_MENU_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_MENU_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_MENU_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		click2(trainingScheduledMenu, TrainingScheduleStrings.TRAINING_SCHEDULE_MENU.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_SS.getTrainingScheduleStrings());

		switchToBodyFrame(driver);

		TimeUtil.mediumWait();

		click2(searchByDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				TrainingScheduleStrings.SearchBy_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SearchBy_AR.getTrainingScheduleStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(trainingScheduledName,
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_SS.getTrainingScheduleStrings());

		trainingScheduleModValue = testData.get("TrainingScheduleName");

		sendKeys2(searchTextbox, TrainingScheduleStrings.Like_TrainScheName_DC.getTrainingScheduleStrings(),
				trainingScheduleModValue + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainingScheduleStrings.Like_Training_Schedule_Name_SS.getTrainingScheduleStrings());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		click2(trainingScheduledNameRecord,
				TrainingScheduleStrings.TRAINING_SCHEDULE_NAME_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_TRAINING_SCHEDULE_NAME_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_TRAINING_SCHEDULE_NAME_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_TRAINING_SCHEDULE_NAME_SS.getTrainingScheduleStrings());
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		selectMultipleCheckBoxes(trainingScheduleModData,
				TrainingScheduleStrings.SELECT_CURRENT_MONTH_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SELECT_CURRENT_MONTH_SS.getTrainingScheduleStrings(), testData);
		TimeUtil.longwait();
		TimeUtil.longwait();
		scrollToViewElement(remarksReasonField);


//		try {
//
//			if (noOfApprovalsReqForInitiationHeading.getText().equals("Number of Approvals Required for Initiation")) {
//
//				scrollToViewElement(noOfApprovalsReqForInitiationHeading);
//
//				click2(LineofApprover, CourseSessionStrings.Click_Approval1_DC.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_AC.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_AR.getCourseSessionStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//				sendKeys2(LineofApproverValue, CourseSessionStrings.Enter_Approval1_DC.getCourseSessionStrings(),
//						testData.get("Line of Approvers"), CommonStrings.sendKeys_AC.getCommonStrings(),
//						CommonStrings.sendKeys_AR.getCommonStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//				click2(SelectLineofApproverValue, CourseSessionStrings.Select_Apprval1_DC.getCourseSessionStrings(),
//						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//						CourseSessionStrings.Click_Approval1_SS.getCourseSessionStrings());
//				TimeUtil.shortWait();
//				;
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

		sendKeys2(remarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("InitiationRemarks/Reasons"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(submitBtn, TrainingScheduleStrings.Submit_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_Submit_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_Submit_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Submit_SS.getTrainingScheduleStrings());

		TimeUtil.longwait();
		TimeUtil.longwait();
		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				
				waitForElementVisibile(confirmationText);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		waitForElementVisibile(confirmationText);
		switchToDefaultContent(driver);

	}

	// Training Schedule Audit Trails

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement auditTrail;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Audit Trails_MEN143']")
	WebElement aduitTrailsTrainingSchedule;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]//div[@class='textdiv qsTitle']")
	WebElement latestModTransaction;

	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement auditTrailProceedBtn;

	@FindBy(id = "status_heading")
	WebElement finalStatusHeading;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement pageHeaderTitle;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;

	public void trainingScheduleAuditTrail() {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		scrollToViewElement(auditTrail);

		click2(auditTrail, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		scrollToViewElement(aduitTrailsTrainingSchedule);

		click2(aduitTrailsTrainingSchedule,
				TrainingScheduleStrings.TrainingScheduleSubMenu_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleAudittrails_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleAudittrails_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TrainingScheduleAudittrails_SS.getTrainingScheduleStrings());

		switchToBodyFrame(driver);

		click2(searchByDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				TrainingScheduleStrings.SearchBy_TrainScheAudit_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SearchBy_TrainScheAudit_AC.getTrainingScheduleStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(trainingScheduledName, TrainingScheduleStrings.Select_TrainScheName_DC.getTrainingScheduleStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				TrainingScheduleStrings.TrainingSchName_SS.getTrainingScheduleStrings());

		// Note - while executing scenario, need to call string value is courseNameVal
		// but now temporary i'm calling course value from CM_BatchFormation.courseName
		sendKeys2(searchTextbox, TrainingScheduleStrings.Like_TrainScheName_DC.getTrainingScheduleStrings(),
				trainingScheduleModValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainingScheduleStrings.TrainingSchName_SS.getTrainingScheduleStrings());

		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(trainingScheduledNameRecord,
				TrainingScheduleStrings.Click_TrainScheName_for_AuditTrails_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Click_TrainScheName_for_AuditTrails_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Click_TrainScheName_for_AuditTrails_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Click_TrainScheName_for_AuditTrails_SS.getTrainingScheduleStrings());

		driver.switchTo().frame(0);

		click2(latestModTransaction, TrainerStrings.Click_LastesModTab_DC.getTrainerStrings(),
				TrainerStrings.Click_LastesModTab_AC.getTrainerStrings(),
				TrainerStrings.Click_LastesModTab_AR.getTrainerStrings(),
				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_SS.getTrainerStrings());

		click2(auditTrailProceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				TrainerStrings.Click_Proceed_AC.getTrainerStrings(),
				TrainerStrings.Click_Proceed_AR.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_SS.getTrainerStrings());

		TimeUtil.shortWait();

		scrollToViewElement(finalStatusHeading);

		TimeUtil.shortWait();

		scrollToViewElement(pageHeaderTitle);

		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				TrainingScheduleStrings.Close_AuditTrailsTopic_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Close_AuditTrailsTopic_AR.getTrainingScheduleStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.shortWait();

		switchToDefaultContent(driver);
	}

	// 1 - Approval of Training Schedule

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li/a[text()='Approve']")
	WebElement approveMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Approve']/li/a[text()='Training Schedule']")
	WebElement approveTrainingScheduleMenu;

	@FindBy(xpath = "//button[text()='Modification']")
	WebElement modificationBtn;

	@FindBy(xpath = "//label[@class='title-heading' and contains(text(), 'Decision')]")
	WebElement decisionField;

	@FindBy(xpath = "//label[text()='Approve']/preceding-sibling::input[@type='radio']")
	WebElement ApproveRadioBtn;

	@FindBy(id = "Remarks")
	WebElement approveRemarksReasonTextbox;

	public void approveModifyTrainingScheduled(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(approveMenu, TrainingScheduleStrings.APPROVE_MENU.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVE_TRAINING_SCHEDULE_MENU_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVE_TRAINING_SCHEDULE_MENU_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVE_TRAINING_SCHEDULE_MENU_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		click2(approveTrainingScheduleMenu, TrainingScheduleStrings.TRAINING_SCHEDULE_MENU.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.TRAINING_SCHEDULE_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		switchToBodyFrame(driver);

		click2(modificationBtn, TrainingScheduleStrings.CLICK_MODIFICATION_TAB_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_MOD_RECORD_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_MOD_RECORD_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MODIFICATION_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		click2(searchByDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				TrainingScheduleStrings.SearchBy_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SearchBy_AR.getTrainingScheduleStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(trainingScheduledName,
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.SEARCH_BY_TRAINING_SCHEDULE_NAME_SS.getTrainingScheduleStrings());

		sendKeys2(searchTextbox, TrainingScheduleStrings.Like_TrainScheName_DC.getTrainingScheduleStrings(),
				trainingScheduleModValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainingScheduleStrings.Like_Training_Schedule_Name_SS.getTrainingScheduleStrings());

		TimeUtil.shortWait();

		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(trainingScheduledNameRecord,
				TrainingScheduleStrings.TRAINING_SCHEDULE_NAME_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVE_MOD_TRAINING_SCHEDULE_NAME_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVE_MOD_TRAINING_SCHEDULE_NAME_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.MOD_TRAINING_SCHEDULE_NAME_SS.getTrainingScheduleStrings());

		TimeUtil.longwait();

		scrollToViewElement(decisionField);

		click2(ApproveRadioBtn, CommonStrings.Approval_DC.getCommonStrings(),
				CommonStrings.Approval_AC.getCommonStrings(), CommonStrings.Approval_AR.getCommonStrings(),
				CommonStrings.Approval_SS.getCommonStrings());

		TimeUtil.shortWait();

		sendKeys2(approveRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ApproveRemarks/Reasons"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(submitBtn, TrainingScheduleStrings.Submit_DC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_MOD_Submit_AC.getTrainingScheduleStrings(),
				TrainingScheduleStrings.APPROVAL_MOD_Submit_AR.getTrainingScheduleStrings(),
				TrainingScheduleStrings.Submit_SS.getTrainingScheduleStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}

	public void selectMultipleCheckBoxes(List<WebElement> element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName, HashMap<String, String> testData) {

		try {
			
			if(isReportedRequired==true) {
			n = n + 1;

			String[][] data = {
					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
							"<b>Actual Result</b>" },
					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data);
			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
			test.log(Status.PASS, modifiedMarkup);
			attachScreenshot(driver, true, scName, "click2");

			for (WebElement row : element) {

				WebElement courseName = row.findElement(By.xpath("//td[@title='" + CM_Course.Course + "']"));
				System.out.println(courseName.getText());

				scrollToViewElement(courseName);

				if (courseName.getText() != null) {

//					WebElement employeeNameElement = row.findElement(By.xpath(".//td[@title='"
//							+ CM_BatchFormation.courseName + "']/following-sibling::td/input[@type='checkbox']"));

					LocalDate currentDate = LocalDate.now();

					// Extract the month number and convert to the month name
					Month month = currentDate.getMonth();
					String monthName = month.getDisplayName(java.time.format.TextStyle.SHORT, Locale.ENGLISH);
					System.out.println("Current month is: " + monthName);
					switch (monthName) {
					case "Jan":

						WebElement jan = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jan')]"));

						if (jan.isEnabled()) {

							jan.click();
						}
						break;
					case "Feb":

						WebElement febMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Feb')]"));

						if (febMonthcheckbox.isEnabled()) {

							febMonthcheckbox.click();
						}
						break;
					case "Mar":

						WebElement marMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Mar')]"));

						if (marMonthcheckbox.isEnabled()) {

							marMonthcheckbox.click();
						}
						break;
					case "Apr":

						WebElement aprMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Apr')]"));

						if (aprMonthcheckbox.isEnabled()) {

							aprMonthcheckbox.click();
						}
						break;
					case "May":

						WebElement mayMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'May')]"));

						if (mayMonthcheckbox.isEnabled()) {

							mayMonthcheckbox.click();
						}
						break;
					case "Jun":

						WebElement junMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jun')]"));

						if (junMonthcheckbox.isEnabled()) {

							junMonthcheckbox.click();
						}
						break;
					case "Jul":

						WebElement julMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jul')]"));

						if (julMonthcheckbox.isEnabled()) {

							julMonthcheckbox.click();
						}
						break;
					case "Aug":

						WebElement augMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Aug')]"));

						if (augMonthcheckbox.isEnabled()) {

							augMonthcheckbox.click();
						}
						break;
					case "Sep":

						WebElement sepMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Sep')]"));

						if (sepMonthcheckbox.isEnabled()) {

							sepMonthcheckbox.click();
						}
						break;
					case "Oct":

						WebElement octMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Oct')]"));

						if (octMonthcheckbox.isEnabled()) {

							octMonthcheckbox.click();
						}
						break;
					case "Nov":

						WebElement novMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Nov')]"));

						if (novMonthcheckbox.isEnabled()) {

							novMonthcheckbox.click();
						}
						break;
					case "Dec":

						WebElement decMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Dec')]"));

						if (decMonthcheckbox.isEnabled()) {

							decMonthcheckbox.click();
						}
						break;
					default:
						break;
					}
					break;
				}
			}

			// TimeUtil.shortWait();
			--screenshotCounter;
			attachScreenshot(driver, true, scName, "click2");
			screenshotCounter = Math.round(screenshotCounter);
		}
			
		else {
			
			for (WebElement row : element) {

				WebElement courseName = row.findElement(By.xpath("//td[@title='" + CM_Course.Course + "']"));

				scrollToViewElement(courseName);

				if (courseName.getText() != null) {

//					WebElement employeeNameElement = row.findElement(By.xpath(".//td[@title='"
//							+ CM_BatchFormation.courseName + "']/following-sibling::td/input[@type='checkbox']"));

					LocalDate currentDate = LocalDate.now();

					// Extract the month number and convert to the month name
					Month month = currentDate.getMonth();
					String monthName = month.getDisplayName(java.time.format.TextStyle.SHORT, Locale.ENGLISH);
//					System.out.println("Current month is: " + monthName);
					switch (monthName) {
					case "Jan":

						WebElement jan = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jan')]"));

						if (jan.isEnabled()) {

							jan.click();
						}
						break;
					case "Feb":

						WebElement febMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Feb')]"));

						if (febMonthcheckbox.isEnabled()) {

							febMonthcheckbox.click();
						}
						break;
					case "Mar":

						WebElement marMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Mar')]"));

						if (marMonthcheckbox.isEnabled()) {

							marMonthcheckbox.click();
						}
						break;
					case "Apr":

						WebElement aprMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Apr')]"));

						if (aprMonthcheckbox.isEnabled()) {

							aprMonthcheckbox.click();
						}
						break;
					case "May":

						WebElement mayMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'May')]"));

						if (mayMonthcheckbox.isEnabled()) {

							mayMonthcheckbox.click();
						}
						break;
					case "Jun":

						WebElement junMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jun')]"));

						if (junMonthcheckbox.isEnabled()) {

							junMonthcheckbox.click();
						}
						break;
					case "Jul":

						WebElement julMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Jul')]"));

						if (julMonthcheckbox.isEnabled()) {

							julMonthcheckbox.click();
						}
						break;
					case "Aug":

						WebElement augMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Aug')]"));

						if (augMonthcheckbox.isEnabled()) {

							augMonthcheckbox.click();
						}
						break;
					case "Sep":

						WebElement sepMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Sep')]"));

						if (sepMonthcheckbox.isEnabled()) {

							sepMonthcheckbox.click();
						}
						break;
					case "Oct":

						WebElement octMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Oct')]"));

						if (octMonthcheckbox.isEnabled()) {

							octMonthcheckbox.click();
						}
						break;
					case "Nov":

						WebElement novMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Nov')]"));

						if (novMonthcheckbox.isEnabled()) {

							novMonthcheckbox.click();
						}
						break;
					case "Dec":

						WebElement decMonthcheckbox = driver
								.findElement(By.xpath("//table[@id='ListTab']/tbody/tr//td[@title='" + CM_Course.Course
										+ "']/following-sibling::td/input[@type='checkbox' and contains(@id, 'Dec')]"));

						if (decMonthcheckbox.isEnabled()) {

							decMonthcheckbox.click();
						}
						break;
					default:
						break;
					}
					break;
				}
			}
		}
			}
		catch (Exception e) {
			
			if(isReportedRequired==true) {
			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
			test.log(Status.FAIL, "Exception :" + e.getMessage());
			attachScreenshot(driver, true, scName, "click2");
			}
			else {
				
				System.out.println(e.getMessage());
			}
			}
	}

}
