package com.Automation.Strings;

public enum SelfNominationStrings {
	// SelfNominationConfiguration

	SelfNominationForMenu_DC("Click on 'Self Nomination' submenu."),
	SelfNominationForConfig_AC("'Self-Nomination' Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),

	SelfNominationForConfig_AR("'Self-Nomination Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' are avialable.</div>"),

	SelfNominationForConfig_SS("'Configuration'"),

	RespondSelf_DC(
			"Enter the valid 'User ID' of the user who is selected at above proposed course session in 'User ID' field."),

	// SelfNomination Registration

	SelfMenu_DC("Click on 'Self-Nomination ' submenu."),
	SelfMenu_AC("'Self-Nomination Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'Course Session Name, Unique Code,Course Name,Course Session Start Date' columns.</div>"),

	SelfMenu_AR("'Self-Nomination Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Course Session Name, Unique Code,Course Name,Course Session Start Date' columns.</div>"),
	SelfMenu_SS("'Course Invitation'"),

	// Search By Course Session Name

	SearchBy_CourseSessionName_DC("Select 'Course Session Name'."),
	SearchBy_CourseSessionName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseSessionName_AR("Selection is getting accepted.</div>"),
	SearchBy_CourseSessionName_SS("'Course Session Name'"),

	UserName_DC1("Enter the valid 'User ID' of the user who is selected at above proposed course session."),

	SearchBy1_DC("Click on the above registered Course Session Name."), SearchBy1_SS("'Course Session Name'"),
	SearchBy1_AC("'Self-Nomination Registration Initiation' screen 'should be displayed.</div>"
			+ "<div><b>*</b> Option to select the sessions should be displayed.</div>"),
	SearchBy1_AR("'Self-Nomination Registration Initiation' screen 'is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the sessions is getting displayed.</div>"),

	SearchBy_DC("Click on 'Search This Page' icon."),
	SearchBy_AC("Search This Page' text box should be displayed.</div>"),
	SearchBy_AR("Search This Page' text box is getting displayed.</div>"), SearchBy_SS("'Search this Page'."),

	SearchBy2_DC("Enter the above registered Course Session Name."), SearchBy2_SS("'Course Name'"),

	UserSelectionScreenNextBtn_DC("Select the required session number under 'Selected Session from List'"),
	UserSelectionScreenNextBtn_AC("Session details should be displayed accurately.</div>"),
	UserSelectionScreenNextBtn_AR("Session details are getting displayed accurately.</div>"),
	UserSelectionScreenNextBtn_SS("'Course Session from list'"),

	ApplyButton_DC("Click on 'Apply' button."),
	SearchBy2_AC("Records should be displayed based on the search criteria."),
	SearchBy2_AR("Records are getting displayed based on the search criteria.</div>"), ApplyButton_SS("'Apply'"),
	remarks_DC("Enter the value less than or equals to 250 characters in 'Remarks' field."), remarks_SS("'Remarks'"),

	Submit_SS("'Submit'"), Click_Done_DC("Click 'Done' button."),

	// Proceed
	Esign_ProceedSelfNominationFor_AC(
			"'Self-Nomination Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedSelfNominationFor_AR(
			"'Self-Nomination  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),
	// SelfNomination Audit trails

	SelfNominationFor_AuditTrails_Menu_AC("'Self-Nomination Audit trails' screen should be displayed.<div>"),
	SelfNominationFor_AuditTrails_Menu_AR("'Self-Nomination Audit trails' screen is getting displayed.</div>"),
	SelfNominationFor_AuditTrails_Menu_SS("'Self-Nomination Audit Trails'"),

	// Audit trails search by

	AT_SearchBy_AC(
			"Option to search with 'Top 250 Records, Self-Nomination Name, Unique Code, Initiated Between' should be available."),
	AT_SearchBy_AR(
			"Option to search with 'Top 250 Records, Self-Nomination Name, Unique Code,Initiated Between' are available.</div>"),

	// Search By Self Nomination Name
	SearchBy_SelfNominationName_DC("Select 'Self-Nomination Name'."),
	SearchBy_SelfNominationName_AC("Selection should be accepted.<div>"),
	SearchBy_SelfNominationName_AR("Selection is getting accepted.<div>"),
	SearchBy_SelfNominationName_SS("'Self-Nomination Name'."),

	// Self Nomination audit trails list screen

	Click_Self_for_AuditTrails_DC("Click on the above registered 'Self-Nomination Name'."),
	Click_Self_for_AuditTrails_AC(
			"'Self-Nomination-Audit Trails: Revision No.: 0 - Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> All the entered/selected at registration details should be displayed in read only mode.<div>"),
	Click_Self_for_AuditTrails_AR(
			"'Self-Nomination-Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the entered/selected at registration details are getting displayed in read only mode.<div>"),

	Click_Self_for_AuditTrails_SS("'Self-Nomination'" + "</br>" + "Audit Trails."),

	SelfFor_AuditTrails_Menu_AC("'Self-Nomination Audit Trails' screen should be displayed.<div>"),
	SelfFor_AuditTrails_Menu_AR("'Self-Nomination Audit trails' screen is getting displayed.</div>"),

	// Submit Button
	Submit_DC("Click on 'Submit' button."),
	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Self-Nomination: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Self-Nomination: Registration Initiation'.</div>"),
	SubmitwithEsign_SS("'E-Sign'"),
	
	//
	SelfMenu_Approval_AC("'Self-Nomination Approval Tasks' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'Self-Nomination Name, Unique Code,Initiated By,Initiated On' columns.</div>"),

	SelfMenu_Approval_AR("'Self-Nomination Approval Tasks' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Self-Nomination Name, Unique Code,Initiated By,Initiated On' columns.</div>"),
	//Approval Screen
	
		ApprovalScreen_AC("'Self-Nomination Registration Approval' screen should be displayed."),
		ApprovalScreen_AR("'Self-Nomination Registration Approval' screen is getting displayed."),
		Submit_Approve_AC(
				"'Meaning of This Electronic Signature' window should be displayed as 'Self-Nomination: Registration Approval: Approve'.</div>"),
		Submit_Approve_AR(
				"'Meaning of This Electronic Signature' window is getting displayed as 'Self-Nomination: Registration Approval: Approve'.</div>"),
		
		//Apprver Confirmation
		Esign_ProceedSelfNominationFor_APR_AC(
				"'Self-Nomination Registration Approved Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
		Esign_ProceedSelfNominationFor_APR_AR(
				"'Self-Nomination  Registration Approved Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>");

	private final String SelfNominationStrings;

	SelfNominationStrings(String SelfNominationStrings) {

		this.SelfNominationStrings = SelfNominationStrings;

	}

	public String getSelfNominationStrings() {
		return SelfNominationStrings;
	}

}
