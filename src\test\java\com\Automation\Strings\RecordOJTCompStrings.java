package com.Automation.Strings;

public enum RecordOJTCompStrings {
	
	OJTMeu_DC("Click on 'On Job Training Completion' submenu."),
	OJTMeu_AC("'On Job Training Completion' screen should be displayed."),
	OJTMeu_AR("'On Job Training Completion' screen is getting displayed."),
	OJTMeu_SS("On Job Training Completion'"),
	
	//Search By
	
    SearchBy_AC("Option to search with 'Top 250 Records, Employee Name and Employee ID' should be displayed."),
    SearchBy_AR("Option to search with 'Top 250 Records, Employee Name and Employee ID' is getting displayed."),
    
    Select_DC("Select 'Employee Name'."),
    
    Enter_EmployeeName_DC("Enter the above selected employee name"),
    Enter_EmployeeName_SS("'Employee Name'"),
    
    
    //Select above employee Name
    
   Select_Emp_DC("Click on the above selected employee name."),
   Select_Emp_AC("'OJT Training(s) of (First name).(Last Name)' screen should be displayed.</div>" 
   +"<div><b>* </b> List of Course Session Names for which the above user is selected should be displayed with accurate 'Trainee Status'</div>"),
   Select_Emp_AR("'OJT Training(s) of (First name).(Last Name)' screen is getting displayed.</div>" +"<div><b>* </b> List of Course Session Names for which the above user is selected should be displayed with accurate 'Trainee Status'</div>"),
   Select_Emp_SS("'OJT Training(s) of (First name).(Last Name)'"),
   
   //
   Click_Back_DC("Click on 'Back' button."),
   Click_Back_SS("'Back'"),
    //Select check box
    
    Select_CB_DC("Select the check box against the above employee and click 'OK' at alert(if any)."),
    
    //Preview
    
    Click_Preview_DC("Click on 'Preview' button."),
    Click_Preview_AC("'Selected Employee List' window should be displayed."),
    Click_Preview_AR("'Selected Employee List' window is getting displayed."),
    Click_Preview_SS("'Preview'"),
    
    
    //Confirm
    
    Click_Confirm_DC("Click on 'Confirm' button."),
    Click_Confirm_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Record: On Job Training Completion'.</div>"),
    Click_Confirm_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Record: On Job Training Completion'.</div>"),

    
    		
    
    //Confirmation
    
    Esign_Proceed_AC(
			"'On Job Training Completion Confirmed' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_Proceed_AR(
			"''On Job Training Completion Confirmed' confirmation  message is getting displayed with 'Done' button.</div>"),

    
  
	
	
	;
	
	
	private final String recordOJT;

	RecordOJTCompStrings(String RecordOJT) {

		this.recordOJT = RecordOJT;

	}

	public String getRecordOJT() {
		return recordOJT;
	}

}
