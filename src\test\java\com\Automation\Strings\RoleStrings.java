package com.Automation.Strings;

public enum RoleStrings {

	// Role Registration

	RoleRegistrationScreen_DC("Click on 'Register Role' menu."),
	RoleRegistrationScreen_AC("'Role Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain ‘Level, Role, and Category’ fields.</div>"
			+ "<div><b>*</b>Screen should contain ‘View Existing’, and ‘Submit’ buttons Under ‘View Existing’,registered active records of ‘Role’ should be displayed <if any>.</div>"),
	RoleRegistrationScreen_AR("'Role Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with ‘Level, Role, and Category’ fields.</div>"
			+ "<div><b>*</b>Screen is getting displayed with ‘View Existing’, and ‘Submit’ buttons Under ‘View Existing’,registered active records of ‘Role’.</div>"),
	RoleRegistrationScreen_SS("'Register Role'"),
//level 
	Level_DC("Enter the value between 1 to 99.9 for ‘Level’ field."),
	Level_AC("Entered value should be displayed for the field.</div>"),
	Level_AR("Entered value is getting displayed for the field.</div>"), Level_SS("'Level'"),
//Role
	Role_DC("Enter a value less than or equal to 25 characters for ‘Role’ field."),
	Role_AC("Entered value should be displayed for the field.</div>"),
	Role_AR("Entered value is getting displayed for the field.</div>"), Role_SS("'Role'"),
//Category Dropdown
	CategoryDropDown_DC("Click 'Category' drop down."),
	CategoryDropDown_AC(
			"Option to select the ‘Login and Non-Login’ categories only should be available for selection."),

	CategoryDropDown_AR("Option to select the ‘Login and Non-Login’ categories  are available.</div>"),
	CategoryDropDown_SS("'Category'"),
//Category login
	CategoryLogin_Select_DC("Select the 'Login' for 'Category' field."),
	CategoryNon_Login_Select_DC("Select the 'Non-Login' for 'Category' field."),
	CategoryLogin_Select_AC("Selected value should be displayed for 'Category' field</div>"),
	CategoryLogin_Select_AR("Selected value is getting displayed for 'Category' field</div>"),
	CategoryLogin_SS("'Login'"), CategoryNon_Login_Select_SS("'Non-Login'"),
//Search by level
	SearchBylevel_Dropdown_DC("Click on 'Search By' dropdown."),
	SearchBylevel_Dropdown_AC(
			"Option to search with 'Top 250 Records,  Level ,Role, Initiated Between should be displayed.</div>"),
	SearchBylevel_Dropdown_AR(
			"Option to search with 'Top 250 Records,  Level ,Role, Initiated Between  is getting displayed.</div>"),
	SearchBylevel_Dropdown_SS("'Search By'"),
	// Search by Role
	SearchByRole_Dropdown_DC("Click on 'Search By' dropdown."),
	SearchByRole_Dropdown_AC(
			"Option to search with 'Top 250 Records,  Level ,Role, Initiated Between should be displayed.</div>"),
	SearchByRole_Dropdown_AR(
			"Option to search with 'Top 250 Records,  Level ,Role, Initiated Between  is getting displayed.</div>"),
	SearchByRole_Dropdown_SS("'Search By'"),

	// select Role
	Select_Role_DC("Select 'Role'."), Select_Role_SS("'Role'."),

	// select level
	Select_Level_DC("Select 'Level'."), Select_CoursName_AC("Selection should be accepted."),
	Select_CoursName_AR("Selection is getting accepted."), Select_Level_SS("'Level'."),
	Like_Level_DC("Enter the 'Level' of the above registered 'Role.' "), Like_Level_SS("Level"),
	
	Like_Role_DC("Enter the 'Role' of the above registered 'Role.' "), Like_Role_SS("Role"),
	
	
//Esign_proceed atRegisterrole
	Esign_ProceedRegisterRole_AC(
			"‘Role Registration Initiated Role:(Role)’ confirmation message should be displayed with ‘Done’ button.</div>"),
	Esign_ProceedRegisterRole_AR(
			"‘Role Registration Initiated Role:(Role)’ confirmation message is getting displayed with ‘Done’ button..</div>"),
//submit
	Submit_AC("'Role Registration Initiated'  confirmation message should be displayed with ‘Done’ button.</div>"),
	Submit_AR("'Role Registration Initiated' confirmation message is getting displayed with ‘Done’ button.</div>"),
//close Role AuditTrails
	Close_AuditTrails_Role_AC("'Role Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_Role_AR("'Role Audit trails' screen  is getting displayed.</div>"),
//click role Audit Trails
	Click_Role_for_AuditTrails_DC("Click on the 'Level' of the above registered 'Role.' "),
	Click_Role_for_AuditTrails_AC(
			"‘Role - Audit Trails - Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should contain the details of the Role entered during registration.<div>"
					+ "<div><b>*</b> ‘Final Status’ should be displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b>The ‘Events’ section should contain only the Registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks/ Reasons’ details..<div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘0’..<div>"
					+ "<div><b>*</b>All the particulars should  be displayed in read only format..<div>"),
	Click_Role_for_AuditTrails_AR(
			"‘Role - Audit Trails - Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>*</b> Screen is getting displayed with the details of the Role entered during registration.<div>"
					+ "<div><b>*</b> ‘Final Status’ is getting displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b>The ‘Events’ section is contain only the Registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks/ Reasons’ details..<div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both are  read as ‘0’..<div>"
					+ "<div><b>*</b>All the particulars is getting  displayed in read only format.<div>"),
	Click_Role_for_AuditTrails_SS("'Role Audit Trails'."),
//Submit registerRole
	SubmitRegisterRolewithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Role: Registration Initiation'.</div>"),
	SubmitRegisterRolewithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Role: Registration Initiation'.</div>"),
	CategoryLike_AC("Option to select the 'Login' should be displayed."), CategoryLike_AR("'Login'.</div>"),
	CategoryLike_SS("'Login'");

	private final String RoleStrings;

	RoleStrings(String RoleStrings) {

		this.RoleStrings = RoleStrings;

	}

	public String getRoleStrings() {
		return RoleStrings;
	}

}
