package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_SelfStudyCourse extends OQActionEngine {
	Properties prop;
	public static String Course = "";
	public static String ModifiedCourse = "";
	public static String QualifieduserID = "";
	public static String QualifiedPSW = "";
	public static String QualifiedEmpID = "";

	public static String DRInProgressUserID = "";
	public static String DRInProgressPSW = "";
	public static String DRInProgressEmpID = "";

	public static String RQPPendingUserID = "";
	public static String RQPPendingPSW = "";
	public static String RQPPendingEmpID = "";

	public static String ToBeRetrainedUserID = "";
	public static String ToBeRetrainedPSW = "";
	public static String ToBeRetrainedEmpID = "";

	public static String SGP_QualifieduserID = "";
	public static String SGP_QualifiedPSW = "";
	public static String SGP_QualifiedEmpID = "";

	public static String SGP_DRInProgressUserID = "";
	public static String SGP_DRInProgressPSW = "";
	public static String SGP_DRInProgressEmpID = "";

	public static String SGP_RQPPendingUserID = "";
	public static String SGP_RQPPendingPSW = "";
	public static String SGP_RQPPendingEmpID = "";

	public static String SGP_ToBeRetrainedUserID = "";
	public static String SGP_ToBeRetrainedPSW = "";
	public static String SGP_ToBeRetrainedEmpID = "";

	public static String SelfStudyRequired = "";
	public static String MandatorySubgroup = "";
	public static String OpenForAll = "";
	
	public static String checkCourseUserID = "";
	public static String checkCoursePSW = "";
	public static String checkCourseEmpID = "";

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[2]//a[contains(@class,'sub-menu')][contains(text(),'Initiate')]")
	WebElement initiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li//a[contains(@class,'sub-menu')][contains(text(),'Modify')]")
	WebElement modifyMenu;
	@FindBy(xpath = "//*[@data-menupath='Modify -> Course']")
	WebElement modifyCourseMenu;

	WebElement configCourseMenu;
	@FindBy(xpath = "//*[@data-menupath='Initiate -> Course']")
	WebElement courseMenu;
	@FindBy(xpath = "//input[@id='CMCourse_CourseDesc']")
	WebElement courseNameTxtFld;
	@FindBy(xpath = "//input[@id='CMCourse_Description']")
	WebElement descriptionText;
	@FindBy(xpath = "//input[@id='SelfStudyReqRD_1']")
	WebElement selfStudyNo;
	@FindBy(xpath = "//div[1]/span[2]/span[1]/span[1]/span[1]")
	WebElement trainingType;
	@FindBy(id = "CourseTypeRD_1")
	WebElement refresherType;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement trainingTypeSearchField;
	@FindBy(xpath = "//span[@class='select2-search select2-search--dropdown']//input")
	WebElement txtEnter;
	@FindBy(xpath = "//ul[@id='select2-CMCourse_TrainingType-results']/li[1]")
	WebElement trainingTypeOption;
	@FindBy(xpath = "//div[@class='conrol-column col-sm-12 p-2 border rounded']//input[@value='3']")
	WebElement topicRadioButton;
	@FindBy(xpath = "//input[@id='CMCourseTopic_TreeVC_SearchTxt']")
	WebElement searchField;
	@FindBy(id = "SelfStudyReqRD_0")
	WebElement selfStudy_Yes;
	@FindBy(xpath = "//input[@class='radio'][3]")
	WebElement topicRadio;
	@FindBy(xpath = "//label[text()='Topic Name']")
	WebElement topicRadioNew;
	@FindBy(id = "CMCourseTopic_TreeVC_SearchTxt")
	WebElement findText;
	@FindBy(id = "SearchBtn")
	WebElement fetchClick;
	@FindBy(xpath = "//ul[@id='CMCourse_AvailableTopics_ul']/li[1]")
	WebElement topicSelect1;
	@FindBy(xpath = "//button[@class='caliber-button-primary nextTab']")
	WebElement nextButton;
	@FindBy(xpath = "//th[normalize-space()='Select All']/input")
	WebElement docCheckbox;
	@FindBy(xpath = "//*[contains(text(),'Select All')]//input")
	WebElement docCheckbox1;
	@FindBy(id = "ExamReqFlgRD_1")
	WebElement assessmentNo;

	@FindBy(id = "ExamReqFlgRD_0")
	WebElement assessmentYes;

	@FindBy(id = "MqaReqFlgRD_1")
	WebElement MQANo;

	@FindBy(id = "MaximumMarks")
	WebElement MaximumMarks;

	@FindBy(id = "CMCourseQualPer")
	WebElement QlfPercentage;

	@FindBy(id = "GTPOnlyForSelSgpsFlagRD_0")
	WebElement courseOpenforAllYes;

	@FindBy(id = "GTPOnlyForSelSgpsFlagRD_1")
	WebElement courseOpenforAll_No;
	@FindBy(id = "GTPReqRD_1")
	WebElement selectMandatorySubgrpNo;

	@FindBy(id = "GTPReqRD_0")
	WebElement selectMandatorySubgrpYes;
	@FindBy(xpath = "//button[contains(text(), 'Preview')]")
	WebElement previewButton;
	@FindBy(id = "btnSubmit")
	WebElement finalSubmit;
	@FindBy(id = "txtESignPassword")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//*[@data-menupath='Initiate -> Self-Study Open Courses']")
	WebElement SScourseMenu;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchBy;
	@FindBy(xpath = "//li[text()='Course Name']")
	WebElement selectCourseName;
	@FindBy(id = "Description")
	WebElement CourseName;
	@FindBy(id = "crsRetrng")
	WebElement courseRetraining;
	@FindBy(id = "CourseDsc")
	WebElement enterCourseName;
	@FindBy(id = "displayBtn")
	WebElement apply;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr[1]")
	WebElement clickCourseName;
	@FindBy(id = "TakeTheCourse")
	WebElement addToReadingList;
	@FindBy(id = "ReadNow")
	WebElement ReadNow;
	@FindBy(xpath = "//a[@title='Prepare Question Bank']")
	WebElement prepareQB;

	WebElement retakeAssessmentNew;
	@FindBy(id = "NextBtn2")
	WebElement nextBtn11;
	@FindBy(xpath = "//span[text()='Subgroup Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement subGrp;
	@FindBy(xpath = "//label[text()='Subgroup Name']/preceding-sibling::input[@name='SearchType'][@value='2']")
	WebElement subGrpNew;
	@FindBy(id = "CMCourseGTP_TreeVC_SearchTxt")
	WebElement subGroupSelect;
	@FindBy(linkText = "Fetch Records")
	WebElement fetchClick1;
	@FindBy(xpath = "//ul[@id='CMCourseGTP_AvailSubgrps_ul']/li")
	WebElement subGrpSelect;
	@FindBy(id = "SelSgpAppTypeRD_2")
	WebElement approvals;

	@FindBy(id = "displayBtn")
	WebElement applyBtn;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr[1]")
	WebElement clickCourse;
	@FindBy(id = "CMCourse_Remarks")
	WebElement remarks;

	@FindBy(xpath = "//a[@title='Edit Question Bank']")
	WebElement editQuestioBankBu;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement getCourseName;

	@FindBy(xpath = "//b[text()='Course Name']/parent::label/following-sibling::span")
	WebElement CourseNameAfterClicking;

	public void Self_Study_Course_Registration_OpenForAll(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;
		QualifieduserID = testData.get("QualifiedUserID");
		QualifiedPSW = testData.get("QualifiedEmpPSW");
		QualifiedEmpID = testData.get("QualifiedEmployeeID");

		DRInProgressUserID = testData.get("InProgressUserID");
		;
		DRInProgressPSW = testData.get("InProgressEmpPSW");
		;
		DRInProgressEmpID = testData.get("InProgressEmplpoyeeID");
		;
		
		checkCourseUserID = testData.get("CheckCourseUserID");
		checkCoursePSW = testData.get("CheckCoursePSW");
		checkCourseEmpID = testData.get("CheckCourseEmpID");

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());

		if (testData.get("CourseType").equals("Refresher")) {
			click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		}
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");

		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentNo, "Select Assessment Required as 'Yes'", "", "", "");
		click2(courseOpenforAllYes, "Select Course Open For All as 'Yes'", "", "", "");
		click2(selectMandatorySubgrpNo, "Select Mandatory subgroup as 'No'", "", "", "");
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		if (testData.get("Assessment").equals("Yes")) {
			click2(prepareQB, "Click on Prepare Question Bank", "", "", "");

		}

		switchToDefaultContent(driver);

	}

	public void Self_Study_Course_Registration_OpenForAll_Assessment_Yes(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;
		QualifieduserID = testData.get("QualifiedUserID");
		QualifiedPSW = testData.get("QualifiedEmpPSW");
		QualifiedEmpID = testData.get("QualifiedEmployeeID");

		RQPPendingUserID = testData.get("RespondQPPendingUserID");
		;
		RQPPendingPSW = testData.get("RespondQPPendingPSW");
		;
		RQPPendingEmpID = testData.get("RespondQPPendingEmployeeID");
		;

		ToBeRetrainedUserID = testData.get("ToBeRetrainedUserID");
		ToBeRetrainedPSW = testData.get("ToBeRetrainedPSW");
		ToBeRetrainedEmpID = testData.get("ToBeRetrainedEmployeeID");
		
		
		DRInProgressUserID = testData.get("RespondDRPendingUserID");
		DRInProgressPSW = testData.get("RespondDRPendingPSW");
		DRInProgressEmpID = testData.get("RespondDRPendingEmployeeID");

		SelfStudyRequired = testData.get("SelfStudy");
		OpenForAll = testData.get("OpenForAll");

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		if (testData.get("CourseType").equals("Refresher")) {
			click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		}
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentYes, "Select Assessment Required as 'Yes'", "", "", "");
		click2(MQANo, "Select MQA Required as 'Yes'", "", "", "");

		sendKeys2(MaximumMarks, "Enter required max marks", testData.get("MaxMarks"), "", "", "");
		waitForElementVisibile(QlfPercentage);
		sendKeys2(QlfPercentage, "Enter required Qualified Percentage", testData.get("QualifyPercentageval"), "", "",
				"");

		click2(courseOpenforAllYes, "Select Course Open For All as 'Yes'", "", "", "");
		click2(selectMandatorySubgrpNo, "Select Mandatory subgroup as 'No'", "", "", "");
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);

	}

	public void initiate_Self_Study_Course() {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(SScourseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);

		click2(searchBy, "Click on Search By", "", "", "");

		click2(selectCourseName, "Select Course Name", "", "", "");

		sendKeys2(enterCourseName, "Enter Course Name", Course + Constants.PERCENTAGE_SIGN, "", "", "");
		click2(apply, "Click on Apply Button", "", "", "");
		TimeUtil.mediumWait();
		click2(clickCourseName, "Click on above Registered Course", "", "", "");
		waitForElementVisibile(addToReadingList);
		click2(addToReadingList, "Click on Add To Reading List Button", "", "", "");
		waitForElementVisibile(ReadNow);
		click2(ReadNow, "Click on Read Now button", "", "", "");
		TimeUtil.mediumWait();

	}

	public void Self_Study_Course_Registration_OpenForAll_MandatorySGP_yes_Assessment_No(
			HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;
		QualifieduserID = testData.get("QualifiedUserID");
		QualifiedPSW = testData.get("QualifiedEmpPSW");
		QualifiedEmpID = testData.get("QualifiedEmployeeID");

		DRInProgressUserID = testData.get("InProgressUserID");
		;
		DRInProgressPSW = testData.get("InProgressEmpPSW");
		;
		DRInProgressEmpID = testData.get("InProgressEmplpoyeeID");
		;

		SGP_QualifieduserID = testData.get("SubGrpQualifiedUserID");
		SGP_QualifiedPSW = testData.get("SubGrpQualifiedEmpPSW");
		SGP_QualifiedEmpID = testData.get("SubGrpQualifiedEmployeeID");

		ToBeRetrainedUserID = testData.get("ToBeRetrainedUserID");
		ToBeRetrainedPSW = testData.get("ToBeRetrainedPSW");
		ToBeRetrainedEmpID = testData.get("ToBeRetrainedEmployeeID");

		SGP_DRInProgressUserID = testData.get("SubGrpInProgressUserID");
		SGP_DRInProgressPSW = testData.get("SubGrpInProgressEmpPSW");
		SGP_DRInProgressEmpID = testData.get("SubGrpInProgressEmplpoyeeID");
		MandatorySubgroup = testData.get("MandatorySubgroup");
		OpenForAll = testData.get("OpenForAll");
		
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		if (testData.get("CourseType").equals("Refresher")) {
			click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		}
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentNo, "Select Assessment Required as 'No'", "", "", "");

		click2(courseOpenforAllYes, "Select Course Open For All as 'Yes'", "", "", "");
		click2(selectMandatorySubgrpYes, "Select Mandatory subgroup as 'Yes'", "", "", "");
		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
		sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
				testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
				CourseStrings.AvailableSubgrp_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);

	}

	public void Self_Study_Course_Registration_OpenForAll_MandatorySGP_yes_Assessment_Yes(
			HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;
		QualifieduserID = testData.get("QualifiedUserID");
		QualifiedPSW = testData.get("QualifiedEmpPSW");
		QualifiedEmpID = testData.get("QualifiedEmployeeID");

		DRInProgressUserID = testData.get("RespondDRPendingUserID");
		DRInProgressPSW = testData.get("RespondDRPendingPSW");
		DRInProgressEmpID = testData.get("RespondDRPendingEmployeeID");

		RQPPendingUserID = testData.get("RespondQPPendingUserID");
		RQPPendingPSW = testData.get("RespondQPPendingPSW");
		RQPPendingEmpID = testData.get("RespondQPPendingEmployeeID");

		ToBeRetrainedUserID = testData.get("ToBeRetrainedUserID");
		ToBeRetrainedPSW = testData.get("ToBeRetrainedPSW");
		ToBeRetrainedEmpID = testData.get("ToBeRetrainedEmployeeID");

		SGP_QualifieduserID = testData.get("SubGrpQualifiedUserID");
		SGP_QualifiedPSW = testData.get("SubGrpQualifiedEmpPSW");
		SGP_QualifiedEmpID = testData.get("SubGrpQualifiedEmployeeID");

		SGP_DRInProgressUserID = testData.get("SubGrpRespondDRPendingUserID");
		SGP_DRInProgressPSW = testData.get("SubGrpRespondDRPendingPSW");
		SGP_DRInProgressEmpID = testData.get("SubGrpRespondDRPendingEmployeeID");

		SGP_RQPPendingUserID = testData.get("SubGrpRespondQPPendingUserID");
		SGP_RQPPendingPSW = testData.get("SubGrpRespondQPPendingPSW");
		SGP_RQPPendingEmpID = testData.get("SubGrpRespondQPPendingEmployeeID");

		SGP_ToBeRetrainedUserID = testData.get("SubGrpToBeRetrainedUserID");
		SGP_ToBeRetrainedPSW = testData.get("SubGrpToBeRetrainedPSW");
		SGP_ToBeRetrainedEmpID = testData.get("SubGrpToBeRetrainedEmployeeID");

		MandatorySubgroup = testData.get("MandatorySubgroup");

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());

		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentYes, "Select Assessment Required as 'Yes'", "", "", "");
		click2(MQANo, "Select MQA Required as 'Yes'", "", "", "");

		sendKeys2(MaximumMarks, "Enter required max marks", testData.get("MaxMarks"), "", "", "");
		waitForElementVisibile(QlfPercentage);
		sendKeys2(QlfPercentage, "Enter required Qualified Percentage", testData.get("QualifyPercentageval"), "", "",
				"");
		click2(courseOpenforAllYes, "Select Course Open For All as 'Yes'", "", "", "");
		click2(selectMandatorySubgrpYes, "Select Mandatory subgroup as 'Yes'", "", "", "");
		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
		sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
				testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
				CourseStrings.AvailableSubgrp_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);

	}

	public void Self_Study_Course_Registration_OpenForAll_No_MandatorySGP_yes_Assessment_No(
			HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;

		SGP_QualifieduserID = testData.get("SubGrpQualifiedUserID");
		SGP_QualifiedPSW = testData.get("SubGrpQualifiedEmpPSW");
		SGP_QualifiedEmpID = testData.get("SubGrpQualifiedEmployeeID");

		SGP_DRInProgressUserID = testData.get("SubGrpInProgressUserID");
		SGP_DRInProgressPSW = testData.get("SubGrpInProgressEmpPSW");
		SGP_DRInProgressEmpID = testData.get("SubGrpInProgressEmplpoyeeID");

		MandatorySubgroup = testData.get("MandatorySubgroup");
		
		checkCourseUserID = testData.get("CheckCourseUserID");
		checkCoursePSW = testData.get("CheckCoursePSW");
		checkCourseEmpID = testData.get("CheckCourseEmpID");

		
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		if (testData.get("CourseType").equals("Refresher")) {
			click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		}
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentNo, "Select Assessment Required as 'No'", "", "", "");

		click2(courseOpenforAll_No, "Select Course Open For All as 'Yes'", "", "", "");
//		click2(selectMandatorySubgrpYes, "Select Mandatory subgroup as 'Yes'", "", "", "");
		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
		sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
				testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
				CourseStrings.AvailableSubgrp_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);

	}

	public void Self_Study_Course_Registration_OpenForAll_No_MandatorySGP_yes_Assessment_Yes(
			HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		Course = testData.get("CourseName") + s;

		SGP_QualifieduserID = testData.get("SubGrpQualifiedUserID");
		SGP_QualifiedPSW = testData.get("SubGrpQualifiedEmpPSW");
		SGP_QualifiedEmpID = testData.get("SubGrpQualifiedEmployeeID");

		SGP_DRInProgressUserID = testData.get("SubGrpRespondDRPendingUserID");
		SGP_DRInProgressPSW = testData.get("SubGrpRespondDRPendingPSW");
		SGP_DRInProgressEmpID = testData.get("SubGrpRespondDRPendingEmployeeID");

		SGP_RQPPendingUserID = testData.get("SubGrpRespondQPPendingUserID");
		SGP_RQPPendingPSW = testData.get("SubGrpRespondQPPendingPSW");
		SGP_RQPPendingEmpID = testData.get("SubGrpRespondQPPendingEmployeeID");

		SGP_ToBeRetrainedUserID = testData.get("SubGrpToBeRetrainedUserID");
		SGP_ToBeRetrainedPSW = testData.get("SubGrpToBeRetrainedPSW");
		SGP_ToBeRetrainedEmpID = testData.get("SubGrpToBeRetrainedEmployeeID");

		MandatorySubgroup = testData.get("MandatorySubgroup");
		OpenForAll = testData.get("OpenForAll");
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(courseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);
		sendKeys2(courseNameTxtFld, CourseStrings.CourseName_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseName_SS.getCourseStrings());
		sendKeys2(descriptionText, CourseStrings.CourseDescription_DC.getCourseStrings(), Course,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.CourseDescription_SS.getCourseStrings());
		if (testData.get("CourseType").equals("Refresher")) {
			click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		}
		click2(trainingType, CourseStrings.TrainingTypeDropDown_DC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AC.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_AR.getCourseStrings(),
				CourseStrings.TrainingTypeDropDown_SS.getCourseStrings());

		sendKeys2(txtEnter, CourseStrings.TrainingTypeLike_DC.getCourseStrings(), testData.get("TrainingtypeVal"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TrainingTypeLike_SS.getCourseStrings());
		click2(trainingTypeOption, CourseStrings.TechnicalTraining_Select_DC.getCourseStrings(),
				CourseStrings.TechnicalTraining_Select_AC.getCourseStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.TechnicalTraining_Select_SS.getCourseStrings());

		click2(selfStudy_Yes, "Select Self-Study as 'Yes'", "", "", "");
		click2(topicRadio, CourseStrings.SearchByTopicName_DC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AC.getCourseStrings(),
				CourseStrings.SearchByTopicName_AR.getCourseStrings(),
				CourseStrings.SearchByTopicName_SS.getCourseStrings());
		String TopicNameVal = CM_Topic.getTopic();
		sendKeys2(findText, CourseStrings.TopicNameLike_DC.getCourseStrings(), TopicNameVal + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.TopicNameLike_SS.getCourseStrings());
		click2(fetchClick, CourseStrings.FetchRecords_DC.getCourseStrings(),
				CourseStrings.FetchRecords_AC.getCourseStrings(), CourseStrings.FetchRecords_AR.getCourseStrings(),
				CourseStrings.FetchRecords_SS.getCourseStrings());
		waitForElementVisibile(topicSelect1);
		TimeUtil.mediumWait();
		click2(topicSelect1, CourseStrings.AvailableTopic_DC.getCourseStrings(),
				CourseStrings.AvailableTopic_AC.getCourseStrings(), CourseStrings.AvailableTopic_AR.getCourseStrings(),
				CourseStrings.AvailableTopic_SS.getCourseStrings());
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.longwait();
		click2(docCheckbox1, CourseStrings.SelectAllDoc_DC.getCourseStrings(),
				CourseStrings.SelectAllDoc_AC.getCourseStrings(), CourseStrings.SelectAllDoc_AR.getCourseStrings(),
				CourseStrings.SelectAllDoc_SS.getCourseStrings());

		click2(assessmentYes, "Select Assessment Required as 'Yes'", "", "", "");
		click2(MQANo, "Select MQA Required as 'Yes'", "", "", "");

		sendKeys2(MaximumMarks, "Enter required max marks", testData.get("MaxMarks"), "", "", "");
		waitForElementVisibile(QlfPercentage);
		sendKeys2(QlfPercentage, "Enter required Qualified Percentage", testData.get("QualifyPercentageval"), "", "",
				"");
		click2(courseOpenforAll_No, "Select Course Open For All as 'No'", "", "", "");
		click2(nextBtn11, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButton1_AC.getCourseStrings(), CourseStrings.NextButton1_AR.getCourseStrings(),
				CourseStrings.NextButton1_SS.getCourseStrings());
		click2(subGrp, CourseStrings.SearchBySubgroupName_DC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AC.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_AR.getCourseStrings(),
				CourseStrings.SearchBySubgroupName_SS.getCourseStrings());
		sendKeys2(subGroupSelect, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
				testData.get("SubgroupNamevalue") + Constants.PERCENTAGE_SIGN,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(fetchClick1, CourseStrings.SubgrpName_FetchRcrds_DC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AC.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_AR.getCourseStrings(),
				CourseStrings.SubgrpName_FetchRcrds_SS.getCourseStrings());
		TimeUtil.mediumWait();
		click2(subGrpSelect, CourseStrings.AvailableSubgrp_DC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AC.getCourseStrings(),
				CourseStrings.AvailableSubgrp_AR.getCourseStrings(),
				CourseStrings.AvailableSubgrp_SS.getCourseStrings());
		TimeUtil.shortWait();
		click2(approvals, CourseStrings.ApprovalforCandidature_DC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AC.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_AR.getCourseStrings(),
				CourseStrings.ApprovalforCandidature_SS.getCourseStrings());
		waitForElementVisibile(previewButton);
		click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(), CourseStrings.Preview_AC.getCourseStrings(),
				CourseStrings.Preview_AR.getCourseStrings(), CourseStrings.Preview_SS.getCourseStrings());
		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);
		switchToDefaultContent(driver);

	}

	public void modifyCourse_CourseRetraining() {

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(modifyMenu, "Click on Modify menu", CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(modifyCourseMenu, "Click on Course menu", "", "", "");
		switchToBodyFrame(driver);
		click2(searchBy, "Click on Search By drop down", "", "", "");
		click2(selectCourseName, "Select Course Name", "", "", "");

		sendKeys2(CourseName, "Enter above registered course name", CM_SelfStudyCourse.Course, "", "", "");
		click2(applyBtn, "Click on Apply button", "", "", "");
		click2(clickCourse, "Click on above registred course", "", "", "");
		waitForElementVisibile(nextButton);
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.mediumWait();

		click2(courseRetraining, "Select Course Retraining Check Box", "", "", "");
		click2(nextBtn11, "Click on Next Button", "", "", "");
		if (MandatorySubgroup.equals("Yes")) {
			waitForElementVisibile(previewButton);
			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());

		}

//		waitForElementVisibile(remarks);
		sendKeys2(remarks, "Enter required remarks", "Remarks", "", "", "");

		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);
//		saveUniqueCode(driver, confirmationText);
		try {
			if (!OpenForAll.equals("Yes")) {
				click2(editQuestioBankBu, "Click on Edit Question Bank", "", "", "");
			} else {
				switchToDefaultContent(driver);
			}
		} catch (Exception e) {

		}

	}

	public void modifyCourse_To_Refrehser() {
		ModifiedCourse = CM_SelfStudyCourse.Course + "MOD1";
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(modifyMenu, "Click on Modify menu", CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(modifyCourseMenu, "Click on Course menu", "", "", "");
		switchToBodyFrame(driver);
		click2(searchBy, "Click on Search By drop down", "", "", "");
		click2(selectCourseName, "Select Course Name", "", "", "");

		sendKeys2(CourseName, "Enter above registered course name", CM_SelfStudyCourse.Course, "", "", "");
		click2(applyBtn, "Click on Apply button", "", "", "");
		click2(clickCourse, "Click on above registred course", "", "", "");
		waitForElementVisibile(courseNameTxtFld);
		sendKeys2(courseNameTxtFld, "Modify the course name", ModifiedCourse, "", "", "");
		waitForElementVisibile(refresherType);
		click2(refresherType, "Select course type as 'Refresher Training Required'", "", "", "");
		waitForElementVisibile(nextButton);
		click2(nextButton, CourseStrings.NextButtonCR_DC.getCourseStrings(),
				CourseStrings.NextButtonCR_AC.getCourseStrings(), CourseStrings.NextButtonCR_AR.getCourseStrings(),
				CourseStrings.NextButtonCR_SS.getCourseStrings());
		TimeUtil.mediumWait();

		click2(nextBtn11, "Click on Next Button", "", "", "");
		if (MandatorySubgroup.equals("Yes")) {
			waitForElementVisibile(previewButton);
			click2(previewButton, CourseStrings.Preview_DC.getCourseStrings(),
					CourseStrings.Preview_AC.getCourseStrings(), CourseStrings.Preview_AR.getCourseStrings(),
					CourseStrings.Preview_SS.getCourseStrings());

		}

//		waitForElementVisibile(remarks);
		sendKeys2(remarks, "Enter required remarks", "Remarks", "", "", "");

		waitForElementVisibile(finalSubmit);
		click2(finalSubmit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseStrings.SubmitCoursewithEsign_AC.getCourseStrings(),
				CourseStrings.SubmitCoursewithEsign_AR.getCourseStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
		}

		waitForElementVisibile(confirmationText);
//		saveUniqueCode(driver, confirmationText);
		try {
			if (OpenForAll.equals("Yes")) {
				click2(editQuestioBankBu, "Click on Edit Question Bank", "", "", "");
			} else {
				switchToDefaultContent(driver);
			}
		} catch (Exception e) {

		}

	}

	public void validate_Initiate_Self_Study_Course_AftrCoursename_Modification() {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
		}
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(SScourseMenu, CourseStrings.CourseRegistrationScreen_DC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AC.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_AR.getCourseStrings(),
				CourseStrings.CourseRegistrationScreen_SS.getCourseStrings());
		switchToBodyFrame(driver);

		click2(searchBy, "Click on Search By", "", "", "");

		click2(selectCourseName, "Select Course Name", "", "", "");

		sendKeys2(enterCourseName, "Enter Course Name", Course, "", "", "");
		click2(apply, "Click on Apply Button", "", "", "");
		TimeUtil.mediumWait();
		verifyExactCaption(getCourseName, " No data available in table", "Course Name");
		click2(searchBy, "Click on Search By", "", "", "");
		click2(selectCourseName, "Select Course Name", "", "", "");
		sendKeys2(enterCourseName, "Enter Course Name", ModifiedCourse, "", "", "");
		click2(apply, "Click on Apply Button", "", "", "");
		TimeUtil.mediumWait();
		verifyExactCaption(getCourseName, ModifiedCourse, "Modified Course Name");
		click2(clickCourseName, "Click on above Registered Course", "", "", "");
		waitForElementVisibile(addToReadingList);
		verifyExactCaption(CourseNameAfterClicking, ModifiedCourse, CourseNameAfterClicking.getText().trim());
		switchToDefaultContent(driver);

	}

}
