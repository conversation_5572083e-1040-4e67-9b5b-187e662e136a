package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Online IT Session without Assessment for scheduled course with Content
 * Delivery as 'Yes', respond Document reading atleast with 1 user and before
 * and after batch formation.
 **/
public class UNSCH_IT_ON_WITHOUT_ASSESSMENT_CONTENT_DELIVERY_YES extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/UnscheduledScenariosData/UNSCH_IT_ON_WITHOUT_ASSESSMENT_CONTENT_DELIVERY_YES.xlsx";

	public UNSCH_IT_ON_WITHOUT_ASSESSMENT_CONTENT_DELIVERY_YES() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//				epiclogin.masterPlant();
		//
//				InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//						Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//				Logout.signOutPage();

//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//				epiclogin.plant1();
		//
//				InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//				Logout.signOutPage();

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));

//				epiclogin.masterPlant();
		//
//				Initiate_Course.courseConfiguration_Reg(testData);

//				epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//				Logout.signOutPage();

//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//				epiclogin.plant1();
		//
//				Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
//				Logout.signOutPage();

	}

	
	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
		}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));
//					epiclogin.plant1();
//					trainer.TrainerModificationConfigurations(testData);
		trainer.trainer_Modification_AuditTrails(testData);
//					Logout.signOutPage();
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//					epiclogin.plant1();
//					trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//					Logout.signOutPage();

	}
	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated excel = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegDOC");

	@DataProvider(name = "courseSessionRegistration")
	public Object[][] getdocument_preparation_Request() throws Exception {
		Object[][] obj = new Object[excel.getRowCount()][1];
		for (int i = 1; i <= excel.getRowCount(); i++) {
			HashMap<String, String> testData = excel.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "courseSessionRegistration", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
		test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_OnLine_WithExam_Content_Delivery_Yes(testData);

//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//
//		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Proposed Status");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);

		Logout.signOutPage();

	}
	// Test Method for Respond Document Reading Before Batch Formation

	@Test(priority = 6, enabled = true)
	public void verifyRespondDocReading() {
		if (isReportedRequired == true) {
		test = extent.createTest("Respond Document Reading Before Batch Formation")

				.assignAuthor(CM_CourseSession.QualifiedTraineeID)

				.assignCategory("Respond Document Reading Before Batch Formation");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.QualifiedTraineeID,
				CM_CourseSession.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.verifyRespondDocReading();
		Logout.signOutPage();
	}

	// Test Method for BatchFormation Configuration, Registration with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "batchFormation", enabled = true)
	public void batchFormationForOnlineSession(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Online_NotRespondedUsers_SelectAll(testData);

		// BatchFormation.proposeBatchFormationAuditTrail();
		
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Employee  selected in batch")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report For Employee selected in batch");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);

		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading
	// ---------------------------------------------------------------------------------------------

	@Test(priority = 8, enabled = true)
	public void respondDocReading() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading")

					.assignAuthor(CM_CourseSession.QualifiedTraineeID)

					.assignCategory("Respond Document Reading");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.QualifiedTraineeID,
				CM_CourseSession.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_CourseSession.QualifiedTraineePsw);
		Logout.signOutPage();
	}	
	// Test Method for Record Attendance
	// ---------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendanceForOnlineSessionType(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();

		RecordAttendance.recordAttendance_CandidatesList_SelectAll(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Qualified Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Qualified Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.ITType);

//			test = extent.createTest("Check Trainees at Course Session Screen")
//					.assignAuthor( ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Check Trainees at Course Session Screen");
//			
//			CourseSession.verify_Employees_At_Coursesession();
		//
//			Logout.signOutPage();
	}
}
