package com.Automation.ObjectModelFlows_OQ.System_Manager.JDK;

import java.awt.AWTException;
import java.awt.Robot;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.awt.event.KeyEvent;
import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.SYS_Subgroup;
import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.DocumentRegistrationStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;

public class SYS_JobResponsibility extends OQActionEngine {
	public static String QueryDocName = "";
	String RegReportingToValue = "";
	String RegAuthorizedDeputyValue = "";
	String RegQualificationValue = "";
	String RegPreviousExperienceVal = "";
	String RegExternalCertificates = "";
	String RegsubGroupNameValue = "";
	String RegJobResponsibilityValue = "";
	String RegDesignationValue = "";
	String RegEmployeeName = "";
	String RegEmployeeID = "";
	String RegDepartName = "";
	public static String JobResNew = "";

	public static String QualificationValue = "";
	public static String PreviousExperienceval = "";
//	String RegQualificationValue = "";

	String RegEmpName = "";
	String RegAuthorizedDeputy = "";
	Properties prop;

	public String getRegAuthorizedDeputy() {
		return RegAuthorizedDeputy;
	}

	public void setRegAuthorizedDeputy(String regAuthorizedDeputy) {
		RegAuthorizedDeputy = regAuthorizedDeputy;
	}

	public String getRegEmpName() {
		return RegEmpName;
	}

	public void setRegEmpName(String regEmpName) {
		RegEmpName = regEmpName;
	}

	public static String getQueryDocumentName() {
		return QueryDocName;
	}

	public static void setQueryDocumentName(String querydocumentName) {
		QueryDocName = querydocumentName;
	}

	public static String getQualificationValue() {
		return QualificationValue;
	}

	public static void setQualificationValue(String qualificationValue) {
		QualificationValue = qualificationValue;
	}

	public static String getPreviousExperienceVal() {
		return PreviousExperienceval;
	}

	public static void setPreviousExperienceVal(String previousExperienceVal) {
		PreviousExperienceval = previousExperienceVal;
	}

	public static String getJobRes() {
		return JobResNew;
	}

	public static void setJobRes(String jobRes) {
		JobResNew = jobRes;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManager;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN10")
	WebElement jobResponsbilityMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN07_SUBMEN10")
	WebElement jobResponsibiltyAprvMenu;
	@FindBy(id = "JobResponsibility_DOJ_btn")
	WebElement dateofJoining;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[1]")
	WebElement mElement;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[2]")
	WebElement yElement;
	@FindBy(xpath = "//button[@id='JobResponsibility_Employee_selectBtn']")
	WebElement addNew;
	@FindBy(xpath = "//span[@id='select2-JobResponsibility_DeptCode-container']")
	WebElement departmentdd;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement departmentSearchfield;
	@FindBy(xpath = "//textarea[@id='JobResponsibility_JobDescription']")
	WebElement jobResponsibilty;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_AdptPopUpVc']")
	WebElement authorizedDeputy;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_FindTxt']")
	WebElement authorizedDeputyFindNew;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_DisplayBtn']")
	WebElement authorizedDeputyApplyNew;
	@FindBy(xpath = "//table[@id='ListTab']//tbody/tr[1]/td[1]/input[1]")
	WebElement authorizedDeputyEmployeeRB;
	@FindBy(xpath = "//button[@id='JobResponsibility_AdptPopUpVc_selectBtn']")
	WebElement authorizedDeputyAddNew;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_DmsDoc']")
	WebElement externalcerificates;
	@FindBy(xpath = "//tbody/tr[1]/td[3]/button[1]")
	WebElement documentCodeAddNew;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_selectBtn']")
	WebElement documentUploaderNew;
	@FindBy(xpath = "//span[@id='select2-JobResponsibility_AppSubGrpsApp_Select-container']")
	WebElement approverNew;
	@FindBy(xpath = "//body/span[1]/span[1]/span[1]/input[1]")
	WebElement approverSearchNew;
	@FindBy(xpath = "/html[1]/body[1]/span[1]/span[1]/span[2]/ul[1]/li[1]")
	WebElement approverNewoption;
	@FindBy(id = "JobResponsibility_Qualification")
	WebElement qualification;
	@FindBy(id = "JobResponsibility_PreviousExperience")
	WebElement previousExperience;
	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;
	@FindBy(xpath = "//span[contains(@class,'conficonfirmationMsgon_text')]")
	WebElement confirmationMsg;
	@FindBy(xpath = "//ul[@class='sub-menu']//li[contains(@class,'nav-item open')]//a[contains(@class,'nav-link nav-toggle')][contains(text(),'Audit Trails')]")
	WebElement auditTrails;
	@FindBy(id = "displayBtn")
	WebElement displayBtn;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement searchFilter;
	@FindBy(xpath = "//span[@id='select2-SearchType-container']")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Employee Name')]")
	WebElement searchByNewDropdown;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement auditEmployeeNameField;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Employee Name']")
	WebElement searchByEmployeeNameOption;
	@FindBy(xpath = "//input[@id='EmployeeName']")
	WebElement employeeName;
	@FindBy(xpath = "//button[@id='displayBtn']")
	WebElement applyNew;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement empFullName;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement empID;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement Dept;

	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(xpath = "//input[@id='AppClsJson']/preceding-sibling::button")
	WebElement submitBtn1;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Audit Trails')]")
	WebElement audittrails;
	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN10")
	WebElement auditJobResponsibility;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[1]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditEmpName;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[7]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditJobResponsibility1;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[9]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditAuthorizedDeputy;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[10]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditQualification;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[11]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditPreviousExperience;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[14]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditLineOfApprovers;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[2]")
	WebElement initiatedBY;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[2]")
	WebElement returnActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[2]")
	WebElement reInitiateActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[2]")
	WebElement reInitiateActionApprove1;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[5]//td[2]")
	WebElement reInitiateActionApprove2;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[6]//td[2]")
	WebElement reInitiateActionApprove3;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[2]")
	WebElement subGrpApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[2]")
	WebElement userApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[2]")
	WebElement adApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[3]")
	WebElement auditDateTimeValue1;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[3]")
	WebElement auditDateTimeValue2;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[3]")
	WebElement auditDateTimeValue3;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[3]")
	WebElement auditDateTimeValue4;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[5]//td[3]")
	WebElement auditDateTimeValue5;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[6]//td[3]")
	WebElement auditDateTimeValue6;
	@FindBy(xpath = "//div[@class=\"approve-status\"]//span[1]//span[1]")
	WebElement auditApprovalRequiredValue;
	@FindBy(xpath = "//div[@class=\"approve-status\"]//span[2]//span[1]")
	WebElement auditApprovalCompletedValue;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Modification\")]")
	WebElement modificationAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Registration\")]")
	WebElement registrationAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Re-initiation Task Transfer (Reg.)\")]")
	WebElement regReinitTransferAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Re-initiation Task Transfer (Mod.)\")]")
	WebElement modREInitTransferAuditBtn;
	@FindBy(xpath = "//button[@class=\"caliber-button-primary btn_center\"]")
	WebElement auditProceedBtn;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager_User Groups\"]/child::li/a[contains(text(),'accept')]")
	WebElement accept;
	@FindBy(id = "TMS_System Manager_User Groups_MEN02_SUBMEN10")
	WebElement acceptJR;
	@FindBy(id = "TMS_System Manager_User Groups_MEN02_SUBMEN12")
	WebElement acceptJRADAcceptance;
	@FindBy(id = "SelectedDecision_2")
	WebElement approveRadioBtn;
	@FindBy(id = "SelectedDecision_3")
	WebElement returnRadioBtn;
	@FindBy(id = "Remarks")
	WebElement approveRemarks;
	@FindBy(xpath = "//ul[@class=\"nav nav-pills nav-justified\"]//li[2]//button")
	WebElement approveModification;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager_User Groups\"]/child::li/a[contains(text(),'Modify')]")
	WebElement modify;
	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN10")
	WebElement modifyJobResMenu;
	@FindBy(id = "JobResponsibility_Remarks")
	WebElement modifyJobResRemarks;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']//li[2]")
	WebElement defaultAdminSecurity;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager\"]//li[2]//li[1]")
	WebElement defaultAdminSecurityInitiate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']//li[2]//li[1]//ul//li[3]")
	WebElement setCentralCnfiguration;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobRspAccRequired\"]")
	WebElement jobResponsibilityAcceptanceYes;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizeDeputyAcceptanceYes;
	@FindBy(id = "CentralConfiguration_Remarks")
	WebElement centralConfigurationRemarks;
	@FindBy(id = "Remarks")
	WebElement remarksVal;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Re-Initiation')]")
	WebElement reInitiateMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN109_SUBMEN10")
	WebElement reInitiateJobResMenu;
	@FindBy(id = "JobResponsibility_Remarks")
	WebElement reInitRemarks;
	@FindBy(xpath = "//ul[@class=\"nav nav-pills nav-justified\"]//li[2]//button")
	WebElement modificationReIntiate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'RI Transfer')]")
	WebElement RITransferMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MODMEN21_SUBMEN10")
	WebElement riTransferjobresponsibility;
	@FindBy(id = "TransferUserPopUpBtn")
	WebElement addItemBtn;
	@FindBy(xpath = "//input[contains(@placeholder,'Search this Page')]")
	WebElement user;
	@FindBy(xpath = "//tbody/tr[1]/td[1]/input[1]")
	WebElement displayedUserRadioBtn;
	@FindBy(id = "Transfer_selectBtn")
	WebElement addBtn;
	@FindBy(id = "Remarks")
	WebElement riTransferRemarks;
	@FindBy(xpath = "//div[@class=\"event-div\"]//span[2]")
	WebElement transferedFrom;
	@FindBy(xpath = "//div[@class=\"event-div\"]//span[4]")
	WebElement transferedTo;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[2]")
	WebElement trasferedBy;
	@FindBy(id = "Description")
	WebElement sendTxtLike;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceYes;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobRspAccRequired\"]")
	WebElement jobResponsibilityAcceptanceNO;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceNO;
	@FindBy(xpath = "//span[text()='Add Users']")
	WebElement authorizedDeputyAddUsers;
	@FindBy(xpath = "//i[@class='ft-search']")
	WebElement authorizedDeputyGlobalSearch;
	@FindBy(xpath = "//div[@id='ListTab_filter']//label//input")
	WebElement authorizedDeputyGlobalSearchTextEnter;
	@FindBy(xpath = "//table[@id='ListTab']//td//input")
	WebElement authorizedDeputySelectEmployee;
	@FindBy(id = "JobResponsibility_AdptPopUpVc_selectBtn")
	WebElement authorizedDeputyAdd;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/li[7]/a[1]")
	WebElement configMenu;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/li[7]/a[1]/following-sibling::ul/li[4]")
	WebElement jobResponsbilityMenu1;
	@FindBy(id = "select2-JobResponsibility_DeptCode-container")
	WebElement department;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement searchFieldtxt;
	@FindBy(xpath = "//ul[@id='select2-JobResponsibility_DeptCode-results']/li[1]")
	WebElement departmentFieldoption;
	@FindBy(xpath = "//span[text()='Job Responsibility: Registration Initiation']")
	WebElement confirmationText1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//div[@class='form-group']//input[@id='JobResponsibility_EmployeeName']")
	WebElement EmployeeNameJobRes;
	@FindBy(xpath = "//input[@id='JobResponsibility_EmployeeId']")
	WebElement EmployeeIDJobRes;
	@FindBy(xpath = "//input[@id='JobResponsibility_DesignationName']")
	WebElement DesignationJobRes;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approvetext;
	@FindBy(xpath = "//input[@id='JobResponsibility_DesignationName']")
	WebElement DesignationJobName;
	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement approveremarks;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_AdptPopUpVc']")
	WebElement AddItemAuthorizedDeputy;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Accept')]")
	WebElement usergroupsAccept;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN02_SUBMEN10']")
	WebElement acceptJobresponsiblity;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN02_SUBMEN12']")
	WebElement acceptADacceptance;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_FindTxt']")
	WebElement AddItemAuthorizedDeputytext;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_DisplayBtn']")
	WebElement AddItemAuthorizedDeputyapply;
	@FindBy(xpath = "//input[@name='recordSelection']")
	WebElement AddItemAuthorizedDeputyadd;
	@FindBy(xpath = "//button[@id='JobResponsibility_AdptPopUpVc_selectBtn']")
	WebElement AddItemAuthorizedDeputyadd1;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN10")
	WebElement jobResAuditTrails;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[6]")
	WebElement auditTrailPageColumn6;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Employee Name']/following-sibling::span")
	WebElement auditCompareTRNEmployeeName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Employee ID']/following-sibling::span")
	WebElement auditCompareTRNEmployeeID;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Designation']/following-sibling::span")
	WebElement auditCompareTRNDesignation;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Department')]/following-sibling::span")
	WebElement auditCompareTRNDepartment;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Date of Joining']/following-sibling::span")
	WebElement auditCompareTRNDateOfJoining;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Revision No.']/following-sibling::span")
	WebElement auditCompareTRNRevisionNo;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Job Responsibility']/following-sibling::span")
	WebElement auditCompareTRNJobResp;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Reporting To']/following-sibling::span")
	WebElement auditCompareTRNReportingTo;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Authorized Deputy']/following-sibling::div//span")
	WebElement auditCompareTRNAuthorizedDeputy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Qualification']/following-sibling::span")
	WebElement auditCompareTRNQualification;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Previous Experience']/following-sibling::span")
	WebElement auditCompareTRNPreviousExpe;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'External Certificates')]/following-sibling::span//a")
	WebElement auditCompareTRNExternalCertificates;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Line of Approvers')]/following-sibling::span")
	WebElement auditCompareTRNLineOfApprovers;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_DmsDoc']//span[@class='add-item'][normalize-space()='Add Item']")
	WebElement addExternalCertificates;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDocUploadFileTabBtn']")
	WebElement uploadFile;
	@FindBy(xpath = "//input[@id='DocumentDesc']")
	WebElement DocumentDescription;
	@FindBy(xpath = "//input[@id='KeyWords']")
	WebElement Keyword;
	@FindBy(xpath = "//label[@for='DocFile']")
	WebElement chooseFile;

	@FindBy(xpath = "//input[@id='DocFile']")
	WebElement externalDocumentUpload;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;
	@FindBy(xpath = "//p[@id='status-message-1-2']")
	WebElement auditCompareTRNUserAccepApproveActionValue;
	@FindBy(xpath = "//p[@id='approve-name-1-2']")
	WebElement auditCompareTRNUserAccepApproveActionByValue;
	@FindBy(xpath = "//p[@id='approve-date-1-2']")
	WebElement auditCompareTRNUserAccepApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='progress-reports-1-2']")
	WebElement auditCompareTRNUserAccepApproveRemarksValue;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_submitBtn']")
	WebElement chooseFileUpload;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDocSearchFileTabBtn']")
	WebElement searchfile;
	@FindBy(xpath = "(//span[@class='select2-selection__arrow'])[2]")
	WebElement searchbydrop;
	@FindBy(xpath = "//li[text()='Document Description']")
	WebElement selectdocumentdescription;
	@FindBy(xpath = "//li[text()='Document Code']")
	WebElement selectdocumentcode;
	@FindBy(xpath = "//input[@id='JobResponsibility_DmsDoc_FindTxt']")
	WebElement documentfindtext;
	@FindBy(xpath = "//input[@id='JobResponsibility_DmsDoc_DisplayBtn']")
	WebElement applydoc;
	@FindBy(xpath = "//button[@id='OkBtn']")
	WebElement okdoc;
	@FindBy(xpath = "//tr[@role='row']//button[@type='button'][normalize-space()='Add']")
	WebElement adddoc;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_selectBtn']")
	WebElement adddocum;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement auditEmployeeNameLike;
	@FindBy(xpath = "/html[1]/body[1]/div[5]/h3[1]")
	WebElement docconfirmationText;
	@FindBy(xpath = "//input[@id='JobResponsibility_RevisionNo']")
	WebElement revisionnumber;
	@FindBy(xpath = "//input[@id='JobResponsibility_ReportingToName']")
	WebElement jobReportingTo;

	@FindBy(xpath = "//p[@id='status-message-1-3']")
	WebElement auditCompareTRNADApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[1]")
	WebElement auditCompareTRNADApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[2]")
	WebElement auditCompareTRNADApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[3]")
	WebElement auditCompareTRNADApproveRemarksValue;

	public SYS_JobResponsibility() {
		PageFactory.initElements(driver, this);
	}

	public void jobresponsbility_Configuration(HashMap<String, String> testData) {
		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configMenu, jobResponsbilityMenu1,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu1,
				JobResponsibilityStrings.JobResponsibility_Config_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "Approval");
		TimeUtil.shortWait();
		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void jobResponsibiltyRegWith1Approval(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegAuthorizedDeputyValue = testData.get("AuthorizedDeputy");
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegsubGroupNameValue = testData.get("SubGroupNameValue");
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));
		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		 RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		//RegEmployeeName = "userJCUZ.userJCUZ";
		//RegEmployeeID = "userJCUZ";
		 RegEmployeeID = SSO_UserRegistration.getEmployeeID();
	//	RegDepartName = MDM_Department_Registration.getDepartmentName();

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, initiateMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(initiateMenu, jobResponsbilityMenu,
				CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		waitForElementVisibile(dateofJoining);
		scrollToViewElement(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(AddItemAuthorizedDeputy, JobResponsibilityStrings.Authorized_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(AddItemAuthorizedDeputytext,
				JobResponsibilityStrings.AuthorizedDeputy_DC.getJobResponsibilityStrings(),
				testData.get("AuthorizedDeputy") + testData.get("percentageSign"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.AuthorizedDeputy_SS.getJobResponsibilityStrings());
		click2(AddItemAuthorizedDeputyapply, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(AddItemAuthorizedDeputyadd, AddItemAuthorizedDeputyadd1,
				JobResponsibilityStrings.Authorized_Deputyadd_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_SS.getJobResponsibilityStrings());
		click2(AddItemAuthorizedDeputyadd1, JobResponsibilityStrings.DeputyAuthorized_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.AuthorizedDeputyadd_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.AuthorizedDeputyadd_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
//		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
//				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(uploadFile, DocumentDescription,
//				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
//		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
//				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
//				testData.get("Keyworddescription"),
//				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
////		clickChooseFile(chooseFile, testData.get("DocumentUpload"),
////				DocumentRegistrationStrings.Choosefiledocument_DC.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_AC.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_AR.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_SS.getDocumentRegistrationStrings());
//		scrollToViewElement(chooseFile);
//		TimeUtil.mediumWait();
//		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
////		externalDocumentUpload
//		TimeUtil.mediumWait();
//		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		saveExternalCode(driver, docconfirmationText);
//		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(searchfile, searchbydrop,
//				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
//				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
//				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
//		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
//				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		clickAndWaitforNextElement(adddoc, adddocum,
//				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(adddocum, approverNew,
//				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		// TimeUtil.longwait();
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		// String ConfirmationTextAtEsign =
		// Constants.JOBRESPONSIBILITY_REGISTRATION_CONFIRMATION_TEXT_ESIGN;
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
//		String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
//		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void jobResponsibilityLineOfApproversApproveWithAudiTrails(HashMap<String, String> testData) {

//	RegDesignationValue = SSO_UserRegistration.getDesignationValue();
//	RegEmployeeName = SSO_UserRegistration.getEmployeeName();
//	RegEmployeeID = SSO_UserRegistration.getEmployeeID();
//	RegDepartName = MDM_Deparment.getDepartmentName();
//		RegReportingToValue = "--";
//		RegDesignationValue = "Junior SQA Engineer-L105";
//		RegEmployeeName = "sessionnew15.sessionnew15";
//		RegEmployeeID = "sessionnew15";
//		RegDepartName = "DepartmentEYO";

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(approve, jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameField,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		clickAndWaitforNextElement(displayBtn, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, displayedRecord, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityapproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityapproval_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void jobResponsibilityUserAcceptanceApprove(HashMap<String, String> testData) {

//	RegDesignationValue = SSO_UserRegistration.getDesignationValue();
//	RegEmployeeName = SSO_UserRegistration.getEmployeeName();
//	RegEmployeeID = SSO_UserRegistration.getEmployeeID();
//	RegDepartName = MDM_Deparment.getDepartmentName();
//		RegReportingToValue = "--";
//		RegDesignationValue = "Junior SQA Engineer-L105";
//		RegEmployeeName = "sessionnew15.sessionnew15";
//		RegEmployeeID = "sessionnew15";
//		RegDepartName = "DepartmentEYO";

//	RegAuthorizedDeputyValue = "laxmi4";
//	RegQualificationValue = "Tester";
//	RegPreviousExperienceVal = "3";
//	RegExternalCertificates = "querydocument";
//	RegsubGroupNameValue = "Approvers Subgroup";
//	RegJobResponsibilityValue = "DocumentReading";

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, usergroupsAccept, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(usergroupsAccept);
		clickAndWaitforNextElement(usergroupsAccept, acceptJobresponsiblity,
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		click2(acceptJobresponsiblity,
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveclickemployyee_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveclickemployyee_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("SSONewUserRegPassword"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, displayedRecord, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityacceptapproval_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityacceptapproval_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void jobResponsibility_AuthorizedDeputyAcceptance_ApproveWithAudiTrails(HashMap<String, String> testData) {

		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		RegEmployeeID = SSO_UserRegistration.getEmployeeID();
		// RegDepartName = MDM_Deparment.getDepartmentName();

//		RegReportingToValue = "--";
//		RegDesignationValue = "Junior SQA Engineer-L105";
//		RegEmployeeName = "sessionnew15.sessionnew15";
//		RegEmployeeID = "sessionnew15";
//		RegDepartName = "DepartmentEYO";

//	RegAuthorizedDeputyValue = "laxmi4";
//	RegQualificationValue = "Tester";
//	RegPreviousExperienceVal = "3";
//	RegExternalCertificates = "querydocument";
//	RegsubGroupNameValue = "Approvers Subgroup";
//	RegJobResponsibilityValue = "DocumentReading";
//	ExternalCode = "LEIQ24000150";

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, usergroupsAccept, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(usergroupsAccept);
		clickAndWaitforNextElement(usergroupsAccept, acceptADacceptance,
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		click2(acceptADacceptance,
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		clickAndWaitforNextElement(displayBtn, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresADApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresADApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), testData.get("ADPassword"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, displayedRecord, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityADDapproval_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityADDapproval_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
}
