package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Record_InductionTraining extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement learnIQ_Icon;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Record']/preceding-sibling::a")
	WebElement recordSubMenu;

	@FindBy(id = "TMS_Course Manager_Record_MEN63")
	WebElement inductionTrainingSubmenu;

	@FindBy(xpath = "//button[@class='caliber-button-primary SubmitCls' and @id='btnSubmit']")
	WebElement button_Submit;

	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement textBox_Remarks;

	@FindBy(xpath = "//button[@id='VUserAccStatus_selectBtn']")
	WebElement button_OK;

	@FindBy(id = "cfnMsg_Next")
	WebElement button_Done;

	public void recordInductionTraining(HashMap<String, String> testData) {

		waitForElementVisibile(learnIQ_Icon);
		click2(learnIQ_Icon, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		waitForElementVisibile(courseManagerMenu);
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				"Course Manager menu");
		click2(recordSubMenu, CommonStrings.Record_menu_DC.getCommonStrings(),
				CommonStrings.Record_menu_AC.getCommonStrings(), CommonStrings.Record_menu_AR.getCommonStrings(),
				CommonStrings.Record_menu_SS.getCommonStrings());
		scrollToViewElement(inductionTrainingSubmenu);
		click2(inductionTrainingSubmenu, CommonStrings.RecordInduction_DC.getCommonStrings(),
				CommonStrings.RecordInduction_AC.getCommonStrings(),
				CommonStrings.RecordInduction_AR.getCommonStrings(),
				CommonStrings.RecordInduction_SS.getCommonStrings());
		switchToBodyFrame(driver);
		// Course Selection
		WebElement CourseTable = driver.findElement(By.xpath("//div[@id='ListTab_wrapper']"));
		List<WebElement> CourseRows = CourseTable.findElements(By.xpath(".//tr"));
		try {
			for (WebElement row : CourseRows) {
				List<WebElement> elementObjectAll = row.findElements(By.xpath(".//td"));
				for (WebElement element : elementObjectAll) {
					String course = element.getText();
					if (course.equals(testData.get("courseName"))) {
						click2(element, CommonStrings.CourseSelect_DC.getCommonStrings(),
								CommonStrings.CourseSelect_AC.getCommonStrings(),
								CommonStrings.CourseSelect_AR.getCommonStrings(),
								CommonStrings.CourseSelect_SS.getCommonStrings());

					}
				}
			}
		} catch (Exception e) {
			System.out.println("Execption Ocuured");
		}

		// Employee Selection
		WebElement EmployeeTable = driver.findElement(By.xpath("//table[@id='EmpListTab']/tbody"));
		List<WebElement> EmployeeRows = EmployeeTable.findElements(By.xpath(".//tr"));
		try {
			for (WebElement row : EmployeeRows) {
				List<WebElement> elementObjectAll = row.findElements(By.xpath(".//td"));
				for (WebElement element : elementObjectAll) {
					String employee = element.getText();
					if (employee.equals(SSO_UserRegistration.getEmployeeName())) {
						WebElement employeeNameCheckBox = row.findElement(By.xpath("./td[4]/input"));
						scrollToViewElement(employeeNameCheckBox);
						TimeUtil.mediumWait();
						click2(employeeNameCheckBox, "Click on check box", "Check box should be clicked",
								"Check is clicked", "Employee Check box");

					}
				}
			}
		} catch (Exception e) {
			System.out.println("Execption Ocuured");
		}
		waitForElementVisibile(textBox_Remarks);
		sendKeys2(textBox_Remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("remarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		waitForElementVisibile(button_OK);
		click2(button_OK, CommonStrings.cllick_OK_DC.getCommonStrings(), CommonStrings.cllick_OK_AC.getCommonStrings(),
				CommonStrings.cllick_OK_AR.getCommonStrings(), CommonStrings.cllick_OK_SS.getCommonStrings());
		waitForElementVisibile(button_Submit);
		scrollToViewElement(button_Submit);
		click2(button_Submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		waitForElementVisibile(button_Done);
		click2(button_Done, CommonStrings.Click_Done_DC.getCommonStrings(),
				CommonStrings.Click_Done_AC.getCommonStrings(), CommonStrings.Click_Done_AR.getCommonStrings(),
				CommonStrings.Click_Done_SS.getCommonStrings());
	}

}
