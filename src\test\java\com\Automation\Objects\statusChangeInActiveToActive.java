package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;

public class statusChangeInActiveToActive extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	public statusChangeInActiveToActive() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration")
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Registration With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Group Registration With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course")
	public Object[][] getGroupReturnData() throws Exception {
		Object[][] obj = new Object[topicData2.getRowCount()][1];
		for (int i = 1; i <= topicData2.getRowCount(); i++) {
			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "course")
	public void groupModification(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify Group Modification");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "statuschange");

	@DataProvider(name = "Statuschange")
	public Object[][] getGroupStatusChange() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "Statuschange")
	public void groupStatusChange(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify Group Modification");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChange(testdata);
		sYS_Group_StatusChange.groupStatusChangeWithAuditTrails(testdata);
		Logout.signOutPage();
	}

}
