package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Completion_InductionTraining extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement learnIQ_Icon;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Record']/preceding-sibling::a")
	WebElement recordSubMenu;

	@FindBy(id = "TMS_Course Manager_Record_MEN65")
	WebElement inductionTrainingCompletionSubmenu;

	@FindBy(xpath = "//button[@class='caliber-button-primary SubmitCls' and @id='btnSubmit']")
	WebElement button_Submit;

	@FindBy(id = "cfnMsg_Next")
	WebElement button_Done;

	public void completionInductionTraining(HashMap<String, String> testData) {

		waitForElementVisibile(learnIQ_Icon);
		click2(learnIQ_Icon, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		waitForElementVisibile(courseManagerMenu);
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(recordSubMenu, CommonStrings.Record_menu_DC.getCommonStrings(),
				CommonStrings.Record_menu_AC.getCommonStrings(), CommonStrings.Record_menu_AR.getCommonStrings(),
				CommonStrings.Record_menu_SS.getCommonStrings());
		scrollToViewElement(inductionTrainingCompletionSubmenu);
		click2(inductionTrainingCompletionSubmenu, CommonStrings.Record_menu_DC.getCommonStrings(),
				CommonStrings.Record_menu_AC.getCommonStrings(), CommonStrings.Record_menu_AR.getCommonStrings(),
				CommonStrings.Record_menu_SS.getCommonStrings());

		// click2(inductionTrainingCompletionSubmenu,
		// CommonStrings.CompletionInduction_DC.getCommonStrings(),
//				CommonStrings.CompletionInduction_AC.getCommonStrings(),
//				CommonStrings.CompletionInduction_AR.getCommonStrings(),
//				CommonStrings.CompletionInduction_SS.getCommonStrings());
		switchToBodyFrame(driver);
		WebElement employeeTable = driver.findElement(By.xpath("//div[@id='ListTab_wrapper']/table"));
		List<WebElement> employeeRows = employeeTable.findElements(By.xpath(".//tr"));
		try {
			for (WebElement row : employeeRows) {
				List<WebElement> elementObjectAll = row.findElements(By.xpath(".//td"));
				for (WebElement element : elementObjectAll) {
					String course = element.getText();
					if (course.equals(SSO_UserRegistration.getEmployeeName())) {
						WebElement statusCheckBox = row.findElement(By.xpath(".//input[@class='EmpStatus']"));
						click2(statusCheckBox, "", "", "", "Required Employee Status");
						
					}
				}
			}
		} catch (Exception e) {
			System.out.println("Execption Ocuured");
		}
		waitForElementVisibile(button_Submit);
		scrollToViewElement(button_Submit);
		click2(button_Submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		waitForElementVisibile(button_Done);
		click2(button_Done, CommonStrings.Click_Done_DC.getCommonStrings(),
				CommonStrings.Click_Done_AC.getCommonStrings(), CommonStrings.Click_Done_AR.getCommonStrings(),
				CommonStrings.Click_Done_SS.getCommonStrings());
	}
}
