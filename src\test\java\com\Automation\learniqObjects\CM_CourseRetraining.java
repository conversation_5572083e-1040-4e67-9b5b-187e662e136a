package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Stream;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_CourseRetraining extends OQActionEngine {
	Properties prop;
	public static int InprogressQualifiedUsers;
	public static String[] InsideTBPDataBeforeCourseRetraining;
	public static String[] SkippedData;
	public static String[] AbsentData;
	public static String[] InProgressDataAfterReset;
	public static String[] ToBePlannedCountAfterResetUnscheduled;
	public static String[] To_Be_PlannedData_Before_CourseRetraining_Auditrails;
	public static String[] skipped_Data_Before_CourseRetraining_Auditrails;
	public static String[] absent_Data_Before_CourseRetraining_Auditrails;
	public static String[] AT_ToBePlannedData_After_CourseRetraining;

	public static String[] AT_ToBeRetrainedData_After_CourseRetraining;
	public static String[] AT_ToBERetrainedData_AfterReset_CourseRetraining;
	public static String[] AT_ToBePlannedData_After_CourseRetraining_Scheduledtab;
	public static String[] AT_Skipped_After_CourseRetraining_Scheduledtab;

	public static String[] AT_Absent_After_CourseRetraining_Scheduledtab;

	public static String[] AT_ToBeRetrained_After_CourseRetraining_Scheduledtab;

	public static String[] ToBeRetrainedDataAfterReset;
	public static String[] TotalTraineesAfterReset;
	public static String[] ToBeRetrainedDataBeforeSubgroup;
	public static String[] SkippedDataAfterReset;
	public static String[] AbsentDataAfterReset;
	public static String[] AT_SkippedData_AfterReset_Unsch;
	public static String[] AT_AbsentData_AfterReset_Unsch;
	public static String[] Scheduled_ToBePlanned_After_Reset;
	public static String[] Inside_Skipped_BeforeCourseRetraining;
	public static String[] Inside_Absent_BeforeCourseRetraining;
	public static String[] Inside_ToBe_Retrained_BeforeCourseRetraining;
	public static String[] Scheduled_Skipped_After_Reset;
	public static String[] Scheduled_Absent_After_Reset;

	public static String[] Scheduled_ToBeRetrained_After_Reset;

	@FindBy(id = "TMS_Course Manager_Propose_MEN23")
	WebElement proposeCourseRetraining;
	@FindBy(xpath = "//label[@class='title-heading' and text()='Select Subgroup(s)']/parent::div/child::div//button[@id='btnModal_CourseRetraining_RtrngSgpIds']")
	WebElement addItem;
	@FindBy(xpath = "//input[@id='CourseRetraining_RtrngSgpIds_FindTxt']")
	WebElement subGroupNameFind_New;
	@FindBy(id = "CourseRetraining_RtrngSgpIds_DisplayBtn")
	WebElement subGroupApply_New;
	@FindBy(xpath = "//button[@id='CourseRetraining_RtrngSgpIds_selectBtn']")
	WebElement addNew;
	@FindBy(id = "CourseRetraining_RtrngSgpIds_selectBtn")
	WebElement addAll;

	@FindBy(xpath = "(//button[text()='Add'])[1]")
	WebElement subGroupAdd_New1;
	@FindBy(xpath = "//button[text()='Confirm Subgroup Selection']")
	WebElement confirmSubgroupSelection;

	@FindBy(xpath = "//button[text()='Reset Count']")
	WebElement resetCount;

	@FindBy(xpath = "//textarea[@id='CourseRetraining_Remarks']")
	WebElement courseRetrainingRemarks;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Name')]")
	WebElement searchByCourseNameDropdown;
	@FindBy(xpath = "//input[@id='CourseName']")
	WebElement courseNameLikeRetrain;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//*[@id='ListTabUser']/tbody")
	WebElement courseRetrainingInsideCount;

	@FindBy(xpath = "//*[@id=\"BeSchedTab\"]/tbody/tr/td[1]/a/span")
	WebElement toBePlannedBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id='BeUnSchTab']/tbody/tr/td[1]/a/span")
	WebElement toBePlanned_unsch_BeforeCourseRetraining;

	@FindBy(xpath = "//*[@id=\"BeSchedTab\"]/tbody/tr/td[2]/a/span")
	WebElement SkippedBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id='BeUnSchTab']/tbody/tr/td[2]/a/span")
	WebElement SkippedUnschBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id=\"BeSchedTab\"]/tbody/tr/td[3]/a/span")
	WebElement absentBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id='BeUnSchTab']/tbody/tr/td[3]/a/span")
	WebElement absentUnschBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id=\"BeSchedTab\"]/tbody/tr/td[4]/a/span")
	WebElement ToBeRetrainedBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id='BeUnSchTab']/tbody/tr/td[4]/a/span")
	WebElement ToBeRetrainedUnschBeforeCourseRetraining;

	@FindBy(id = "TransferModal_CloseBtn")
	WebElement closeIcon;

	@FindBy(xpath = "//span[@id='ASHPlanTraineeIds']")
	WebElement ScheduleToBePlannedCount_After_Reset;

	@FindBy(xpath = "//*[@id=\"AfterSchedTab\"]/tbody/tr/td[2]/a/span")
	WebElement Schedule_Skipped_Count_After_Reset;

	@FindBy(xpath = "//*[@id=\"AfterSchedTab\"]/tbody/tr/td[3]/a/span")
	WebElement Schedule_Absent_Count_After_Reset;

	@FindBy(xpath = "//*[@id=\"AfterSchedTab\"]/tbody/tr/td[4]/a/span")
	WebElement Schedule_ToBeRetrained_Count_After_Reset;

	@FindBy(xpath = "//*[@id='AfterSchedTab']/tbody/tr/td[5]/a/span")
	WebElement Schedule_TotalTraineesDataAfterCourseReset;

	@FindBy(id = "AUNPlanTraineeIds")
	WebElement UnscheduleToBePlannedCount;

	@FindBy(xpath = "//*[@id='AfterUnSchTab']/tbody/tr/td[4]/a/span")
	WebElement ToBeRetrainedDataAfterCourseReset;

	@FindBy(xpath = "//*[@id='AfterUnSchTab']/tbody/tr/td[2]/a/span")
	WebElement Skipped_Unsch_DataAfter_Reset;

	@FindBy(xpath = "//*[@id='AfterUnSchTab']/tbody/tr/td[3]/a/span")
	WebElement Absent_Unsch_DataAfter_Reset;

	@FindBy(xpath = "//*[@id=\"AfterUnSchTab\"]/tbody/tr/td[5]/a/span")
	WebElement TotalTraineesDataAfterCourseReset;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']//li//a//span[text()='Course Retraining']")
	WebElement aduitTrailsCourseRetraining;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement courseNameLike;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;

	@FindBy(xpath = "//*[@id='BeSchedTab']/tbody/tr/td[1]/a/span")
	WebElement AtTBPBeforeCourseRetraining;

	@FindBy(xpath = "//*[@id=\"CompareTRN\"]/div[2]/div/div[6]/table/tbody/tr/td[1]/a/span")
	WebElement AtTBPBeforeCourseRetraining_Unsch;

	@FindBy(xpath = "//*[@id='BeSchedTab']/tbody/tr/td[2]/a/span")
	WebElement At_Skipped_before_CourseRetraining;

	@FindBy(xpath = "//*[@id='BeSchedTab']/tbody/tr/td[3]/a/span")
	WebElement At_Absent_before_CourseRetraining;

//	@FindBy(xpath = "//span[normalize-space()='1']")
//	WebElement AT_UnscheduleToBePlannedCountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id='CompareTRN']/div[2]/div/div[10]/table/tbody/tr/td[1]/a/span")
	WebElement AT_UnscheduleToBePlannedCountAfter_CourseRetraining;
	// *[@id="CompareTRN"]/div[2]/div/div[10]/table/tbody/tr/td[1]/a/span
	@FindBy(xpath = "//*[@id='CompareTRN']/div[2]/div/div[10]/table/tbody/tr/td[4]/a/span")
	WebElement AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining;

	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[9]/table[1]/tbody[1]/tr[1]/td[1]/a[1]")
	WebElement AT_ScheduledToBePlannedCountAfter_CourseRetraining;
//	

//	@FindBy(xpath = "(//a[@class='btn caliber-button-tertiary SelUserList'])[1]")
//	WebElement AT_ScheduledToBePlannedCountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[2]/a/span")
	WebElement AT_ScheduledT_SkippedCountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[3]/a/span")
	WebElement AT_ScheduledT_Absent_CountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[4]/a/span")
	WebElement AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id='CompareTRN']/div[2]/div/div[10]/table/tbody/tr/td[2]/a/span")
	WebElement AT_Unschedule_Skipped_CountAfter_CourseRetraining;

	@FindBy(xpath = "//*[@id='CompareTRN']/div[2]/div/div[10]/table/tbody/tr/td[3]/a/span")
	WebElement AT_Unschedule_Absent_CountAfter_CourseRetraining;

	@FindBy(id = "txtESignPassword")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	public CM_CourseRetraining() {
		PageFactory.initElements(driver, this);
	}

	public void course_Retraining_Classroom_Type_Without_Assessment() {

		int count;
		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(courseManagerMenu, courseManagerAuditTrails,
				CommonStrings.CM_Menus_DC.getCommonStrings(), CommonStrings.CM_Menus_AC.getCommonStrings(),
				CommonStrings.CM_Menus_AR.getCommonStrings(), CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(proposeMenu);
		clickAndWaitforNextElement(proposeMenu, proposeCourseRetraining,
				CommonStrings.Propse_Menu_DC.getCommonStrings(), CommonStrings.Propse_Menu_AC.getCommonStrings(),
				CommonStrings.Propse_Menu_AR.getCommonStrings(), CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				"Course Retraining To Be Planned", "Course Session To be Planned");
		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

//		WebElement courseRetrainingInsideCountScheduled = driver
//				.findElement(By.xpath("//*[@id=\"ListTabUser\"]/tbody"));
//		List<WebElement> insideCountrows = courseRetrainingInsideCountScheduled.findElements(By.tagName("tr"));
//
//		// Return the number of rows
//		int ToBePlannedCountInside = insideCountrows.size();
//
//		String[] InsideDataToBePlanned = new String[ToBePlannedCountInside];
//
//		for (int i = 0; i <ToBePlannedCountInside; i++) {
//			WebElement cell = insideCountrows.get(i).findElements(By.tagName("td")).get(1);
//			InsideDataToBePlanned[i] = cell.getText().trim();
//			System.out.println(InsideDataToBePlanned[i]);
//			
//		}

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length, "Course Retraining To Be Planned Outside",
				"Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "CourseSession To be Planned Data After Record Attendance",
				"Course Retraining Screen To be Planned Data");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		TimeUtil.shortWait();

		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.skippedCountOutisde, "Course Retraining Skipped Count",
				"Course Session Skipped");
		waitForElementVisibile(SkippedBeforeCourseRetraining);
		click2(SkippedBeforeCourseRetraining, "", "", "", "");

		InsideData();
		SkippedData = InsideData;

		compareCount(count, SkippedData.length, "Course Retraining Skipped Count Outside",
				"Course Retraining Skipped Count Inside");

		toBePlannedCountData(CM_BatchFormation.SkippedUsers, SkippedData,
				"CourseSession Skipped Data After Record Attendance", "Course Retraining Skipped Data");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(absentBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentOutsideCount, "Course Retraining Absent Count",
				"Course Session Absent Count");
		waitForElementVisibile(absentBeforeCourseRetraining);
		click2(absentBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		AbsentData = InsideData;

		compareCount(count, AbsentData.length, "Course Retraining Absent Count Outside",
				"Course Retraining Absent Count Inside");

		toBePlannedCountData(CM_RecordAttendance.AbsentUsers, AbsentData,
				"CourseSession Absent Data After Record Attendance", "Course Retraining Absent Data");
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		subGroupNameFind_New.clear();
//		TimeUtil.mediumWait();
//		sendKeys2(subGroupNameFind_New, CourseSessionStrings.Find_Subgroup_DC.getCourseSessionStrings(),
//				CM_Course.getSubgroupName(), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseSessionStrings.Find_Subgroup_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		click2(subGroupApply_New, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.mediumWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, OQActionEngine.InProgresUsersActualCount,
				"Course Retaining Screen In Progress Usrs", "Actual In Progress and To be Retrained USers Users");

		toBePlannedCountData(OQActionEngine.InProgresUsers, InProgressDataAfterReset, "Actual InProgress users",
				"In Progress Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, OQActionEngine.InProgresUsersActualCount,
				"To Be planned Count After Reset", "Actual In Progress and Qualified USers Users");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(OQActionEngine.InProgresUsers, ToBePlannedCountAfterResetUnscheduled,
				"Actual InProgress/Qualified users", "To be planned Data After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void course_Retraining_AuditTrials_Yes() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);
		// TimeUtil.shortWait();

		waitForElementVisibile(AtTBPBeforeCourseRetraining);
		convertString_To_Integer(AtTBPBeforeCourseRetraining.getText());
		count = stringToInt;
		AtTBPBeforeCourseRetraining.click();

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"AuditTrails To Planned Count Before Course Retraining in Audit trails",
				"To Planned Count Before Course Retraining screen");

		InsideData();
		To_Be_PlannedData_Before_CourseRetraining_Auditrails = InsideData;

		toBePlannedCountData(To_Be_PlannedData_Before_CourseRetraining_Auditrails, InsideTBPDataBeforeCourseRetraining,
				"To Be Planned Data Before Course Retraining At Audit trails",
				"To Be Planned Data Before Course Retraining at Course Retraining screen");

		compareCount(count, To_Be_PlannedData_Before_CourseRetraining_Auditrails.length,
				"Audit Trails Outside To Planned Count Before Course Retraining in Audit trails",
				"Audit Trails Inside To Planned Count Before Course Retraining in Audit trails");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(At_Skipped_before_CourseRetraining);
		convertString_To_Integer(At_Skipped_before_CourseRetraining.getText());
		count = stringToInt;
		At_Skipped_before_CourseRetraining.click();

		compareCount(count, SkippedData.length, "AuditTrails Skippped Count Before Course Retraining in Audit trails",
				"Skipped Count Before Course Retraining in Course Retraining Registration screen");

		InsideData();
		skipped_Data_Before_CourseRetraining_Auditrails = InsideData;

		toBePlannedCountData(skipped_Data_Before_CourseRetraining_Auditrails, SkippedData,
				"Skipped Data Before Course Retraining At Audit trails",
				"Skipped Data Before Course Retraining at Course Retraining screen");

		compareCount(count, skipped_Data_Before_CourseRetraining_Auditrails.length,
				"Audit Trails Outside Skipped Count Before Course Retraining in Audit trails",
				"Audit Trails Inside Skipped Count Before Course Retraining in Audit trails");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		TimeUtil.shortWait();

		waitForElementVisibile(At_Absent_before_CourseRetraining);
		convertString_To_Integer(At_Absent_before_CourseRetraining.getText());
		count = stringToInt;
		At_Absent_before_CourseRetraining.click();

		compareCount(count, AbsentData.length, "Audit Trails Absent Count Before Course Retraining in Audit trails",
				"Absent Count Before Course Retraining in Course Retraining Registration screen");

		InsideData();
		absent_Data_Before_CourseRetraining_Auditrails = InsideData;

		toBePlannedCountData(absent_Data_Before_CourseRetraining_Auditrails, AbsentData,
				"Absent Data Before Course Retraining At Audit trails",
				"Absent Data Before Course Retraining at Course Retraining screen");

		compareCount(count, absent_Data_Before_CourseRetraining_Auditrails.length,
				"Audit Trails Outside Skipped Count Before Course Retraining in Audit trails",
				"Audit Trails Inside Skipped Count Before Course Retraining in Audit trails");

		waitForElementVisibile(closeIcon);
		closeIcon.click();
		TimeUtil.shortWait();
		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
		InsideData();

		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
		InsideData();

		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();

		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				" To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, InProgresUsers,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "In Progress/ Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void course_Retraining_ScheduledCourse_RE_Type_Without_Assessment() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));

		WebElement applyBtn = wait.until(ExpectedConditions.elementToBeClickable(By.id("displayBtn")));

		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		WebDriverWait wait1 = new WebDriverWait(driver, Duration.ofSeconds(20));

		WebElement element = wait1
				.until(ExpectedConditions.elementToBeClickable(By.xpath("//table[@id='ListTab']/tbody/tr[1]")));

		click2(element, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");

		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();

		Scheduled_ToBePlanned_After_Reset = InsideData;

		String[] array = Stream.concat(Arrays.stream(InsideTBPDataBeforeCourseRetraining),
				Arrays.stream(CM_VerifyCourseSessionScreen.InprogressUsers)).toArray(String[]::new);

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");

		toBePlannedCountData(array, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedUser.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();

		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.mediumWait();
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		waitForElementVisibile(confirmationText);

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		TimeUtil.mediumWait();

		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		TimeUtil.mediumWait();

		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		TimeUtil.mediumWait();

		TimeUtil.mediumWait();

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();

		driver.switchTo().frame(0);
		TimeUtil.longwait();
		TimeUtil.longwait();

//		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
//
////		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(40));
////		WebElement clickableElement = wait.until(ExpectedConditions
////				.elementToBeClickable(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span")));
////		
////		clickableElement.click();
//		
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();

		// Ensure the element is visible and in view
		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		// Get the count as integer
		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;

		// Wait before interacting (you can keep if needed)
		TimeUtil.longwait();
		TimeUtil.longwait();

		// Wait until the element is clickable
		WebDriverWait wait11 = new WebDriverWait(driver, Duration.ofSeconds(30));
		wait11.until(ExpectedConditions.visibilityOf(AT_ScheduledToBePlannedCountAfter_CourseRetraining));
		wait11.until(ExpectedConditions.elementToBeClickable(AT_ScheduledToBePlannedCountAfter_CourseRetraining));

		// Scroll into view again just to ensure visibility
		((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);",
				AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		// Click using JavaScript to avoid intercept exception
		((JavascriptExecutor) driver).executeScript("arguments[0].click();",
				AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		// Post-click wait
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		TimeUtil.longwait();
		TimeUtil.longwait();

		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();

//		WebDriverWait wait1 = new WebDriverWait(driver, Duration.ofSeconds(40));
//		WebElement clickableElement1 = wait1.until(ExpectedConditions.elementToBeClickable(
//				By.xpath("//*[@id='CompareTRN']/div[2]/div/div[10]/table/tbody/tr/td[1]/a/span")));
//		
//		clickableElement1.click();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedUser,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void UnSchduled_course_Retraining_Online_RE_SystemEvaluation_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length, "Course Retraining To Be Planned Outside",
				"Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR,
				InsideTBPDataBeforeCourseRetraining,
				"CourseSession To be Planned Data After Keeping all employees in different States",
				"To be Planned Data in course retraining screen before selecting/confirming subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		TimeUtil.shortWait();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

//		compareCount(tbpUnscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
//				"To Be planned Count in Unscheduled Tab After Reset", "Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR,
				CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR.length
						+ CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, 0, result,
				CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR.length,
				CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of In Progress/Qualified Employees including Retake Pending",
				"To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedDataAfterCourseReset);
		convertString_To_Integer(ToBeRetrainedDataAfterCourseReset.getText());
		int tbRetrinedCountAfterReset = stringToInt;
		click2(ToBeRetrainedDataAfterCourseReset, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		ToBeRetrainedDataAfterReset = InsideData;

		toBePlannedCountData(ToBeRetrainedDataAfterReset, CM_VerifyCourseSessionScreen.ToBeRetrainedData,
				"Actual To BE Retrained Data", "Actual To Be Retrained Data After reset");

		compareCount(ToBeRetrainedDataAfterReset.length, tbRetrinedCountAfterReset,
				"To Be Retrained Count Inside Count After Reset", "To Be Retrained Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

//			click2(TotalTraineesDataAfterCourseReset, "", "", "", "");
//			TimeUtil.mediumWait();
//			InsideData();
//			TotalTraineesAfterReset = InsideData;

		String[] ActualTotalTrainees = Arrays.copyOf(result,
				result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//		        // Append elements from array2
		System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length,
				CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);

//			 toBePlannedCountData(ActualTotalTrainees, TotalTraineesAfterReset,
//						"Actual Total Trainees Data after reset", "Total Trainees Displayed After reset");

		compareCount(ActualTotalTrainees.length, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.mediumWait();
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		waitForElementVisibile(confirmationText);

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

		waitForElementVisibile(AtTBPBeforeCourseRetraining_Unsch);
		convertString_To_Integer(AtTBPBeforeCourseRetraining_Unsch.getText());
		count = stringToInt;
		AtTBPBeforeCourseRetraining_Unsch.click();

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"AuditTrails To Planned Count Before Course Retraining in Audit trails",
				"To Planned Count Before Course Retraining screen");

		InsideData();
		To_Be_PlannedData_Before_CourseRetraining_Auditrails = InsideData;

		toBePlannedCountData(To_Be_PlannedData_Before_CourseRetraining_Auditrails, InsideTBPDataBeforeCourseRetraining,
				"To Be Planned Data Before Course Retraining At Audit trails",
				"To Be Planned Data Before Course Retraining at Course Retraining screen");

		compareCount(count, To_Be_PlannedData_Before_CourseRetraining_Auditrails.length,
				"Audit Trails Outside To Planned Count Before Course Retraining in Audit trails",
				"Audit Trails Inside To Planned Count Before Course Retraining in Audit trails");

		waitForElementVisibile(closeIcon);
		closeIcon.click();
//		
//		
//		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		
//		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
//		InsideData();
//		
//		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
//		
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				" To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To Planned Data After Course Retraining Under Unscheduled Tab",
				"To Be Planned Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, ToBeRetrainedDataAfterReset.length,
				"Audit Trails To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining",
				" To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBERetrainedData_AfterReset_CourseRetraining = InsideData;

		compareCount(count, AT_ToBERetrainedData_AfterReset_CourseRetraining.length,
				"Outisde To Be Retrained Count At Unscheduled Tab after reset in Audit trails",
				"Inside To Be Retrained Count At Unscheduled Tab after reset in Audit trails");

		toBePlannedCountData(AT_ToBERetrainedData_AfterReset_CourseRetraining, ToBeRetrainedDataAfterReset,
				"To Be Retrained Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"To Be Retrained Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void UnSchduled_course_Retraining_IT_With_Assessment_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"To be Planned Count and that is displaying in Course Sesson screen after keping employees in different states");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"To Be Planned Outside at Course Retraining screen before selecting subgroup",
				"Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining,
				"To be Planned Data After Keeping all employees in different States",
				"To be Planned Data in course retraining screen before selecting/confirming subgroup");
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(SkippedUnschBeforeCourseRetraining);
		convertString_To_Integer(SkippedUnschBeforeCourseRetraining.getText());
		count = stringToInt;

		click2(SkippedUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		SkippedData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped Outside Count at Course Retraining screen Before selecting any sub group",
				"Skipped Count at Course Session after keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
				"Skipped Data After Keeping all employees in different States",
				"Skipped Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(absentUnschBeforeCourseRetraining);
		convertString_To_Integer(absentUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(absentUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		AbsentData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"Absent Count at Course Retraining Screen before selecting any subgroup/confirming subgroup",
				"Absent Count at Course Session screen after keeping all employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
				"Absent Data After Keeping all employees in different States",
				"Absent Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedUnschBeforeCourseRetraining);
		convertString_To_Integer(ToBeRetrainedUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(ToBeRetrainedUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		ToBeRetrainedDataBeforeSubgroup = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Retrained Count at Course Retraining Screen before selecting any subgroup/confirming subgroup",
				"To Be Retrained Count at Course Session screen after keeping all employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP, ToBeRetrainedDataBeforeSubgroup,
				"To Be Retrained Data After Keeping all employees in different States",
				"To Be Retrained Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users",
				"In Progress/Qualified users Displayed at Course Retraining screen after selecting subgroup");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		TimeUtil.mediumWait();
		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining, InsideTBPDataBeforeCourseRetraining.length
				+ CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, 0, result,
				InsideTBPDataBeforeCourseRetraining.length,
				CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of In Progress/Qualified Employees including Retake Pending with To Be Planned Data",
				"To be planned Data displayed in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Skipped_Unsch_DataAfter_Reset);
		convertString_To_Integer(Skipped_Unsch_DataAfter_Reset.getText());
		int tbRetrinedCountAfterReset = stringToInt;
		click2(Skipped_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		InsideData();
		SkippedDataAfterReset = InsideData;

		toBePlannedCountData(SkippedDataAfterReset, CM_VerifyCourseSessionScreen.SkippedData,
				"Skipped Data after reset at Course Retraining", "Actual Skipped Data");

		compareCount(SkippedDataAfterReset.length, tbRetrinedCountAfterReset, "Skipped Count Inside Count After Reset",
				"Skipped Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Absent_Unsch_DataAfter_Reset);
		convertString_To_Integer(Absent_Unsch_DataAfter_Reset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(Absent_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		InsideData();

		AbsentDataAfterReset = InsideData;

		toBePlannedCountData(AbsentDataAfterReset, CM_VerifyCourseSessionScreen.AbsentData,
				"Absent Data after reset at Course Retraining", "Actual Absent Data");

		compareCount(AbsentDataAfterReset.length, tbRetrinedCountAfterReset, "Absent Count Inside Count After Reset",
				"Absent Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedDataAfterCourseReset);
		convertString_To_Integer(ToBeRetrainedDataAfterCourseReset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(ToBeRetrainedDataAfterCourseReset, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		InsideData();
		ToBeRetrainedDataAfterReset = InsideData;

		toBePlannedCountData(ToBeRetrainedDataAfterReset, CM_VerifyCourseSessionScreen.ToBeRetrainedData,
				"To BE Retrained Data at Course Retraining after reset", "Actual To Be Retrained Data");

		compareCount(ToBeRetrainedDataAfterReset.length, tbRetrinedCountAfterReset,
				"To Be Retrained Count Inside Count After Reset", "To Be Retrained Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();
		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

//			 String[] ActualTotalTrainees = Arrays.copyOf(result, result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//	            System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length, CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
		int combineAllcolumns = ToBePlannedCountAfterResetUnscheduled.length + ToBeRetrainedDataAfterReset.length
				+ AbsentDataAfterReset.length + SkippedDataAfterReset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.mediumWait();
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		waitForElementVisibile(confirmationText);
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);
//To be Planned Information
		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				"To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To Planned Data After Course Retraining Under Unscheduled Tab after reset in Audit trails",
				"To Be Planned Data in Unscheduled Tab After reset in Couse Retraining screen");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Skipped Information
		waitForElementVisibile(AT_Unschedule_Skipped_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Skipped_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Skipped_CountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, SkippedDataAfterReset.length,
				"Audit Trails Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_SkippedData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_SkippedData_AfterReset_Unsch.length,
				"Outisde Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_SkippedData_AfterReset_Unsch, SkippedDataAfterReset,
				"Skipped Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Skipped Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Absent Information
		waitForElementVisibile(AT_Unschedule_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Absent_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Absent_CountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, AbsentDataAfterReset.length,
				"Audit Trails Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_AbsentData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_AbsentData_AfterReset_Unsch.length,
				"Outisde Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_AbsentData_AfterReset_Unsch, AbsentDataAfterReset,
				"Absent Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Absent Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// To Be Retrained Information
		waitForElementVisibile(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, ToBeRetrainedDataAfterReset.length,
				"Audit Trails To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining",
				" To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");
		InsideData();
		AT_ToBERetrainedData_AfterReset_CourseRetraining = InsideData;
		compareCount(count, AT_ToBERetrainedData_AfterReset_CourseRetraining.length,
				"Outisde To Be Retrained Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside To Be Retrained Count At Unscheduled Tab after reset in Audit trails of Course Retraining");
		toBePlannedCountData(AT_ToBERetrainedData_AfterReset_CourseRetraining, ToBeRetrainedDataAfterReset,
				"To Be Retrained Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"To Be Retrained Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}

//	public void UnSchduled_course_Retraining_Offline_IT_With_Assessment_() {
//
//		int count;
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
//				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
//				CommonStrings.Propse_Menu_SS.getCommonStrings());
//		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
//				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
//				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
//		courseNameLikeRetrain.clear();
//		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
//				CM_Course.getCourse() + "%",
//				// "CRSNewGHOB%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.mediumWait();
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		TimeUtil.mediumWait();
//		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
//				"To be planned count is displayed at course retraining before selecting any subgroup",
//				"To be planned count is displayed on the Course Session screen after assigning employees to different states");
//
//		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//		InsideData();
//		InsideTBPDataBeforeCourseRetraining = InsideData;
//
//		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
//				"To be planned outside is displayed on the course retraining screen before selecting the subgroupa",
//				"To be planned inside on the course retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
//				InsideTBPDataBeforeCourseRetraining,
//				"To be planned data after assigning all employees to different states",
//				"To be planned data is displayed on the course retraining screen before selecting or confirming the subgroup");
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(SkippedUnschBeforeCourseRetraining);
//		convertString_To_Integer(SkippedUnschBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		click2(SkippedUnschBeforeCourseRetraining, "", "", "", "");
//
//		InsideData();
//		SkippedData = InsideData;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
//				"Skipped outside count is displayed on the course retraining screen before selecting any subgroup",
//				"Skipped count is displayed on the Course Session screen after assigning employees to different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
//				"Skipped data is displayed after assigning all employees to different states",
//				"Skipped data is displayed on the course retraining screen before selecting or confirming the subgroup");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(absentUnschBeforeCourseRetraining);
//		convertString_To_Integer(absentUnschBeforeCourseRetraining.getText());
//		count = stringToInt;
//		click2(absentUnschBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//		InsideData();
//		AbsentData = InsideData;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
//				"Absent count is displayed on the course retraining screen before selecting or confirming any subgroup",
//				"Absent count is displayed on the Course Session screen after assigning all employees to different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
//				"Absent data is displayed after assigning all employees to different states",
//				"Absent data is displayed on the course retraining screen before selecting or confirming a subgroup");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(ToBeRetrainedUnschBeforeCourseRetraining);
//		convertString_To_Integer(ToBeRetrainedUnschBeforeCourseRetraining.getText());
//		count = stringToInt;
//		click2(ToBeRetrainedUnschBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//		InsideData();
//		ToBeRetrainedDataBeforeSubgroup = InsideData;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
//				"To be retrained count is displayed on the course retraining screen before selecting or confirming any subgroup",
//				"To be retrained count is displayed on the Course Session screen after assigning all employees to different states");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP, ToBeRetrainedDataBeforeSubgroup,
//				"To be retrained data is displayed after assigning all employees to different states",
//				"To be retrained data is displayed on the course retraining screen before selecting or confirming the subgroup");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(addItem);
//		TimeUtil.mediumWait();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//		InprogressQualifiedUsers = rows.size();
//
//		InProgressDataAfterReset = new String[InprogressQualifiedUsers];
//
//		for (int i = 0; i < InprogressQualifiedUsers; i++) {
//			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
//			InProgressDataAfterReset[i] = cell.getText().trim();
//
//		}
//		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedUser.length,
//				"Course retraining screen displays the count of in-progress and qualified users",
//				"The actual count of in-progress and qualified users");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, InProgressDataAfterReset,
//				"Actual in-progress/qualified users",
//				"In-progress/qualified users are displayed on the course retraining screen after selecting the subgroup");
//
//		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		waitForElementVisibile(UnscheduleToBePlannedCount);
//		convertString_To_Integer(UnscheduleToBePlannedCount.getText());
//
//		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
//
//		click2(UnscheduleToBePlannedCount, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		ToBePlannedCountAfterResetUnscheduled = InsideData;
//
//		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
//				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.QualifiedUser.length);
//		System.arraycopy(CM_VerifyCourseSessionScreen.QualifiedUser, 0, result,
//				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.QualifiedUser.length);
//
//		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
//				"Combination of in-progress/qualified employees, including retake pending, with to be planned data",
//				"To be planned data is displayed in the Unscheduled tab after reset");
//
//		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
//				"To be planned inside count is displayed after reset",
//				"To be planned outside count is displayed after reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Skipped_Unsch_DataAfter_Reset);
//		convertString_To_Integer(Skipped_Unsch_DataAfter_Reset.getText());
//		int tbRetrinedCountAfterReset = stringToInt;
//		click2(Skipped_Unsch_DataAfter_Reset, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		SkippedDataAfterReset = InsideData;
//
//		toBePlannedCountData(SkippedDataAfterReset, CM_VerifyCourseSessionScreen.SkippedData,
//				"Skipped data is displayed after reset on the course retraining screen", "Actual skipped data");
//
//		compareCount(SkippedDataAfterReset.length, tbRetrinedCountAfterReset,
//				"Skipped inside count is displayed after reset", "Skipped outside count is displayed after reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Absent_Unsch_DataAfter_Reset);
//		convertString_To_Integer(Absent_Unsch_DataAfter_Reset.getText());
//		tbRetrinedCountAfterReset = stringToInt;
//		click2(Absent_Unsch_DataAfter_Reset, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		AbsentDataAfterReset = InsideData;
//
//		toBePlannedCountData(AbsentDataAfterReset, CM_VerifyCourseSessionScreen.AbsentData,
//				"Absent data is displayed after reset on the course retraining screen", "Actual absent data");
//
//		compareCount(AbsentDataAfterReset.length, tbRetrinedCountAfterReset,
//				"Absent inside count is displayed after reset", "Absent outside count is displayed after reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(ToBeRetrainedDataAfterCourseReset);
//		convertString_To_Integer(ToBeRetrainedDataAfterCourseReset.getText());
//		tbRetrinedCountAfterReset = stringToInt;
//		click2(ToBeRetrainedDataAfterCourseReset, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		ToBeRetrainedDataAfterReset = InsideData;
//
//		toBePlannedCountData(ToBeRetrainedDataAfterReset, CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP,
//				"To be retrained data is displayed on the course retraining screen after reset",
//				"Actual to be retrained data");
//
//		compareCount(ToBeRetrainedDataAfterReset.length, tbRetrinedCountAfterReset,
//				"To be retrained inside count is displayed after reset",
//				"To be retrained outside count is displayed after reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
//		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
//		int TotalTraineesCountAfterReset = stringToInt;
//
////			 String[] ActualTotalTrainees = Arrays.copyOf(result, result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
////	            System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length, CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//		int combineAllcolumns = ToBePlannedCountAfterResetUnscheduled.length + ToBeRetrainedDataAfterReset.length
//				+ AbsentDataAfterReset.length + SkippedDataAfterReset.length;
//
//		compareCount(combineAllcolumns, TotalTraineesCountAfterReset,
//				"Actual total trainees data is displayed after reset",
//				"Total trainees outside count is displayed after reset");
//
//		scrollToViewElement(courseRetrainingRemarks);
//		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		scrollToViewElement(submit);
//		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		driver.switchTo().frame(0);
//
////To be Planned Information
//		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
//		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
//				"Audit trails for to be planned outside count are displayed in the Unscheduled tab after course retraining",
//				"To be planned outside count is displayed in the Unscheduled tab after course retraining on the course retraining screen");
//
//		InsideData();
//		AT_ToBePlannedData_After_CourseRetraining = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
//				"To be planned outside count is displayed in the Unscheduled tab",
//				"To be planned inside count is displayed in the Unscheduled tab");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
//				"To be planned data is displayed under the Unscheduled tab in the audit trails after course retraining and reset",
//				"To be planned data is displayed in the Unscheduled tab after reset on the course retraining screen");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		// Skipped Information
//		waitForElementVisibile(AT_Unschedule_Skipped_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_Unschedule_Skipped_CountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_Unschedule_Skipped_CountAfter_CourseRetraining.click();
//
//		compareCount(count, SkippedDataAfterReset.length,
//				"Audit trails for skipped outside count are displayed in the Unscheduled tab after course retraining and reset",
//				"Skipped outside count is displayed in the Unscheduled tab after course retraining and reset on the course retraining screen");
//
//		InsideData();
//		AT_SkippedData_AfterReset_Unsch = InsideData;
//
//		compareCount(count, AT_SkippedData_AfterReset_Unsch.length,
//				"Skipped outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
//				"Skipped inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining.");
//
//		toBePlannedCountData(AT_SkippedData_AfterReset_Unsch, SkippedDataAfterReset,
//				"Skipped data is displayed in the audit trails under the Unscheduled tab after course retraining",
//				"Skipped data is displayed in the Unscheduled tab after reset on the course retraining screen");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		// Absent Information
//		waitForElementVisibile(AT_Unschedule_Absent_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_Unschedule_Absent_CountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_Unschedule_Absent_CountAfter_CourseRetraining.click();
//
//		compareCount(count, AbsentDataAfterReset.length,
//				"Audit trails for absent outside count are displayed in the Unscheduled tab after course retraining and reset",
//				"Absent outside count is displayed in the Unscheduled tab after course retraining and reset on the course retraining screen");
//
//		InsideData();
//		AT_AbsentData_AfterReset_Unsch = InsideData;
//
//		compareCount(count, AT_AbsentData_AfterReset_Unsch.length,
//				"Absent outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
//				"Absent inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining");
//		toBePlannedCountData(AT_AbsentData_AfterReset_Unsch, AbsentDataAfterReset,
//				"Absent data is displayed in the audit trails under the Unscheduled tab after course retraining",
//				"Absent data is displayed in the Unscheduled tab after reset on the course retraining screen");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//		// To Be Retrained Information
//		waitForElementVisibile(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining);
//		convertString_To_Integer(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.click();
//		compareCount(count, ToBeRetrainedDataAfterReset.length,
//				"Audit trails for to be retrained outside count are displayed in the Unscheduled tab after course retraining",
//				"To be retrained outside count is displayed in the Unscheduled tab after course retraining on the course retraining screen");
//		InsideData();
//		AT_ToBERetrainedData_AfterReset_CourseRetraining = InsideData;
//		compareCount(count, AT_ToBERetrainedData_AfterReset_CourseRetraining.length,
//				"To be retrained outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
//				"To be retrained inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining");
//
//		toBePlannedCountData(AT_ToBERetrainedData_AfterReset_CourseRetraining, ToBeRetrainedDataAfterReset,
//				"To be retrained data is displayed in the audit trails under the Unscheduled tab after course retraining",
//				"To be retrained data is displayed in the Unscheduled tab after reset on the course retraining screen");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//		scrollToViewElement(auditCompareTRNActionValue);
//		scrollToViewElement(auditClose);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//	}

	public void UnSchduled_course_Retraining_Offline_IT_With_Assessment_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
				"To be planned count is displayed at course retraining before selecting any subgroup",
				"To be planned count is displayed on the Course Session screen after assigning employees to different states");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"To be planned outside is displayed on the course retraining screen before selecting the subgroupa",
				"To be planned inside on the course retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining,
				"To be planned data after assigning all employees to different states",
				"To be planned data is displayed on the course retraining screen before selecting or confirming the subgroup");
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(SkippedUnschBeforeCourseRetraining);
		convertString_To_Integer(SkippedUnschBeforeCourseRetraining.getText());
		count = stringToInt;

		click2(SkippedUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		SkippedData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped outside count is displayed on the course retraining screen before selecting any subgroup",
				"Skipped count is displayed on the Course Session screen after assigning employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
				"Skipped data is displayed after assigning all employees to different states",
				"Skipped data is displayed on the course retraining screen before selecting or confirming the subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(absentUnschBeforeCourseRetraining);
		convertString_To_Integer(absentUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(absentUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		AbsentData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"Absent count is displayed on the course retraining screen before selecting or confirming any subgroup",
				"Absent count is displayed on the Course Session screen after assigning all employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
				"Absent data is displayed after assigning all employees to different states",
				"Absent data is displayed on the course retraining screen before selecting or confirming a subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedUnschBeforeCourseRetraining);
		convertString_To_Integer(ToBeRetrainedUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(ToBeRetrainedUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		ToBeRetrainedDataBeforeSubgroup = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To be retrained count is displayed on the course retraining screen before selecting or confirming any subgroup",
				"To be retrained count is displayed on the Course Session screen after assigning all employees to different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP, ToBeRetrainedDataBeforeSubgroup,
				"To be retrained data is displayed after assigning all employees to different states",
				"To be retrained data is displayed on the course retraining screen before selecting or confirming the subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}
		TimeUtil.longwait();
		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"Course retraining screen displays the count of in-progress and qualified users",
				"The actual count of in-progress and qualified users");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, InProgressDataAfterReset,
				"Actual in-progress/qualified users",
				"In-progress/qualified users are displayed on the course retraining screen after selecting the subgroup");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.QualifiedData.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.QualifiedData, 0, result,
				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.QualifiedData.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of in-progress/qualified employees, including retake pending, with to be planned data",
				"To be planned data is displayed in the Unscheduled tab after reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To be planned inside count is displayed after reset",
				"To be planned outside count is displayed after reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Skipped_Unsch_DataAfter_Reset);
		convertString_To_Integer(Skipped_Unsch_DataAfter_Reset.getText());
		int tbRetrinedCountAfterReset = stringToInt;
		click2(Skipped_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		SkippedDataAfterReset = InsideData;

		toBePlannedCountData(SkippedDataAfterReset, CM_VerifyCourseSessionScreen.SkippedData,
				"Skipped data is displayed after reset on the course retraining screen", "Actual skipped data");

		compareCount(SkippedDataAfterReset.length, tbRetrinedCountAfterReset,
				"Skipped inside count is displayed after reset", "Skipped outside count is displayed after reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Absent_Unsch_DataAfter_Reset);
		convertString_To_Integer(Absent_Unsch_DataAfter_Reset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(Absent_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		AbsentDataAfterReset = InsideData;

		toBePlannedCountData(AbsentDataAfterReset, CM_VerifyCourseSessionScreen.AbsentData,
				"Absent data is displayed after reset on the course retraining screen", "Actual absent data");

		compareCount(AbsentDataAfterReset.length, tbRetrinedCountAfterReset,
				"Absent inside count is displayed after reset", "Absent outside count is displayed after reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedDataAfterCourseReset);
		convertString_To_Integer(ToBeRetrainedDataAfterCourseReset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(ToBeRetrainedDataAfterCourseReset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		ToBeRetrainedDataAfterReset = InsideData;

		toBePlannedCountData(ToBeRetrainedDataAfterReset, CM_VerifyCourseSessionScreen.ToBeRetrainedDataAfterSGP,
				"To be retrained data is displayed on the course retraining screen after reset",
				"Actual to be retrained data");

		compareCount(ToBeRetrainedDataAfterReset.length, tbRetrinedCountAfterReset,
				"To be retrained inside count is displayed after reset",
				"To be retrained outside count is displayed after reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

//			 String[] ActualTotalTrainees = Arrays.copyOf(result, result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//	            System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length, CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
		int combineAllcolumns = ToBePlannedCountAfterResetUnscheduled.length + ToBeRetrainedDataAfterReset.length
				+ AbsentDataAfterReset.length + SkippedDataAfterReset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset,
				"Actual total trainees data is displayed after reset",
				"Total trainees outside count is displayed after reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
//			e.printStackTrace();
		}

		TimeUtil.longwait();

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

//To be Planned Information
		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit trails for to be planned outside count are displayed in the Unscheduled tab after course retraining",
				"To be planned outside count is displayed in the Unscheduled tab after course retraining on the course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"To be planned outside count is displayed in the Unscheduled tab",
				"To be planned inside count is displayed in the Unscheduled tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To be planned data is displayed under the Unscheduled tab in the audit trails after course retraining and reset",
				"To be planned data is displayed in the Unscheduled tab after reset on the course retraining screen");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Skipped Information
		waitForElementVisibile(AT_Unschedule_Skipped_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Skipped_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Skipped_CountAfter_CourseRetraining.click();

		compareCount(count, SkippedDataAfterReset.length,
				"Audit trails for skipped outside count are displayed in the Unscheduled tab after course retraining and reset",
				"Skipped outside count is displayed in the Unscheduled tab after course retraining and reset on the course retraining screen");
		TimeUtil.longwait();
		InsideData();
		AT_SkippedData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_SkippedData_AfterReset_Unsch.length,
				"Skipped outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
				"Skipped inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining.");

		toBePlannedCountData(AT_SkippedData_AfterReset_Unsch, SkippedDataAfterReset,
				"Skipped data is displayed in the audit trails under the Unscheduled tab after course retraining",
				"Skipped data is displayed in the Unscheduled tab after reset on the course retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Absent Information
		waitForElementVisibile(AT_Unschedule_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Absent_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Absent_CountAfter_CourseRetraining.click();

		compareCount(count, AbsentDataAfterReset.length,
				"Audit trails for absent outside count are displayed in the Unscheduled tab after course retraining and reset",
				"Absent outside count is displayed in the Unscheduled tab after course retraining and reset on the course retraining screen");
		TimeUtil.longwait();
		InsideData();
		AT_AbsentData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_AbsentData_AfterReset_Unsch.length,
				"Absent outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
				"Absent inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining");
		toBePlannedCountData(AT_AbsentData_AfterReset_Unsch, AbsentDataAfterReset,
				"Absent data is displayed in the audit trails under the Unscheduled tab after course retraining",
				"Absent data is displayed in the Unscheduled tab after reset on the course retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		// To Be Retrained Information
		waitForElementVisibile(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining);
		convertString_To_Integer(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, ToBeRetrainedDataAfterReset.length,
				"Audit trails for to be retrained outside count are displayed in the Unscheduled tab after course retraining",
				"To be retrained outside count is displayed in the Unscheduled tab after course retraining on the course retraining screen");
		InsideData();
		AT_ToBERetrainedData_AfterReset_CourseRetraining = InsideData;
		compareCount(count, AT_ToBERetrainedData_AfterReset_CourseRetraining.length,
				"To be retrained outside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining",
				"To be retrained inside count is displayed in the Unscheduled tab after reset in the audit trails of course retraining");

		toBePlannedCountData(AT_ToBERetrainedData_AfterReset_CourseRetraining, ToBeRetrainedDataAfterReset,
				"To be retrained data is displayed in the audit trails under the Unscheduled tab after course retraining",
				"To be retrained data is displayed in the Unscheduled tab after reset on the course retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();
		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void UnSchduled_course_Retraining_IT_Offline_WithOut_Assessment_VerbalEvaluation() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"To be Planned Count and that is displaying in Course Sesson screen after keping employees in different states");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"To Be Planned Outside at Course Retraining screen before selecting subgroup",
				"Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining,
				"To be Planned Data After Keeping all employees in different States",
				"To be Planned Data in course retraining screen before selecting/confirming subgroup");
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(SkippedUnschBeforeCourseRetraining);
		convertString_To_Integer(SkippedUnschBeforeCourseRetraining.getText());
		count = stringToInt;

		click2(SkippedUnschBeforeCourseRetraining, "", "", "", "");

		InsideData();
		SkippedData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped Outside Count at Course Retraining screen Before selecting any sub group",
				"Skipped Count at Course Session after keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
				"Skipped Data After Keeping all employees in different States",
				"Skipped Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(absentUnschBeforeCourseRetraining);
		convertString_To_Integer(absentUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(absentUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
		InsideData();
		AbsentData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"Absent Count at Course Retraining Screen before selecting any subgroup/confirming subgroup",
				"Absent Count at Course Session screen after keeping all employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
				"Absent Data After Keeping all employees in different States",
				"Absent Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedUnschBeforeCourseRetraining);
		convertString_To_Integer(ToBeRetrainedUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(ToBeRetrainedUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
		InsideData();
		ToBeRetrainedDataBeforeSubgroup = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Retrained Count at Course Retraining Screen before selecting any subgroup/confirming subgroup",
				"To Be Retrained Count at Course Session screen after keeping all employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP, ToBeRetrainedDataBeforeSubgroup,
				"To Be Retrained Data After Keeping all employees in different States",
				"To Be Retrained Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users",
				"In Progress/Qualified users Displayed at Course Retraining screen after selecting subgroup");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining, InsideTBPDataBeforeCourseRetraining.length
				+ CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, 0, result,
				InsideTBPDataBeforeCourseRetraining.length,
				CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of In Progress/Qualified Employees including Retake Pending with To Be Planned Data",
				"To be planned Data displayed in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Skipped_Unsch_DataAfter_Reset);
		convertString_To_Integer(Skipped_Unsch_DataAfter_Reset.getText());
		int tbRetrinedCountAfterReset = stringToInt;
		click2(Skipped_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		SkippedDataAfterReset = InsideData;

		toBePlannedCountData(SkippedDataAfterReset, CM_VerifyCourseSessionScreen.SkippedData,
				"Skipped Data after reset at Course Retraining", "Actual Skipped Data");

		compareCount(SkippedDataAfterReset.length, tbRetrinedCountAfterReset, "Skipped Count Inside Count After Reset",
				"Skipped Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Absent_Unsch_DataAfter_Reset);
		convertString_To_Integer(Absent_Unsch_DataAfter_Reset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(Absent_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		AbsentDataAfterReset = InsideData;

		toBePlannedCountData(AbsentDataAfterReset, CM_VerifyCourseSessionScreen.AbsentData,
				"Absent Data after reset at Course Retraining", "Actual Absent Data");

		compareCount(AbsentDataAfterReset.length, tbRetrinedCountAfterReset, "Absent Count Inside Count After Reset",
				"Absent Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(ToBeRetrainedDataAfterCourseReset);
		convertString_To_Integer(ToBeRetrainedDataAfterCourseReset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(ToBeRetrainedDataAfterCourseReset, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBeRetrainedDataAfterReset = InsideData;

		toBePlannedCountData(ToBeRetrainedDataAfterReset, CM_VerifyCourseSessionScreen.ToBeRetrainedData,
				"To BE Retrained Data at Course Retraining after reset", "Actual To Be Retrained Data");

		compareCount(ToBeRetrainedDataAfterReset.length, tbRetrinedCountAfterReset,
				"To Be Retrained Count Inside Count After Reset", "To Be Retrained Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

//			 String[] ActualTotalTrainees = Arrays.copyOf(result, result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//	            System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length, CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
		int combineAllcolumns = ToBePlannedCountAfterResetUnscheduled.length + ToBeRetrainedDataAfterReset.length
				+ AbsentDataAfterReset.length + SkippedDataAfterReset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
//			e.printStackTrace();
		}

		TimeUtil.longwait();

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

//To be Planned Information
		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				"To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To Planned Data After Course Retraining Under Unscheduled Tab after reset in Audit trails",
				"To Be Planned Data in Unscheduled Tab After reset in Couse Retraining screen");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Skipped Information
		waitForElementVisibile(AT_Unschedule_Skipped_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Skipped_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Skipped_CountAfter_CourseRetraining.click();

		compareCount(count, SkippedDataAfterReset.length,
				"Audit Trails Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_SkippedData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_SkippedData_AfterReset_Unsch.length,
				"Outisde Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_SkippedData_AfterReset_Unsch, SkippedDataAfterReset,
				"Skipped Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Skipped Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Absent Information
		waitForElementVisibile(AT_Unschedule_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Absent_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Absent_CountAfter_CourseRetraining.click();

		compareCount(count, AbsentDataAfterReset.length,
				"Audit Trails Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_AbsentData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_AbsentData_AfterReset_Unsch.length,
				"Outisde Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_AbsentData_AfterReset_Unsch, AbsentDataAfterReset,
				"Absent Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Absent Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// To Be Retrained Information
		waitForElementVisibile(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBeRetrainedDaaCountAfter_CourseRetraining.click();

		compareCount(count, ToBeRetrainedDataAfterReset.length,
				"Audit Trails To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining",
				" To Be Retrained Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBERetrainedData_AfterReset_CourseRetraining = InsideData;
		compareCount(count, AT_ToBERetrainedData_AfterReset_CourseRetraining.length,
				"Outisde To Be Retrained Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside To Be Retrained Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_ToBERetrainedData_AfterReset_CourseRetraining, ToBeRetrainedDataAfterReset,
				"To Be Retrained Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"To Be Retrained Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void UnSchduled_course_Retraining_RE_Without_Assessment_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"To be Planned Count and that is displaying in Course Sesson screen after keping employees in different states");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"To Be Planned Outside at Course Retraining screen before selecting subgroup",
				"Inside Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection,
				InsideTBPDataBeforeCourseRetraining,
				"To be Planned Data at Course Session After Keeping all employees in different States",
				"To be Planned Data in course retraining screen before selecting/confirming subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users",
				"In Progress/Qualified users Displayed at Course Retraining screen after selecting subgroup");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining, InsideTBPDataBeforeCourseRetraining.length
				+ CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, 0, result,
				InsideTBPDataBeforeCourseRetraining.length,
				CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of In Progress/Qualified Employees and To Be Planned Data",
				"To be planned Data displayed in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

//			 String[] ActualTotalTrainees = Arrays.copyOf(result, result.length + CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);
//	            System.arraycopy(CM_VerifyCourseSessionScreen.ToBeRetrainedData, 0, ActualTotalTrainees, result.length, CM_VerifyCourseSessionScreen.ToBeRetrainedData.length);

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, TotalTraineesCountAfterReset,
				"Actual Total Trainees Data after reset", "Total Trainees Outside Count After Reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				// "CRSNewNNDS%",
				CM_Course.getCourse() + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();
		driver.switchTo().frame(0);

//To be Planned Information

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				"To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To Planned Data After Course Retraining Under Unscheduled Tab after reset in Audit trails",
				"To Be Planned Data in Unscheduled Tab After reset in Couse Retraining screen");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void UnSchduled_course_Retraining_IT_WithOut_Assessment() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();
		convertString_To_Integer(toBePlanned_unsch_BeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"To be Planned Count and that is displaying in Course Sesson screen after keping employees in different states");

		click2(toBePlanned_unsch_BeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"To Be Planned Outside at Course Retraining screen before selecting subgroup",
				"Course Retraining To Be Planned Inside");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining,
				"To be Planned Data After Keeping all employees in different States",
				"To be Planned Data in course retraining screen before selecting/confirming subgroup");
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(SkippedUnschBeforeCourseRetraining);
		convertString_To_Integer(SkippedUnschBeforeCourseRetraining.getText());
		count = stringToInt;

		click2(SkippedUnschBeforeCourseRetraining, "", "", "", "");

		InsideData();
		SkippedData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped Outside Count at Course Retraining screen Before selecting any sub group",
				"Skipped Count at Course Session after keeping employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, SkippedData,
				"Skipped Data After Keeping all employees in different States",
				"Skipped Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(absentUnschBeforeCourseRetraining);
		convertString_To_Integer(absentUnschBeforeCourseRetraining.getText());
		count = stringToInt;
		click2(absentUnschBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		InsideData();
		AbsentData = InsideData;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"Absent Count at Course Retraining Screen before selecting any subgroup/confirming subgroup",
				"Absent Count at Course Session screen after keeping all employees in different states");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, AbsentData,
				"Absent Data After Keeping all employees in different States",
				"Absent Data in course retraining screen before selecting/confirming subgroup");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
		List<WebElement> rows = table.findElements(By.tagName("tr"));
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedUser.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, InProgressDataAfterReset,
				"Actual InProgress/Qualified users",
				"In Progress/Qualified users Displayed at Course Retraining screen after selecting subgroup");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		String[] result = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.QualifiedUser.length);
		System.arraycopy(CM_VerifyCourseSessionScreen.QualifiedUser, 0, result,
				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.QualifiedUser.length);

		toBePlannedCountData(result, ToBePlannedCountAfterResetUnscheduled,
				"Combination of In Progress/Qualified Employees including Retake Pending with To Be Planned Data",
				"To be planned Data displayed in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Skipped_Unsch_DataAfter_Reset);
		convertString_To_Integer(Skipped_Unsch_DataAfter_Reset.getText());
		int tbRetrinedCountAfterReset = stringToInt;
		click2(Skipped_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		SkippedDataAfterReset = InsideData;

		toBePlannedCountData(SkippedDataAfterReset, CM_VerifyCourseSessionScreen.SkippedData,
				"Skipped Data after reset at Course Retraining", "Actual Skipped Data");

		compareCount(SkippedDataAfterReset.length, tbRetrinedCountAfterReset, "Skipped Count Inside Count After Reset",
				"Skipped Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Absent_Unsch_DataAfter_Reset);
		convertString_To_Integer(Absent_Unsch_DataAfter_Reset.getText());
		tbRetrinedCountAfterReset = stringToInt;
		click2(Absent_Unsch_DataAfter_Reset, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		AbsentDataAfterReset = InsideData;

		toBePlannedCountData(AbsentDataAfterReset, CM_VerifyCourseSessionScreen.AbsentData,
				"Absent Data after reset at Course Retraining", "Actual Absent Data");

		compareCount(AbsentDataAfterReset.length, tbRetrinedCountAfterReset, "Absent Count Inside Count After Reset",
				"Absent Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

		int combineAllcolumns = ToBePlannedCountAfterResetUnscheduled.length + AbsentDataAfterReset.length
				+ SkippedDataAfterReset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);
//To be Planned Information
		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		click2(AT_UnscheduleToBePlannedCountAfter_CourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		compareCount(count, ToBePlannedCountAfterResetUnscheduled.length,
				"Audit Trails To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining",
				"To Be Planned Out Side Count count at Unscheduled tab  After Course Retraining in course retraining screen");

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, ToBePlannedCountAfterResetUnscheduled,
				"To Planned Data After Course Retraining Under Unscheduled Tab after reset in Audit trails",
				"To Be Planned Data in Unscheduled Tab After reset in Couse Retraining screen");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Skipped Information
		waitForElementVisibile(AT_Unschedule_Skipped_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Skipped_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Skipped_CountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, SkippedDataAfterReset.length,
				"Audit Trails Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Skipped Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_SkippedData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_SkippedData_AfterReset_Unsch.length,
				"Outisde Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Skipped Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_SkippedData_AfterReset_Unsch, SkippedDataAfterReset,
				"Skipped Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Skipped Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		// Absent Information
		waitForElementVisibile(AT_Unschedule_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_Unschedule_Absent_CountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_Unschedule_Absent_CountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		compareCount(count, AbsentDataAfterReset.length,
				"Audit Trails Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset",
				"Absent Out Side Count count at Unscheduled tab  After Course Retraining and after reset in course retraining screen");

		InsideData();
		AT_AbsentData_AfterReset_Unsch = InsideData;

		compareCount(count, AT_AbsentData_AfterReset_Unsch.length,
				"Outisde Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining",
				"Inside Absent Count At Unscheduled Tab after reset in Audit trails of Course Retraining");

		toBePlannedCountData(AT_AbsentData_AfterReset_Unsch, AbsentDataAfterReset,
				"Absent Data at Audit trails After Course Retraining Under Unscheduled Tab",
				"Absent Data in Unscheduled Tab After reset in Couse Retraining screen");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

//	public void Scheduled_course_Retraining_IT_Online_Offline_WithOut_Assessment() {
//
//		int count;
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
//				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
//				CommonStrings.Propse_Menu_SS.getCommonStrings());
//		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
//				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
//				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
//		courseNameLikeRetrain.clear();
//		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
//				CM_Course.getCourse() + "%",
//				// "CRSNewGHOB%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		TimeUtil.mediumWait();
//		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
//				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");
//
//		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		InsideTBPDataBeforeCourseRetraining = InsideData;
//
//		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
//				"Outside To Be planned at Course Retraining screen",
//				"Inside To Be planned at Course Retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
//				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
//				"To be Planned Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
//				"Skipped Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Skipped Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(SkippedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		Inside_Skipped_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
//				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
//				"Skipped Data after keeping employee in different states",
//				"Skipped Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(absentBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
//				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(absentBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		Inside_Absent_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
//				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
//				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
//				"Absent Data after keeping employee in different states",
//				"Absent Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		//
//
//		TimeUtil.shortWait();
//		scrollToViewElement(addItem);
//		TimeUtil.mediumWait();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
//
//		// Find all the rows (<tr>) inside the table
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//
//		// Return the number of rows
//		InprogressQualifiedUsers = rows.size();
//
//		InProgressDataAfterReset = new String[InprogressQualifiedUsers];
//
//		for (int i = 0; i < InprogressQualifiedUsers; i++) {
//			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
//			InProgressDataAfterReset[i] = cell.getText().trim();
//
//			// System.out.println(InsideData[i]);
//
//		}
//
//		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedUser.length,
//				"Course Retaining Screen In Progress and Qualified Users count",
//				"Actual In Progress and Qualified Users count");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, InProgressDataAfterReset,
//				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");
//
//		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
//		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
//		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
//		InsideData();
//
//		Scheduled_ToBePlanned_After_Reset = InsideData;
//
////		String[] array = Stream.concat(Arrays.stream(InsideTBPDataBeforeCourseRetraining),
////				Arrays.stream(CM_VerifyCourseSessionScreen.InprogressUsers)).toArray(String[]::new);
//
//		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
//				"OutSide To Be Planned Count in Schedule Tab after reset button",
//				"Inside To Be Planned Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
//				"Comination of In Progess and To Be planned Count before reset button",
//				"To be planned Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
//		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
//		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
//		InsideData();
//
//		Scheduled_Skipped_After_Reset = InsideData;
//
//		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
//				"OutSide Skipped Count in Schedule Tab after reset button",
//				"Inside Skipped Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
//				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
//		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
//		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
//		InsideData();
//
//		Scheduled_Absent_After_Reset = InsideData;
//
//		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
//				"OutSide Absent Count in Schedule Tab after reset button",
//				"Inside Absent Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
//				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(UnscheduleToBePlannedCount);
//		convertString_To_Integer(UnscheduleToBePlannedCount.getText());
//
//		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
//
//		compareCount(tbpUnscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedUser.length,
//				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");
//
//		click2(UnscheduleToBePlannedCount, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		ToBePlannedCountAfterResetUnscheduled = InsideData;
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, ToBePlannedCountAfterResetUnscheduled,
//				"Qualified users", "To be planned Data in Unscheduled tab After reset");
//
//		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
//				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(courseRetrainingRemarks);
//		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		scrollToViewElement(submit);
//		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		driver.switchTo().frame(0);
//
//		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
//		InsideData();
//
//		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
//				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
//				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
//		InsideData();
//
//		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
//				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		TimeUtil.mediumWait();
//		AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
//		TimeUtil.mediumWait();
//		InsideData();
//
//		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
//				"Absent Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Absent Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
//				"Absent Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Absent Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
//
//		InsideData();
//		AT_ToBePlannedData_After_CourseRetraining = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
//				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedUser,
//				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(auditCompareTRNActionValue);
//		scrollToViewElement(auditClose);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//	}

	public void Scheduled_course_Retraining_IT_Online_Offline_WithOut_Assessment() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_Count_AftrRespondDR_AfterSubgroup_selection,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");
		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Skipped Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(SkippedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		Inside_Skipped_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
				"Skipped Data after keeping employee in different states",
				"Skipped Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(absentBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(absentBeforeCourseRetraining, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		Inside_Absent_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
				"Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		//

		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.longwait();
//This X path IN fr build
		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedUser.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		Scheduled_ToBePlanned_After_Reset = InsideData;

//		String[] array = Stream.concat(Arrays.stream(InsideTBPDataBeforeCourseRetraining),
//				Arrays.stream(CM_VerifyCourseSessionScreen.InprogressUsers)).toArray(String[]::new);

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");

		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();

		Scheduled_Skipped_After_Reset = InsideData;

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
				"OutSide Skipped Count in Schedule Tab after reset button",
				"Inside Skipped Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();

		Scheduled_Absent_After_Reset = InsideData;

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		tbpUnscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpUnscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedUser.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedUser, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpUnscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.longwait();
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.longwait();

		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.longwait();
		driver.switchTo().frame(0);

		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();

		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		TimeUtil.mediumWait();
		AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.mediumWait();
		InsideData();

		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
				"Absent Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Absent Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
				"Absent Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Absent Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedUser,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	/*
	 * public void
	 * Scheduled_course_Retraining_IT_Offline_WithOut_Assessment_Verbal_Evaluation()
	 * {
	 * 
	 * int count; click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_SS.getCommonStrings()); click2(courseManagerMenu,
	 * CommonStrings.CM_Menus_DC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AR.getCommonStrings(),
	 * CommonStrings.CM_Menus_SS.getCommonStrings()); click2(proposeMenu,
	 * CommonStrings.Propse_Menu_DC.getCommonStrings(),
	 * CommonStrings.Propse_Menu_AC.getCommonStrings(),
	 * CommonStrings.Propse_Menu_AR.getCommonStrings(),
	 * CommonStrings.Propse_Menu_SS.getCommonStrings());
	 * click2(proposeCourseRetraining,
	 * CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
	 * switchToBodyFrame(driver); click2(searchByNew,
	 * CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
	 * clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
	 * CourseStrings.Select_CoursName_DC.getCourseStrings(),
	 * CommonStrings.dropdown_AC.getCommonStrings(),
	 * CommonStrings.dropdown_AR.getCommonStrings(),
	 * CourseStrings.Select_CoursName_SS.getCourseStrings());
	 * courseNameLikeRetrain.clear(); sendKeys2(courseNameLikeRetrain,
	 * CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() +
	 * "%", // "CRSNewGHOB%", CommonStrings.sendKeys_AC.getCommonStrings(),
	 * CommonStrings.sendKeys_AR.getCommonStrings(),
	 * CourseStrings.Like_CourseName_SS.getCourseStrings());
	 * clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
	 * CommonStrings.ApplyButton_DC.getCommonStrings(),
	 * CommonStrings.ApplyButton_AC.getCommonStrings(),
	 * CommonStrings.ApplyButton_AR.getCommonStrings(),
	 * CommonStrings.ApplyButton_SS.getCommonStrings()); click2(displayedRecord,
	 * CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
	 * TimeUtil.mediumWait();
	 * convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText()); count
	 * = stringToInt;
	 * 
	 * compareCount(count,
	 * CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
	 * "To Be Planned Count Displayed at Course Retraining before selecting any subgroup"
	 * ,
	 * "Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading"
	 * );
	 * 
	 * click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
	 * TimeUtil.shortWait();
	 * 
	 * InsideData(); InsideTBPDataBeforeCourseRetraining = InsideData;
	 * 
	 * compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
	 * "Outside To Be planned at Course Retraining screen",
	 * "Inside To Be planned at Course Retraining screen");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.
	 * AfterRecordAttendanceToBePlannedData, InsideTBPDataBeforeCourseRetraining,
	 * "To be Planned Data after keeping employee in different states",
	 * "To be Planned Data in course retraining screen before confirming/selection subgroup"
	 * );
	 * 
	 * TimeUtil.shortWait(); waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * convertString_To_Integer(SkippedBeforeCourseRetraining.getText()); count =
	 * stringToInt;
	 * 
	 * compareCount(count,
	 * CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
	 * "To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup"
	 * ,
	 * "Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state"
	 * );
	 * 
	 * click2(SkippedBeforeCourseRetraining, "", "", "", ""); TimeUtil.shortWait();
	 * 
	 * InsideData(); Inside_Skipped_BeforeCourseRetraining = InsideData;
	 * 
	 * compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
	 * "Outside Skipped Data at Course Retraining screen",
	 * "Inside Skipped count at Course Retraining screen");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData,
	 * Inside_Skipped_BeforeCourseRetraining,
	 * "Skipped Data after keeping employee in different states",
	 * "Skipped Data in course retraining screen before confirming/selection subgroup"
	 * );
	 * 
	 * TimeUtil.shortWait(); waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * convertString_To_Integer(absentBeforeCourseRetraining.getText()); count =
	 * stringToInt;
	 * 
	 * compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
	 * "To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup"
	 * ,
	 * "Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state"
	 * );
	 * 
	 * click2(absentBeforeCourseRetraining, "", "", "", ""); TimeUtil.shortWait();
	 * 
	 * InsideData(); Inside_Absent_BeforeCourseRetraining = InsideData;
	 * 
	 * compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
	 * "Outside Absent count at Course Retraining screen before confirming/selecting subgroup"
	 * ,
	 * "Inside Absent count at Course Retraining screen before confirming/selecting subgroup"
	 * );
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData,
	 * Inside_Absent_BeforeCourseRetraining,
	 * "Absent Data after keeping employee in different states",
	 * "Absent Data in course retraining screen before confirming/selection subgroup"
	 * );
	 * 
	 * TimeUtil.shortWait(); waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
	 * count = stringToInt;
	 * 
	 * compareCount(count,
	 * CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
	 * "To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup"
	 * ,
	 * "Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state"
	 * );
	 * 
	 * click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
	 * TimeUtil.shortWait();
	 * 
	 * InsideData(); Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;
	 * 
	 * compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
	 * "Outside Absent count at Course Retraining screen before confirming/selecting subgroup"
	 * ,
	 * "Inside Absent count at Course Retraining screen before confirming/selecting subgroup"
	 * );
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
	 * Inside_ToBe_Retrained_BeforeCourseRetraining,
	 * "Absent Data after keeping employee in different states",
	 * "Absent Data in course retraining screen before confirming/selection subgroup"
	 * );
	 * 
	 * TimeUtil.shortWait(); waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * //
	 * 
	 * TimeUtil.shortWait(); scrollToViewElement(addItem); TimeUtil.mediumWait();
	 * click2(addItem,
	 * CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
	 * CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
	 * click2(subGroupAdd_New1,
	 * CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
	 * CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
	 * TimeUtil.mediumWait(); click2(addNew,
	 * CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
	 * TimeUtil.shortWait(); click2(confirmSubgroupSelection,
	 * CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
	 * TimeUtil.shortWait();
	 * 
	 * WebElement table = driver.findElement(By.
	 * xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
	 * 
	 * // Find all the rows (<tr>) inside the table List<WebElement> rows =
	 * table.findElements(By.tagName("tr"));
	 * 
	 * // Return the number of rows InprogressQualifiedUsers = rows.size();
	 * 
	 * InProgressDataAfterReset = new String[InprogressQualifiedUsers];
	 * 
	 * for (int i = 0; i < InprogressQualifiedUsers; i++) { WebElement cell =
	 * rows.get(i).findElements(By.tagName("td")).get(1);
	 * InProgressDataAfterReset[i] = cell.getText().trim();
	 * 
	 * // System.out.println(InsideData[i]);
	 * 
	 * }
	 * 
	 * compareCount(InprogressQualifiedUsers,
	 * CM_VerifyCourseSessionScreen.QualifiedData.length,
	 * "Course Retaining Screen In Progress and Qualified Users count",
	 * "Actual In Progress and Qualified Users count");
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers,
	 * InProgressDataAfterReset, "Actual InProgress/Qualified users",
	 * "In Progress/Qualified users Displayed after reset");
	 * 
	 * click2(resetCount,
	 * CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
	 * TimeUtil.shortWait();
	 * 
	 * waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
	 * convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText()); int
	 * tbpscheduledAfterCourseRetrainingCount = stringToInt;
	 * click2(ScheduleToBePlannedCount_After_Reset, "", "", "", ""); InsideData();
	 * 
	 * Scheduled_ToBePlanned_After_Reset = InsideData;
	 * 
	 * compareCount(tbpscheduledAfterCourseRetrainingCount,
	 * Scheduled_ToBePlanned_After_Reset.length,
	 * "OutSide To Be Planned Count in Schedule Tab after reset button",
	 * "Inside To Be Planned Count in Schedule Tab after reset button");
	 * 
	 * toBePlannedCountData(InsideTBPDataBeforeCourseRetraining,
	 * Scheduled_ToBePlanned_After_Reset,
	 * "Comination of In Progess and To Be planned Count before reset button",
	 * "To be planned Data in Scheduled tab After reset");
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
	 * convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
	 * tbpscheduledAfterCourseRetrainingCount = stringToInt;
	 * click2(Schedule_Skipped_Count_After_Reset, "", "", "", ""); InsideData();
	 * 
	 * Scheduled_Skipped_After_Reset = InsideData;
	 * 
	 * compareCount(tbpscheduledAfterCourseRetrainingCount,
	 * Scheduled_Skipped_After_Reset.length,
	 * "OutSide Skipped Count in Schedule Tab after reset button",
	 * "Inside Skipped Count in Schedule Tab after reset button");
	 * 
	 * toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining,
	 * Scheduled_Skipped_After_Reset,
	 * "Skipped Data in Scheduled tab before subgroup selection",
	 * "Skipped Data in Scheduled tab After reset");
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(Schedule_Absent_Count_After_Reset);
	 * convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
	 * tbpscheduledAfterCourseRetrainingCount = stringToInt;
	 * click2(Schedule_Absent_Count_After_Reset, "", "", "", ""); InsideData();
	 * 
	 * Scheduled_Absent_After_Reset = InsideData;
	 * 
	 * compareCount(tbpscheduledAfterCourseRetrainingCount,
	 * Scheduled_Absent_After_Reset.length,
	 * "OutSide Absent Count in Schedule Tab after reset button",
	 * "Inside Absent Count in Schedule Tab after reset button");
	 * 
	 * toBePlannedCountData(Inside_Absent_BeforeCourseRetraining,
	 * Scheduled_Absent_After_Reset,
	 * "Absent Data in Scheduled tab before subgroup selection",
	 * "Absent Data in Scheduled tab After reset");
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
	 * convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
	 * tbpscheduledAfterCourseRetrainingCount = stringToInt;
	 * click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
	 * InsideData();
	 * 
	 * Scheduled_ToBeRetrained_After_Reset = InsideData;
	 * 
	 * compareCount(tbpscheduledAfterCourseRetrainingCount,
	 * Scheduled_ToBeRetrained_After_Reset.length,
	 * "OutSide Absent Count in Schedule Tab after reset button",
	 * "Inside Absent Count in Schedule Tab after reset button");
	 * 
	 * toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining,
	 * Scheduled_ToBeRetrained_After_Reset,
	 * "Absent Data in Scheduled tab before subgroup selection",
	 * "Absent Data in Scheduled tab After reset");
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(TotalTraineesDataAfterCourseReset);
	 * convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText()); int
	 * TotalTraineesCountAfterReset = stringToInt;
	 * 
	 * int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length +
	 * Scheduled_Skipped_After_Reset.length + Scheduled_Absent_After_Reset.length +
	 * Scheduled_ToBeRetrained_After_Reset.length;
	 * 
	 * compareCount(combineAllcolumns, TotalTraineesCountAfterReset,
	 * "Actual Total Trainees Data after reset",
	 * "Total Trainees Outside Count After Reset");
	 * 
	 * waitForElementVisibile(UnscheduleToBePlannedCount);
	 * convertString_To_Integer(UnscheduleToBePlannedCount.getText());
	 * 
	 * tbpscheduledAfterCourseRetrainingCount = stringToInt;
	 * 
	 * compareCount(tbpscheduledAfterCourseRetrainingCount,
	 * CM_VerifyCourseSessionScreen.QualifiedData.length,
	 * "To Be planned Count in Unscheduled Tab After Reset",
	 * "Actual Qualified Users Count");
	 * 
	 * click2(UnscheduleToBePlannedCount, "", "", "", ""); TimeUtil.mediumWait();
	 * InsideData(); ToBePlannedCountAfterResetUnscheduled = InsideData;
	 * 
	 * toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData,
	 * ToBePlannedCountAfterResetUnscheduled, "Qualified users",
	 * "To be planned Data in Unscheduled tab After reset");
	 * 
	 * compareCount(ToBePlannedCountAfterResetUnscheduled.length,
	 * tbpscheduledAfterCourseRetrainingCount,
	 * "To Be planned Inside Count After Reset",
	 * "To Be planned Outside Count After Reset");
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * scrollToViewElement(courseRetrainingRemarks);
	 * sendKeys2(courseRetrainingRemarks,
	 * CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
	 * CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
	 * CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
	 * TimeUtil.shortWait(); scrollToViewElement(submit); click2(submit,
	 * CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
	 * CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
	 * CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
	 * CommonStrings.Close_AuditTrails_SS.getCommonStrings());
	 * 
	 * 
	 * try { if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
	 * sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
	 * ConfigsReader.getPropValue("EpicUserPWD"),
	 * CommonStrings.Password_AC.getCommonStrings(),
	 * CommonStrings.Password_AR.getCommonStrings(),
	 * CommonStrings.Password_SS.getCommonStrings()); TimeUtil.shortWait();
	 * click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
	 * CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
	 * CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
	 * CommonStrings.Esign_Proceed_SS.getCommonStrings()); TimeUtil.shortWait(); } }
	 * catch (Exception e) { e.printStackTrace(); } TimeUtil.longwait();
	 * switchToDefaultContent(driver);
	 * 
	 * click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
	 * CommonStrings.LearnIQ_ICON_SS.getCommonStrings()); click2(courseManagerMenu,
	 * CommonStrings.CM_Menus_DC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AC.getCommonStrings(),
	 * CommonStrings.CM_Menus_AR.getCommonStrings(),
	 * CommonStrings.CM_Menus_SS.getCommonStrings());
	 * click2(courseManagerAuditTrails,
	 * CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
	 * CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
	 * click2(aduitTrailsCourseRetraining,
	 * CourseStrings.CourseAudittrails_DC.getCourseStrings(),
	 * CourseStrings.CourseAudittrails_AC.getCourseStrings(),
	 * CourseStrings.CourseAudittrails_AR.getCourseStrings(),
	 * CourseStrings.CourseAudittrails_SS.getCourseStrings());
	 * switchToBodyFrame(driver); click2(searchByNew,
	 * CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
	 * CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
	 * click2(searchByCourseNameDropdown,
	 * CourseStrings.Select_CoursName_DC.getCourseStrings(),
	 * CommonStrings.dropdown_AC.getCommonStrings(),
	 * CommonStrings.dropdown_AR.getCommonStrings(),
	 * CourseStrings.Select_CoursName_SS.getCourseStrings());
	 * sendKeys2(courseNameLike,
	 * CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() +
	 * "%", CommonStrings.sendKeys_AC.getCommonStrings(),
	 * CommonStrings.sendKeys_AR.getCommonStrings(),
	 * CourseStrings.Like_CourseName_SS.getCourseStrings());
	 * clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
	 * CommonStrings.ApplyButton_DC.getCommonStrings(),
	 * CommonStrings.ApplyButton_AC.getCommonStrings(),
	 * CommonStrings.ApplyButton_AR.getCommonStrings(),
	 * CommonStrings.ApplyButton_SS.getCommonStrings()); click2(displayedRecord,
	 * CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
	 * CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
	 * driver.switchTo().frame(0);
	 * 
	 * waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
	 * scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
	 * 
	 * convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.
	 * getText().trim()); count = stringToInt;
	 * AT_ScheduledToBePlannedCountAfter_CourseRetraining.click(); InsideData();
	 * 
	 * AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
	 * 
	 * compareCount(count,
	 * AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
	 * "To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining"
	 * ,
	 * "To Be Planned InSide Count count at Sccheduled tab  After Course Retraining"
	 * );
	 * 
	 * toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab,
	 * Scheduled_ToBePlanned_After_Reset,
	 * "To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen"
	 * ,
	 * "To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button"
	 * );
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
	 * scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
	 * 
	 * convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.
	 * getText().trim()); count = stringToInt;
	 * AT_ScheduledT_SkippedCountAfter_CourseRetraining.click(); InsideData();
	 * 
	 * AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;
	 * 
	 * compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
	 * "Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * ,
	 * "Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * );
	 * 
	 * toBePlannedCountData(Scheduled_Skipped_After_Reset,
	 * AT_Skipped_After_CourseRetraining_Scheduledtab,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen"
	 * ,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button"
	 * );
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
	 * scrollToViewElement(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
	 * 
	 * convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.
	 * getText().trim()); count = stringToInt;
	 * AT_ScheduledT_Absent_CountAfter_CourseRetraining.click(); InsideData();
	 * 
	 * AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;
	 * 
	 * compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
	 * "Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * ,
	 * "Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * );
	 * 
	 * toBePlannedCountData(Scheduled_Absent_After_Reset,
	 * AT_Absent_After_CourseRetraining_Scheduledtab,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen"
	 * ,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button"
	 * );
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(
	 * AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
	 * scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
	 * 
	 * convertString_To_Integer(
	 * AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
	 * count = stringToInt;
	 * AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
	 * InsideData();
	 * 
	 * AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;
	 * 
	 * compareCount(count,
	 * AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
	 * "Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * ,
	 * "Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails"
	 * );
	 * 
	 * toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset,
	 * AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen"
	 * ,
	 * "Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button"
	 * );
	 * 
	 * waitForElementVisibile(closeIcon); closeIcon.click();
	 * 
	 * waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
	 * scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
	 * 
	 * convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.
	 * getText()); count = stringToInt;
	 * AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
	 * 
	 * InsideData(); AT_ToBePlannedData_After_CourseRetraining = InsideData;
	 * 
	 * compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
	 * "Outisde To Be Planned Count At Unscheduled Tab",
	 * "Inside To Be Planned Count At Unscheduled Tab");
	 * 
	 * toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining,
	 * CM_VerifyCourseSessionScreen.QualifiedData,
	 * "To Planned Data After Course Retraining Under Unscheduled Tab",
	 * "Actual Qualified Users"); waitForElementVisibile(closeIcon);
	 * closeIcon.click();
	 * 
	 * scrollToViewElement(auditCompareTRNActionValue);
	 * scrollToViewElement(auditClose); click2(auditClose,
	 * CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
	 * CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
	 * CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
	 * CommonStrings.Close_AuditTrails_SS.getCommonStrings());
	 * switchToDefaultContent(driver);
	 * 
	 * }
	 */

	public void Scheduled_course_Retraining_IT_Offline_WithOut_Assessment_Verbal_Evaluation() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");

		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.SkippedData.length,
				"Skipped Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual Skipped Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(SkippedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_Skipped_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
				"Skipped Data after keeping employee in different states",
				"Skipped Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(absentBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(absentBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_Absent_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
				"Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
				Inside_ToBe_Retrained_BeforeCourseRetraining, "Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		//

		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_ToBePlanned_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");

		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_Skipped_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
				"OutSide Skipped Count in Schedule Tab after reset button",
				"Inside Skipped Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_Absent_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_ToBeRetrained_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(Schedule_TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_Skipped_After_Reset.length
				+ Scheduled_Absent_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		tbpscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.longwait();

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
		InsideData();

		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
		InsideData();

		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
		InsideData();

		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void Scheduled_course_Retraining_IT_Offline_With_Assessment_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");

		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(SkippedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_Skipped_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
				"Skipped Data after keeping employee in different states",
				"Skipped Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(absentBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(absentBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_Absent_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
				"Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
				Inside_ToBe_Retrained_BeforeCourseRetraining, "Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		//

		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_ToBePlanned_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");

		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_Skipped_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
				"OutSide Skipped Count in Schedule Tab after reset button",
				"Inside Skipped Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_Absent_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
		InsideData();

		Scheduled_ToBeRetrained_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_Skipped_After_Reset.length
				+ Scheduled_Absent_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;

		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
				"Total Trainees Outside Count After Reset");

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		tbpscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;

		// AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		WebElement element = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span"));

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].click();", element);

		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();

		WebElement element1 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[2]/a/span"));

		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].click();", element1);

		InsideData();

		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_Absent_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();

		WebElement element11 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[3]/a/span"));

		JavascriptExecutor js11 = (JavascriptExecutor) driver;
		js11.executeScript("arguments[0].click();", element11);

		InsideData();

		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
		InsideData();

		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();

		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}
//
//	public void Scheduled_course_Retraining_IT_Online_With_Assessment_() {
//
//		int count;
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
//				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
//				CommonStrings.Propse_Menu_SS.getCommonStrings());
//		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
//				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
//				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
//		courseNameLikeRetrain.clear();
//		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
//				CM_Course.getCourse() + "%",
//				// "CRSNewGHOB%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		TimeUtil.mediumWait();
//		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
//				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");
//
//		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		InsideTBPDataBeforeCourseRetraining = InsideData;
//
//		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
//				"Outside To Be planned at Course Retraining screen",
//				"Inside To Be planned at Course Retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
//				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
//				"To be Planned Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
////		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
////				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
////				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(SkippedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.mediumWait();
//
//		InsideData();
//		Inside_Skipped_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
//				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
//				"Skipped Data after keeping employee in different states",
//				"Skipped Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(absentBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
//				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(absentBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		Inside_Absent_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
//				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
//				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
//				"Absent Data after keeping employee in different states",
//				"Absent Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
//				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
//				"Outside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup",
//				"Inside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
//				Inside_ToBe_Retrained_BeforeCourseRetraining,
//				"To Be Retrained Data after keeping employee in different states",
//				"To Be Retrained Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		//
//
//		TimeUtil.shortWait();
//		scrollToViewElement(addItem);
//		TimeUtil.mediumWait();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));
////          WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
////
////		
////		
////		
////		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
//
//		// Find all the rows (<tr>) inside the table
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//
//		// Return the number of rows
//		InprogressQualifiedUsers = rows.size();
//
//		InProgressDataAfterReset = new String[InprogressQualifiedUsers];
//
//		for (int i = 0; i < InprogressQualifiedUsers; i++) {
//			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
//			InProgressDataAfterReset[i] = cell.getText().trim();
//
//			// System.out.println(InsideData[i]);
//
//		}
//
//		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
//				"Course Retaining Screen In Progress and Qualified Users count",
//				"Actual In Progress and Qualified Users count");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
//				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");
//
//		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
//		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
//		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
//
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		InsideData();
//
//		Scheduled_ToBePlanned_After_Reset = InsideData;
//
////		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
////				"OutSide To Be Planned Count in Schedule Tab after reset button",
////				"Inside To Be Planned Count in Schedule Tab after reset button");
////
////		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
////				"Comination of In Progess and To Be planned Count before reset button",
////				"To be planned Data in Scheduled tab After reset");
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
//				"OutSide To Be Planned Count in Schedule Tab after reset button",
//				"Inside To Be Planned Count in Schedule Tab after reset button");
//		String[] combined = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
//				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.InprogressUsers.length);
//
//		System.arraycopy(CM_VerifyCourseSessionScreen.InprogressUsers, 0, combined,
//				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.InprogressUsers.length);
//
//		toBePlannedCountData(combined, Scheduled_ToBePlanned_After_Reset,
//				"Comination of In Progess and To Be planned Count before reset button",
//				"To be planned Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
//		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		InsideData();
//
//		Scheduled_Skipped_After_Reset = InsideData;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
//				"OutSide Skipped Count in Schedule Tab after reset button",
//				"Inside Skipped Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
//				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
//		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		InsideData();
//
//		Scheduled_Absent_After_Reset = InsideData;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
//				"OutSide Absent Count in Schedule Tab after reset button",
//				"Inside Absent Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
//				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
//		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		InsideData();
//
//		Scheduled_ToBeRetrained_After_Reset = InsideData;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
//				"OutSide ToBeRetrained Count in Schedule Tab after reset button",
//				"Inside ToBeRetrained Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
//				"ToBeRetrained Data in Scheduled tab before subgroup selection",
//				"ToBeRetrained Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
//		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
//		int TotalTraineesCountAfterReset = stringToInt;
//
//		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_Skipped_After_Reset.length
//				+ Scheduled_Absent_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;
//
//		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
//				"Total Trainees Outside Count After Reset");
//
//		waitForElementVisibile(UnscheduleToBePlannedCount);
//		convertString_To_Integer(UnscheduleToBePlannedCount.getText());
//
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
//				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");
//
//		click2(UnscheduleToBePlannedCount, "", "", "", "");
//		TimeUtil.mediumWait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		TimeUtil.longwait();
//		InsideData();
//		ToBePlannedCountAfterResetUnscheduled = InsideData;
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
//				"Qualified users", "To be planned Data in Unscheduled tab After reset");
//
//		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
//				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(courseRetrainingRemarks);
//		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		scrollToViewElement(submit);
//		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		try {
//			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
//				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
//						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
//						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
//				// TimeUtil.shortWait();
//				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
//						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
//						CommonStrings.Esign_Proceed_SS.getCommonStrings());
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		switchToDefaultContent(driver);
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		driver.switchTo().frame(0);
//
//		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//
//		// AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
//		WebElement element = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span"));
//
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		js.executeScript("arguments[0].click();", element);
//
//		InsideData();
//
//		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
//				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
//				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		// AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
//
//		WebElement element1 = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[2]/a/span"));
//
//		JavascriptExecutor js1 = (JavascriptExecutor) driver;
//		js1.executeScript("arguments[0].click();", element1);
//
//		InsideData();
//
//		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
//				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		// AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
//
//		WebElement element11 = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[3]/a/span"));
//
//		JavascriptExecutor js11 = (JavascriptExecutor) driver;
//		js11.executeScript("arguments[0].click();", element11);
//
//		InsideData();
//
//		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
//				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		// AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
//
//		WebElement element111 = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[4]/a/span"));
//
//		JavascriptExecutor js111 = (JavascriptExecutor) driver;
//		js111.executeScript("arguments[0].click();", element111);
//
//		InsideData();
//
//		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
//				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
//
//		InsideData();
//		AT_ToBePlannedData_After_CourseRetraining = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
//				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
//				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(auditCompareTRNActionValue);
//		scrollToViewElement(auditClose);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//	}

//	public void Scheduled_course_Retraining_RE_Online_With_Assessment_() {
//
//		int count;
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
//				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
//				CommonStrings.Propse_Menu_SS.getCommonStrings());
//		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
//				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
//				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
//		courseNameLikeRetrain.clear();
//		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
//				CM_Course.getCourse() + "%",
//				// "CRSNewGHOB%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		TimeUtil.mediumWait();
//		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.AfterAnySession,
//				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");
//
//		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		InsideTBPDataBeforeCourseRetraining = InsideData;
//
//		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
//				"Outside To Be planned at Course Retraining screen",
//				"Inside To Be planned at Course Retraining screen");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
//				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
//				"To be Planned Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
//		count = stringToInt;
//
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
//				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
//
//		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
//		TimeUtil.shortWait();
//
//		InsideData();
//		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;
//
//		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
//				"Outside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup",
//				"Inside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
//				Inside_ToBe_Retrained_BeforeCourseRetraining,
//				"To Be Retrained Data after keeping employee in different states",
//				"To Be Retrained Data in course retraining screen before confirming/selection subgroup");
//
//		TimeUtil.shortWait();
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		//
//
//		TimeUtil.shortWait();
//		scrollToViewElement(addItem);
//		TimeUtil.mediumWait();
//		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
//				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
//		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
//		TimeUtil.mediumWait();
//		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		WebElement table = driver.findElement(By.xpath("//*[starts-with(@id, 'TneListTr_')]/td/table/tbody"));
//
//		// Find all the rows (<tr>) inside the table
//		List<WebElement> rows = table.findElements(By.tagName("tr"));
//
//		// Return the number of rows
//		InprogressQualifiedUsers = rows.size();
//
//		InProgressDataAfterReset = new String[InprogressQualifiedUsers];
//
//		for (int i = 0; i < InprogressQualifiedUsers; i++) {
//			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
//			InProgressDataAfterReset[i] = cell.getText().trim();
//
//			// System.out.println(InsideData[i]);
//
//		}
//
////		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.QualifiedData.length,
////				"Course Retaining Screen In Progress and Qualified Users count",
////				"Actual In Progress and Qualified Users count");
////
////		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, InProgressDataAfterReset,
////				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");
//
//		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
//				"Course Retaining Screen In Progress and Qualified Users count",
//				"Actual In Progress and Qualified Users count");
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
//				"Expected InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");
//
//		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//
//		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
//		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
//		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
//		InsideData();
//
//		Scheduled_ToBePlanned_After_Reset = InsideData;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
//				"OutSide To Be Planned Count in Schedule Tab after reset button",
//				"Inside To Be Planned Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
//				"Comination of In Progess and To Be planned Count before reset button",
//				"To be planned Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
//		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
//		InsideData();
//
//		Scheduled_ToBeRetrained_After_Reset = InsideData;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
//				"OutSide ToBeRetrained Count in Schedule Tab after reset button",
//				"Inside ToBeRetrained Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
//				"ToBeRetrained Data in Scheduled tab before subgroup selection",
//				"ToBeRetrained Data in Scheduled tab After reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
//		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
//		int TotalTraineesCountAfterReset = stringToInt;
//
//		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;
//
//		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
//				"Total Trainees Outside Count After Reset");
//
//		waitForElementVisibile(UnscheduleToBePlannedCount);
//		convertString_To_Integer(UnscheduleToBePlannedCount.getText());
//
//		tbpscheduledAfterCourseRetrainingCount = stringToInt;
//
//		compareCount(tbpscheduledAfterCourseRetrainingCount,
//				CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
//				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");
//
//		click2(UnscheduleToBePlannedCount, "", "", "", "");
//		TimeUtil.mediumWait();
//		InsideData();
//		ToBePlannedCountAfterResetUnscheduled = InsideData;
//
//		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
//				"Qualified users", "To be planned Data in Unscheduled tab After reset");
//
//		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
//				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(courseRetrainingRemarks);
//		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
//				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
//				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
//		TimeUtil.shortWait();
//		scrollToViewElement(submit);
//		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//
//		TimeUtil.mediumWait();
//		try {
//			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
//				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
//						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
//						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
//				// TimeUtil.shortWait();
//				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
//						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
//						CommonStrings.Esign_Proceed_SS.getCommonStrings());
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		waitForElementVisibile(confirmationText);
//
//		switchToDefaultContent(driver);
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
//				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
//				CourseStrings.CourseAudittrails_SS.getCourseStrings());
//		switchToBodyFrame(driver);
//		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
//				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
//		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
//				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//				CourseStrings.Select_CoursName_SS.getCourseStrings());
//		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.Like_CourseName_SS.getCourseStrings());
//		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
//				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
//		driver.switchTo().frame(0);
//
//		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//
//		// AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
//		WebElement element = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span"));
//
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		js.executeScript("arguments[0].click();", element);
//
//		InsideData();
//
//		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
//				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
//				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
//		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
//		count = stringToInt;
//		// AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
//
//		WebElement element111 = driver
//				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[4]/a/span"));
//
//		JavascriptExecutor js111 = (JavascriptExecutor) driver;
//		js111.executeScript("arguments[0].click();", element111);
//
//		InsideData();
//
//		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;
//
//		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
//				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
//				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
//
//		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
//				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
//
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
//
//		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
//		count = stringToInt;
//		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
//
//		InsideData();
//		AT_ToBePlannedData_After_CourseRetraining = InsideData;
//
//		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
//				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");
//
//		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
//				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
//		waitForElementVisibile(closeIcon);
//		closeIcon.click();
//
//		scrollToViewElement(auditCompareTRNActionValue);
//		scrollToViewElement(auditClose);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
//				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		switchToDefaultContent(driver);
//
//	}
//
//}

	public void Scheduled_course_Retraining_RE_Online_With_Assessment_() {

		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after different states");

		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;

		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
		count = stringToInt;

		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");

		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();

		InsideData();
		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;

		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
				"Outside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup",
				"Inside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
				Inside_ToBe_Retrained_BeforeCourseRetraining,
				"To Be Retrained Data after keeping employee in different states",
				"To Be Retrained Data in course retraining screen before confirming/selection subgroup");

		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		//

		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));

		// Return the number of rows
		InprogressQualifiedUsers = rows.size();

		InProgressDataAfterReset = new String[InprogressQualifiedUsers];

		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");

		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		Scheduled_ToBePlanned_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");
		String[] combined = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.InprogressUsers.length);

		System.arraycopy(CM_VerifyCourseSessionScreen.InprogressUsers, 0, combined,
				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.InprogressUsers.length);

		toBePlannedCountData(combined, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		Scheduled_ToBeRetrained_After_Reset = InsideData;

		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
				"OutSide ToBeRetrained Count in Schedule Tab after reset button",
				"Inside ToBeRetrained Count in Schedule Tab after reset button");

		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
				"ToBeRetrained Data in Scheduled tab before subgroup selection",
				"ToBeRetrained Data in Scheduled tab After reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;

		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;

//		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
//				"Total Trainees Outside Count After Reset");

		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());

		tbpscheduledAfterCourseRetrainingCount = stringToInt;

		compareCount(tbpscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");

		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;

		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");

		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		waitForElementVisibile(confirmationText);
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);

		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;

		// AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		WebElement element = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span"));

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].click();", element);
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();

		InsideData();

		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);

		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();

		WebElement element111 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[4]/a/span"));

		JavascriptExecutor js111 = (JavascriptExecutor) driver;
		js111.executeScript("arguments[0].click();", element111);
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();

		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;

		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");

		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");

		waitForElementVisibile(closeIcon);
		closeIcon.click();

		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);

		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;

		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");

		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();

		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}
	public void Scheduled_course_Retraining_IT_Online_With_Assessment_() {
		 
		int count;
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(proposeCourseRetraining, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		clickAndWaitforNextElement(searchByCourseNameDropdown, courseNameLikeRetrain,
				CourseStrings.Select_CoursName_DC.getCourseStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), CourseStrings.Select_CoursName_SS.getCourseStrings());
		courseNameLikeRetrain.clear();
		sendKeys2(courseNameLikeRetrain, CourseStrings.Like_CourseName_DC.getCourseStrings(),
				CM_Course.getCourse() + "%",
				// "CRSNewGHOB%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, displayedRecord,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		TimeUtil.mediumWait();
		convertString_To_Integer(toBePlannedBeforeCourseRetraining.getText());
		count = stringToInt;
 
		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_Data_AftrRespondDR_AfterSubgroup_selection.length,
				"To Be Planned Count Displayed at Course Retraining before selecting any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after respond document reading");
 
		click2(toBePlannedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
 
		InsideData();
		InsideTBPDataBeforeCourseRetraining = InsideData;
 
		compareCount(count, InsideTBPDataBeforeCourseRetraining.length,
				"Outside To Be planned at Course Retraining screen",
				"Inside To Be planned at Course Retraining screen");
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				InsideTBPDataBeforeCourseRetraining, "To be Planned Data after keeping employee in different states",
				"To be Planned Data in course retraining screen before confirming/selection subgroup");
 
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		convertString_To_Integer(SkippedBeforeCourseRetraining.getText());
		count = stringToInt;
 
//		compareCount(count, CM_VerifyCourseSessionScreen.ToBePlanned_CountAfterRespondDR,
//				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
//				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
 
		click2(SkippedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.mediumWait();
 
		InsideData();
		Inside_Skipped_BeforeCourseRetraining = InsideData;
 
		compareCount(count, Inside_Skipped_BeforeCourseRetraining.length,
				"Outside Skipped Data at Course Retraining screen", "Inside Skipped count at Course Retraining screen");
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, Inside_Skipped_BeforeCourseRetraining,
				"Skipped Data after keeping employee in different states",
				"Skipped Data in course retraining screen before confirming/selection subgroup");
 
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		convertString_To_Integer(absentBeforeCourseRetraining.getText());
		count = stringToInt;
 
		compareCount(count, CM_VerifyCourseSessionScreen.AbsentData.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
 
		click2(absentBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
 
		InsideData();
		Inside_Absent_BeforeCourseRetraining = InsideData;
 
		compareCount(count, Inside_Absent_BeforeCourseRetraining.length,
				"Outside Absent count at Course Retraining screen before confirming/selecting subgroup",
				"Inside Absent count at Course Retraining screen before confirming/selecting subgroup");
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, Inside_Absent_BeforeCourseRetraining,
				"Absent Data after keeping employee in different states",
				"Absent Data in course retraining screen before confirming/selection subgroup");
 
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		convertString_To_Integer(ToBeRetrainedBeforeCourseRetraining.getText());
		count = stringToInt;
 
		compareCount(count, CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP.length,
				"To Be Planned Count Displayed at Course Retraining before selecting/Confirming any subgroup",
				"Actual To be Planned Count and that is displaying in Course Sesson screen after Keeping all employees in different state");
 
		click2(ToBeRetrainedBeforeCourseRetraining, "", "", "", "");
		TimeUtil.shortWait();
 
		InsideData();
		Inside_ToBe_Retrained_BeforeCourseRetraining = InsideData;
 
		compareCount(count, Inside_ToBe_Retrained_BeforeCourseRetraining.length,
				"Outside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup",
				"Inside ToBeRetrained count at Course Retraining screen before confirming/selecting subgroup");
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.ToBeRetrainedDatabeforeSGP,
				Inside_ToBe_Retrained_BeforeCourseRetraining,
				"To Be Retrained Data after keeping employee in different states",
				"To Be Retrained Data in course retraining screen before confirming/selection subgroup");
 
		TimeUtil.shortWait();
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		//
 
		TimeUtil.shortWait();
		scrollToViewElement(addItem);
		TimeUtil.mediumWait();
		click2(addItem, CourseSessionStrings.AddItem_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AC.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_AR.getCourseSessionStrings(),
				CourseSessionStrings.AddItem_Subgroup_SS.getCourseSessionStrings());
		click2(subGroupAdd_New1, CourseSessionStrings.Add_Required_Subgroup_DC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AC.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_AR.getCourseSessionStrings(),
				CourseSessionStrings.Add_Required_SS.getCourseSessionStrings());
		TimeUtil.mediumWait();
		click2(addNew, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		click2(confirmSubgroupSelection, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
 
		WebElement table = driver.findElement(By.xpath("//*[contains(@id, 'TneListTr_')]/td/table/tbody"));

		// Find all the rows (<tr>) inside the table
		List<WebElement> rows = table.findElements(By.tagName("tr"));
 
		// Return the number of rows
		InprogressQualifiedUsers = rows.size();
 
		InProgressDataAfterReset = new String[InprogressQualifiedUsers];
 
		for (int i = 0; i < InprogressQualifiedUsers; i++) {
			WebElement cell = rows.get(i).findElements(By.tagName("td")).get(1);
			InProgressDataAfterReset[i] = cell.getText().trim();
 
			// System.out.println(InsideData[i]);
 
		}
 
		compareCount(InprogressQualifiedUsers, CM_VerifyCourseSessionScreen.InProgressQualifiedusers.length,
				"Course Retaining Screen In Progress and Qualified Users count",
				"Actual In Progress and Qualified Users count");
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.InProgressQualifiedusers, InProgressDataAfterReset,
				"Actual InProgress/Qualified users", "In Progress/Qualified users Displayed after reset");
 
		click2(resetCount, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
 
		waitForElementVisibile(ScheduleToBePlannedCount_After_Reset);
		convertString_To_Integer(ScheduleToBePlannedCount_After_Reset.getText());
		int tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(ScheduleToBePlannedCount_After_Reset, "", "", "", "");
 
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
 
		Scheduled_ToBePlanned_After_Reset = InsideData;
 
//		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
//				"OutSide To Be Planned Count in Schedule Tab after reset button",
//				"Inside To Be Planned Count in Schedule Tab after reset button");
//
//		toBePlannedCountData(InsideTBPDataBeforeCourseRetraining, Scheduled_ToBePlanned_After_Reset,
//				"Comination of In Progess and To Be planned Count before reset button",
//				"To be planned Data in Scheduled tab After reset");
 
		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBePlanned_After_Reset.length,
				"OutSide To Be Planned Count in Schedule Tab after reset button",
				"Inside To Be Planned Count in Schedule Tab after reset button");
		String[] combined = Arrays.copyOf(InsideTBPDataBeforeCourseRetraining,
				InsideTBPDataBeforeCourseRetraining.length + CM_VerifyCourseSessionScreen.InprogressUsers.length);
 
		System.arraycopy(CM_VerifyCourseSessionScreen.InprogressUsers, 0, combined,
				InsideTBPDataBeforeCourseRetraining.length, CM_VerifyCourseSessionScreen.InprogressUsers.length);
 
		toBePlannedCountData(combined, Scheduled_ToBePlanned_After_Reset,
				"Comination of In Progess and To Be planned Count before reset button",
				"To be planned Data in Scheduled tab After reset");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(Schedule_Skipped_Count_After_Reset);
		convertString_To_Integer(Schedule_Skipped_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Skipped_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
 
		Scheduled_Skipped_After_Reset = InsideData;
 
		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Skipped_After_Reset.length,
				"OutSide Skipped Count in Schedule Tab after reset button",
				"Inside Skipped Count in Schedule Tab after reset button");
 
		toBePlannedCountData(Inside_Skipped_BeforeCourseRetraining, Scheduled_Skipped_After_Reset,
				"Skipped Data in Scheduled tab before subgroup selection", "Skipped Data in Scheduled tab After reset");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(Schedule_Absent_Count_After_Reset);
		convertString_To_Integer(Schedule_Absent_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_Absent_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
 
		Scheduled_Absent_After_Reset = InsideData;
 
		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_Absent_After_Reset.length,
				"OutSide Absent Count in Schedule Tab after reset button",
				"Inside Absent Count in Schedule Tab after reset button");
 
		toBePlannedCountData(Inside_Absent_BeforeCourseRetraining, Scheduled_Absent_After_Reset,
				"Absent Data in Scheduled tab before subgroup selection", "Absent Data in Scheduled tab After reset");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(Schedule_ToBeRetrained_Count_After_Reset);
		convertString_To_Integer(Schedule_ToBeRetrained_Count_After_Reset.getText());
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
		click2(Schedule_ToBeRetrained_Count_After_Reset, "", "", "", "");
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
 
		Scheduled_ToBeRetrained_After_Reset = InsideData;
 
		compareCount(tbpscheduledAfterCourseRetrainingCount, Scheduled_ToBeRetrained_After_Reset.length,
				"OutSide ToBeRetrained Count in Schedule Tab after reset button",
				"Inside ToBeRetrained Count in Schedule Tab after reset button");
 
		toBePlannedCountData(Inside_ToBe_Retrained_BeforeCourseRetraining, Scheduled_ToBeRetrained_After_Reset,
				"ToBeRetrained Data in Scheduled tab before subgroup selection",
				"ToBeRetrained Data in Scheduled tab After reset");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(TotalTraineesDataAfterCourseReset);
		convertString_To_Integer(TotalTraineesDataAfterCourseReset.getText());
		int TotalTraineesCountAfterReset = stringToInt;
 
		int combineAllcolumns = Scheduled_ToBePlanned_After_Reset.length + Scheduled_Skipped_After_Reset.length
				+ Scheduled_Absent_After_Reset.length + Scheduled_ToBeRetrained_After_Reset.length;
 
//		compareCount(combineAllcolumns, TotalTraineesCountAfterReset, "Actual Total Trainees Data after reset",
//				"Total Trainees Outside Count After Reset");
 
		waitForElementVisibile(UnscheduleToBePlannedCount);
		convertString_To_Integer(UnscheduleToBePlannedCount.getText());
 
		tbpscheduledAfterCourseRetrainingCount = stringToInt;
 
		compareCount(tbpscheduledAfterCourseRetrainingCount, CM_VerifyCourseSessionScreen.QualifiedData.length,
				"To Be planned Count in Unscheduled Tab After Reset", "Actual Qualified Users Count");
 
		click2(UnscheduleToBePlannedCount, "", "", "", "");
		TimeUtil.mediumWait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		InsideData();
		ToBePlannedCountAfterResetUnscheduled = InsideData;
 
		toBePlannedCountData(CM_VerifyCourseSessionScreen.QualifiedData, ToBePlannedCountAfterResetUnscheduled,
				"Qualified users", "To be planned Data in Unscheduled tab After reset");
 
		compareCount(ToBePlannedCountAfterResetUnscheduled.length, tbpscheduledAfterCourseRetrainingCount,
				"To Be planned Inside Count After Reset", "To Be planned Outside Count After Reset");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		scrollToViewElement(courseRetrainingRemarks);
		sendKeys2(courseRetrainingRemarks, CourseSessionStrings.Click_AddButton_DC.getCourseSessionStrings(), "remarks",
				CourseSessionStrings.Click_AddButton_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_AddButton_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
 
		switchToDefaultContent(driver);
 
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(aduitTrailsCourseRetraining, CourseStrings.CourseAudittrails_DC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AC.getCourseStrings(),
				CourseStrings.CourseAudittrails_AR.getCourseStrings(),
				CourseStrings.CourseAudittrails_SS.getCourseStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CourseStrings.SearchByCourse_Dropdown_DC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AC.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_AR.getCourseStrings(),
				CourseStrings.SearchByCourse_Dropdown_SS.getCourseStrings());
		click2(searchByCourseNameDropdown, CourseStrings.Select_CoursName_DC.getCourseStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CourseStrings.Select_CoursName_SS.getCourseStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		sendKeys2(courseNameLike, CourseStrings.Like_CourseName_DC.getCourseStrings(), CM_Course.getCourse() + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CourseStrings.Like_CourseName_SS.getCourseStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(searchFilterApplyBtn,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(displayedRecord, CourseStrings.Click_Course_for_AuditTrails_DC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AC.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_AR.getCourseStrings(),
				CourseStrings.Click_Course_for_AuditTrails_SS.getCourseStrings());
		driver.switchTo().frame(0);
 
		waitForElementVisibile(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledToBePlannedCountAfter_CourseRetraining);
 
		convertString_To_Integer(AT_ScheduledToBePlannedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
 
		// AT_ScheduledToBePlannedCountAfter_CourseRetraining.click();
		WebElement element = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[1]/a/span"));
 
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].click();", element);
 
		InsideData();
 
		AT_ToBePlannedData_After_CourseRetraining_Scheduledtab = InsideData;
 
		compareCount(count, AT_ToBePlannedData_After_CourseRetraining_Scheduledtab.length,
				"To Be Planned Out Side Count count at Sccheduled tab  After Course Retraining",
				"To Be Planned InSide Count count at Sccheduled tab  After Course Retraining");
 
		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining_Scheduledtab, Scheduled_ToBePlanned_After_Reset,
				"To Planned Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"To Planned Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_SkippedCountAfter_CourseRetraining);
 
		convertString_To_Integer(AT_ScheduledT_SkippedCountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_SkippedCountAfter_CourseRetraining.click();
 
		WebElement element1 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[2]/a/span"));
 
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].click();", element1);
 
		InsideData();
 
		AT_Skipped_After_CourseRetraining_Scheduledtab = InsideData;
 
		compareCount(count, AT_Skipped_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
 
		toBePlannedCountData(Scheduled_Skipped_After_Reset, AT_Skipped_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_Absent_CountAfter_CourseRetraining);
 
		convertString_To_Integer(AT_ScheduledT_Absent_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_Absent_CountAfter_CourseRetraining.click();
 
		WebElement element11 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[3]/a/span"));
 
		JavascriptExecutor js11 = (JavascriptExecutor) driver;
		js11.executeScript("arguments[0].click();", element11);
 
		InsideData();
 
		AT_Absent_After_CourseRetraining_Scheduledtab = InsideData;
 
		compareCount(count, AT_Absent_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
 
		toBePlannedCountData(Scheduled_Absent_After_Reset, AT_Absent_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
		scrollToViewElement(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining);
 
		convertString_To_Integer(AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.getText().trim());
		count = stringToInt;
		// AT_ScheduledT_ToBeRetrained_CountAfter_CourseRetraining.click();
 
		WebElement element111 = driver
				.findElement(By.xpath("//*[@id=\"CompareTRN\"]/div[2]/div/div[9]/table/tbody/tr/td[4]/a/span"));
 
		JavascriptExecutor js111 = (JavascriptExecutor) driver;
		js111.executeScript("arguments[0].click();", element111);
 
		InsideData();
 
		AT_ToBeRetrained_After_CourseRetraining_Scheduledtab = InsideData;
 
		compareCount(count, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab.length,
				"Skipped Out Side Count count at Sccheduled tab  After Course Retraining at audit trails",
				"Skipped Inside Count count at Sccheduled tab  After Course Retraining at audit trails");
 
		toBePlannedCountData(Scheduled_ToBeRetrained_After_Reset, AT_ToBeRetrained_After_CourseRetraining_Scheduledtab,
				"Skipped Data After Course Retraining Under Sccheduled Tab at Audit trails screen",
				"Skipped Data After Course Retraining Under Sccheduled Tab at Course Retraining after reset button");
 
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		waitForElementVisibile(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
		scrollToViewElement(AT_UnscheduleToBePlannedCountAfter_CourseRetraining);
 
		convertString_To_Integer(AT_UnscheduleToBePlannedCountAfter_CourseRetraining.getText());
		count = stringToInt;
		AT_UnscheduleToBePlannedCountAfter_CourseRetraining.click();
 
		InsideData();
		AT_ToBePlannedData_After_CourseRetraining = InsideData;
 
		compareCount(count, AT_ToBePlannedData_After_CourseRetraining.length,
				"Outisde To Be Planned Count At Unscheduled Tab", "Inside To Be Planned Count At Unscheduled Tab");
 
		toBePlannedCountData(AT_ToBePlannedData_After_CourseRetraining, CM_VerifyCourseSessionScreen.QualifiedData,
				"To Planned Data After Course Retraining Under Unscheduled Tab", "Actual Qualified Users");
		waitForElementVisibile(closeIcon);
		closeIcon.click();
 
		scrollToViewElement(auditCompareTRNActionValue);
		scrollToViewElement(auditClose);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CourseStrings.Close_AuditTrails_Course_AC.getCourseStrings(),
				CourseStrings.Close_AuditTrails_Course_AR.getCourseStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		switchToDefaultContent(driver);
 
	}
 
}
