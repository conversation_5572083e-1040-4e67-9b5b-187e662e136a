package com.Automation.Strings;

public enum CourseInvitationStrings {
//	 CourseInvitation Registration

	CIMenu_DC("Click on 'Course Invitation' submenu."),
	CIMenu_AC("'Course Invitation Course List' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'CourseName, Unique Code,Training Type,Last Date of Response' columns.</div>"),

	CIMenu_AR("'Course Invitation Course List' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'CourseName, Unique Code,Training Type,Last Date of Response' columns.</div>"),
	CIMenu_SS("'Course Invitation'"),

	UserName_DC1("Enter the valid 'User ID' of the user who is selected at above proposed course session."),

	// Search By Course Name

	SearchBy1_DC("Click on the 'Course Name' for which the above session is proposed"),
//
	SearchBy1_SS("'Course Name'"),

	SearchBy1_AC("Course Sessions' and 'Response to Course Invitation' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the sessions should be displayed.</div>"),

	SearchBy1_AR("Course Sessions' and 'Response to Course Invitation' screen is getting .</div>"
			+ "<div><b>*</b> Option to select the sessions is getting displayed.</div>"),
	
	
	//Search this page icon
	SearchBy_DC("Click on 'Search This Page' icon."),
	SearchBy_AC("Search This Page' text box should be displayed.</div>"),
	SearchBy_AR("Search This Page' text box is getting displayed.</div>"), SearchBy_SS("'Search this Page'."),

	SearchBy2_DC("Enter the 'Course Name' for which the above  session is proposed"), SearchBy2_SS("'Course Name'"),

	//Selected Session from List
	UserSelectionScreenNextBtn_DC("Select the required session number under 'Selected Session from List'"),
	UserSelectionScreenNextBtn_AC("Session Details should be displayed.</div>"),
	UserSelectionScreenNextBtn_AR("Session Details are getting displayed.</div>"),
	UserSelectionScreenNextBtn_SS("'Course Session from list'"),
//Apply
	ApplyButton_DC("Click on 'Apply' button."),
	SearchBy2_AC("Records should be displayed based on the search criteria."),
	SearchBy2_AR("Records are getting displayed based on the search criteria.</div>"), ApplyButton_SS("'Apply'"),
	remarks_DC("Enter the value less than or equals to 250 characters in 'Remarks' field."), remarks_SS("'Remarks'"),
//Submit
	Submit_DC("Click on 'Submit' button."),
	Submit_AC("'Course Invitaion Updated' confirmation message should be displayed with 'Done' button.</div>"),
	Submit_AR("'Course I"
			+ "nvitaion Updated' confirmation message is getting displayed with 'Done' button.</div>"),
	Submit_SS("'Submit'"), Click_Done_DC("Click 'Done' button."),
	Click_Done_AC("'Course Invitation Course List' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should displayed with 'CourseName, Unique Code,Training Type,Last Date of Response' columns.</div>"),
//DONE
	
//	Click_Done_AC("Course Invitation Course List' screen should be displayed.</div>"),
	Click_Done_AR("'Course Invitation Course List' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'CourseName, Unique Code,Training Type,Last Date of Response' columns.</div>"),

	Click_Done_SS("'Done'"),
	
	
	//Reject
	
	Select_Reject_DC("Select Response as 'Reject'."),
	Select_Reject_SS("'Reject'");
	
	
	

	private final String CourseInvitationStrings;

	CourseInvitationStrings(String CourseInvitationStrings) {

		this.CourseInvitationStrings = CourseInvitationStrings;

	}

	public String getCourseInvitationStrings() {
		return CourseInvitationStrings;
	}

}
