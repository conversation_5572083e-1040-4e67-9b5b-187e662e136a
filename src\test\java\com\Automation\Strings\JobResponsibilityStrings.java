package com.Automation.Strings;

public enum JobResponsibilityStrings {
	// Job Responsibility Config

	JobResponsibility_Config_DC("Click on 'Job Responsibility' submenu."),
	JobResponsibility_Config_AC(
			"'Job Responsibility Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
					+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	JobResponsibility_Config_AR("'Job Responsibility Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	JobResponsibility_Config_SS("'Configuration'"),

//Job responsibility submenu
	JobResponsibilityDC("Click on 'Job Responsibility' submenu."),

	JobResponsibilityAC("'Job Responsibility’ Screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Search this Page’, ‘Advanced Search’ and ‘Total Record Count’ icons.</div>"
			+ "<div><b>*</b> Screen should contain 'Employee Name, Employee ID, Department' columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, and ‘Employee ID’ should be available.</div>"
			+ "<div><b>*</b>List of Employee Name(s) who have been assigned to current plant should be displayed..</div>"),
	JobResponsibilityAR("'Job Responsibility’ Screen is getting displayed.</div>"
			+ "<div><b>*</b>The screen  contains ‘Search this Page’, ‘Advanced Search’ and ‘Total Record Count’ icons.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Employee Name, Employee ID, Department' columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, and ‘Employee ID’ are available.</div>"
			+ "<div><b>*</b>List of Employee Name(s) who have been assigned to current plant is displayed..</div>"),

	JobResponsibilitySS("'Job Responsibility'"),

//search by
	SearchBy_JobResponsibility_AC(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID' should be available.</div>"),
	SearchBy_JobResponsibility_AR(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID' are available.</div>"),
//search by employee name
	Select_EmployeeName_DC("Select 'Employee Name'."), EmployeeName_SS("'Employee Name'."),
	Like_EmployeeName_DC("Enter the above registered 'Employee Name' in 'Like' field."),

//click displayed  employee name record
	Click_EmpName_For_JobResponsibility_DC("Click on the above registered 'Employee Name' record."),
	Click_EmpName_For_JobResponsibility_AC(
			"'Job Responsibility Registration Initiation' screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain ‘Employee Name, Employee ID, Designation, Department, Date of Joining, Revision No, Job Responsibility, Reporting To, Authorized Deputy, Qualification, Previous Experience, External Certificate and Line of Approvers’ fields.</div>"
					+ "<div><b>*</b>‘Employee Name, Employee ID and Revision No.’ fields details should be displayed in read only format.</div>"
					+ "<div><b>*</b> Designation which is selected at the time of selected user registration should be displayed.</div>"
					+ "<div><b>*</b>Department which is selected at the time of user registration should be displayed.</div>"
					+ "<div><b>*</b>If a 'Reporting To' has been assigned to the selected employee in 'user registration', the details of the selected employee should be displayed.</div>"
					+ "<div><b>*</b> If not, '--' should be displayed in the 'Reporting To' field, in read-only format.</div>"
					+ "<div><b>*</b>contain ‘View Existing’, and ‘Submit’ buttons. </div>"
					+ "<div><b>*</b>Under ‘View Existing’, job responsibility registered employee records should be displayed (if any).</div>"),

	Click_EmpName_For_JobResponsibility_AR(
			"'Job Responsibility Registration Initiation' screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains ‘Employee Name, Employee ID, Designation, Department, Date of Joining, Revision No, Job Responsibility, Reporting To, Authorized Deputy, Qualification, Previous Experience, External Certificate and Line of Approvers’ fields.</div>"
					+ "<div><b>*</b>‘Employee Name, Employee ID and Revision No.’ fields details is displayed in read only format.</div>"
					+ "<div><b>*</b> Designation which is selected at the time of selected user registration is getting displayed.</div>"
					+ "<div><b>*</b>Department which is selected at the time of user registration is displayed.</div>"
					+ "<div><b>*</b>If a 'Reporting To' has been assigned to the selected employee in 'user registration', the details of the selected employee is displayed.</div>"
					+ "<div><b>*</b> If not, '--' is  displayed in the 'Reporting To' field, in read-only format.</div>"
					+ "<div><b>*</b>contain ‘View Existing’, and ‘Submit’ buttons. </div>"
					+ "<div><b>*</b>Under ‘View Existing’, job responsibility registered employee records is displayed (if any).</div>"),
	Click_EmpName_For_JobResponsibility_SS("'Job Responsibility Registration Initiation'."),

	Click_EmpName_For_JobResponsibilityapproval_AC(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘3’ and the ‘No. of Approvals Completed’ should be read as ‘1’.</div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	Click_EmpName_For_JobResponsibilityapproval_AR(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen is  getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section contains ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ read as ‘3’ and the ‘No. of Approvals Completed’ are read as ‘1’.</div>"
					+ "<div><b>*</b>All the particulars is displayed in read only format.</div>"),

//click date of joining
	Click_DateofJoining_DC("Select the required  date in 'Date of Joining' field."),
	Click_DateofJoining_AC("Selected date should be displayed for the 'Date of Joining'."),
	Click_DateofJoining_AR("Selected date is getting displayed for the 'Date of Joining'."),
	Click_DateofJoining_SS("'Date of Joining'"),
//click authorized deputy additem
	Authorized_DC("Click on 'Add Item' for 'Authorized Deputy' field"),

	DeputyAuthorized_DC("Click on 'Add Item'"),

	Authorized_AC("'‘Employee List’ window should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Search this Page’, ‘Advanced search’ & ‘Close’ icons.</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name and Employee ID’ columns.</div>"
			+ "<div><b>*</b>The option to search with the ‘Employee Name and Employee ID’ should be available.</div>"
			+ "<div><b>*</b>All the registered and active Employees should be listed for selection.</div>"
			+ "<div><b>*</b>The screen should contain ‘Apply, Cancel and Add’ button.</div>"
			+ "<div><b>*</b>‘Add’ button should be displayed in disabled mode.</div>"),

	Authorized_AR("'‘Employee List’ window is getting  displayed.</div>"
			+ "<div><b>*</b>The screen  contains ‘Search this Page’, ‘Advanced search’ & ‘Close’ icons.</div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Employee Name and Employee ID’ columns.</div>"
			+ "<div><b>*</b>The option to search with the ‘Employee Name and Employee ID’ are available.</div>"
			+ "<div><b>*</b>All the registered and active Employees is listed for selection.</div>"
			+ "<div><b>*</b>The screen contains ‘Apply, Cancel and Add’ button.</div>"
			+ "<div><b>*</b>‘Add’ button is getting displayed in disabled mode.</div>"),

	Authorized_SS("'Employee List'.<div>"),
//click external add item
	ClickExternaladditem_DC("Click ‘Add Item’ button for ‘External Certificates’ field."),
	ClickExternaladditem_SS("'Add Item'.<div>"),
	ClickExternaladditem_AC("'DMS Document Uploader’ window should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Search this Page and Advanced search and Close’ icons.</div>"
			+ "<div><b>*</b>The screen should contain ‘Search File(s)’ and ‘Upload File(s)’ tabs.</div>"
			+ "<div><b>*</b>By default, ‘Search File(s)’ tab particulars should be displayed.</div>"
			+ "<div><b>*</b> The screen should  contain ‘Document Code, Document Description & Add All’ columns.</div>"
			+ "<div><b>*</b>The option to search with ‘Document Code & Document Description’ should be available.</div>"),

	ClickExternaladditem_AR("'‘DMS Document Uploader’ window is getting displayed.</div>"
			+ "<div><b>*</b>The screen  contains ‘Search this Page and Advanced search and Close’ icons.</div>"
			+ "<div><b>*</b>The screen contains ‘Search File(s)’ and ‘Upload File(s)’ tabs.</div>"
			+ "<div><b>*</b>By default, ‘Search File(s)’ tab particulars is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘Document Code, Document Description & Add All’ columns.</div>"
			+ "<div><b>*</b>The option to search with ‘Document Code & Document Description’ are available.</div>"),
// click upload file
	ClickExternaluploadfile_DC("Click the ‘Upload File(s)’ tab."),
	ClickExternaluploadfile_SS("'DMS Document Uploader'"),
	ClickExternaluploadfile_AC(
			"'The screen should contain ‘Document Code, Document Description, Keyword(s) and File To Upload’ fields.</div>"
					+ "<div><b>*</b>Document code should be system generated.</div>"
					+ "<div><b>*</b>The option to upload new document should be available.</div>"),

	ClickExternaluploadfile_AR(
			"'The screen contains ‘Document Code, Document Description, Keyword(s) and File To Upload’ fields.</div>"
					+ "<div><b>*</b>Document code is system generated.</div>"
					+ "<div><b>*</b>The option to upload new document is available.</div>"),
//Enter Description
	ExternalDescription_DC("Enter the value less than or equals to 200 characters for ‘Document Description’ field."),
	ExternalDescription_AC("Entered value should be displayed for the field.</div>"),
	ExternalDescription_AR("Entered value is getting displayed for the field."),
	ExternalDescription_SS("'Document Description'."),
//Enter Keyword
	KeywordDescription_DC("Enter a value less than or equal to 200 characters for ‘Keyword(s)’ field."),
	KeywordDescription_AC("Entered value should be displayed for the field.</div>"),
	KeywordDescription_AR("Entered value is getting displayed for the field."), KeywordDescription_SS("‘Keyword(s)’."),
//click upload document
	uploadocument_DC("Click ‘Upload’ button."),
	uploadocument_AC(
			"‘Document Uploaded Successfully Document Code :< Document Code >’ message should be displayed with ‘Ok’ button.</div>"),
	uploadocument_AR(
			"‘Document Uploaded Successfully Document Code :< Document Code >’ message is getting displayed with ‘Ok’ button.</div>"),
	uploadocument_SS("‘Upload’."),
//Click OK button
	clickok_DC("Click ‘Ok’ button and click ‘Search File(s)’ tab."),
	clickok_AC("Just uploaded documents should be listed for selection under ‘Search File(s)’ tab..</div>"),
	clickok_AR("Just uploaded documents is listed for selection under ‘Search File(s)’ tab..</div>"),
	clickok_SS("‘DMS Document Uploader’."),
//click dropdown
	ExternalCertificatesdropdown_DC("Click on  Search by 'External Certificates' drop down."),
	ExternalCertificatesdropdown_AC(
			"'Option to select ‘Document Code’ and ‘Document Description’ should be available."),
	ExternalCertificatesdropdown_AR("'Option to select ‘Document Code’ and ‘Document Description’ are available."),
	ExternalCertificatesdropdown_SS("'Document Description'"),
//select document description
	SelectDocumentDescription_DC("Select the  'Document Description'."),
	SelectDocumentDescription_AC("Selection should be accepted."),
	SelectDocumentDescription_AR("Selection is getting accepted."),
	SelectDocumentDescription_SS("'Document Description'"),
	// Select Document Code
	SelectDocumentCode_DC("Select the  'Document Code'."), SelectDocumentCode_AC("Selection should be accepted."),
	SelectDocumentCode_AR("Selection is getting accepted."), SelectDocumentCode_SS("'Document Code'"),

	// add
	addDocument_Code_DC("Click ‘Add’ button against uploaded document and click ‘Add’ button."),
	addDocument_Code_AC("The selected documents should be displayed against ‘External Certificates’ field."),
	addDocument_Code_AR("The selected documents is getting displayed against ‘External Certificates’ field."),
	addDocument_Code_SS("'Add button'"),

	addDocumentCode_DC("Click ‘Add’ button against uploaded document."),
	addDocumentCode_AC("Selected record should be moved to 'Selected Items' column."),
	addDocumentCode_AR("Selected record is getting moved to 'Selected Items' cloumn."), addDocumentCode_SS("'Add'"),

	Authorizedadd_DC("Click on 'Add Item'"),
//Enter document description
	Enter_DocumentCode_DC("Enter the valid 'Document Code' in 'Find' textbox"),
	Enter_DocumentCode_SS("'Document Code'"), AuthorizedDeputy_DC("Enter the valid ‘Employee Name’ in 'Find' textbox"),
	AuthorizedDeputy_SS("'‘Employee Name'.<div>"),

	Authorized_Deputyadd_AC("Selected record should be moved to 'Selected Items' column.</div>"),
	Authorized_Deputyadd_AR("Selected record is getting moved to 'Selected Items' column.</div>"),
	Authorized_Deputyadd_DC("'Add item'.</div>"), Authorized_Deputyadd_SS("'Add'."),

	AuthorizedDeputyadd_AC(
			"Selected Authorized Deputy Name should be displayed for 'Selected Employee List' field.</div>"),
	AuthorizedDeputyadd_AR(
			"Selected Authorized Deputy Name is/are getting displayed for 'Selected Employee List' field.</div>"),

//Enter job responsibility
	Job_Responsibility_DC("Enter the value less than or equals to 10000 characters in 'Job Responsibility' field."),
	Job_Responsibility_SS("'Job Responsibility'"),

//Enter Qualification
	Qualification_DC("Enter the value less than or equals to 250 characters in 'Qualification' field."),
	Qualification_SS("'Qualification'"),

//Enter previous experience
	PreviousExperience_DC("Enter the value less than or equals to 250 characters in 'Previous Experience' field."),
	PreviousExperience_SS("'Previous Experience'"),
//submit
	Submit_DC("Click on 'Submit' button."),
	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Job Responsibility: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Job Responsibility: Registration Initiation'.</div>"),
	Submit_SS("'E-Sign window'"),
//e-sign
	Esign_JobProceed_AC(
			"'Job Responsibility Registration Initiated Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_JobProceed_AR(
			"'Job Responsibility Registration Initiated Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	// Line of approvers
	ApproverGroupSelect_DC("Click on 'Select' dropdown to select required Group/Subgroups for 'Line of Approvers'."),
	ApproverGroupSelect_AC("List of Group/Subgroups should be displayed.</div>"),
	ApproverGroupSelect_AR("List of Group/Subgroups are getting  displayed.</div>"),
	ApproverGroupSelect_SS("'Select'."),

	Enter_ApproverGroup_DC("Enter 'Approvers Group/Approvers SubGroup' in drop down."),
	Enter_ApproverGroup_AC("Option to select only 'Approvers Group/Approvers SubGroup' should be available."),
	Enter_ApproverGroup_AR("Option to select only 'Approvers Group/Approvers SubGroup' is available."),
	Enter_ApproverGroup_SS("'Select'"),

	Click_ApproverGroup_DC("Click on 'Approvers Group/Approvers SubGroup'."),

	Click_ApproverGroup_SS("'Select'"),

	// job responsibity approval
	// approve menu
	SYS_UserGroupsApproveMenu_DC("Click on 'Approve' menu."),
	SYS_UserGroupsApproveMenu_AC("Assigned sub menus for the user under 'User Groups' menu should be displayed.</div>"),
	SYS_UserGroupsApproveMenu_AR("Assigned sub menus for user under 'User Groups' menu are getting displayed."),
	SYS_UserGroupsApproveMenu_SS("'Approve'"),
//Job responsibility menu
	SYS_UserGroupsjobresApproveMenu_DC("Click on 'Job Responsibility' submenu."),
	SYS_UserGroupsjobresApproveMenu_AC("'Job Responsibility Approval Tasks' screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’, ‘Initiated Between’ and ‘Department’ should be available.</div>"
			+ "<div><b>*</b>'By default, the ‘Registration’ tab details should be displayed.</div>"
			+ "<div><b>*</b>List of Employee Name(s) whose Job Responsibility registration request is to be approved by the current login user should be listed and available for approval.</div>"),

	SYS_UserGroupsjobresApproveMenu_AR("'Job Responsibility Approval Tasks' screen is getting  displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’, ‘Initiated Between’ and ‘Department’ are available.</div>"
			+ "<div><b>*</b>'By default, the ‘Registration’ tab details is displayed.</div>"
			+ "<div><b>*</b>List of Employee Name(s) whose Job Responsibility registration request is to be approved by the current login user is listed and available for approval.</div>"),

	SYS_UserGroupsjobresApproveclickemployyee_AC(
			"'‘Job Responsibility Registration Approval’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b>The ‘Events’ section should contain the ‘Initiated’ transaction’s details only.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as 3 and the ‘No. of Approvals Completed’ should be read as 0.</div>"
					+ "<div><b>*</b>Approve, Return & Drop’ options should be available for ‘Decision’ field.</div>"
					+ "<div><b>*</b>‘Drop’ radio button should be displayed in disabled mode.</div>"
					+ "<div><b>*</b>The option to enter/ select the ‘Remark(s)/ Reason(s)’ should be available.</div>"),

	SYS_UserGroupsjobresApproveclickemployyee_AR(
			"'‘Job Responsibility Registration Approval’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b>The ‘Events’ section contains the ‘Initiated’ transaction’s details only.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ are read as 3 and the ‘No. of Approvals Completed’ are read as 0.</div>"
					+ "<div><b>*</b>Approve, Return & Drop’ options are available for ‘Decision’ field.</div>"
					+ "<div><b>*</b>‘Drop’ radio button is displayed in disabled mode.</div>"
					+ "<div><b>*</b>The option to enter/ select the ‘Remark(s)/ Reason(s)’is  available.</div>"),
	SYS_UserGroupsjobresApproveclickemployyee_SS("'Job Responsibility Registration Approval'"),
	SYS_UserGroupsjobresApproveMenu_SS("'Job Responsibility'"),

	SYS_UserGroupsjobres_ApproveMenu_SS("'Job Responsibility Approval Tasks'"),

	// search by
	SearchBy_JobResponsibilityApproval_AC(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID ,Initiated Between,Department should be available.</div>"),
	SearchBy_JobResponsibilityApproval_AR(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID ,Initiated Between,Department is available.</div>"),

	Click_EmpName_For_JobResponsibilityApproval_SS("'Job Responsibility  Registration Approval'"),

	// approve radio button
	JobResponsibilityApproval_DC("Select 'Decision' as 'Approve'."),
	JobResponsibilityApproval_AC("Selected option should be accepted for 'Decision' field.</div>"),
	JobResponsibilityApproval_AR("Selected option is getting accepted for 'Decision' field.</div>"),
	JobResponsibilityApproval_SS("'Approve'"),
	// submit
	Submit_Button_SS("'E-Sign window'"),

	Click_EmpName_For_JobResponsibilityApproval1_DC(
			"Click on the above 'Employee Name' record for whom the job responsibility registration is initiated."),
	Like_EmployeeNameapp_DC(
			"Enter the above 'Employee Name' for whom the job responsibility registration is initiated in 'Like' field."),
	// submit at job responsibility approval
	Submitjobapp_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Job Responsibility:Registration Approval:Approve'.</div>"),
	Submitjobapp_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Job Responsibility:Registration Approval:Approve'.</div>"),
	Submitjobapp_SS("'E-Sign window'"),

	// esign- approval
	Esign_JobProceedapp_AC(
			"'Job Responsibility Registration Approved' Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_JobProceedapp_AR(
			"'Job Responsibility Registration Approved' Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	JobResponsibilityAudittrails_SS("'Job Responsibility Audit Trails'."),

	ClickJobResponsibilityAudittrails_AC(
			"'‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b>The ‘Events’ section should contain registration ‘Initiated’ transaction’s   with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals required’ should be read as ‘3’ and the ‘No. of Approvals completed’ should be read as 0.</div>"
					+ "<div><b>*</b>	All the particulars should be displayed in read only format.</div>"),

	ClickJobResponsibilityAudittrails_AR(
			"'‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen is getting  displayed.</div>"
					+ "<div><b>*</b‘Final Status’ is displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b>The ‘Events’ section  contains registration ‘Initiated’ transaction’s   with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals required’ are read as ‘3’ and the ‘No. of Approvals completed’ are read as 0.</div>"
					+ "<div><b>*</b>All the particulars is displayed in read only format.</div>"),

	Close_AuditTrails_JR_AC("'Job Responsibility Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_JR_AR("'Job Responsibility Audit Trails' screen is getting displayed.</div>"),

	// Job responsibility menu
	SYS_UserGroupsjobresacceptApproveMenu_DC("Click on 'Job Responsibility' submenu."),
	SYS_UserGroupsjobresacceptApproveMenu_AC("'Job Responsibility Acceptance' screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’ should be available.</div>"),

	SYS_UserGroupsjobresacceptApproveMenu_AR("'Job Responsibility Acceptance' screen is getting  displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Employee Name’, ‘Employee ID’,‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’, are available.</div>"),

	// Job responsibility menu
	SYS_UserGroupsjobresacceptadApproveMenu_DC("Click on 'JR AD Acceptance' submenu."),
	SYS_UserGroupsjobresacceptadApproveMenu_AC("'Job Responsibility Acceptance' screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’ should be available.</div>"),

	SYS_UserGroupsjobresacceptadApproveMenu_AR("'Job Responsibility Acceptance' screen is getting  displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Employee Name’, ‘Employee ID’,‘Initiated By’ and ‘Initiated On’ columns..</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, ‘Employee Name’, ‘Employee ID’, are available.</div>"),

	SYS_UserGroupsjobresacceptadApproveMenu_SS("'JR AD Acceptance'"),

	SYS_UserGroupsjobresacceptApproveMenu_SS("'Job Responsibility Acceptance'"),

	// SYS_UserGroupsjobresacceptadApproveMenu_SS("'Job Responsibility
	// Acceptance'"),

	SYS_UserGroupsAcceptMenu_DC("Click on 'Accept' menu."),
	SYS_UserGroupsAcceptMenu_AC("Assigned sub menus for the user under 'Accept' menu should be displayed.</div>"),
	SYS_UserGroupsAcceptMenu_AR("Assigned sub menus for user under 'Accept' menu are getting displayed."),
	SYS_UserGroupsAcceptMenu_SS("'Accept'"),

	SYS_UserGroupsADMenu_DC("Click on 'Accept' menu."),
	SYS_UserGroupsADMenu_AC("Assigned sub menus for the user under 'Accept' menu should be displayed.</div>"),
	SYS_UserGroupsADMenu_AR("Assigned sub menus for user under 'Accept' menu are getting displayed."),
	SYS_UserGroupsADMenu_SS("'Accept'"),

	// search by
	SearchBy_JobResponsibilityacceptApproval_AC(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID  should be available.</div>"),
	SearchBy_JobResponsibilityacceptApproval_AR(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID is available.</div>"),

	SYS_UserGroupsjobresacceptApproveclickemployyee_AC(
			"'‘Job Responsibility Registration Approval’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b>The ‘Events’ section should be updated with latest ‘Approved’ transaction’s details with ‘Username, Date & Time & Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as 3 and the ‘No. of Approvals Completed’ should be read as 1.</div>"),

	SYS_UserGroupsjobresacceptApproveclickemployyee_AR(
			"'‘Job Responsibility Registration Approval’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Approved’.</div>"
					+ "<div><b>*</b>The ‘Events’ section is updated with latest ‘Approved’ transaction’s details with ‘Username, Date & Time & Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ are read as 3 and the ‘No. of Approvals Completed’ should be read as 1.</div>"),

	SYS_UserGroupsjobresADApproveclickemployyee_AC(
			"'‘Job Responsibility Registration Approval’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b>The ‘Events’ section should be updated with latest ‘Approved’ transaction’s details with ‘Username, Date & Time & Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as 3 and the ‘No. of Approvals Completed’ should be read as 2.</div>"),

	SYS_UserGroupsjobresADApproveclickemployyee_AR(
			"'‘Job Responsibility Registration Approval’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the particulars entered at the time of Job Responsibility registration. .</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Approved’.</div>"
					+ "<div><b>*</b>The ‘Events’ section is updated with latest ‘Approved’ transaction’s details with ‘Username, Date & Time & Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ are read as 3 and the ‘No. of Approvals Completed’ should be read as 2.</div>"),

	Click_EmpName_For_JobResponsibilityacceptapproval_AC(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘3’ and the ‘No. of Approvals Completed’ should be read as ‘2’.</div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	Click_EmpName_For_JobResponsibilityacceptapproval_AR(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen is  getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section contains ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ read as ‘3’ and the ‘No. of Approvals Completed’ are read as ‘2’.</div>"
					+ "<div><b>*</b>All the particulars is displayed in read only format.</div>"),

	Click_EmpName_For_JobResponsibilityADDapproval_AC(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b>The screen should contain the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘3’ and the ‘No. of Approvals Completed’ should be read as ‘3’.</div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	Click_EmpName_For_JobResponsibilityADDapproval_AR(
			"'	‘Job Responsibility - Audit Trails Revision No.: 0 - Registration’ screen is  getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the details entered during registration..</div>"
					+ "<div><b>*</b>‘Final Status’ is displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section contains ‘Initiated and Approved’ transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ read as ‘3’ and the ‘No. of Approvals Completed’ are read as ‘3’.</div>"
					+ "<div><b>*</b>All the particulars is displayed in read only format.</div>"),

//		JobResponsibilityAudittrails_AC(
//				"‘Job Responsibility Audit Trails’ screen should be displayed.</div>"
//						+ "<div><b>*</b>The screen should contain ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
//						+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns with.</div>"
//						+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, ‘Employee ID’ should be available.</div>"),
//					
//		JobResponsibilityAudittrails_AD(
//				"‘Job Responsibility Audit Trails’ screen is displayed.</div>"
//						+ "<div><b>*</b>The screen contains ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
//						+ "<div><b>*</b>The screen contains ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns with.</div>"
//						+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, ‘Employee ID’, are available.</div>"),
//					

	JobResponsibilityAudittrails_AC("‘Job Responsibility Audit Trails’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, ‘Employee ID’, ‘Initiated Between’ and ’Department’ should be available.</div>"),

	JobResponsibilityAudittrails_AR("‘Job Responsibility Audit Trails’ screen is displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Record Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen contains ‘Employee Name’, ‘Employee ID’, ‘Department’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’ columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Employee Name’, ‘Employee ID’, ‘Initiated Between’ and ’Department’ are available.</div>"),

	// Report

	SYS_UserGroupsPrintMenu_AC("Assigned sub menus for the user under 'Print' menu should be displayed.</div>"),
	SYS_UserGroupsPrintMenu_AR("Assigned sub menus for user under 'Print' menu are getting displayed."),

	SYS_UserGroupsPrintMenu_DC("Click on 'Print' menu."),
	
	
	JobResponsibilityhistory_DC("Click on 'Job Responsibility History’ submenu'."),
		JobResponsibilityhistory_AC("‘Job Responsibility History’ report screen should be displayed.</div>"
				+ "<div><b>*</b>The screen should contain ‘Employee Name’, ‘Employee ID’ and ‘Department’ columns.</div>"
				+ "<div><b>*</b>The screen should contain ‘Total Records Count, Company Logo and Address’.</div>"
				+ "<div><b>*</b>The screen should contain ‘Generated By, Generated On, Page No and Note: This document has been generated electronically and is valid without signature.’ On bottom of the screen.</div>"
				+ "<div><b>*</b>The screen should contain ‘Total Records Count’.</div>"
				+ "<div><b>*</b>The screen should contain ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.</div>"
				+ "<div><b>*</b>The screen should contain ‘Find’ and ‘Next’ search fields.</div>"
				+ "<div><b>*</b>The screen should contain ‘Back’ button.</div>"
				+ "<div><b>*</b>List of Employee Name(s) for whom job responsibility is completed should be displayed.</div>"
				+ "<div><b>*</b> Address and Company Logo should be displayed on the header part of the report.</div>"
				+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details should be displayed in bottom of the report.</div>"
				+ "<div><b>*</b>The option to take the print should be available.</div>"),
	 
		JobResponsibilityhistory_AR("‘Job Responsibility History’ report screen is getting displayed.</div>"
				+ "<div><b>*</b>The screen contains ‘Employee Name’, ‘Employee ID’ and ‘Department’ columns.</div>"
				+ "<div><b>*</b>The screen contains ‘Total Records Count, Company Logo and Address’.</div>"
				+ "<div><b>*</b>The screen contains ‘Generated By, Generated On, Page No and Note: This document has been generated electronically and is valid without signature.’ On bottom of the screen.</div>"
				+ "<div><b>*</b>The screen contains ‘Total Records Count’.</div>"
				+ "<div><b>*</b>The screen contains ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.</div>"
				+ "<div><b>*</b>The screen contains ‘Find’ and ‘Next’ search fields.</div>"
				+ "<div><b>*</b>The screen contains ‘Back’ button..</div>"
				+ "<div><b>*</b>List of Employee Name(s) for whom job responsibility is completed is displayed.</div>"
				+ "<div><b>*</b> Address and Company Logo is displayed on the header part of the report.</div>"
				+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details is displayed in bottom of the report.</div>"
				+ "<div><b>*</b>The option to take the print are available..</div>"),

		JobResponsibilityhistory_SS("'Job Responsibility History.'"),
		
		JobResponsibilityemployee_DC("'Click on required ‘Employee Name’ hyperlink.'"),
		 
		JobResponsibilityemployee_SS("‘Employee Name'"),
	 
		JobResponsibilityrvesionlist_SS("'Job Responsibility Revision List.'"),;
		
		
		

	private final String jobResponsibilityStrings;

	JobResponsibilityStrings(String jobResponsibilityStrings) {

		this.jobResponsibilityStrings = jobResponsibilityStrings;

	}

	public String getJobResponsibilityStrings() {
		return jobResponsibilityStrings;
	}

}