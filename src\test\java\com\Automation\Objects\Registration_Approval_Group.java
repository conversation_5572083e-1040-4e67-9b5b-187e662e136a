package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;

public class Registration_Approval_Group extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";

	public Registration_Approval_Group() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration", enabled = false)
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration1")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration1")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprove")
	public Object[][] getGroupVerifyData() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 3, dataProvider = "groupApprove")
	public void groupRegistrationApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData4 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification1")
	public Object[][] getGroupApproveData() throws Exception {
		Object[][] obj = new Object[topicData4.getRowCount()][1];
		for (int i = 1; i <= topicData4.getRowCount(); i++) {
			HashMap<String, String> testData = topicData4.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "verifyModification1")
	public void VerifyModification(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check that the registered and approved Group is available for further modification at Group Modification scree")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check that the registered and approved Group is available for further modification at Group Modification scree");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.verifiModificationMod(testData);
		Logout.signOutPage();
	}
//-------------------------------------Return Registration----------------------------

	ExcelUtilUpdated topicData5 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration2")
	public Object[][] getGroupDataRe() throws Exception {
		Object[][] obj = new Object[topicData5.getRowCount()][1];
		for (int i = 1; i <= topicData5.getRowCount(); i++) {
			HashMap<String, String> testData = topicData5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 5, dataProvider = "groupRegistration2")
	public void groupRegistrationRe(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData7 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn")
	public Object[][] getGroupReturnnData() throws Exception {
		Object[][] obj = new Object[topicData7.getRowCount()][1];
		for (int i = 1; i <= topicData7.getRowCount(); i++) {
			HashMap<String, String> testData = topicData7.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 6, dataProvider = "groupReturn")
	public void groupRegistrationReturn(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Return Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Registration Return Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.group_Returns(testData);
		Group.group_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData8 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate1")
	public Object[][] getGroupReInitiateData() throws Exception {
		Object[][] obj = new Object[topicData8.getRowCount()][1];
		for (int i = 1; i <= topicData8.getRowCount(); i++) {
			HashMap<String, String> testData = topicData8.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 7, dataProvider = "groupReinitiate1")
	public void groupRegistrationReInitiate(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Re-initiation Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the Group Registration Re-initiation Flow and ensure its update in the Audit Trails.");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupReInitiate(testData);
		Group.groupReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData9 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1Approval")
	public Object[][] getGroupReApproveData() throws Exception {
		Object[][] obj = new Object[topicData9.getRowCount()][1];
		for (int i = 1; i <= topicData9.getRowCount(); i++) {
			HashMap<String, String> testData = topicData9.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 8, dataProvider = "groupRegistration1Approval")
	public void groupRegistrationReApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check that Group Registration Re-initiation Approval Flow and ensure its update in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check that Group Registration Re-initiation Approval Flow and ensure its update in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.group_Approval(testData);
		Group.group_ApprovalWithAuditTrials_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData10 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification2")
	public Object[][] getGroupReVerifyData() throws Exception {
		Object[][] obj = new Object[topicData10.getRowCount()][1];
		for (int i = 1; i <= topicData10.getRowCount(); i++) {
			HashMap<String, String> testData = topicData10.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "verifyModification2")
	public void VerifyModificationAgain(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check that the registered and approved Group is available for further modification at Group Modification scree")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check that the registered and approved Group is available for further modification at Group Modification scree");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.verifiModificationReAgain(testData);
		Logout.signOutPage();
	}

//	------------------------------------------------------RiTranswer ---------------------------------

	ExcelUtilUpdated topicData11 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration3")
	public Object[][] getGroupDataRi() throws Exception {
		Object[][] obj = new Object[topicData11.getRowCount()][1];
		for (int i = 1; i <= topicData11.getRowCount(); i++) {
			HashMap<String, String> testData = topicData11.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 10, dataProvider = "groupRegistration3")
	public void groupRegistrationRI(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData12 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn1")
	public Object[][] getGroupRITReturnnData() throws Exception {
		Object[][] obj = new Object[topicData12.getRowCount()][1];
		for (int i = 1; i <= topicData12.getRowCount(); i++) {
			HashMap<String, String> testData = topicData12.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 11, dataProvider = "groupReturn1")
	public void groupRegistrationRIReturn(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Return Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Registration Return Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.group_Returns(testData);
		Group.group_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	
	ExcelUtilUpdated topicData13 = new ExcelUtilUpdated(ExcelPath, "Sheet8");

	@DataProvider(name = "riTranswerA")
	public Object[][] getGroupRItranswerData() throws Exception {
		Object[][] obj = new Object[topicData13.getRowCount()][1];
		for (int i = 1; i <= topicData13.getRowCount(); i++) {
			HashMap<String, String> testData = topicData13.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 12, dataProvider = "riTranswerA")
	public void groupRITranswerA(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps checks the Group Registration Ri-Transfer Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps checks the Group Registration Ri-Transfer Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRITranswaer(testData);
		Group.groupRITranswaerWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData14 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate2")
	public Object[][] getGroupReInitiateDataRI() throws Exception {
		Object[][] obj = new Object[topicData14.getRowCount()][1];
		for (int i = 1; i <= topicData14.getRowCount(); i++) {
			HashMap<String, String> testData = topicData14.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 13, dataProvider = "groupReinitiate2")
	public void groupRegistrationReInitiateRi(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Re-initiation Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the Group Registration Re-initiation Flow and ensure its update in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupTRSID"),
				ConfigsReader.getPropValue("GroupTRSPwd"));

		epiclogin.plant1();

		Group.groupRITRInitiate(testData);
		Group.groupRIReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData15 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1ApprovalGB")
	public Object[][] getGroupApproveDataRi() throws Exception {
		Object[][] obj = new Object[topicData15.getRowCount()][1];
		for (int i = 1; i <= topicData15.getRowCount(); i++) {
			HashMap<String, String> testData = topicData15.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 14, dataProvider = "groupRegistration1ApprovalGB")
	public void groupRegistrationApproveRI(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Registration Re-initiation Approval Flow and ensure its update in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Registration Re-initiation Approval Flow and ensure its update in the Audit Trails.");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.group_TranswerApproval(testData);
		Group.group_TranswerApprovalWithAuditTrials_Yes(testData);

		Logout.signOutPage();
	}
//
//	@Test(priority = 9)
//	public void VerifyModificationAgain() {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Group Registration With AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Group Registration With AuditTrails");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		Group.verifiModificationReAgain();
//		Logout.signOutPage();
//	}

	// ------------------------------------Drop-----------------------------------------

	ExcelUtilUpdated topicData16 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration5")
	public Object[][] getGroupDataDrop() throws Exception {
		Object[][] obj = new Object[topicData16.getRowCount()][1];
		for (int i = 1; i <= topicData16.getRowCount(); i++) {
			HashMap<String, String> testData = topicData16.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 15, dataProvider = "groupRegistration5")
	public void groupRegistrationDrop(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

//	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");
//
//	@DataProvider(name = "groupReturn")
//	public Object[][] getGroupReturnData() throws Exception {
//		Object[][] obj = new Object[topicData2.getRowCount()][1];
//		for (int i = 1; i <= topicData2.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 3, dataProvider = "groupReturn")
//	public void VerifyModification(HashMap<String, String> testdata) {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Verify Group Modification");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		Group.verifiModification(testdata);
//		Logout.signOutPage();
//	}

	ExcelUtilUpdated topicData17 = new ExcelUtilUpdated(ExcelPath, "Drop");

	@DataProvider(name = "groupDrop")
	public Object[][] getGroupVerifyDataDp() throws Exception {
		Object[][] obj = new Object[topicData17.getRowCount()][1];
		for (int i = 1; i <= topicData17.getRowCount(); i++) {
			HashMap<String, String> testData = topicData17.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 16, dataProvider = "groupDrop")
	public void groupRegistrationDropDp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationDrop(testData);
		Group.groupRegistrationDropWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData18 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification3")
	public Object[][] getGroupverifyApproveData() throws Exception {
		Object[][] obj = new Object[topicData18.getRowCount()][1];
		for (int i = 1; i <= topicData18.getRowCount(); i++) {
			HashMap<String, String> testData = topicData18.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 17, dataProvider = "verifyModification3")
	public void againVerifyModification(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check that the registered and approved Group is available for further modification at Group Modification screen")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check that the registered and approved Group is available for further modification at Group Modification screen");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.verifiModificationReAgainRe(testData);
		Logout.signOutPage();
	}

	@AfterTest

	public void afterTest() {

		extent.flush();
		MyScreenRecorder.stopRecording();

		driver.quit();
	}
}
