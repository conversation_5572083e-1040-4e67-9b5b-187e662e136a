package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_SelfNomination;

/**
 * Verify Online IT Session without assessment for scheduled course and make
 * atleast one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, present by viewing Individual employee
 * report at each transaction starting from course session, also add at least
 * one user and check IER report and to be retrained and view IER for those
 * employees.
 */

public class UNSCH_IT_ON_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/UnscheduledScenariosData/UNSCH_IT_ON_WITHOUT_ASSESSMENT.xlsx";

	public UNSCH_IT_ON_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic  Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//		epiclogin.masterPlant();
//
//		InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//				Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//		Logout.signOutPage();

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
//
//		Logout.signOutPage();

	}

	// Test Method for Course  Registration
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));

//		epiclogin.masterPlant();
//
//		Initiate_Course.courseConfiguration_Reg(testData);

//		epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//		Logout.signOutPage();

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		Initiate_Course.course_Approval_AuditTrials_Yes(testData);
//
//		Logout.signOutPage();

	}



	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//			epiclogin.plant1();
//			trainer.TrainerModificationConfigurations(testData);
		trainer.trainer_Modification_AuditTrails(testData);
//			Logout.signOutPage();
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//			epiclogin.plant1();
//			trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//			Logout.signOutPage();

	}
	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData)
			throws InterruptedException {

		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Online_WithOutExam(testData);

//		CourseSession.courseSessionAuditTrails();
//		if (isReportedRequired == true) {
//			test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report for Course Session Under Approval");
//		}
//		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL, Constants.ITType);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//		if (isReportedRequired == true) {
//			test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report for Course Session Proposed Status");
//		}
//		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED, Constants.ITType);
//
		Logout.signOutPage();

	}

	// Test Method for Respond CourseInvitaion
	// (Accept)---------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseInvData = new ExcelUtilUpdated(ExcelPath, "CourseInvitiationAccept");

	@DataProvider(name = "AcceptCourseInvitation")
	public Object[][] getCIacceptData() throws Exception {
		Object[][] obj = new Object[CourseInvData.getRowCount()][1];
		for (int i = 1; i <= CourseInvData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseInvData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 6, dataProvider = "AcceptCourseInvitation", enabled = true)
	public void Respond_CourseInvitaion_Accept(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Respond CourseInvitaion (Accept)")

					.assignAuthor(CM_CourseSession.getCIAcceptTraineeID())

					.assignCategory("Respond CourseInvitaion (Accept)");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getCIAcceptTraineeID(),
				CM_CourseSession.CIAcceptTraineePsw);

		epiclogin.plant1();

		CourseInvitation.respondCourseInvitation(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report After CourseInvitation Accept")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report After CourseInvitation Accept");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_CIACCEPT,
				Constants.ITType);

		Logout.signOutPage();
	}

	// Test Method for Respond
	// CourseInvitaion(Reject)---------------------------------------------------------------------------------------------

	ExcelUtilUpdated CIData = new ExcelUtilUpdated(ExcelPath, "CourseInvitionReject");

	@DataProvider(name = "RejectCourseInvition")
	public Object[][] getCI_Reject_Data() throws Exception {
		Object[][] obj = new Object[CIData.getRowCount()][1];
		for (int i = 1; i <= CIData.getRowCount(); i++) {
			HashMap<String, String> testData = CIData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "RejectCourseInvition", enabled = true)
	public void Respond_CourseInvitaion_Reject(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond CourseInvitaion (Reject)")

					.assignAuthor(CM_CourseSession.getCIRejectTraineeID())

					.assignCategory("Respond CourseInvitaion (Reject)");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getCIRejectTraineeID(),
				CM_CourseSession.CIRejectTraineePsw);

		epiclogin.plant1();

		CourseInvitation.respondCourseInvitationReject(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report After CourseInvitation Reject")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report After CourseInvitation Reject");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.CIRejectEmployeeID, Constants.EMPLOYEESTATUS_AS_CIREJECT,
				Constants.ITType);

		Logout.signOutPage();
	}

	// Test Method for SelfNomination  Registration
	ExcelUtilUpdated SelfNominated = new ExcelUtilUpdated(ExcelPath, "SelfNominationRegApproval");

	@DataProvider(name = "SelfNominationReg")
	public Object[][] getSelfNominationData() throws Exception {
		Object[][] obj = new Object[SelfNominated.getRowCount()][1];
		for (int i = 1; i <= SelfNominated.getRowCount(); i++) {
			HashMap<String, String> testData = SelfNominated.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "SelfNominationReg", enabled = true)
	public void SelfNomination_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("SelfNomination Registration")

					.assignAuthor(CM_SelfNomination.selfNominatedTraineeID)

					.assignCategory("SelfNomination Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("selfNominatedTraineeID"),
				testData.get("selfNominatedTraineePsw"));

		epiclogin.plant1();

		// SelfNominate.selfNominationConfiguration(testData);

		SelfNominate.selfNomination_Registration(testData);

//			Logout.signOutPage();
		//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//			epiclogin.plant1();
		//
//			SelfNominate.SelfNominationRegistrationApproval_With_AuditTrails(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report After SelfNomination")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report After SelfNomination");
		}
		IERReport.individualEmployeeReport(CM_SelfNomination.selfNominatedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_SELFNOMINATED, Constants.ITType);
		Logout.signOutPage();

	}

	// Test Method for BatchFormation  Registration 
	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Online_NotResponded_Responded_SelfNominatedUsers(testData);

//			BatchFormation.proposeBatchFormationAuditTrail();
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Selected Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report For Selected Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Not Selected Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report For Not Selected Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED, Constants.ITType);

		// Logout.signOutPage();

	}
	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 10, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//		
//			epiclogin.plant1();

		RecordAttendance.recordAttendance_OnlineSession_1AdditionalUser(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Skipped Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Skipped Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID, Constants.EMPLOYEESTATUS_AS_SKIPPED,
				Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Absent Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Absent Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.AbsentEmployeeID, Constants.EMPLOYEESTATUS_AS_ABSENT,
				Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Present Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Present Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.ITType);
//			if (isReportedRequired == true) {
//			test = extent.createTest("Check Trainees at Course Session Screen")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Check Trainees at Course Session Screen");
//			}
//			CourseSession.verify_Employees_At_Coursesession();
		//
			Logout.signOutPage();
	}

	@AfterTest
	public void afterTest() {
		extent.flush();
		MyScreenRecorder.stopRecording();
		driver.quit();
	}
}
