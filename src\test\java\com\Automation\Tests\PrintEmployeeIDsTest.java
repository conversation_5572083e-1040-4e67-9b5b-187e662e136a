package com.Automation.Tests;

import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.learniqBase.PageInitializer;

/**
 * Test class to demonstrate printing Employee IDs one by one
 * Uses the XPath: //div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div
 */
public class PrintEmployeeIDsTest extends PageInitializer {

    public PrintEmployeeIDsTest() {
        super(ConfigsReader.getPropValue("applicationUrl"));
    }

    /**
     * Test method to print Employee IDs without reporting
     */
    @Test(priority = 1, description = "Print Employee IDs one by one - Simple Method")
    public void testPrintEmployeeIDsSimple() {
        try {
            // Navigate to the page where employee table is displayed
            // You may need to add navigation steps here based on your application flow
            
            // Call the method to print Employee IDs
            printEmployeeIDsOneByOne();
            
        } catch (Exception e) {
            System.out.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test method to print Employee IDs with ExtentReports integration
     */
    @Test(priority = 2, description = "Print Employee IDs one by one - With Reporting")
    public void testPrintEmployeeIDsWithReporting() {
        try {
            // Navigate to the page where employee table is displayed
            // You may need to add navigation steps here based on your application flow
            
            // Call the method to print Employee IDs with reporting
            printEmployeeIDsWithReporting(
                "Retrieve and print all Employee IDs from the employee table",
                "All Employee IDs should be retrieved and printed successfully",
                "Employee IDs retrieved and printed one by one",
                "EmployeeIDs_Screenshot"
            );
            
        } catch (Exception e) {
            System.out.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Alternative method using direct XPath approach
     * This method shows how to use the XPath directly in your existing test methods
     */
    @Test(priority = 3, description = "Print Employee IDs - Direct XPath Approach")
    public void testPrintEmployeeIDsDirectApproach() {
        try {
            // Your XPath
            String employeeIDXPath = "//div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div";
            
            // Find all Employee ID elements
            java.util.List<org.openqa.selenium.WebElement> employeeIDElements = 
                driver.findElements(org.openqa.selenium.By.xpath(employeeIDXPath));
            
            System.out.println("=== Direct XPath Approach ===");
            System.out.println("Total Employee IDs found: " + employeeIDElements.size());
            
            // Print each Employee ID
            for (int i = 0; i < employeeIDElements.size(); i++) {
                try {
                    String employeeID = employeeIDElements.get(i).getText().trim();
                    System.out.println("Employee ID " + (i + 1) + ": " + employeeID);
                } catch (Exception e) {
                    System.out.println("Error reading Employee ID at index " + (i + 1) + ": " + e.getMessage());
                }
            }
            
            System.out.println("==============================");
            
        } catch (Exception e) {
            System.out.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
