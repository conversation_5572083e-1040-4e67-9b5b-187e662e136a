package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_Course;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.learniqObjects.SYS_JobResponsibility;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.Reports;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.aventstack.extentreports.Status;

public class JRreport extends OQActionEngine {
	String RegAuthorizedDeputyValue = "";

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Reports']")
	WebElement courseManagerReportsMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Course Sessions Report (Complete)']")
	WebElement courseSessionReportComplete;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Course List Report']")
	WebElement courseListReport;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Date Wise Course Sessions']")
	WebElement dateWiseCourseSession;
	@FindBy(id = "ReportViewerControl_ctl05_ctl03_ctl00")
	WebElement courseSessionSearch;
	@FindBy(id = "ReportViewerControl_ctl05_ctl03_ctl01")
	WebElement find;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[2]//input")
	WebElement courseSessionNameNullCheckBox;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl05_cbNull']")
	WebElement UniqueCodeNullCheckBox;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl03_cbNull']")
	WebElement UniqueCodeNullCheckBoxEmployeeName;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl03_txtValue']")
	WebElement TextboxEmployeeName;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl05_txtValue']")
	WebElement UniqueCodeSearchBox;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[1]//input")
	WebElement courseSessionNameTextBox;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl00']")
	WebElement viewReport;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]//td[1]//div[1]//a")
	WebElement courseSessionName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement employeeName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[4]/div[1]/div[1]")
	WebElement employeeStatus;
	@FindBy(xpath = "(//div[contains(text(),'Training Certificate')])[2]")
	WebElement trainingCertificate;
	@FindBy(xpath = "(//div[contains(text(),'To Be Re-trained')])[1]")
	WebElement traineeStatus;
	@FindBy(xpath = "//div[contains(text(),'Qualified')]")
	WebElement result;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[2]/td[2]/div[1]/select[1]")
	WebElement trainingMethod;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[2]/td[2]/div[1]/select[1]/option[2]")
	WebElement trainingMethodAsDocumentReading;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement documentReadingSessionName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[6]/td[1]/div[1]/a[1]/div[1]")
	WebElement documentReadingTrainingCertificate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[11]/td/div/a/div")
	WebElement ClassroomTypeTrainingCertificate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManager;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Individual Employee Report (All)']")
	WebElement individualEmployeeReportTC;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement user1;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[4]/td[1]/div[1]/a[1]/div[1]")
	WebElement user2;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[6]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement status1;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[6]/td[3]/table[1]/tbody[1]/tr[4]/td[5]/div[1]/div[1]")
	WebElement status2;
	@FindBy(xpath = "//table/tbody[1]/tr[1]/td[1]//span[@id=\"ReportViewerControl_ctl05_ctl00_Last_ctl01_ctl00\"]")
	WebElement arrowButton;
	@FindBy(xpath = "//table[1]/tbody[1]/tr/td[1]/div[1]/a[1]/div[1]")
	WebElement CourseNamehyperlink;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement CourseNamehyperlink2;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[7]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement Status1;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[5]/div")
	WebElement Status2;
	@FindBy(xpath = "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement TraineeReport;
	@FindBy(xpath = "//ul[@id='TMS_Document Manager']//li//a[text()='Audit Trails']")
	WebElement DocumentManagerAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Document Manager']//li//a[text()='Reports']")
	WebElement documentManagerReports;
	@FindBy(xpath = "//a[@id='TMS_Document Manager_Reports_SUBMEN05']")
	WebElement DocumentManagerdocumentlist;
	@FindBy(xpath = "//a[@id='TMS_Document Manager_Reports_SUBMEN06']")
	WebElement DocumentManagerdocumenttobereviewwedlist;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Print')]")
	WebElement usergroupsPrint;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN94_SUBMEN11']")
	WebElement jobresponsibilityhistory;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/div[1]")
	WebElement reportdocumentname;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/div[1]")
	WebElement reportdocumentName;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement reportdocumentuniqueCode;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement reportdocumentuniquecode;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement dreportdocumentVersionNo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement dreportdocumentVersionNum;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[4]/div[1]/div[1]")
	WebElement dreportdocumentEffectivefrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[4]/div[1]/div[1]")
	WebElement dreportdocumentEffectiveFrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement dreportdocumentNextReview;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement dreportdocumentNextreview;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Document Manager']")
	WebElement documentManagermenu;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement highLightScreenTitleDocumentList;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement highLightScreenTitleDocumentTobeReviewed;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]/div[1]/div[1]/span[1]")
	WebElement reportemployeeName;
	@FindBy(xpath = "//span[@class='Aca7ac9f112a246e09f194a1c9a32710d64']")
	WebElement employeeNameclick;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[2]")
	WebElement reportemployeeID;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement reportDepartment;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]/div[1]/div[1]")
	WebElement reportrevNo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement Validfrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement reportvalidTo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[1]")
	WebElement employeNamereport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement employeIDreport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[4]/td[2]/div[1]/div[1]")
	WebElement designationreport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[5]/td[2]/div[1]/div[1]")
	WebElement Departmentreport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[6]/td[2]/div[1]/div[1]")
	WebElement Qualificationreport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[7]/td[2]/div[1]/div[1]")
	WebElement previousexperiencereport;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[8]/td[2]/div[1]/div[1]")
	WebElement ReportingTo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[9]/td[2]/div[1]/div[1]")
	WebElement reportjobdescription;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[10]/td[2]/div[1]/div[1]")
	WebElement reportrevno;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[11]/td[2]/div[1]/div[1]")
	WebElement reportauthorizeddeputy;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[3]/table[1]/tbody[1]/tr[12]/td[2]/div[1]/div[1]")
	WebElement reportexternalcertificates;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement reportjobinitiated;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[4]/td[2]/div[1]/div[1]")
	WebElement reportjobapproved;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[5]/td[2]/div[1]/div[1]")
	WebElement reportjobaccepted;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[6]/td[2]/div[1]/div[1]")
	WebElement reportjobacceptedauthorizeddeputy;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[3]/child::td[3]")
	WebElement reportjobinitiateddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[4]/child::td[3]")
	WebElement reportjobapproveddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[5]/child::td[3]")
	WebElement reportjobaccepteddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[6]/child::td[3]")
	WebElement reportjobacceptedauthorizeddeputydate;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement jobresscreen;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement jobresprevsionlistascreen;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[2]/td[3]/div[1]/div[1]")
	WebElement jobresponsiblityscreen;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyEmpName;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifyEmpID;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifyDept;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifyRole;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyCSName1;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionInitiatedDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionStrtDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifySessionEndDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyCSName2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionInitiatedDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionStrtDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifySessionEndDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[1]/div/div")
	WebElement VerifyCSName3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionStrtDate3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionEndDate3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td/div/div")
	WebElement VerifyTraineeStatus;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[5]/td/div/div")
	WebElement VerifyShortTermEval;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[7]/td/div")
	WebElement VerifyLongTermEval;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[9]/td/div/div")
	WebElement VerifyfeedbackReq;
	@FindBy(xpath = "//span[text()='Trainee Report']")
	WebElement TraineeReportScreen;

	@FindBy(xpath = "//span[text()='Training Certificate']")
	WebElement TCscreenName;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[2]/td[2]/div/div")
	WebElement VerifyEmpNameTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifyEmpNameIDTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[4]/td[2]")
	WebElement VerifyEmpNameDeptTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[5]/td[2]/div/div")
	WebElement VerifyEmpNameDesignationTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[6]/td[2]/div/div")
	WebElement VerifyCourseTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[8]/td[2]/div/div")
	WebElement VerifyEvaluationMethod;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[9]/td[2]/div/div")
	WebElement VerifyCourseDateTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[10]/td[2]/div/div")
	WebElement VerifyTrainingMethodTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[7]/td[2]/div/div")
	WebElement VerifyCourseTypeTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[1]/div/div")
	WebElement VerifytopicNameTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifytopicUCTC;

	public JRreport() {
	}

	public void jobResponsibilityReport() {
		String RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		String RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		// String RegEmployeeName ="userSANG";
		String employee_ID = SSO_UserRegistration.getEmployeeID();
		// String department = MDM_Deparment.getDepartmentName();
		// RegAuthorizedDeputyValue = AuthorizedDeputy;
//		RegQualificationValue = Qualificationvalue;
//		RegPreviousExperienceVal = PreviousExperienceval;
//		RegExternalCertificates = getQueryDocumentName();
//		RegsubGroupNameValue = SubGroupNameValue;
//		RegJobResponsibilityValue = JobRes;
		String RegQualificationValue = SYS_JobResponsibility.getQualificationValue();
		String RegJobResponsibilityValue = SYS_JobResponsibility.getJobRes();
		String RegPreviousExperienceVal = SYS_JobResponsibility.getPreviousExperienceVal();
		String RegExternalCertificates = SYS_JobResponsibility.getQueryDocumentName();

		// String RegReportingToValue = "--";
//		String RegDesignationValue = "Junior SQA Engineer-L105";
//		String RegEmployeeName = "userHGO.userHGO";
//		String employee_ID = "userHGO";
//		String department = "NEWDEPT33-12Apr_M1";
		// String RegAuthorizedDeputyValue = firstName;
//		String iniatedby = "Shyam22.Shyam22";
		// String approvedby = "Shyam2.shyam2";
//		String acceptedby = "sessionnew61.sessionnew61";
//		String acceptedaddeputy = "laxmi4.laxmi4";

//String employeeName = SSO_UserRegistration.getEmployeeName();
		// String employee_ID = SSO_UserRegistration.getEmployeeID();
//	String department = SSO_UserRegistration.getDepartmentName();

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, usergroupsPrint, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(usergroupsPrint);
		clickAndWaitforNextElement(usergroupsPrint, jobresponsibilityhistory,
				JobResponsibilityStrings.SYS_UserGroupsPrintMenu_DC.getJobResponsibilityStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AR.getCommonStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		clickForReports(jobresponsibilityhistory,
				JobResponsibilityStrings.JobResponsibilityhistory_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		JavascriptExecutor js = (JavascriptExecutor) driver;
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		click2(button, Reports.Click_ToggleBtn_DC.getReports(), Reports.Click_ToggleBtnforjobres_AC.getReports(),
				Reports.Click_ToggleBtnforjobres_AR.getReports(), Reports.Click_ToggleBtn_SS.getReports());
		TimeUtil.mediumWait();
		click2(UniqueCodeNullCheckBoxEmployeeName, Reports.UnselectEmployeename_DC.getReports(),
				Reports.UnselectEmployeename_AC.getReports(), Reports.UnselectEmployeename_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());
		sendKeys2(TextboxEmployeeName, Reports.Employeenametextbox_DC.getReports(), RegEmployeeName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.mediumWait();
		click2(documentReadingSessionName,
				JobResponsibilityStrings.JobResponsibilityemployee_DC.getJobResponsibilityStrings(),
				Reports.click_employeename_AC.getReports(), Reports.click_employeename_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilityrvesionlist_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		click2(employeeName, Reports.click_revisonno_DC.getReports(), Reports.click_revisonno_AC.getReports(),
				Reports.click_revisonno_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		scrollToViewElement(employeNamereport);
		scrollToViewElement(reportjobacceptedauthorizeddeputy);

		switchToDefaultContent(driver);

	}
}