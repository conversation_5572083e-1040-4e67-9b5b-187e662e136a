package com.Automation.learniqObjects;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.Reports;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class Print_JobReponsibility extends OQActionEngine {
	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[11]/td/div/a/div")
	WebElement ClassroomTypeTrainingCertificate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManager;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl00']")
	WebElement viewReport;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl03_cbNull']")
	WebElement UniqueCodeNullCheckBoxEmployeeName;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl03_txtValue']")
	WebElement TextboxEmployeeName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]//span")
	WebElement documentReadingSessionName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]//span")
	WebElement employeeName;
	
	@FindBy(xpath = "//table/tbody/tr[4]/td[1]/div/a/div/div/div/span")
	WebElement employeeName2;
	
	@FindBy(xpath = "//table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement ValidFrom;
	@FindBy(xpath = "//table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement ValidTo;
	
	@FindBy(xpath = "//table/tbody/tr[4]/td[3]/table/tbody/tr[4]/td[2]/div/div")
	WebElement ValidFrom2;
	@FindBy(xpath = "//table/tbody/tr[4]/td[3]/table/tbody/tr[4]/td[3]/div/div")
	WebElement ValidTo2;
	
	
	
	
	@FindBy(xpath = "//div[text()='Job Responsibility History']")
	WebElement titleJRH;
	@FindBy(xpath = "//div[text()='Job Responsbility Revision List']")
	WebElement titleJRRevList;
	
	
	
	@FindBy(xpath = "//div[text()='Job Responsibility']")
	WebElement jr;
	
	
	
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Reports']//li//a[text()='Individual Employee Report (All)']")
	WebElement individualEmployeeReportTC;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement user1;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[4]/td[1]/div[1]/a[1]/div[1]")
	WebElement user2;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[6]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement status1;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[6]/td[3]/table[1]/tbody[1]/tr[4]/td[5]/div[1]/div[1]")
	WebElement status2;
	@FindBy(xpath = "//table/tbody[1]/tr[1]/td[1]//span[@id=\"ReportViewerControl_ctl05_ctl00_Last_ctl01_ctl00\"]")
	WebElement arrowButton;
	@FindBy(xpath = "//table[1]/tbody[1]/tr/td[1]/div[1]/a[1]/div[1]")
	WebElement CourseNamehyperlink;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement CourseNamehyperlink2;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[7]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement Status1;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[5]/div")
	WebElement Status2;
	@FindBy(xpath = "//table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement TraineeReport;
	@FindBy(xpath = "//ul[@id='TMS_Document Manager']//li//a[text()='Audit Trails']")
	WebElement DocumentManagerAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Document Manager']//li//a[text()='Reports']")
	WebElement documentManagerReports;
	@FindBy(xpath = "//a[@id='TMS_Document Manager_Reports_SUBMEN05']")
	WebElement DocumentManagerdocumentlist;
	@FindBy(xpath = "//a[@id='TMS_Document Manager_Reports_SUBMEN06']")
	WebElement DocumentManagerdocumenttobereviewwedlist;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Print')]")
	WebElement usergroupsPrint;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN94_SUBMEN11']")
	WebElement jobresponsibilityhistory;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/div[1]")
	WebElement reportdocumentname;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/div[1]")
	WebElement reportdocumentName;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement reportdocumentuniqueCode;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement reportdocumentuniquecode;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement dreportdocumentVersionNo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement dreportdocumentVersionNum;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[4]/div[1]/div[1]")
	WebElement dreportdocumentEffectivefrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[4]/div[1]/div[1]")
	WebElement dreportdocumentEffectiveFrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement dreportdocumentNextReview;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement dreportdocumentNextreview;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Document Manager']")
	WebElement documentManagermenu;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement highLightScreenTitleDocumentList;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement highLightScreenTitleDocumentTobeReviewed;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]/div[1]/div[1]/span[1]")
	WebElement reportemployeeName;
	@FindBy(xpath = "//span[@class='Aca7ac9f112a246e09f194a1c9a32710d64']")
	WebElement employeeNameclick;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[2]")
	WebElement reportemployeeID;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[5]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement reportDepartment;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]/div[1]/div[1]")
	WebElement reportrevNo;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[2]/div[1]/div[1]")
	WebElement Validfrom;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[3]/td[3]/div[1]/div[1]")
	WebElement reportvalidTo;
	@FindBy(xpath = "//*[text()='Employee Name']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement employeNamereport;
	@FindBy(xpath = "//*[text()='Employee ID']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement employeIDreport;
	@FindBy(xpath = "//*[text()='Designation']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement designationreport;
	@FindBy(xpath = "//*[text()='Department']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement Departmentreport;
	@FindBy(xpath = "//*[text()='Qualification']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement Qualificationreport;
	@FindBy(xpath = "//*[text()='Previous Experience']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement previousexperiencereport;
	@FindBy(xpath = "//*[text()='Reporting To']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement ReportingTo;
	@FindBy(xpath = "//*[text()='Job Description']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement reportjobdescription;
	@FindBy(xpath = "//*[text()='Revision No.']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement reportrevno;
	@FindBy(xpath = "//*[text()='Authorized Deputy']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement reportauthorizeddeputy;
	@FindBy(xpath = "//*[text()='External Certificates']/parent::*/parent::td/following-sibling::td/div/div")
	WebElement reportexternalcertificates;
	@FindBy(xpath = "//*[text()='Initiated']/parent::*/parent::td/following-sibling::td[1]/div/div")
	WebElement reportjobinitiated;
	@FindBy(xpath = "//*[text()='Approved']/parent::*/parent::td/following-sibling::td[1]/div/div")
	WebElement reportjobapproved;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[5]/td[2]/div[1]/div[1]")
	WebElement reportjobaccepted;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[2]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[4]/td[3]/table[1]/tbody[1]/tr[6]/td[2]/div[1]/div[1]")
	WebElement reportjobacceptedauthorizeddeputy;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[3]/child::td[3]")
	WebElement reportjobinitiateddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[4]/child::td[3]")
	WebElement reportjobapproveddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[5]/child::td[3]")
	WebElement reportjobaccepteddate;
	@FindBy(xpath = "//div[text()='Activity On']/parent::div/parent::td/parent::tr/parent::tbody/child::tr[6]/child::td[3]")
	WebElement reportjobacceptedauthorizeddeputydate;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement jobresscreen;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[3]/td[5]/div[1]/div[1]")
	WebElement jobresprevsionlistascreen;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/div[1]/table[1]/tbody[1]/tr[5]/td[3]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/table[1]/tbody[1]/tr[2]/td[3]/div[1]/div[1]")
	WebElement jobresponsiblityscreen;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyEmpName;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifyEmpID;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifyDept;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifyRole;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyCSName1;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionInitiatedDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionStrtDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifySessionEndDate;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement VerifyCSName2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionInitiatedDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionStrtDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement VerifySessionEndDate2;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[1]/div/div")
	WebElement VerifyCSName3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifySessionStrtDate3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[3]/div/div")
	WebElement VerifySessionEndDate3;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td/div/div")
	WebElement VerifyTraineeStatus;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[5]/td/div/div")
	WebElement VerifyShortTermEval;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[7]/td/div")
	WebElement VerifyLongTermEval;
	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[9]/td/div/div")
	WebElement VerifyfeedbackReq;
	@FindBy(xpath = "//span[text()='Trainee Report']")
	WebElement TraineeReportScreen;

	@FindBy(xpath = "//span[text()='Training Certificate']")
	WebElement TCscreenName;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[2]/td[2]/div/div")
	WebElement VerifyEmpNameTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifyEmpNameIDTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[4]/td[2]")
	WebElement VerifyEmpNameDeptTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[5]/td[2]/div/div")
	WebElement VerifyEmpNameDesignationTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[6]/td[2]/div/div")
	WebElement VerifyCourseTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[8]/td[2]/div/div")
	WebElement VerifyEvaluationMethod;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[9]/td[2]/div/div")
	WebElement VerifyCourseDateTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[10]/td[2]/div/div")
	WebElement VerifyTrainingMethodTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[7]/td[2]/div/div")
	WebElement VerifyCourseTypeTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[1]/div/div")
	WebElement VerifytopicNameTC;

	@FindBy(xpath = "/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[5]/td[3]/div/div[1]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[4]/td[3]/table/tbody/tr[3]/td[2]/div/div")
	WebElement VerifytopicUCTC;

	public void jobResponsibilityReport(String stage) {

		click2(menu,  CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager,  CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(userGroups,  CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", usergroupsPrint);
		clickAndWaitforNextElement(usergroupsPrint, jobresponsibilityhistory,
				JobResponsibilityStrings.SYS_UserGroupsPrintMenu_DC.getJobResponsibilityStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AR.getCommonStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		clickForReports(jobresponsibilityhistory,
				JobResponsibilityStrings.JobResponsibilityhistory_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
	//	waitForElementVisibile(titleJRH);
		JavascriptExecutor js = (JavascriptExecutor) driver;
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		highLightElement(driver, highLightScreenTitleDocumentTobeReviewed, "Topic Approval Intiation", test);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		click2(button, "Click on Toggle drop down", "", "", "");
		TimeUtil.mediumWait();
		click2(UniqueCodeNullCheckBoxEmployeeName, "Uncheck Employee name check box", "", "","");

		sendKeys2(TextboxEmployeeName, Reports.Employeenametextbox_DC.getReports(), 
				SYS_JobResponsibility.RegEmployeeName,
			//	"VijayDCOM.VijayDCOM",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());

		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.longwait();
		//waitForElementVisibile(titleJRH);
		//highLightElement(driver, jobresscreen, "Topic Approval Intiation", test);
//		verifyExactCaption(reportemployeeName, RegEmployeeName, "documentName,");
//		verifyExactCaption(reportemployeeID, employee_ID, "UniqueCode");
//		verifyExactCaption(reportDepartment, department, "VersionNum");
		waitForElementVisibile(documentReadingSessionName);
		
		verifyExactCaption(documentReadingSessionName, SYS_JobResponsibility.RegEmployeeName, "Employee Name");
//		verifyExactCaption(documentReadingSessionName, SSO_UserRegistration.getEmployeeID(), "Employee ID");
//		verifyExactCaption(documentReadingSessionName, SSO_UserProductModuleAssignment.getDepartmentName(), "Department Name");
		
		click2(documentReadingSessionName,
				JobResponsibilityStrings.JobResponsibilityemployee_DC.getJobResponsibilityStrings(),
				Reports.click_employeename_AC.getReports(), Reports.click_employeename_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilityrvesionlist_SS.getJobResponsibilityStrings());
		TimeUtil.longwait();
//		highLightElement(driver, jobresprevsionlistascreen, "Topic Approval Intiation", test);
//		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(employeeName, Constants.REVISION_NO_AS_0, "Revision No");
		validateMultipleDateFormats(ValidFrom);
		verifyExactCaption(ValidTo, Constants.VALID_TO, "Valid To");
//		// verifyExactCaption(Validfrom, DocEffDate, "EffectiveFrom");
//		highLightElement(driver, Validfrom, "Topic Approval Intiation", test);
//		String validto = Constants.VALID_TO;
//		verifyExactCaption(reportvalidTo, validto, "Nextreview");
		click2(employeeName, Reports.click_revisonno_DC.getReports(), Reports.click_revisonno_AC.getReports(),
				Reports.click_revisonno_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		TimeUtil.longwait();
		//waitForElementVisibile(jr);
//		highLightElement(driver, jobresponsiblityscreen, "Topic Approval Intiation", test);
		verifyExactCaption(employeNamereport, SYS_JobResponsibility.RegEmployeeName, "documentName,");
		verifyExactCaption(employeIDreport, SYS_JobResponsibility.RegEmployeeID, "UniqueCode");
		verifyExactCaption(designationreport, SYS_JobResponsibility.RegDesignationValue, "VersionNum");
		verifyExactCaption(Departmentreport, SSO_UserProductModuleAssignment.getDepartmentName(), "documentName,");
		verifyExactCaption(Qualificationreport, SYS_JobResponsibility.RegQualificationValue, "UniqueCode");
		verifyExactCaption(previousexperiencereport, SYS_JobResponsibility.PreviousExperienceval, "VersionNum");
		// verifyExactCaption(ReportingTo, RegReportingToValue, "SYS_JobResponsibility,");
		verifyExactCaption(reportjobdescription, SYS_JobResponsibility.RegJobResponsibilityValue, "UniqueCode");
		verifyExactCaption(reportrevno, Constants.REVISION_NO_AS_0, "VersionNum");
		js.executeScript("arguments[0].scrollIntoView();", reportauthorizeddeputy);
		verifyExactCaption(reportauthorizeddeputy, Constants.VALID_TO, "documentName,");
		verifyExactCaption(reportexternalcertificates, SYS_JobResponsibility.QueryDocName, "UniqueCode");
		if(stage.equals("Re_Initiate")){
				verifyExactCaption(reportjobinitiated, SYS_JobResponsibility.Re_Int_Name, "VersionNum");	
		}
		else {
		verifyExactCaption(reportjobinitiated, SYS_JobResponsibility.InitiatorName, "VersionNum");
		}
		verifyExactCaption(reportjobapproved, SYS_JobResponsibility.ApproverName, "documentName,");
//		highLightElement(driver, reportjobapproveddate, "Topic Approval Intiation", test);
//		verifyExactCaption(reportjobaccepted, UserAcceptanceFullName, "UniqueCode");
//		highLightElement(driver, reportjobaccepteddate, "Topic Approval Intiation", test);
//		verifyExactCaption(reportjobacceptedauthorizeddeputy, ADFullName, "VersionNum");
//		highLightElement(driver, reportjobacceptedauthorizeddeputydate, "Topic Approval Intiation", test);

		switchToDefaultContent(driver);

	}

	
	public void jobResponsibilityReport_MOD_UA_AD_NO(String stage) {

		click2(menu,  CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager,  CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(userGroups,  CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", usergroupsPrint);
		clickAndWaitforNextElement(usergroupsPrint, jobresponsibilityhistory,
				JobResponsibilityStrings.SYS_UserGroupsPrintMenu_DC.getJobResponsibilityStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsPrintMenu_AR.getCommonStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		clickForReports(jobresponsibilityhistory,
				JobResponsibilityStrings.JobResponsibilityhistory_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();
		JavascriptExecutor js = (JavascriptExecutor) driver;
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		highLightElement(driver, highLightScreenTitleDocumentTobeReviewed, "Topic Approval Intiation", test);
		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		click2(button, "Click on Toggle drop down", "", "", "");
		TimeUtil.mediumWait();
		click2(UniqueCodeNullCheckBoxEmployeeName, "Uncheck Employee name check box", "", "","");

		sendKeys2(TextboxEmployeeName, Reports.Employeenametextbox_DC.getReports(), 
				SYS_JobResponsibility.RegEmployeeName,
			//	"VijayDCOM.VijayDCOM",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.JobResponsibilityhistory_SS.getJobResponsibilityStrings());

		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		TimeUtil.longwait();
		//waitForElementVisibile(titleJRH);
		//highLightElement(driver, jobresscreen, "Topic Approval Intiation", test);
//		verifyExactCaption(reportemployeeName, RegEmployeeName, "documentName,");
//		verifyExactCaption(reportemployeeID, employee_ID, "UniqueCode");
//		verifyExactCaption(reportDepartment, department, "VersionNum");
		waitForElementVisibile(documentReadingSessionName);
		
		verifyExactCaption(documentReadingSessionName, SYS_JobResponsibility.RegEmployeeName, "Employee Name");
//		verifyExactCaption(documentReadingSessionName, SSO_UserRegistration.getEmployeeID(), "Employee ID");
//		verifyExactCaption(documentReadingSessionName, SSO_UserProductModuleAssignment.getDepartmentName(), "Department Name");
		
		click2(documentReadingSessionName,
				JobResponsibilityStrings.JobResponsibilityemployee_DC.getJobResponsibilityStrings(),
				Reports.click_employeename_AC.getReports(), Reports.click_employeename_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilityrvesionlist_SS.getJobResponsibilityStrings());
		TimeUtil.longwait();
//		highLightElement(driver, jobresprevsionlistascreen, "Topic Approval Intiation", test);
//		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(employeeName2, Constants.REVISION_NO_AS_1, "Revision No");
		validateMultipleDateFormats(ValidFrom2);
		verifyExactCaption(ValidTo2, Constants.VALID_TO, "Valid To");
		click2(employeeName2,"Click on Revision 1", Reports.click_revisonno_AC.getReports(),
				Reports.click_revisonno_AR.getReports(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		TimeUtil.longwait();
		verifyExactCaption(employeNamereport, SYS_JobResponsibility.RegEmployeeName, "documentName,");
		verifyExactCaption(employeIDreport, SYS_JobResponsibility.RegEmployeeID, "UniqueCode");
		verifyExactCaption(designationreport, SYS_JobResponsibility.RegDesignationValue, "VersionNum");
		verifyExactCaption(Departmentreport, SSO_UserProductModuleAssignment.getDepartmentName(), "documentName,");
		verifyExactCaption(Qualificationreport, SYS_JobResponsibility.RegQualificationValue, "UniqueCode");
		verifyExactCaption(previousexperiencereport, SYS_JobResponsibility.PreviousExperienceval, "VersionNum");
		// verifyExactCaption(ReportingTo, RegReportingToValue, "SYS_JobResponsibility,");
		verifyExactCaption(reportjobdescription, SYS_JobResponsibility.RegJobResponsibilityValue, "UniqueCode");
		verifyExactCaption(reportrevno, Constants.REVISION_NO_AS_1, "VersionNum");
		js.executeScript("arguments[0].scrollIntoView();", reportauthorizeddeputy);
		verifyExactCaption(reportauthorizeddeputy, Constants.VALID_TO, "documentName,");
		verifyExactCaption(reportexternalcertificates, SYS_JobResponsibility.QueryDocName, "UniqueCode");
		if(stage.equals("Re_Initiate")){
				verifyExactCaption(reportjobinitiated, SYS_JobResponsibility.Re_Int_Name, "VersionNum");	
		}
		else {
		verifyExactCaption(reportjobinitiated, SYS_JobResponsibility.Mod_Initiator_Name, "VersionNum");
		}
		verifyExactCaption(reportjobapproved, SYS_JobResponsibility.ApproverName, "documentName,");
//		highLightElement(driver, reportjobapproveddate, "Topic Approval Intiation", test);
//		verifyExactCaption(reportjobaccepted, UserAcceptanceFullName, "UniqueCode");
//		highLightElement(driver, reportjobaccepteddate, "Topic Approval Intiation", test);
//		verifyExactCaption(reportjobacceptedauthorizeddeputy, ADFullName, "VersionNum");
//		highLightElement(driver, reportjobacceptedauthorizeddeputydate, "Topic Approval Intiation", test);

		switchToDefaultContent(driver);

	}
	
}
