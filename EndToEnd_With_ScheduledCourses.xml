<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="EndToEnd_With_ScheduledCourses">


	<!--<PERSON><PERSON><PERSON>-->

	<!--<test name="IT_OFF_WITHOUT_ASSESSMENT_VERBAL_EVALUATION">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_OFF_WITHOUT_ASSESSMENT_VERBAL_EVALUATION" />
		</classes>
	</test>

	<test name="IT_ON_WITH_ASSESSMENT_MQA_YES">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITH_ASSESSMENT_MQA_YES" />
		</classes>
	</test>
	<test name="IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION" />
		</classes>
	</test>

	<test name="RE_ON_WITH_ASSESSMENT_MQA_YES">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.RE_ON_WITH_ASSESSMENT_MQA_YES" />
		</classes>
	</test>
	
	Swetha


	<test name="RE_ON_WITHOUT_ASSESSMENT">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.RE_ON_WITHOUT_ASSESSMENT" />
		</classes>
	</test>


	<test name="RE_OFF_WITHOUT_ASSESSMENT">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.RE_OFF_WITHOUT_ASSESSMENT" />
		</classes>
	</test>

	<test name="IT_ON_WITHOUT_ASSESSMENT">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITHOUT_ASSESSMENT" />
		</classes>
	</test>

	<test name="IT_ON_WITHOUT_ASSESSMENT_CONTENT_DELIVERY_YES">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITHOUT_ASSESSMENT_CONTENT_DELIVERY_YES" />
		</classes>
	</test>

	<test name="IT_ON_WITH_ASSESSMENT_FEEDBACK_YES">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITH_ASSESSMENT_FEEDBACK_YES" />
		</classes>
	</test>


	<test name="IT_OFF_WITH_ASSESSMENT">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_OFF_WITH_ASSESSMENT" />
		</classes>
	</test>-->
	
	
<!--	Vijay -->


	<test name="RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION" />
		</classes>
	</test>
	

	<test name="RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION" />
		</classes>
	</test>

	<test name="IT_ON_WITHOUT_ASSESSMENT_FFEDBACK_YES">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_ON_WITHOUT_ASSESSMENT_FFEDBACK_YES" />
		</classes>
	</test>

	<test name="IT_OFF_WITHOUT_ASSESSMENT">
		<classes>
			<class
				name="com.Automation.learniqCRITICALScenarios.IT_OFF_WITHOUT_ASSESSMENT" />
		</classes>
	</test>


	<!--	<test name="OnlineITManualEvlaution">-->
	<!--		<classes>-->
	<!--			<class-->
	<!--
	name="com.Automation.learniqCRITICALScenarios.learniq_CriticalScenario_Individual_Suites.IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION"
	/>-->
	<!--		</classes>-->
	<!--	</test>-->


</suite> 
