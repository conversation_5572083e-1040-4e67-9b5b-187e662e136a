//package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;
//
//import java.util.HashMap;
//import org.testng.annotations.AfterTest;
//import org.testng.annotations.DataProvider;
//import org.testng.annotations.Test;
//import com.Automation.Utils.ConfigsReader;
//import com.Automation.Utils.Constants;
//import com.Automation.Utils.ExcelUtilUpdated;
//import com.Automation.Utils.MyScreenRecorder;
//import com.Automation.learniqBase.OQActionEngine;
//import com.Automation.learniqObjects.CM_CourseSession;
//import com.Automation.learniqObjects.CM_QuestionPaper;
//import com.Automation.learniqObjects.CM_SelfNomination;
//
///**
// * Verify Online IT Session with assessment for scheduled course and make
// * at-least one employee skipped, course invitation rejected, course invitation
// * accepted, self nominated, absent, qualified, To Be Retrained with manual
// * evaluation and by viewing Individual employee report at each transaction
// * starting from course session, also add at least 2 additional users and make
// * sure that should be qualified and to be retrained and view IER for those
// * employees.
// */
//
//public class IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION extends OQActionEngine {
//
//	String ExcelPath = "./learnIQTestData/IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION_FROM_TOPIC.xlsx";
//
//	public IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION() {
//
//		super(ConfigsReader.getPropValue("applicationUrl"));
//	}
//
//	// Test Method for SubgroupAssignment Configuration, Registration, Approve with
//	// AuditTrails----------------------------------------------------------------------------------------------------------------------------
//
//	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "SubgroupAssignRegistration");
//
//	@DataProvider(name = "SubgroupAssignmentRegistration")
//	public Object[][] getSubgroupAssignmentRegistration() throws Exception {
//		Object[][] obj = new Object[excel1.getRowCount()][1];
//		for (int i = 1; i <= excel1.getRowCount(); i++) {
//			HashMap<String, String> testData = excel1.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 1, dataProvider = "SubgroupAssignmentRegistration", enabled = true)
//	public void SubgroupAssignment_Configuration_Registration_Approve(HashMap<String, String> testData) {
//
//		test = extent.createTest("SubgroupAssignment Configuration, Registration, Approve with Audit Trails")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("SubgroupAssignment Configuration, Registration, Approve with Audit Trails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		SubgroupAssignment.subGroupAssignment_Configuration(testData);
//
//		SubgroupAssignment.subGroupAssignment_Registration_AuditTrails(testData);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		SubgroupAssignment.subGroupAssignment_Approve_AuditTrails(testData);
//
//		Logout.signOutPage();
//	}
//
//	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");
//
//	@DataProvider(name = "TopicReg")
//	public Object[][] getTopicData() throws Exception {
//		Object[][] obj = new Object[topicData.getRowCount()][1];
//		for (int i = 1; i <= topicData.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 2, dataProvider = "TopicReg", enabled = true)
//	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
//
//		test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.masterPlant();
//
//		InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//				Constants.CONFIGURE_CONFIRMATION_TEXT);
//		epiclogin.navigateTolearnIQPlant();
//		InitiateTopic.topic_Registration(testData);
//		Logout.signOutPage();
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
//		Logout.signOutPage();
//
//	}
//
//	// Test Method for Course Configuration, Registration, Approve with
//	// AuditTrails----------------------------------------------------------------------------------------------------------------------------
//
//	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");
//
//	@DataProvider(name = "CourseRegistrationData")
//	public Object[][] getCourseData() throws Exception {
//		Object[][] obj = new Object[CourseData.getRowCount()][1];
//		for (int i = 1; i <= CourseData.getRowCount(); i++) {
//			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 3, dataProvider = "CourseRegistrationData", enabled = true)
//	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
//
//		test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")
//
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//
//				.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.masterPlant();
//		Initiate_Course.courseConfiguration_Reg(testData);
//		epiclogin.navigateTolearnIQPlant();
//		Initiate_Course.Course_Registration(testData);
//		Logout.signOutPage();
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		Initiate_Course.course_Approval_AuditTrials_Yes(testData);
//		Logout.signOutPage();
//
//	}
//
//	// Test Method for TrainingSchedule Configuration, Modification, Approve with
//	// AuditTrails---------------------------------------------------------------------------------------------
//
//	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");
//
//	@DataProvider(name = "modifyTrainingSchedule")
//	public Object[][] getModifyTrainingSchedule() throws Exception {
//		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
//		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
//			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 4, dataProvider = "modifyTrainingSchedule", enabled = true)
//	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {
//
//		test = extent.createTest("TrainingSchedule Configuration, Modification, Approve with Audit Trails")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("TrainingSchedule Configuration, Modification, Approve with Audit Trails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		TrainingShcedule.trainingScheduleConfiguration(testData);
//
//		TrainingShcedule.modifyTrainingScheduled(testData);
//
//		TrainingShcedule.trainingScheduleAuditTrail();
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		TrainingShcedule.approveModifyTrainingScheduled(testData);
//		TrainingShcedule.trainingScheduleAuditTrail();
//		Logout.signOutPage();
//	}
//
//	// Test Method for Trainer Configuration, Modification, Approve with
//	// Audit Trails
//
//	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");
//
//	@DataProvider(name = "TrainerModification")
//	public Object[][] getTrainerData() throws Exception {
//		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
//		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
//			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 5, dataProvider = "TrainerModification", enabled = true)
//	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
//
//		test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")
//
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//
//				.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//		trainer.TrainerModificationConfigurations(testData);
//		trainer.trainer_Modification_AuditTrails(testData);
//		Logout.signOutPage();
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//		Logout.signOutPage();
//	}
//
//	// // Test Method for CourseSession Configuration, Registration, Approve with
////	// AuditTrails
//
//	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "SessionRegAppr");
//
//	@DataProvider(name = "CourseSession")
//	public Object[][] getCourseSessionData() throws Exception {
//		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
//		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
//			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 6, dataProvider = "CourseSession", enabled = true)
//	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
//			throws InterruptedException {
//
//		test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		 CourseSession.courseSessionConfiguration(testData);
//
//		CourseSession.courseSession_Online_WithExam_ManualEval_WithPrequisiteTrainee(testData);
//
//		CourseSession.courseSessionAuditTrails();
//
//		test = extent.createTest("Individual Employee Report Before CourseSession Approve")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report Before CourseSession Approve");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//
//		test = extent.createTest("Individual Employee Report After CourseSession Approve")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report After CourseSession Approve");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);
//
//		Logout.signOutPage();
//
//	}
//	// Test Method for Respond CourseInvitaion
//	// (Accept)---------------------------------------------------------------------------------------------
//
//	ExcelUtilUpdated CourseInvData = new ExcelUtilUpdated(ExcelPath, "CourseInvitiationAccept");
//
//	@DataProvider(name = "AcceptCourseInvitation")
//	public Object[][] getCIacceptData() throws Exception {
//		Object[][] obj = new Object[CourseInvData.getRowCount()][1];
//		for (int i = 1; i <= CourseInvData.getRowCount(); i++) {
//			HashMap<String, String> testData = CourseInvData.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//
//	}
//
//	@Test(priority = 7, dataProvider = "AcceptCourseInvitation", enabled = true)
//	public void Respond_CourseInvitaion_Accept(HashMap<String, String> testData) {
//
//		test = extent.createTest("Respond CourseInvitaion (Accept)")
//
//				.assignAuthor(CM_CourseSession.getCIAcceptTraineeID())
//
//				.assignCategory("Respond CourseInvitaion (Accept)");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getCIAcceptTraineeID(),
//				CM_CourseSession.CIAcceptTraineePsw);
//
//		epiclogin.plant1();
//
//		CourseInvitation.respondCourseInvitation(testData);
//
//		test = extent.createTest("Individual Employee Report After CourseInvitation Accept")
//				.assignAuthor(CM_CourseSession.getCIAcceptTraineeID())
//				.assignCategory("Individual Employee Report After CourseInvitation Accept");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_CIACCEPT);
//
//		Logout.signOutPage();
//	}
//	// Test Method for Respond
//	// CourseInvitaion(Reject)---------------------------------------------------------------------------------------------
//
//	ExcelUtilUpdated CIData = new ExcelUtilUpdated(ExcelPath, "CourseInvitionReject");
//
//	@DataProvider(name = "RejectCourseInvition")
//	public Object[][] getCI_Reject_Data() throws Exception {
//		Object[][] obj = new Object[CIData.getRowCount()][1];
//		for (int i = 1; i <= CIData.getRowCount(); i++) {
//			HashMap<String, String> testData = CIData.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 8, dataProvider = "RejectCourseInvition", enabled = true)
//	public void Respond_CourseInvitaion_Reject(HashMap<String, String> testData) {
//
//		test = extent.createTest("Respond CourseInvitaion (Reject)")
//
//				.assignAuthor(CM_CourseSession.getCIRejectTraineeID())
//
//				.assignCategory("Respond CourseInvitaion (Reject)");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getCIRejectTraineeID(),
//				CM_CourseSession.CIRejectTraineePsw);
//
//		epiclogin.plant1();
//
//		CourseInvitation.respondCourseInvitationReject(testData);
//
//		test = extent.createTest("Individual Employee Report After CourseInvitation Reject")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report After CourseInvitation Reject");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.CIRejectEmployeeID, Constants.EMPLOYEESTATUS_AS_CIREJECT);
//
//		Logout.signOutPage();
//	}
//	// Test Method for SelfNomination Configuration, Registration, Approve with
//		// AuditTrails---------------------------------------------------------------------------------------------
//
//		ExcelUtilUpdated SelfNominated = new ExcelUtilUpdated(ExcelPath, "SelfNominationRegApproval");
//
//		@DataProvider(name = "SelfNominationReg")
//		public Object[][] getSelfNominationData() throws Exception {
//			Object[][] obj = new Object[SelfNominated.getRowCount()][1];
//			for (int i = 1; i <= SelfNominated.getRowCount(); i++) {
//				HashMap<String, String> testData = SelfNominated.getTestDataInMap(i);
//				obj[i - 1][0] = testData;
//			}
//			return obj;
//		}
//
//		@Test(priority = 9, dataProvider = "SelfNominationReg", enabled = true)
//		public void SelfNomination_Configuration_Registration_Approve(HashMap<String, String> testData) {
//
//			test = extent.createTest("SelfNomination Configuration, Registration, Approve with AuditTrails")
//
//					.assignAuthor(CM_SelfNomination.selfNominatedTraineeID)
//
//					.assignCategory("SelfNomination Configuration, Registration, Approve with AuditTrails");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("selfNominatedTraineeID"),
//					testData.get("selfNominatedTraineePsw"));
//
//			epiclogin.plant1();
//
//			SelfNominate.selfNominationConfiguration(testData);
//
//			SelfNominate.selfNomination_Registration(testData);
//
//			Logout.signOutPage();
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//			epiclogin.plant1();
//
//			SelfNominate.SelfNominationRegistrationApproval_With_AuditTrails(testData);
//
//			test = extent.createTest("Individual Employee Report After SelfNomination")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report After SelfNomination");
//
//			IERReport.individualEmployeeReport(CM_SelfNomination.selfNominatedEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_SELFNOMINATED);
//
//			Logout.signOutPage();
//
//		}
//		// Test Method for BatchFormation Configuration, Registration with
//		// AuditTrails---------------------------------------------------------------------------------------------
//
//		ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");
//
//		@DataProvider(name = "batchFormation")
//		public Object[][] getBatchFormation() throws Exception {
//			Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
//			for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
//				HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
//				obj[i - 1][0] = testData;
//			}
//			return obj;
//		}
//
//		@Test(priority = 10, dataProvider = "batchFormation", enabled = true)
//		public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {
//
//			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//
//			epiclogin.plant1();
//
//			BatchFormation.batchFormationConfiguration(testData);
//
//			BatchFormation.batchFormation_Online_NotResponded_Responded_SelfNominatedUsers(testData);
//
//			BatchFormation.proposeBatchFormationAuditTrail();
//
//			test = extent.createTest("Individual Employee Report For Selected Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report For Selected Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED);
//
//			test = extent.createTest("Individual Employee Report For Not Selected Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report For Not Selected Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED);
//
//			Logout.signOutPage();
//
//		}
//		// Test Method for QuestionBank Configuration, Registration, Approve with
//		// AuditTrails-------------------------------------------------------------------------------------------
//
//		ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");
//
//		@DataProvider(name = "QuestionBank")
//		public Object[][] getdocument_preparation_RequestFlow() throws Exception {
//			Object[][] obj = new Object[QBData.getRowCount()][1];
//			for (int i = 1; i <= QBData.getRowCount(); i++) {
//				HashMap<String, String> testData = QBData.getTestDataInMap(i);
//				obj[i - 1][0] = testData;
//			}
//			return obj;
//		}
//
//		@Test(priority = 11, dataProvider = "QuestionBank")
//		public void QuestionBank_Configuration_Registration_Approve(HashMap<String, String> testData) {
//
//			test = extent.createTest("QuestionBank Configuration, Registration, Approve with AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("QuestionBank Configuration, Registration, Approve with AuditTrails");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//
//			epiclogin.masterPlant();
//			PrepareQB.QBRegistrationApproval_Configuration(testData);
//			epiclogin.navigateTolearnIQPlant();
//			PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
//			Logout.signOutPage();
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//			epiclogin.plant1();
//			PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);
//			Logout.signOutPage();
//
//		}
//
//		// Test Method for QuestionPaper Registration
//		// -------------------------------------------------------------------------------------------
//
//		ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");
//
//		@DataProvider(name = "QuestionPaperData")
//		public Object[][] getQuestionPaperDataRE() throws Exception {
//			Object[][] obj = new Object[QPReg.getRowCount()][1];
//			for (int i = 1; i <= QPReg.getRowCount(); i++) {
//				HashMap<String, String> testData = QPReg.getTestDataInMap(i);
//				obj[i - 1][0] = testData;
//			}
//			return obj;
//		}
//
//		@Test(priority = 12, dataProvider = "QuestionPaperData", enabled = true)
//		public void QuestionPaper_Registration(HashMap<String, String> testData) {
//
//			test = extent.createTest("QuestionPaper Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("QuestionPaper Registration");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//
//			epiclogin.plant1();
//
//			questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);
//
//			Logout.signOutPage();
//
//		}
//
//		
//		// Test Method for
//		// RecordAttendance-------------------------------------------------------------------------------------------
//
//		ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");
//
//		@DataProvider(name = "recordAttendance")
//		public Object[][] getRecordAttendance() throws Exception {
//			Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
//			for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
//				HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
//				obj[i - 1][0] = testData;
//			}
//			return obj;
//		}
//
//		@Test(priority = 13, dataProvider = "recordAttendance", enabled = true)
//		public void recordAttendance(HashMap<String, String> testData) {
//
//			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("RecordAttendance");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//
//			epiclogin.plant1();
//
//			RecordAttendance.recordAttendance_OnlineSession_2AdditionalUsers(testData);
//
//			test = extent.createTest("Individual Employee Report of Skipped Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report of Skipped Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID, Constants.EMPLOYEESTATUS_AS_SKIPPED);
//
//			test = extent.createTest("Individual Employee Report of Absent Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report of Absent Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.AbsentEmployeeID, Constants.EMPLOYEESTATUS_AS_ABSENT);
//
//			test = extent.createTest("Individual Employee Report of Present Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report of Present Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_PRESENT);
//			Logout.signOutPage();
//
//		}
//
//	// Test Method for RespondQuestionPaper
//	// (Qualify)-------------------------------------------------------------------------------------------
//
//	@Test(priority = 14, enabled = true)
//	public void Respond_QuestionPaper_Qualify() {
//
//		test = extent.createTest("Respond Question paper (Qualify)").assignAuthor(CM_CourseSession.QualifiedTraineeID)
//				.assignCategory("Respond Question paper (Qualify)");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.QualifiedTraineeID,
//				CM_CourseSession.QualifiedTraineePsw);
//
//		epiclogin.plant1();
//
//		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
//				CM_CourseSession.QualifiedTraineePsw);
//
//		test = extent.createTest("Individual Employee Report of Response Submitted Trainee")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report of Response Submitted Trainee");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED);
//
//		Logout.signOutPage();
//
//		n = 0;
//		screenshotCounter = 0;
//	}
//	
//	
//	
//	
//	// Test Method for RespondQuestionPaper
//		// (Qualify)-------------------------------------------------------------------------------------------
//
//		@Test(priority = 15, enabled = true)
//		public void Respond_QuestionPaper_Qualify_respondedCandidate() {
//
//			test = extent.createTest("Respond Question paper (Qualify)").assignAuthor(CM_CourseSession.CIAcceptTraineeID)
//					.assignCategory("Respond Question paper (Qualify)");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.CIAcceptTraineeID,
//					CM_CourseSession.CIAcceptTraineePsw);
//			epiclogin.plant1();
//			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
//					CM_CourseSession.CIAcceptTraineePsw);
//			test = extent.createTest("Individual Employee Report of Response Submitted Trainee")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report of Response Submitted Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED);
//			Logout.signOutPage();
//		}
//		// Test Method for RespondQuestionPaper
//		// (AdditionalUser
//		// Qualify)-------------------------------------------------------------------------------------------
//
////		@Test(priority = 16, enabled = true)
////		public void Respond_QuestionPaper_AdditionalUser_Qualify() {
//	//
////			test = extent.createTest("Respond Question paper (AdditionalUser Qualify)")
//	//
////					.assignAuthor(CM_CourseSession.AdditionalUSerQulaifiedID)
//	//
////					.assignCategory("Respond Question paper (AdditionalUser Qualify)");
//	//
////			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getAddUserQID(),
////					CM_CourseSession.AdditionalUSerQulaifiedPsw);
//	//
////			epiclogin.plant1();
//	//
////			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
////					CM_CourseSession.AdditionalUSerQulaifiedPsw);
//	//
////			Logout.signOutPage();
//	//
////			n = 0;
////			screenshotCounter = 0;
////		}
//
//		// Test Method for RespondQuestionPaper
//		// (ToBeRetrained)-------------------------------------------------------------------------------------------
//
//		@Test(priority = 17, enabled = true)
//		public void Respond_QuestionPaper_ToBeRetrained() {
//			test = extent.createTest("Respond Question paper (ToBeRetrained)")
//					.assignAuthor(CM_CourseSession.ToBeRetrainedTraineeID)
//
//					.assignCategory("Respond Question paper (ToBeRetrained)");
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					CM_CourseSession.getToBeRetrainedTraineeID(), CM_CourseSession.ToBeRetrainedTraineePsw);
//			epiclogin.plant1();
//			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
//					CM_CourseSession.ToBeRetrainedTraineePsw);
//			Logout.signOutPage();
//		}
//
//		// Test Method for RespondQuestionPaper
//		// (AdditionalUser
//		// ToBeRetrained)-------------------------------------------------------------------------------------------
//
////		@Test(priority = 18, enabled = true)
////		public void Respond_QuestionPaper_AdditionalUser_ToBeRetrained() {
////			test = extent.createTest("Respond Question paper (Additional User ToBeRetrained)")
////					.assignAuthor(CM_CourseSession.AdditionalUSerTRBID)
////					.assignCategory("Respond Question paper (Additional User ToBeRetrained)");
////			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.AdditionalUSerTRBID,
////					CM_CourseSession.AdditionalUSerTRBPsw);
////			epiclogin.plant1();
////			RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
////					CM_CourseSession.AdditionalUSerTRBPsw);
////			Logout.signOutPage();
////			n = 0;
////			screenshotCounter = 0;
////		}
//
//		// Test Method for
//		// EvaluateAnswerPaper-------------------------------------------------------------------------------------------
//
//		@Test(priority = 19, enabled = true)
//		public void evaluate_AnswerPaper() {
//
//			test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
//					.assignCategory("Evaluate Answer Paper");
//
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
//					CM_QuestionPaper.EvaluatorPassword);
//
//			epiclogin.plant1();
//
//			Evaluate.manualEvaluation();
//
//			test = extent.createTest("Individual Employee Report of Qualified Trainee")
//					.assignAuthor(CM_QuestionPaper.EvaluatorUserID)
//					.assignCategory("Individual Employee Report of Qualified Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.CIAcceptEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED);
//
//			test = extent.createTest("Individual Employee Report of ToBeRetrained Trainee")
//					.assignAuthor(CM_QuestionPaper.EvaluatorUserID)
//					.assignCategory("Individual Employee Report of ToBeRetrained Trainee");
//
//			IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED);
//			
//			test = extent.createTest("Check Trainees at Course Session Screen")
//					.assignAuthor( ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Check Trainees at Course Session Screen");
//			
//			CourseSession.verify_Employees_At_Coursesession();
//
//			Logout.signOutPage();
//
//			n = 0;
//			screenshotCounter = 0;
//		}
//
//		@AfterTest
//		public void afterTest() {
//			extent.flush();
//			MyScreenRecorder.stopRecording();
//			driver.quit();
//		}
//}
