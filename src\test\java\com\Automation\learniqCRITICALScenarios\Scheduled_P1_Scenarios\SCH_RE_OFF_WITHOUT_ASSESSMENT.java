package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Offline RE type Session without assessment for scheduled course and
 * make atleast one employee should be pending at record document reading and
 * one should be qualified by viewing Individual employee report at each
 * transaction starting from course session.
 */

public class SCH_RE_OFF_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/RE_OFF_WITHOUT_ASSESSMENT.xlsx";

	public SCH_RE_OFF_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {

			test = extent.createTest("Topic  Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//		epiclogin.masterPlant();
//
//		InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//				Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
	

		InitiateTopic.topic_Registration(testData);

//		Logout.signOutPage();

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
//
//		Logout.signOutPage();
		//
	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {

			test = extent.createTest("Course  Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course  Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));

//		epiclogin.masterPlant();
//
//		Initiate_Course.courseConfiguration_Reg(testData);

//		epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//		Logout.signOutPage();

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		Initiate_Course.course_Approval_AuditTrials_Yes(testData);
//
		// Logout.signOutPage();

	}
	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "TrainingScheduleModify");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Modification");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);

		TrainingShcedule.trainingScheduleAuditTrail();

//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		TrainingShcedule.approveModifyTrainingScheduled(testData);
//
//		TrainingShcedule.trainingScheduleAuditTrail();
//
//		Logout.signOutPage();
	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// CourseSession.courseSessionConfiguration(testData);
		CourseSession.courseSession_Offline_DocumentReading(testData);
		//
		// CourseSession.courseSessionAuditTrails();

//				if (isReportedRequired == true) {
//					test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//							.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//							.assignCategory("Individual Employee Report for Course Session Under Approval");
//				}

//				IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//						Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL, Constants.REType);
//
//				Logout.signOutPage();
//
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//				epiclogin.plant1();
//
//				CourseSession.courseSession_Approve(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Course Session Proposed Status");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.Session_PROPOSED_FOR_RE,
				Constants.REType);

	//	Logout.signOutPage();

	}

	// Test Method for Record Document Reading

	@Test(priority = 5, enabled = true)
	public void RecordDocumentReading() {
		if (isReportedRequired == true) {
			test = extent.createTest("Record Document Reading").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Document Reading");
		}

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
		RecordDR.RecordDocumentReading();
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Qualified Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Qualified Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.REType);

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Document Reading Pending Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Document Reading Pending Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedTraineeID, Constants.Session_PROPOSED_FOR_RE,
				Constants.REType);

//				if (isReportedRequired == true) {
//					test = extent.createTest("Check Trainees at Course Session Screen")
//							.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//							.assignCategory("Check Trainees at Course Session Screen");
//				}
		// CourseSession.verify_Employees_At_Coursesession();

		Logout.signOutPage();

	}

}
