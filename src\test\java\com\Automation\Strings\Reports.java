package com.Automation.Strings;

public enum Reports {

	// Menus Strings

	ClickReports_DC("Click on 'Reports' menu."),
	ClickReports_AC("Assigned submenus for the user under 'Reports' menu should be displayed.</div>"),
	ClickReports_AR("Assigned submenus for the user under 'Reports' menu is getting displayed.</div>"),
	ClickReports_SS("'Reports'"),

	ClickDWCS_DC("Click on 'Date Wise Course Session Report' submenu."),
	ClickDWCS_AC("'Date Wise Course Session Report' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Course Session Name, Course Unique Code, Start Date and End Date' columns.</div>"),
	ClickDWCS_AR("'Date Wise Course Session Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Course Session Name, Course Unique Code, Start Date and End Date' columns.</div>"),
	ClickDWCS_SS("'Date Wise Course Session Report'."),

	// Individual Employee Report

	click_IER_DC("Click on 'Individual Employee Report(All)'."),
	click_IER_AC("'Individual Employee Report (All)' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Employee Name, Employee ID, Department and Role' columns.</div>"),

	click_IER_AR("'Individual Employee Report (All)' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Employee Name, Employee ID, Department and Role' columns.</div>"),
	click_IER_SS("'Individual Employee Report(All)'."),

	// Feeback Report

	CLICK_FEEdBACK_REPORT_DC("Click on 'Feedback Report'."),
	CLICK_FEEdBACK_REPORT_AC("'Trainer List' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Trainer Name', 'Trainer Code' and 'Designation' columns.</div>"),
	CLICK_FEEdBACK_REPORT_AR("'Trainer List' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Trainer Name', 'Trainer Code' and 'Designation' columns.</div>"),
	CLICK_FEEdBACK_REPORT_SS("'Feedback Report'."),

	Click_ToggleBtn_DC("Click on 'Toggle dropdown'."),
	Click_ToggleBtn_AC(
			"Option to search with 'Course Session Name, Course Unique Code, Training Method, Start Date, and End Date' should be available.</div>"),
	Click_ToggleBtn_AR(
			"Option to search with 'Course Session Name, Course Unique Code, Training Method, Start Date, and End Date' are available.</div>"),
	Click_ToggleBtn_SS("'Toggle dropdown'"),

	Unselect_CS_Name_DC("Unselect the 'Course Session Name'"),
	Unselect_CS_Name_AC("'Course Session Name' should be unselected.</div>"),
	Unselect_CS_Name_AR("'Course Session Name' is getting unselected.</div>"),
	Unselect_CS_Name_SS("Unselect 'Course Session Name'"),

	// Deselect Employee name

	UnselectEmployeeName_IER_DC("Unselect the 'Employee Name'"),
	UnselectEmployeeName_IER_AC("'Employee Name' should be unselected."),
	UnselectEmployeeName_IER_AR("'Employee Name' should be unselected."),
	UnselectEmployeeName_IER_SS("'Employee Name'"),

	// Deselect Trainer Name

	UNSELECT_TRAINER_NAME_DC("Unselect the 'Trainer Name'"),
	UNSELECT_TRAINER_NAME_AC("'Trainer Name' should be unselected."),
	UNSELECT_TRAINER_NAME_AR("'Trainer Name' should be unselected."), UNSELECT_TRAINER_NAME_SS("'Trainer Name'"),

	TRAINER_NAME_DC("Enter Trainer Name who is selected for the above proposed session."),
	TRAINER_NAME_SS("'Trainer Name'"),
	
	UNSELECT_TRAINER_CODE_DC("Unselect the 'Trainer Code'"),
	UNSELECT_TRAINER_CODE_AC("'Trainer Code' should be unselected."),
	UNSELECT_TRAINER_CODE_AR("'Trainer Code' should be unselected."), UNSELECT_TRAINER_CODE_SS("'Trainer Code'"),

	CLICK_TRAINER_NAME_DC("Click on 'Trainer Name' hyperlink"),
	TRAINER_NAME_AC("Proposed course session name list should be displayed to respective 'Trainer Name'."),
	TRAINER_NAME_AR("Proposed course session name list is/are displayed to the respective 'Trainer Name'."),
	
	TRAINER_CODE_DC("Enter Trainer Code who is selected for the above proposed session."),
	TRAINER_CODE_SS("'Trainer Code'"),

	CLICK_TRAINER_CODE_DC("Click on 'Trainer Code' hyperlink"),

	COURSE_SESSION_DC("Enter proposed 'Course Session' name into a textbox"),
	COURSE_SESSION_SS("'Course Session' name"),

	SEARCH_DS("Click 'Search' option"), SEARCH_AC("Entered value should be displayed and highlighted."),
	SEARCH_AR("Entered value is displayed and highlighted."), SEARCH_SS("Find icon"),

	TOTAL_NUMBER_TRAINEE_DS("Click 'Total Number of Trainees' hyperlink of respective 'Course Session Name"),
	TOTAL_NUMBER_TRAINEE_AC("<div>* 'Feedback' screen should be displayed.</div>"
			+ "<div>* Screen should contain 'Employee Name', 'Employee ID'  and 'Feedback' columns."),
	TOTAL_NUMBER_TRAINEE_AR("<div>* 'Feedback' screen is displayed.</div>"
			+ "<div>* Screen contains 'Employee Name', 'Employee ID'  and 'Feedback' columns."),
	TOTAL_NUMBER_TRAINEE_SS("'Total Number of Trainee'"),

	TRAINEE_FEEDBACK_DS("Click 'Trainee Feedback' hyperlink of respective 'Employee Name"),
	TRAINEE_FEEDBACK_AC(
			"'Individual Employee Feedback' screen should be displayed with 'Question' and 'Response' columns."),
	TRAINEE_FEEDBACK_AR("'Individual Employee Feedback' screen is displayed with 'Question' and 'Response' columns."),
	TRAINEE_FEEDBACK_SS("'Employee Feedback'"),

	// Click Toggle IER Report
	Click_ToggleBtnIER_AC(
			"Option to search with 'Employee Name, Employee ID, Department and Role' should be available.</div>"),
	Click_ToggleBtnIER_AR(
			"Option to search with 'Employee Name, Employee ID, Department and Role' are available.</div>"),

	// Click Toggle IER Report

	Click_ToggleBtnIER2_AC("Option to search with 'Course Session Name' should be available.</div>"),
	Click_ToggleBtnIER2_AR("Option to search with 'Course Session Name' is available.</div>"),

//Document Reding completed (Online Document Reading witout asessment)
	CS_Name_Like_DC("Enter the above document reading completed 'Course Session Name'."),

	// Enter the RE Sesssion Name (Responded Question Paper)
	CS_Name_Like_Exam_DC(
			"Enter the above RE Course Session Name for whih the user is respond to Question Paper and Qualified."),

//Offline RE
	CS_Name_Like_OfflineRE_DC(
			"Enter the above 'Course Session Name' for which the record document reading is completed."),

	ClickCSNameReport_OfflineRE_DC(
			"Click on the above 'Course Session Name' for which the record document reading is completed."),

	ClickEmpNameReport_OfflineRE_DC("Click on the employee name for whom the record document reading is completed."),
// Offline Classroom type with assessment
	CS_Name_ITSessionOffline_Like_DC("Enter the above 'Course Session Name' for which the record marks is completed."),

// Offline Classroom type without assessment
	CS_Name_ITSessionOfflineAssessmentNo_Like_DC(
			"Enter the above 'Course Session Name' for which the Record Attendance is completed."),

	// Enter the employee name for Different Cases

	Skipped_DC("Enter the Employee name of the above skipped user in 'Employee Name' field."),
	CIReject_DC(
			"Enter the Employee name who rejected Course invitiation of the above session in 'Employee Name' field."),
	CIAccept_DC(
			"Enter the Employee name who accepted Course invitiation of the above session in 'Employee Name' field."),
	Qualified_DC("Enter the Employee name of the above 'Qualified' user in 'Employee Name' field."),
	ToBeRetrained_DC("Enter the Employee name of the above 'To Be Retrained' user in 'Employee Name' field."),
	Absent_DC(
			"Enter the Employee name of the above user who is not selected at 'Record Attendance' in 'Employee Name' field."),
	EmployeeName_SS("'Employee Name'"),
	Enter_SelectedUser_DC("Enter any of the employee name who is selected for the above proposed session."),
	Enter_SelectedEmployeeName_DC("Enter the employee name who is selected for the above proposed session."),
	Click_SelectedUser_DC("Click on the above employee name"),
	Enter_SNEmployee_DC("Enter the Employee Name who is self nominated to above proposed session."),
	Click_SCEmployeeName_DC("Click on the above self nominated Employee Name."),

	Enter_BatchNotSelected_User_DC("Enter the employee name who is not selected at batch formation"),

	// Course Invitiation Rejected
	CIRejeted_DC("Enter the Employee name user who rejeted the course invitiation of the above proposed session."),

	CS_Name_Like_SS("'Course Session Name'"),
	Session_Name_Like_DC("Enter the 'Course Session Name' for which the record attendance is updated."),
	CS_Name_Like_AC("Entered value should be displayed.</div>"),
	CS_Name_Like_AR("Entered value is getting displayed.</div>"),

	// Session Name

	CS_Name_DC("Enter the above session name."),

	// Online session strings after the evaluation is done

	CS_Name_ITSessionOnlineManualEval_Like_DC("Enter the 'Course Session Name' for which the Evaluation is completed."),

	ClickClassroomType_CSNameEvalDone_DC("Click on the above Course Session Name for which evaluation is completed."),

	ClickEmpNameEval_DC("Click on the employee name for whom the evaluation is completed."),

	ClickEmpNameRecordMarks_DC("Click on the employee name for whom the record marks is commpleted and qualified."),

	CS1_Name_Like_DC("Enter the above registered 'Course Session Name' for which the record attendance is completed."),
	CS1_Name_Like_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	CS1_Name_Like_AR("<div><b>*</b> Entered value is getting" + "</br>" + "displayed.</div>"),
	CS1_Name_Like_SS("'Course Session Name'"),

	TrainingMethodDrpDwn_DC("Click 'Training Method'"),
	TrainingMethodDrpDwn_AC("Option to select 'Classroom Type and Document Reading' should be available.</div>"),
	TrainingMethodDrpDwn_AR("Option to select 'Classroom Type and Document Reading' are available.</div>"),
	TrainingMethodDrpDwn_SS("'Training Method'"),

	SelectDocumentReading_DC("Select 'Document Reading'."),
	SelectDocumentReading_AC("Selection should be accepted.</div>"),
	SelectDocumentReading_AR("Selection is getting accepted."), SelectDocumentReading_SS("'Document Reading'"),

	ClickViewReport_DC("Click on 'View Report'."),
	ClickViewReport_AC("Records should be displayed based on the search criteria.</div>"),
	ClickViewReport_AR("Records are getting displayed based on the search criteria.</div>"),
	ClickViewReport_SS("'View Report'"),

	//

	Click_CS_hyperlink_DC("Click on the above 'Course Session Name'"),

	// Skipped Status

	ClickViewReport_Skipped_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Skipped' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_Skipped_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Skipped' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Course Invitation Reject

	ClickViewReport_CIReject_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Course Invitiation Rejected' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_CIReject_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Course Invitiation Rejected' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Course Invitation Accepted

	ClickViewReport_CIAccept_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Course Invitation Accepted' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_CIAccept_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Course Invitation Accepted' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Under Approval

	// Course Session Under Approval
	ClickViewReport_CSUnderAppr_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Session Initiated Date, Session Start Date and End Date values should be displayed accurately.</div>"
			+ "<div><b>* </b>'Course Session Under Approval' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_CSUnderAppr_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Session Initiated Date, Session Start Date and End Date values are getting displayed accurately.</div>"
			+ "<div><b>* </b>'Course Session Under Approval' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Course Session Proposed
	ClickViewReport_CSProposed_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Course Session Proposed' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_CSProposed_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Course Session Proposed' status is getting displayed against the above session under 'Training Status'.</div>"),
//Self Nominated 

	ClickViewReport_SNEmp_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Self Nominated' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_SNEmp_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Self Nominated' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Absent

	ClickViewReport_Absent_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Absent for The Training Session' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_Absent_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Absent for The Training Session' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Qualified

	ClickViewReport_Qualfied_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Qualified' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_Qualfied_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Qualified' status is getting displayed against the above session under 'Training Status'.</div>"),

	// To Be Retrianed

	ClickViewReport_TBR_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'To Be Re-trained' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_TBR_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'To Be Re-trained' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Batch Not Selected

	ClickViewReport_BatchNotSelected_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Batch Proposed-Trainee Is Not Selected for The Batch' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_BatchNotSelected_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Batch Proposed-Trainee Is Not Selected for The Batch' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Batch Selected

	ClickViewReport_BatchSelected_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b>'Batch Proposed-Trainee Is Selected for The Batch' status should be displayed against the above session under 'Training Status'.</div>"),
	ClickViewReport_BatchSelected_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b>'Batch Proposed-Trainee Is Selected for The Batch' status is getting displayed against the above session under 'Training Status'.</div>"),

	// Click on Course Session name hyperlink

	Click_CourseSession_hyperlink_DC("Click on the above course session name hyperlink."),

	// Skipped Session

	Click_CourseSessionSkipped_hyperlink_AC("'Individual Employee Report (All)'  screen should be displayed/</div>"
			+ "<div><b>*</b> 'Skipped' status should be displayed against the above seession under 'Training Status'</div>"),
	Click_CourseSessionSkipped_hyperlink_AR("'Individual Employee Report (All)'  screen is getting displayed/</div>"
			+ "<div><b>*</b> 'Skipped' status is getting displayed against the above seession under 'Training Status'</div>"),
//Click on the skipped employee 

	Click_Skipped_EmployeeName_DC("Click on the above skipped employee name."),
	Click_Skipped_EmployeeName_AC("'Individual Employee Report (All)'  screen should be displayed/</div>"
			+ "<div><b>*</b> Screen should contain 'Course Session Name, Session Initiated Date, Start Date, End Date and Training Status' columns.</div>"),

	Click_Skipped_EmployeeName_AR("'Individual Employee Report (All)' screen is getting displayed/</div>"

			+ "<div><b>*</b> Screen is getting displayed with 'Course Session Name, Session Initiated Date, Start Date, End Date and Training Status columns'.</div>"),
	CourseSession_DC("'Course Sessions'"),
//Click on CI Rejected Employee	
	Click_CIRejected_EmployeeName_DC(
			"Click on the above employee name who rejected the 'Course Invitiation' for the above proposed session."),

//Click on CI Accepted Employee	
	Click_CIAccepted_EmployeeName_DC(
			"Click on the above employee name who accepted the 'Course Invitiation' for the above proposed session."),

//Click on ToBe Retrained

	Click_ToBeRetrianed_EmployeeName_DC("Click on the above 'To Be Re-Trained' employee name."),

//Click on Qualified Name

	Click_Qualified_EmployeeName_DC("Click on the above 'Qualified' employee name."),

	// Click Absent employee Name

	Click_Absent_EmployeeName_DC("Click on the above employee name who is not selected at 'Record Attendace'."),

	// Click Batch not selected usr

	Click_BatchNotSelected_User_DC("Click on the above employee name who is not selected at batch formation."),

//	ClickCSNameReport_DC("Click on the above document reading completed Course Session Name."),
	ClickClassroomType_CSNameReport_DC(
			"Click on the above Course Session Name for which the record marks is completed."),
	ClickClassroomType_CSNameRecordAttendance_DC(
			"Click on the above Course Session Name for which the Record Attendance is completed."),

	ClickCSNameClassRoomTypeReport_DC(
			"Click on the above Course Session Name for which the record attendance is updated."),

	ClickCS1NameReport_DC("	Click on the above 'Course Session Name' for which the record attendance is completed."),
	ClickCSNameReport_DC("Click on the above document reading completed Course Session Name."),
	ClickCSNameReport_RETypeExam_DC(
			"Click on the above RE Course Session Name for which the above user is responded ot Question Paper and qualified."),
	ClickCSNameReport_AC("'Training Attendance Record' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain all the details of Topic Covered, Course and Employee Name.</div>"),
	ClickCSNameReport_AR("'Training Attendance Record' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with all the details of Topic Covered, Course and Employee Name.</div>"),
	ClickCSNameReport_SS("'Training Attendance'"),

	ClickEmpNameReport_DC("Click on the employee name who is responded to document reading."),

	ClickEmpNameReportREType_Exam_DC("Click on the employee name who is responded to Question Paper and qualified."),

	ClickEmpNameReport1_DC("Click on the employee name who is selected at record attendance."),

	ClickEmpNameOfflineWithOutExam_DC("Click on the employee name who is selected at the record attendance."),
	ClickEmpNameReport_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' should be displayed as 'Qualified'.</div>"),
	ClickEmpNameReport_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' should is getting displayed as 'Qualified'.</div>"),
	ClickEmpNameReport_SS("'Trainee Report'"),

	ClickEmpNameRecordAttendace_DC("Click on the employee name for whom record attendance is completed"),

//	CS_Name_ITSessionOfflineAssessmentNo_Like_DC(
//			"Enter the above 'Course Session Name' for which the Record Attendance is completed."),

//Training Certificate hyperlink.

	ClickDocRead_TC_DC("Click on 'Training Certificate' hyperlink."),
	ClickDocRead_TC_AC(
			"'Training Certificate' screen should be displayed and all the details should be displayed accurately."
					+ "<div><b>*</b> All the employee and course details should be displayed accurately.</div>"
					+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickDocRead_TC_AR(
			"'Training Certificate' screen should be displayed and all the details should be displayed accurately."
					+ "<div><b>*</b> All the employee and course details are getting displayed accurately.</div>"
					+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickDocRead_TC_SS("'TrainingCertificate'"),

	// IT Qualified

	// user to be Re-trained

	ClickEmpNameRetrainedReport_DC(
			"Click on the employee name for whom the evaluation is completed and employee is To Be Retrained."),
	ClickEmpNameRetrainedReport_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'To Be Re-trained'.</div>"),
	ClickEmpNameRetrainedReport_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'To Be Re-trained'.</div>"),
	ClickEmpNameRetrainedReport_Verbal_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'To Be Re-trained(Verbal)'.</div>"),
	ClickEmpNameRetrainedReport_Verbal_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'To Be Re-trained(Verbal)'.</div>"),

	ClickEmpNameRetrainedReport_SS("'Trainee Report'"),

	// Skipped

	ClickEmpNameSkipped_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Skipped'.</div>"),
	ClickEmpNameSkipped_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Skipped'.</div>"),

	// Course Invitation Reject

	ClickEmpNameCIReject_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Course Invitation Rejected'.</div>"),
	ClickEmpNameCIReject_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Course Invitation Rejected'.</div>"),

	// Course Session Under Approval

	ClickEmpNameCSUnderAppr_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Course Session Under Approval'.</div>"),
	ClickEmpNameCSUnderAppr_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Course Session Under Approval'.</div>"),

	// Course Invitation Accept

	ClickEmpNameCIAccept_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Course Invitation Accepted'.</div>"),
	ClickEmpNameCIAccept_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Course Invitation Accepted'.</div>"),

	// Absent
	ClickEmpNameAbsent_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Absent for The Training Session'.</div>"),
	ClickEmpNameAbsent_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Absent for The Training Session'.</div>"),

	// To BeRetrained

	ClickRetrained_TC_DC("Click on 'Training Certificate'."),
	ClickRetrained_TC_AC(
			"'Training Certificate' screen should be displayed and all the details should be displayed accurately."
					+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickRetrained_TC_AR(
			"'Training Certificate' screen should be displayed and all the details should be displayed accurately."
					+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickRetrained_TC_SS("'TrainingCertificate'"),

	// Course Session Under Approval
	ClickEmpNameCSProposed_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Course Session Proposed'.</div>"),
	ClickEmpNameCSProposed_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Course Session Proposed'.</div>"),

	// Self Nominated
	ClickEmpNameSN_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Self-Nominated'.</div>"),
	ClickEmpNameSN_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Self-Nominated'.</div>"),

	// Batch is Not Selected

	ClickEmpNameBatchNotSelected_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Batch Proposed-Trainee Is Not Selected for The Batch'.</div>"),
	ClickEmpNameBatchNotSelected_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Batch Proposed-Trainee Is Not Selected for The Batch'.</div>"),

	// Batch Selected

	ClickEmpNameBatchSelected_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Batch Proposed-Trainee Is Selected for The Batch'.</div>"),
	ClickEmpNameBatchSelected_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Batch Proposed-Trainee Is Selected for The Batch'.</div>"),

	// DocumentList Report

	click_DocumentList_DC("Click on 'Document List'."),
	click_DocumentList_AC(
			"The screen should contain ‘ Document Name, Unique Code, Version No, Effective From, and Next Review’ columns.</div>"
					+ "<div><b>*</b>List of registered and active documents should be displayed in the list.</div>"
					+ "<div><b>*</b>Next Review Date is already crossed documents should be displayed in red color (if any).</div>"
					+ "<div><b>*</b>Documents that are registered in Master plant should not be displayed (if any).</div>"
					+ "<div><b>*</b>Address and Company Logo should be displayed on the header part of the report.</div>"
					+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details should be displayed in bottom of the report.</div>"
					+ "<div><b>*</b>The option to take the print should be available</div>"
					+ "<div><b>*</b>Note: This document has been generated electronically and is valid without signature. Should be available in bottom of the report.</div>"),

	click_DocumentList_AR(
			"The screen contains‘ Document Name, Unique Code, Version No, Effective From, and Next Review’ columns.</div>"
					+ "<div><b>*</b>List of registered and active documents is getting displayed in the list.</div>"
					+ "<div><b>*</b>Next Review Date is already crossed documents is displayed in red color (if any).</div>"
					+ "<div><b>*</b>Documents that are registered in Master plant are not displayed (if any).</div>"
					+ "<div><b>*</b>Address and Company Logo is displayed on the header part of the report.</div>"
					+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details is getting displayed in bottom of the report.</div>"
					+ "<div><b>*</b>The option to take the print is available</div>"
					+ "<div><b>*</b>Note: This document has been generated electronically and is valid without signature. is available in bottom of the report.</div>"),

	click_DocumentList_SS("'Document List'."),

	// Click Toggle DocumentList Report
	Click_ToggleBtnDocumentList_AC(
			"The option to search the document with ‘Document Name, Unique Code, Next Review From and Next Review To’ should be available..</div>"
					+ "<div><b>*</b>The screen should contain ‘Back and View Report’ buttons.</div>"),

	Click_ToggleBtnDocumentList_AR(
			"The option to search the document with ‘Document Name, Unique Code, Next Review From and Next Review To’ are available..</div>"

					+ "<div><b>*</b>The screen contains ‘Back and View Report’ buttons.</div>"),

	// Deselect Employee name

	UnselectUniqueCode_DocumentList_DC("Unselect the 'Unique Code'"),
	UnselectUniqueCode_DocumentList_AC("'Unique Code' should be unselected."),
	UnselectUniqueCode_DocumentList_AR("'Unique Code' should be unselected."),
	UnselectUniqueCode_DocumentList_SS("'Unique Code'"),
	UniqueCodetextbox_DocumentList_DC("An alert ‘Please enter a value for the parameter 'Unique Code'</div>"
			+ "<div><b>*</b>The parameter cannot be blank’ should be displayed with ‘Ok’ button."),
	UniqueCodetextbox_DocumentList_SS("Unique Code."),

	click_DocumentsTobeReviewedList_DC("Click on 'Documents To be Reviewed List'."),
	click_DocumentsTobeReviewedList_AC("‘Documents To be Reviewed List’ screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain ‘ Document Name, Unique Code, Version No, Code, Version No, Effective From, and Next Review’ columns.</div>"
			+ "<div><b>*</b>List of active documents for which Next Review Date is less than or equal to 30 days from the current date should only displayed.</div>"
			+ "<div><b>*</b>Next Review Date is already crossed should be displayed in red color (if any).</div>"
			+ "<div><b>*</b>Documents that are registered in Master plant should not be displayed (if any).</div>"
			+ "<div><b>*</b>Address and Company Logo should be displayed on the header part of the report.</div>"
			+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details should be displayed in bottom of the report.</div>"
			+ "<div><b>*</b>The option to take the print should be available Note: This document has been generated </div>"),
	click_DocumentsTobeReviewedList_AR("‘Documents To be Reviewed List’ screen should be displayed.<div>"
			+ "<div><b>*</b>The screen contains‘ Document Name, Unique Code, Version No, Code, Version No, Effective From, and Next Review’ columns.</div>"
			+ "<div><b>*</b>List of active documents for which Next Review Date is less than or equal to 30 days from the current date should only displayed.</div>"
			+ "<div><b>*</b>Next Review Date is already crossed is getting displayed in red color (if any).</div>"
			+ "<div><b>*</b>Documents that are registered in Master plant are not displayed (if any).</div>"
			+ "<div><b>*</b>Address and Company Logo is displayed on the header part of the report.</div>"
			+ "<div><b>*</b>‘Generated By’, ‘Generated On’ and Page No. <No.> of <No.>’ details are getting displayed in bottom of the report.</div>"
			+ "<div><b>*</b>The option to take the print is available Note: This document have been generated </div>"),
	click_DocumentsTobeReviewedList_SS("'Documents To be Reviewed List'."),

	// Job responsiblity reports

	Click_ToggleBtnforjobres_AC(
			"‘The option to search with ‘Employee Name, Employee ID and Department’ should be available.<div>"
					+ "<div><b>*</b>The screen should contain ‘View Report’ button.<div>"),

	Click_ToggleBtnforjobres_AR(
			"‘The option to search with ‘Employee Name, Employee ID and Department’ are  available.<div>"
					+ "<div><b>*</b>The screen contains ‘View Report’ button.<div>"),

	UnselectEmployeename_DC("Unselect the 'Employee Name'"),
	UnselectEmployeename_AC("'Employee Name' should be unselected."),
	UnselectEmployeename_AR("'Employee Name' is getting unselected."), UnselectEmployeename_SS("'Employee Name'"),
	Employeenametextbox_DC("An alert ‘Please enter a value for the parameter 'Employee Name'</div>"
			+ "<div><b>*</b>The parameter cannot be blank’ should be displayed with ‘Ok’ button."),

	Employeenametextbox_SS("Employee Name."),

	click_employeename_AC("'job Responsibility Revision List’ screen should be displayed.<div>"

			+ "<div><b>*</b>The screen should contain ‘Employee Name, Employee ID, Department’ details in read only format..</div>"
			+ "<div><b>*</b>The screen should contain ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.</div>"
			+ "<div><b>*</b>The screen should contain ‘Back’ button.</div>"
			+ "<div><b>*</b>The screen should contain ‘Find’ and ‘Next’ search fields..</div>"
			+ "<div><b>*</b> The screen should contain ‘Revision No., Valid From and Valid To’ columns.</div>"
			+ "<div><b>*</b>Revision ‘0’ particulars should be displayed with ‘Valid From’ and ‘Valid To’.</div>"
			+ "<div><b>*</b>‘Valid From’ field should be updated with Date and Time at which job responsibility should be approved.  </div>"
			+ "<div><b>*</b>‘Valid To’ filed should be updated with ‘- -‘.</div>"
			+ "<div><b>*</b>Hyperlink should be available to revision ‘0’.</div>"
			+ "<div><b>*</b>Note: Message should be displayed as ‘Note: To reflect the latest id in respective plant's' job responsibility records, the job responsibility needs to be modified in the respective plant.’ in red color at bottom of the screen.</div>"
			+ "<div><b>*</b>The option to take the print should be available .</div>"),

	click_employeename_AR("'job Responsibility Revision List’ screen is getting  displayed.<div>"

			+ "<div><b>*</b>The screen contains ‘Employee Name, Employee ID, Department’ details in read only format..</div>"
			+ "<div><b>*</b>The screen contains ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.</div>"
			+ "<div><b>*</b>The screen contains ‘Back’ button.</div>"
			+ "<div><b>*</b>The screen contains ‘Find’ and ‘Next’ search fields..</div>"
			+ "<div><b>*</b> The screen should contain ‘Revision No., Valid From and Valid To’ columns.</div>"
			+ "<div><b>*</b>Revision ‘0’ particulars is displayed with ‘Valid From’ and ‘Valid To’.</div>"
			+ "<div><b>*</b>‘Valid From’ field is updated with Date and Time at which job responsibility should be approved.</div>"
			+ "<div><b>*</b>‘Valid To’ filed is updated with ‘- -‘.</div>"
			+ "<div><b>*</b>Hyperlink is  available to revision ‘0’.</div>"
			+ "<div><b>*</b>Note: Message is displayed as ‘Note: To reflect the latest id in respective plant's' job responsibility records, the job responsibility is  modified in the respective plant.’ in red color at bottom of the screen.</div>"
			+ "<div><b>*</b>The option to take the print is available .</div>"),

	click_revisonno_AC("‘Job Responsibility’ screen should be displayed..<div>"
			+ "<div><b>*</b>The screen should contain ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.<div>"
			+ "<div><b>*</b>The screen should contain ‘Find’ and ‘Next’ search fields..</div>"
			+ "<div><b>*</b>The screen should contain ‘Back’ button.</div>"
			+ "<div><b>*</b> The screen should contain ‘Employee Name, Employee ID, Designation, Department, Qualification, Previous Experience, Reporting To, Job Description, Revision No., Authorized Deputy, and External Certificates’ details in read only format..</div>"
			+ "<div><b>*</b>The screen should contain ‘Action, Activity By & Activity On’ columns..</div>"
			+ "<div><b>*</b>‘Initiated’ and ‘Approved’ fields should be displayed.</div>"
			+ "<div><b>*</b>Initiated ‘Activity By & Activity On’ should be updated with initiated user’s <First name>.<Last Name> along with Initiated Date and Time. .</div>"
			+ "<div><b>*</b>‘The 'Activity By & Activity On' fields for Approved should be updated with the approved user's <First Name>.<Last Name>, along with the date and time of approval..</div>"
			+ "<div><b>*</b>This Job Responsibility is valid from ‘<Last Approval Approved Date and Time>’  To –should be displayed at bottom of the screen..</div>"
			+ "<div><b>*</b>The option to take the print should be available.</div>"),

	click_revisonno_AR("‘Job Responsibility’ screen is getting  displayed.<div>"
			+ "<div><b>*</b>The screen contains ‘>I’ and ‘I<’ buttons, Page Number <No.> of <No.>,  Refresh symbol, width of the report in the header screen.<div>"
			+ "<div><b>*</b>The screen contains ‘Find’ and ‘Next’ search fields..</div>"
			+ "<div><b>*</b>The screen contains ‘Back’ button.</div>"
			+ "<div><b>*</b> The screen contains ‘Employee Name, Employee ID, Designation, Department, Qualification, Previous Experience, Reporting To, Job Description, Revision No., Authorized Deputy, and External Certificates’ details in read only format.</div>"
			+ "<div><b>*</b>The screen contains ‘Action, Activity By & Activity On’ columns..</div>"
			+ "<div><b>*</b>‘Initiated’ and ‘Approved’ fields is displayed.</div>"
			+ "<div><b>*</b>Initiated ‘Activity By & Activity On’ is updated with initiated user’s <First name>.<Last Name> along with Initiated Date and Time.</div>"
			+ "<div><b>*</b>‘The 'Activity By & Activity On' fields for Approved is updated with the approved user's <First Name>.<Last Name>, along with the date and time of approval.</div>"
			+ "<div><b>*</b>This Job Responsibility is valid from ‘<Last Approval Approved Date and Time>’  To –is displayed at bottom of the screen..</div>"
			+ "<div><b>*</b>The option to take the print are available.</div>"),

	click_revisonno_DC("Click on the revision ‘0’ hyperlink"),

	Click_ViewReport_ForResponseNotYetSubmitted_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b> 'Response Not Yet Submitted' status should be displayed against the above session under 'Training Status'.</div>"),
	Click_ViewReport_ForResponseNotYetSubmitted_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b> 'Response Not Yet Submitted' status is getting displayed against the above session under 'Training Status'.</div>"),

	Click_EmpName_ForResponseNotYetSubmitted_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Response Not Yet Submitted'.</div>"),
	Click_EmpName_ForResponseNotYetSubmitted_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Response Not Yet Submitted'.</div>"),

	// Attendance Recorded

	Click_ViewReport_ForAttendanceRecorded_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b> 'Attendance Recorded' status should be displayed against the above session under 'Training Status'.</div>"),
	Click_ViewReport_ForAttendanceRecorded_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b> 'Attendance Recorded' status is getting displayed against the above session under 'Training Status'.</div>"),

	Click_EmpName_ForAttendanceRecorded_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Attendance Recorded'.</div>"),
	Click_EmpName_ForAttendanceRecorded_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Attendance Recorded'.</div>"),

	// "Response Submitted"
	Click_ViewReport_ForResponseSubmitted_AC("Records should be displayed based on the search criteria.</div>"
			+ "<div><b>* </b> 'Response Submitted' status should be displayed against the above session under 'Training Status'.</div>"),
	Click_ViewReport_ForResponseSubmitted_AR("Records are getting displayed based on the search criteFria.</div>"
			+ "<div><b>* </b> 'Response Submitted' status is getting displayed against the above session under 'Training Status'.</div>"),

	Click_EmpName_ForResponseSubmitted_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status' and 'Trainee Status' should be displayed as 'Response Submitted'.</div>"),
	Click_EmpName_ForResponseSubmitted_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+ "<div><b>*</b> 'Employee Status'  and 'Trainee Status' are getting displayed as 'Response Submitted'.</div>"),

	

	
	

	
	
	CLICK_SEARCH("Click 'Search' option"),
	
	
	
	


	
	
	

	ClickBatchName_AC("'Missed Question Analysis Log' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Batch Name', 'Questions & Answers', 'Maximum Marks', 'Marks Obtained' and 'Comments' should be updated accurately in the screen.'</div>"),
	ClickBatchName_AR("'Missed Question Analysis Log' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Batch Name', 'Questions & Answers', 'Maximum Marks', 'Marks Obtained' and 'Comments' are getting displayed accurately in the screen.'</div>"),
	// Deselect Employee ID

		UnselectEmployeeID_IER_DC("Unselect the 'Employee ID'"),
		UnselectEmployeeID_IER_AC("'Employee ID' should be unselected."),
		UnselectEmployeeID_IER_AR("'Employee ID' should be unselected."), UnselectEmployeeID_IER_SS("'Employee ID'"),
		Enter_SelectedEmployeeID_DC("Enter the EmployeeID  who is selected for the above proposed session."),
		// Missed Question Analysis Log

		CLICK_MQA_REPORT_DC("Click on 'Missed Question Analysis Log'."),
		CLICK_MQA_REPORT_AC("'Missed Question Analysis Log' screen should be displayed.</div>"
				+ "<div><b>*</b> Screen should contain 'Employee Name', 'Batch Name', 'Maximum Marks', 'Marks Obtained', and 'Evaluated On' columns.</div>"),
		CLICK_MQA_REPORT_AR("'Missed Question Analysis Log' screen is getting displayed.</div>"
				+ "<div><b>*</b> Screen contains 'Employee Name', 'Batch Name', 'Maximum Marks', 'Marks Obtained', and 'Evaluated On' columns.</div>"),
		CLICK_MQA_REPORT_SS("'Missed Question Analysis Log'."),
		SelectType_DC("Slect the 'Type'"), SelectType_AC("'Type' should be selected."),
		SelectType_AR("'Type' is getting selected."), SelectType_SS("'Type'"),
		SelectCompletedList_DC("Slect the 'Completed List'"),
		SelectCompletedList_AC("'Completed List' should be selected."),
		SelectCompletedList_AR("'Completed List' is getting selected."), SelectCompletedList_SS("'Completed List'"),

	;

	private final String reports;

	Reports(String reports) {

		this.reports = reports;

	}

	public String getReports() {
		return reports;
	}

}
