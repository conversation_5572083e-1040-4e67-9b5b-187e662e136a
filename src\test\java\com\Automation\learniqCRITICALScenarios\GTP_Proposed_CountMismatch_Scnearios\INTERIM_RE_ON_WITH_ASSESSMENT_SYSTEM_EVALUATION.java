package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online RE type Session with assessment for scheduled course and make
 * at least one employee qualified, To Be Retrained with System evaluation and
 * by viewing Individual employee report at each transaction starting from
 * course session.
 */

public class INTERIM_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION extends OQActionEngine {

	public INTERIM_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	String ExcelPath = "./learnIQTestData/Interim_P1_TestData/INTERIM_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/INTERIM_RE_SYSTEM_EVAL.xlsx";

	// Test Method for Topic Registration

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("TopicRegistration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);
	}

	// Test Method for Course Configuration
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}

		Initiate_Course.Refresher_Course_Registration(testData);
	}

	ExcelUtilUpdated GTPData = new ExcelUtilUpdated(ExcelPath, "ProposeIGTP");

	@DataProvider(name = "ProposeIGTP")
	public Object[][] getGTPData() throws Exception {
		Object[][] obj = new Object[GTPData.getRowCount()][1];
		for (int i = 1; i <= GTPData.getRowCount(); i++) {
			HashMap<String, String> testData = GTPData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "ProposeIGTP", enabled = true)
	public void proposeigtp(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Propose IGTP")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Propose IGTP");
		}
		InterimGTP.ProposeInterim_GTP(testData);
		TimeUtil.longwait();
	}

	// Test Method for CourseSession Registration
	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");
		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();
		CSRReport.TBPCSRReport();
		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		verifyCSScreen.courseSession_Online_DocumentReading_WithExam(testData);
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");
		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();
		CSRReport.TBPCSRReport();
		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
	}

	// Test Method for QuestionBank Registration
	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "QuestionBank")
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}

//		
		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);

	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with  System Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with System Evaluation");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.REType);
		Logout.signOutPage();

	}

//	// Test Method for Respond Document Reading and Respond to QP and make user
//	// Qualified and view IER
//
	@Test(priority = 7, enabled = true)
	public void qualifiedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user Qualified");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.QualifiedTraineeID, CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType,
				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);
		Logout.signOutPage();

	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 8, enabled = true)
	public void toBeRetrainedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained")
					.assignAuthor(CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID(),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 9, enabled = true)
	public void toretakependingEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user  to retake Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user  to retake Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_VerifyCourseSessionScreen.retakeUserID,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		Logout.signOutPage();

	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// QPPending and view IER

	@Test(priority = 10, enabled = true)
	public void qppendingEmp_RespondDocReading_RespondQuestionPaperppending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user  to QP Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user  to QP Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQPPendingUserID(), CM_VerifyCourseSessionScreen.QPPendingUserPsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QPPendingUserPsw);
		Logout.signOutPage();
	}

	@Test(priority = 11, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session screen after keeping employees in different states")
					.assignAuthor(CM_VerifyCourseSessionScreen.QPPendingUserID)
					.assignCategory("Course Session screen after keeping employees in different states");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		verifyCSScreen.checkCourseSession_RESystemEvaluation_After_Keeping_Employees_In_Different_States();
	}

	@Test(priority = 12, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Report screen after keeping employees in different states")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session Report screen after keeping employees in different states");
		}
		CSRReport.Interim_TBPCSRReport_RE_SystemEval_After_keping_Emplpoyees_In_Diff_States();
	}

}
