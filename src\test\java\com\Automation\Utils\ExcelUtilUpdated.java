package com.Automation.Utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
@SuppressWarnings("deprecation")
public class ExcelUtilUpdated {
	public Workbook book;
	public Sheet sheet;

	public ExcelUtilUpdated(String filePath, String sheetName) {
		try {
			FileInputStream file = new FileInputStream(new File(filePath));
			book = WorkbookFactory.create(file);
			sheet = book.getSheet(sheetName);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (InvalidFormatException e) {
			e.printStackTrace();
		}
	}

	public Object[][] getTestData() {
		int rowCount = getRowCount();
		int columnCount = getCellCount();

		Object[][] data = new Object[rowCount][columnCount];

		for (int i = 0; i < rowCount; i++) {
			Row row = sheet.getRow(i + 1);
			for (int j = 0; j < columnCount; j++) {
				Cell cell = row.getCell(j);
				cell.setCellType(Cell.CELL_TYPE_STRING);
				data[i][j] = cell.getStringCellValue().trim();
			}
		}

		return data;
	}

	public HashMap<String, String> getTestDataInMap(int rowNum) {
		HashMap<String, String> testDataMap = new HashMap<>();
		Row headerRow = sheet.getRow(0);
		Row dataRow = sheet.getRow(rowNum);

		if (dataRow != null) { // Add null check for data row
			for (int i = 0; i < headerRow.getLastCellNum(); i++) {
				Cell headerCell = headerRow.getCell(i);
				Cell dataCell = dataRow.getCell(i);

				if (headerCell != null && dataCell != null) { // Add null checks for header and data cells
					headerCell.setCellType(Cell.CELL_TYPE_STRING);
					dataCell.setCellType(Cell.CELL_TYPE_STRING);

					String key = headerCell.getStringCellValue().trim();
					String value = dataCell.getStringCellValue().trim();

					testDataMap.put(key, value);
				}
			}
		}
		return testDataMap;

	}

	public int getRowCount() {
		return sheet.getLastRowNum();
	}

	public int getCellCount() {
		Row headerRow = sheet.getRow(0);
		return headerRow.getLastCellNum();
	}

}
