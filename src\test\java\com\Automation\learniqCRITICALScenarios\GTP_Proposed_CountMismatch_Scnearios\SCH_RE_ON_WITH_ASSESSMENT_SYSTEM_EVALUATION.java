package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online RE type Session with assessment for scheduled course and make
 * at least one employee qualified, To Be Retrained with System evaluation and
 * by viewing Individual employee report at each transaction starting from
 * course session.
 */

public class SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION extends OQActionEngine {

	public SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	String ExcelPath = "./learnIQTe" + "stData/SGTestData/SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";
	// ----------Topic - Test Method---------- //

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic  Registration");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
		Initiate_Course.Refresher_Course_Registration(testData);
	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule  Modification");
		}

		// TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);
		Logout.signOutPage();
	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails
	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();

		verifyCSScreen.courseSession_Online_DocumentReading_WithExam(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();
		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
//			Logout.signOutPage();
	}
	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "QuestionBank")
	public void QuestionBank_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}

//		
		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
		// Logout.signOutPage();
	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with System Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with System Evaluation");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.REType);
		Logout.signOutPage();

	}

//	// Test Method for Respond Document Reading and Respond to QP and make user
//	// Qualified and view IER
//
	@Test(priority = 7, enabled = true)
	public void qualifiedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user Qualified");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQualifiedTraineeID(), CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType,
				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);
		Logout.signOutPage();

	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 8, enabled = true)
	public void toBeRetrainedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained")
					.assignAuthor(CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getToBeRetrainedTraineeID(),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);
		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 9, enabled = true)
	public void RetakePending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To retake pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To retake pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_VerifyCourseSessionScreen.retakeUserID,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		Logout.signOutPage();

	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// QPPending and view IER

	@Test(priority = 10, enabled = true)
	public void respondDocReading() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQPPendingUserID(), CM_VerifyCourseSessionScreen.QPPendingUserPsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QPPendingUserPsw);
		// Logout.signOutPage();
	}

	@Test(priority = 11, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states before course retraining");
		}
		verifyCSScreen.checkCourseSession_RE_With_Assessment_SystemEval_After_Keeping_Employees_In_Different_States();
	}

	@Test(priority = 12, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session report Screen after keping employees in different states before course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  report Screen after keping employees in different states before course retraining");
		}
		CSRReport.TBPCSRReport_RE_WithAssesment_After_keping_Emplpoyees_In_Diff_States();
	}

	@Test(priority = 13, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("course retraining");
		}
		CourseRetraining.Scheduled_course_Retraining_RE_Online_With_Assessment_();

	}

	@Test(priority = 14, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states in Unscheduled tab after course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states in Unscheduled tab after course retraining");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_();
	}

	@Test(priority = 15, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining1() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states in scheduled tab after course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states  in scheduled tab after course retraining");
		}
		verifyCSScreen.scheduled_RE_online_With_Assessment_VerifyCourseSession_Screen_After_CourseRetraining();
	}

	@Test(priority = 16, enabled = true)
	public void verifyCSR_AfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session Report Screen after keping employees in different states after course retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session Report Screen after keping employees in different states after course retraining");
		}
		CSRReport.TBPCSRReport_RE_WithAssesment_After_CourseRetraining();

		Logout.signOutPage();

	}
}
