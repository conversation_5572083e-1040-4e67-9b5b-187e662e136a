package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.learniqObjects.SSO_UserRegistration;
//import com.Automation.learniqObjects.MDM_Department_Registration;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.RoleStrings;
import com.Automation.Strings.SetGlobleProfileStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Strings.UserProducAssignStrings;
import com.Automation.Strings.UserRegistrationStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class SSO_UserProductModuleAssignment extends OQActionEngine {

	public static String departmentName = "";

	public static String getDepartmentName() {
		return departmentName;
	}

	public static void setDepartmentName(String departmentName) {
		SSO_UserProductModuleAssignment.departmentName = departmentName;
	}

	Properties prop;
	@FindBy(xpath = "//ul[1]/li[3]/a[1]//span[contains(text(),'Identity Manager')]")
	WebElement identityManager;
	@FindBy(xpath = "//ul[1]/li[3]/ul[@class='sub-menu']/li[1]/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(id = "USERASS")
	WebElement userProduct;
	@FindBy(xpath = "//button[@id='btnModal_UserProductsAssignment_User']/span")
	WebElement addItem;
	@FindBy(id = "UserProductsAssignment_User_FindTxt")
	WebElement findTxt;
	@FindBy(id = "UserProductsAssignment_User_DisplayBtn")
	WebElement apply;
	@FindBy(xpath = "//table[@id='ListTab1']/tbody[1]/tr[1]/td[1]/input[1]")
	WebElement userRB;
	@FindBy(id = "UserProductsAssignment_User_selectBtn")
	WebElement addBtn;
	@FindBy(xpath = "//div[@class='col-sm-2']/input[@name='UserProductsAssignment.UserProductsList[2].IsChecked']")
	WebElement selectBtn;
	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[2].CategoryId']/following::span[1]")
	WebElement category;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement searchField;
	@FindBy(xpath = "//ul[@class='select2-results__options']//li[2]")
	WebElement categoryOption;
	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[2].RoleId']/following::span[1]")
	WebElement role;
	@FindBy(xpath = "//ul[@class='select2-results__options']/li[1]")
	WebElement roleOption;
	@FindBy(xpath = "//button[@id='btnModal_Plant_2']/span")
	WebElement plantBtn;
	@FindBy(id = "Plant_2_FindTxt")
	WebElement plantFindText;
	@FindBy(xpath = "//input[@id='Plant_2_DisplayBtn']")
	WebElement plantApplyBtn;
	@FindBy(xpath = "//table[@id='multipopupfilter2']/tbody//tr[@role='row']//td[3]//button[@type='button']")
	WebElement plantR1Btn;
	@FindBy(xpath = "//button[@id='Plant_2_selectBtn']")
	WebElement plantAddBtn;
	@FindBy(xpath = "//button[@id='btnModal_Department_2']")
	WebElement departmentBtn;
	@FindBy(id = "Department_2_FindTxt")
	WebElement deptFindTxt;
	@FindBy(id = "Department_2_DisplayBtn")
	WebElement deptApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab1']/tbody[1]/tr[1]/td[1]/input[1]")
	WebElement depR2Btn;
	@FindBy(xpath = "//input[@id='Department_2_selectBtn']")
	WebElement depAddBtn;
	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[2].InductionTrainingRequired'][@value='0']")
	WebElement inductionTraningNo;
	@FindBy(id = "btnConfirm")
	WebElement submitBtn;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(id = "btnModal_UserProductsAssignment_ReportingTo")
	WebElement reportingToAddItem;
	@FindBy(id = "UserProductsAssignment_ReportingTo_FindTxt")
	WebElement reportingToFindTxt;
	@FindBy(id = "UserProductsAssignment_ReportingTo_DisplayBtn")
	WebElement reportingToApply;
	@FindBy(id = "UserProductsAssignment_ReportingTo_selectBtn")
	WebElement reportingToAddBtn;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
//	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].CategoryId']/following::span[1]")
//	WebElement category1;

	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].CategoryId']/following::span[1]")
	WebElement category1;

	// single build
	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].CategoryId']/following::span[1]")
	WebElement categorysinglebuild1;

//	@FindBy(xpath = "//input[@id='chk_1104']")
//	WebElement selectBtn1;

	@FindBy(xpath = "//*[text()='learn-iq (learn-iq)']//parent::a//parent::div/following-sibling::div/input[1]")
	WebElement selectBtn1;

//	@FindBy(xpath = "//button[@id='btnModal_Plant_0']/span")
//	WebElement plantBtn1;

//	@FindBy(xpath = "//button[@id='btnModal_Plant_1']/span")
//	WebElement plantBtn1;
//	
	// singlebuild
	@FindBy(xpath = "//button[@id='btnModal_Plant_0']/span")
	WebElement plantBtn1;

//	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].RoleId']/following::span[1]")
//	WebElement role1;

//	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].RoleId']/following::span[1]")
//	WebElement role1;

	// singlebuild
	@FindBy(xpath = "//select[@name='UserProductsAssignment.UserProductsList[0].RoleId']/following::span[1]")
	WebElement role1;

//	@FindBy(xpath = "//button[@id='btnModal_Department_0']")
//	WebElement departmentBtn1;
//	
//	
	@FindBy(xpath = "//button[@id='btnModal_Department_2']")
	WebElement departmentBtn1;

//	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='0']")
//	WebElement inductionTraningNo1;
//	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='1']")
//	WebElement inductionTraningYes1;

//	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='0']")
//	WebElement inductionTraningNo1;
//	

	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='0']")
	WebElement inductionTraningNo1;

//	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='1']")
//	WebElement inductionTraningYes1;
//	
	@FindBy(xpath = "//input[@name='UserProductsAssignment.UserProductsList[0].InductionTrainingRequired'][@value='1']")
	WebElement inductionTraningYes1;

//	@FindBy(xpath = "//input[@id='Plant_0_FindTxt']")
//	WebElement plantFindText1;
//	@FindBy(xpath = "//input[@id='Plant_0_FindTxt']")
//	WebElement plantFindText1;
//	

	@FindBy(xpath = "//input[@id='Plant_0_FindTxt']")
	WebElement plantFindText1;

//	@FindBy(xpath = "//input[@id='Plant_0_DisplayBtn']")
//	WebElement plantApplyBtn1;
//	@FindBy(xpath = "//button[@id='Plant_0_selectBtn']")
//	WebElement plantAddBtn1;

//	@FindBy(xpath = "//input[@id='Plant_0_DisplayBtn']")
//	WebElement plantApplyBtn1;
//	
	@FindBy(xpath = "//input[@id='Plant_0_DisplayBtn']")
	WebElement plantApplyBtn1;

//	@FindBy(xpath = "//button[@id='Plant_0_selectBtn']")
//	WebElement plantAddBtn1;
//	
	@FindBy(xpath = "//button[@id='Plant_0_selectBtn']")
	WebElement plantAddBtn1;

//	@FindBy(id = "Department_0_FindTxt")
//	WebElement deptFindTxt1;
//	

	@FindBy(id = "Department_0_FindTxt")
	WebElement deptFindTxt1;

//	@FindBy(id = "Department_0_DisplayBtn")
//	WebElement deptApplyBtn1;

	@FindBy(id = "Department_0_DisplayBtn")
	WebElement deptApplyBtn1;

//	@FindBy(xpath = "//input[@id='Department_0_selectBtn']")
//	WebElement depAddBtn1;

	@FindBy(xpath = "//input[@id='Department_0_selectBtn']")
	WebElement depAddBtn1;

	@FindBy(xpath = "//ul[1]/li[3]/ul[@class='sub-menu']/li[3]/a[contains(text(),'Audit Trails')]")
	WebElement auditTrailsMenu;
	@FindBy(xpath = "//a[text()='Audit Trails']//following-sibling::ul//span[text()='User Product / Module Assignment']")
	WebElement auditUserProductMenu;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(id = "select2-UserProductsAssignment_User_selectDdl-container")
	WebElement searchBy;
	@FindBy(xpath = "//ul[@class='select2-results__options']//li[text()='Employee ID']")
	WebElement searchByEmployeeID;
	@FindBy(xpath = "//label[text()='Find']//following-sibling::input")
	WebElement employeeID_Like;
	@FindBy(xpath = "//input[@id='SrhEmployeeId']")
	WebElement auditEmployeeID_Like;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//span[@title='Advanced Search']")
	WebElement searchfilter;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement iniatiatedby;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement iniatiatedon;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement revNo;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement searchFilter;
//	@FindBy(id = "btnModal_Department_0")
//	WebElement department;
//	@FindBy(xpath = "//span[@id='select2-Department_0_selectDdl-container']//following-sibling::span")
//	WebElement departmentdropdown;
//	@FindBy(xpath = "//ul[@id='select2-Department_0_selectDdl-results']")
//	WebElement selectdepartment;
//	@FindBy(id = "Department_0_FindTxt")
//	WebElement departmentFindTxt;
//	@FindBy(id = "Department_0_DisplayBtn")
//	WebElement departmentDisplayBtn;

//	@FindBy(id = "btnModal_Department_0")
//	WebElement department;
//	

	@FindBy(id = "btnModal_Department_0")
	WebElement department;

//	@FindBy(xpath = "//span[@id='select2-Department_0_selectDdl-container']//following-sibling::span")
//	WebElement departmentdropdown;
//	
	@FindBy(xpath = "//span[@id='select2-Department_0_selectDdl-container']//following-sibling::span")
	WebElement departmentdropdown;

//	@FindBy(xpath = "//ul[@id='select2-Department_0_selectDdl-results']")
//	WebElement selectdepartment;

	@FindBy(xpath = "//ul[@id='select2-Department_0_selectDdl-results']")
	WebElement selectdepartment;

//	@FindBy(id = "Department_0_FindTxt")
//	WebElement departmentFindTxt;
//	

	@FindBy(id = "Department_0_FindTxt")
	WebElement departmentFindTxt;

//	@FindBy(id = "Department_0_DisplayBtn")
//	WebElement departmentDisplayBtn;
//	

	@FindBy(id = "Department_0_DisplayBtn")
	WebElement departmentDisplayBtn;

	@FindBy(xpath = "//input[@class='radioCls margin-right-10 caliber-labeled-option']")
	WebElement departmentRadioBtn;
//	@FindBy(id = "Department_0_selectBtn")
//	WebElement departmentAddBtn;

//	@FindBy(id = "Department_0_selectBtn")
//	WebElement departmentAddBtn;

	@FindBy(id = "Department_0_selectBtn")
	WebElement departmentAddBtn;

	@FindBy(xpath = "//span[text()='BRM (Batch Resource)']")
	WebElement departmentAddBtn1;
	@FindBy(xpath = "//label[contains(text(),'Confirm')]")
	WebElement confirmDecision;
	@FindBy(id = "CurrentPassword")
	WebElement CurrentPasword;
	@FindBy(id = "NewPassword")
	WebElement NewPassword;
	@FindBy(id = "ConfirmPassword")
	WebElement ConfirmPassword;
	@FindBy(id = "Logout")
	WebElement Logout;
	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement remarks;
	@FindBy(id = "txtESignPassword")
	WebElement eSign;
	@FindBy(xpath = "//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Induction Training Required']//following-sibling::label")
	WebElement auditCompareTRNInductionTraining;

	public SSO_UserProductModuleAssignment() {
		PageFactory.initElements(driver, this);
	}

	public void UserProductModuleAssignment_With_AuditTrails(HashMap<String, String> testData) {

		// String DeptCode = MDM_Department_Registration.getDepartmentcode();

		// String DeptCode = "PMGAutomationCode";

		// String role = SYS_RoleRegistration.getRole();
		// String role = "LOGIN";
		// String category = SYS_RoleRegistration.getCategory();
		String firstName = SSO_UserRegistration.getFirstName();
		String LastName = SSO_UserRegistration.getLastName();
		String employeeID = SSO_UserRegistration.getEmployeeID();
		String userID = SSO_UserRegistration.getUserID();
		setDepartmentName(departmentName = testData.get("DepartmentName"));

//			String firstName = "userXGAR";
//			String LastName = "userXGAR";
//			String employeeID = "userXGAR";
//			String userID = "userXGAR";
		String category = testData.get("Category");
		String role = testData.get("Role");
		TimeUtil.shortWait();
		waitForElementVisibile(initiateMenu);
//			clickAndWaitforNextElement(identityManager, initiateMenu,
//					UserRegistrationStrings.Click_IM_DC.getUserRegistrationStrings(),
//					UserRegistrationStrings.Click_IM_AC.getUserRegistrationStrings(),
//					UserRegistrationStrings.Click_IM_AR.getUserRegistrationStrings(),
//					UserRegistrationStrings.Click_IM_SS.getUserRegistrationStrings());
//		clickAndWaitforNextElement(initiateMenu, userProduct, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
//				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
//				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
//				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(userProduct, UserProducAssignStrings.IM_UserProduct_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.IM_UserProduct_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.IM_UserProduct_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.IM_UserProduct_SS.getUserProducAssignStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(addItem, searchBy,
				UserProducAssignStrings.AddItem_UserName_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_UserName_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_UserName_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_UserName_SS.getUserProducAssignStrings());
		clickAndWaitforNextElement(searchBy, searchByEmployeeID, CommonStrings.SearchBy_DC.getCommonStrings(),
				UserProducAssignStrings.UsersList_SearchBy_310_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.UsersList_SearchBy_310_AR.getUserProducAssignStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeID, employeeID_Like,
				UserProducAssignStrings.SearchBy_EmployeeID_DC.getUserProducAssignStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				UserProducAssignStrings.SearchBy_EmployeeID_SS.getUserProducAssignStrings());
		sendKeys2(employeeID_Like, UserProducAssignStrings.Find_EmployeeID_DC.getUserProducAssignStrings(),
				employeeID + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				UserProducAssignStrings.Find_EmployeeID_SS.getUserProducAssignStrings());
		click2(apply, CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userRB, addBtn,
				UserProducAssignStrings.UserNameRadioBtn_DC.getUserProducAssignStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				UserProducAssignStrings.UserNameRadioBtn_SS.getUserProducAssignStrings());
		clickAndWaitforNextElement(addBtn, selectBtn1,
				UserProducAssignStrings.UserName_AddButton_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.UserName_AddButton_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.UserName_AddButton_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.UserName_AddButton_SS.getUserProducAssignStrings());
		click2(selectBtn1,UserProducAssignStrings.Select_learniq_DC.getUserProducAssignStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				UserProducAssignStrings.Select_learniq_SS.getUserProducAssignStrings());
		clickAndWaitforNextElement(categorysinglebuild1, categoryOption,
				UserProducAssignStrings.Category_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_SS.getUserProducAssignStrings());
		clickAndWaitforNextElement(categoryOption, role1,
				UserProducAssignStrings.Category_Login_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_Login_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_Login_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.Category_Login_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(role1, searchField, UserProducAssignStrings.Role_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.Role_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.Role_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.Role_SS.getUserProducAssignStrings());
		sendKeys2(searchField, UserProducAssignStrings.Search_Role_DC.getUserProducAssignStrings(), role,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				UserProducAssignStrings.Search_Role_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(roleOption, plantBtn1,
				UserProducAssignStrings.RoleSelect_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.RoleSelect_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.RoleSelect_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.RoleSelect_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(plantBtn1, plantFindText1,
				UserProducAssignStrings.AddItem_Plant_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_Plant_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_Plant_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItem_Plant_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		sendKeys2(plantFindText1, UserProducAssignStrings.Find_PlantName_DC.getUserProducAssignStrings(),
				testData.get("plant1") + '%', CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				UserProducAssignStrings.Find_PlantName_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		click2(plantApplyBtn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(plantR1Btn, UserProducAssignStrings.AddButtonAgainstPlantName_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		sendKeys2(plantFindText1, UserProducAssignStrings.Find_PlantName2_DC.getUserProducAssignStrings(),
				testData.get("plant2") + '%', CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				UserProducAssignStrings.Find_PlantName_SS.getUserProducAssignStrings());
		TimeUtil.shortWait();
		click2(plantApplyBtn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(plantR1Btn, plantAddBtn1,
				UserProducAssignStrings.AddButtonAgainstPlantName_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddButtonAgainstPlantName_SS.getUserProducAssignStrings());
		click2(plantAddBtn1, UserProducAssignStrings.AddPlantName_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddPlantName_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddPlantName_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddPlantName_SS.getUserProducAssignStrings());
		click2(department, UserProducAssignStrings.AddItemDepartment_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItemDepartment_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItemDepartment_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.AddItemDepartment_SS.getUserProducAssignStrings());
		sendKeys2(departmentFindTxt, UserProducAssignStrings.Find_Department_310_DC.getUserProducAssignStrings(),
				testData.get("DepartmentCode"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				UserProducAssignStrings.Find_Department_310_SS.getUserProducAssignStrings());
		TimeUtil.mediumWait();
		click2(departmentDisplayBtn, 
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(departmentRadioBtn, departmentAddBtn,
				UserProducAssignStrings.DepartmentRadioBtn_310_DC.getUserProducAssignStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				UserProducAssignStrings.DepartmentRadioBtn_310__SS.getUserProducAssignStrings());
		click2(departmentAddBtn, UserProducAssignStrings.Department_AddButton_DC.getUserProducAssignStrings(),
				UserProducAssignStrings.Department_AddButton_310_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.Department_AddButton_310_AR.getUserProducAssignStrings(),
				UserProducAssignStrings.Department_AddButton_SS.getUserProducAssignStrings());
		if (testData.get("inductionTrainingRequired").equals("Yes")) {
			click2(inductionTraningYes1, UserProducAssignStrings.InductionTraininReqYes_DC.getUserProducAssignStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					UserProducAssignStrings.InductionTraininReqYes_SS.getUserProducAssignStrings());
		} else if (testData.get("inductionTrainingRequired").equals("No")) {
			click2(inductionTraningNo1, UserProducAssignStrings.InductionTraininReqNO_DC.getUserProducAssignStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					UserProducAssignStrings.InductionTraininReqNO_SS.getUserProducAssignStrings());

		}
		TimeUtil.shortWait();
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				UserProducAssignStrings.SubmitProductModuleAssignwithEsign_AC.getUserProducAssignStrings(),
				UserProducAssignStrings.SubmitProductModuleAssignwithEsign_AR.getUserProducAssignStrings(),
				CommonStrings.Submit_SS.getCommonStrings());
		TimeUtil.mediumWait();
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("SSOPassword"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		TimeUtil.shortWait();
		String MenuName = Constants.MENUNAME_AS_USER_PRODUCT_MODULE;
		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		switchToDefaultContent(driver);

//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(auditTrailsMenu, auditUserProductMenu,
//				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(auditUserProductMenu, UserProducAssignStrings.IM_UserProduct_DC.getUserProducAssignStrings(),
//				UserProducAssignStrings.IM_UserProductAuditScreen_AC.getUserProducAssignStrings(),
//				UserProducAssignStrings.IM_UserProductAuditScreen_AR.getUserProducAssignStrings(),
//				UserProducAssignStrings.IM_UserProduct_SS.getUserProducAssignStrings());
//		TimeUtil.shortWait();
//		switchToBodyFrame(driver);
//		TimeUtil.shortWait();
//		searchfilter.click();
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(searchByNew, searchByEmployeeID, CommonStrings.SearchBy_DC.getCommonStrings(),
//				UserProducAssignStrings.UserProductAuditScreen_310_SearchBy_AC.getUserProducAssignStrings(),
//				UserProducAssignStrings.UserProductAuditScreen_310_SearchBy_AR.getUserProducAssignStrings(),
//				CommonStrings.SearchBy_SS.getCommonStrings());
//		clickAndWaitforNextElement(searchByEmployeeID, auditEmployeeID_Like,
//				UserProducAssignStrings.SearchBy_EmployeeID_DC.getUserProducAssignStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				UserProducAssignStrings.SearchBy_EmployeeID_SS.getUserProducAssignStrings());
//		sendKeys2(auditEmployeeID_Like, UserProducAssignStrings.Like_EmployeeID_DC.getUserProducAssignStrings(),
//				employeeID, CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				UserProducAssignStrings.Like_EmployeeID_SS.getUserProducAssignStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(displayedRecord, UserProducAssignStrings.Click_UserName_for_AuditTrails_DC.getUserProducAssignStrings(),
//				UserProducAssignStrings.Click_UserName_for_AuditTrails_AC.getUserProducAssignStrings(),
//				UserProducAssignStrings.Click_UserName_for_AuditTrails_AR.getUserProducAssignStrings(),
//				UserProducAssignStrings.Click_UserName_for_AuditTrails_SS.getUserProducAssignStrings());
//		driver.switchTo().frame(0);
//		TimeUtil.shortWait();
//		scrollToViewElement(revisionNoTitleCompareTRN);
//		scrollToViewElement(auditCompareTRNInductionTraining);
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				UserProducAssignStrings.Close_AuditTrails_UserProModAssign_AC.getUserProducAssignStrings(),
//				UserProducAssignStrings.Close_AuditTrails_UserProModAssign_AR.getUserProducAssignStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		switchToDefaultContent(driver);
	}

}