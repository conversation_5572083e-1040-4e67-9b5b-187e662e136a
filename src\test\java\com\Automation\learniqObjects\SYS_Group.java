package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.apache.commons.math3.stat.inference.TestUtils;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.GroupStrings;
import com.Automation.Strings.RecordAttendanceStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class SYS_Group extends OQActionEngine {

	Properties prop;
	public static String RegGroupName1 = "";
	public static String RegDescription1 = "";

	public static String RegGroupName2 = "";
	public static String RegDescription2 = "";
	public static String Initiator = "";
	public static String Approver = "";
	public static String GroupUC = "";

	public static String getRegGroupName() {
		return RegGroupName1;
	}

	public static void setRegGroupName(String regGroupName) {
		RegGroupName1 = regGroupName;
	}

	public static String getRegDescription() {
		return RegDescription1;
	}

	public static void setRegDescription(String regDescription) {
		RegDescription1 = regDescription;
	}

	public static String getInitiatorName() {
		return Initiator;
	}

	public static void setInitiatorName(String InitiatorName) {
		Initiator = InitiatorName;
	}

	public static String getApproverName() {
		return Approver;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	public static String getGroupUC() {
		return GroupUC;
	}

	public static void setGroupUC(String GroupUniqueCode) {
		GroupUC = GroupUniqueCode;
	}

	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;

	@FindBy(xpath = "//select[@id='Groups_Left']/option[1]")
	WebElement availableSubgroup1;
	@FindBy(id = "Groups_Left_rightSelected")
	WebElement rightToggle2;
	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Re-Initiation')]")
	WebElement reinitiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'RI Transfer')]")
	WebElement reTranswerMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Configure')]")
	WebElement configurationMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN08")
	WebElement groupCnfgMenu;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;

	@FindBy(id = "Config.IsEsignAtCreatInit")
	WebElement eSignAtRegInitCheckBox;

	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;

	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDropdown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement r1emarks;
	@FindBy(xpath = "/html[1]/body[1]/span[1]/span[1]/span[2]/ul[1]/li[2]")
	WebElement searchSel;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN08")
	WebElement group;

	@FindBy(id = "TMS_System Manager_User Groups_MODMEN21_SUBMEN08")
	WebElement ritrnaswerGroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN109_SUBMEN08")
	WebElement reinigtiateGroup;

	@FindBy(id = "Groups_GrpDesc")
	WebElement groupNameTxt;
	@FindBy(id = "Groups_Description")
	WebElement descriptionTxt;
	@FindBy(xpath = "//select[@id='Groups_Left']//preceding-sibling::input")
	WebElement availableSubgroupSearchNew;
	@FindBy(xpath = "//div[@class='form-group form-group1']//input[@class='form-control']")
	WebElement availableSubgroupSearchNew2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[1]")
	WebElement searchSel3;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Modify')]")
	WebElement modifyMenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN08")
	WebElement modifyGroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN17")
	WebElement modSubgroupAssignMenu;
	@FindBy(id = "SgpAsn_TreeVC_SearchTxt")
	WebElement modifySubGrpTxt;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement searchFilter;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Employee Name')]")
	WebElement searchByNewDropdown;

	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//div[contains(text(),'Re-initiation Task Transfer (Reg.)')]")
	WebElement transwerRecord;

	@FindBy(id = "AuditEventModal_View")
	WebElement proceedButton;

	@FindBy(id = "Description")
	WebElement sendTxtLike;
	@FindBy(id = "displayBtn")
	WebElement displayBtn;
	@FindBy(xpath = "//a[contains(text(),'Fetch Records')]")
	WebElement fetchRecords;
	@FindBy(xpath = "//ul[@id=\"SgpAsn_Subgrps_ul\"]//li[1]")
	WebElement addSubGroupname;
	@FindBy(id = "SubGroupAssignment_Remarks")
	WebElement subGrpModifyRemarksval;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement filter;
	@FindBy(xpath = "//input[@class='caliber-textbox']")
	WebElement recordsPerPage;
	@FindBy(id = "displayBtn")
	WebElement apply;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(id = "Config_Remarks")
	WebElement remarks;

	@FindBy(id = "Groups_Remarks")
	WebElement ReInitiateremarks;

	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN08")
	WebElement groupAuditTrails;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;

	@FindBy(xpath = "//span[@role='presentation']")
	WebElement searchByNewFilter;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Group')]")
	WebElement searchByGroupNameDropdown;
	@FindBy(id = "Description")
	WebElement groupNameLike;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;

	@FindBy(xpath = "//td[text()='No data available in table']")
	WebElement nodataAvailabel;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']")
	WebElement auditCompareTRNActionDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeDropValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditCompareTRNAppRemarksDropVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']")
	WebElement auditCompareTRNActionReturnValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByReturnValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeReturnValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksReturnVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']")
	WebElement auditCompareTRNActionReInitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeReinitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByReinitiateValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksReinitiateVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareTRNActionApproveValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeApproveValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareTRNAppRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;

	@FindBy(xpath = "//span[@id='caliber-sub-title-1']")
	WebElement audiTransweTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']//following-sibling::span")
	WebElement auditUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Group Name']//following-sibling::span")
	WebElement auditGroupName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Description']//following-sibling::span")
	WebElement auditDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Subgroup Name(s)')]//following-sibling::span")
	WebElement auditSubgroupName;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']//li//a[text()='Groups']")
	WebElement configurationGroupAuditTrails;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Approve']//following-sibling::ul//li//a[text()='Group']")
	WebElement approveGroupMenu;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[contains(text(),'Group Name')]")
	WebElement approveGroup_SearchBy;

	@FindBy(xpath = "//select[@id='SearchType']//option[contains(text(),'Group Name')]")
	WebElement groupSelection;

	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;

	@FindBy(xpath = "//input[@id='SelectedDecision_3']")
	WebElement returnRadioBtn;

	@FindBy(id = "TransferUserPopUpBtn")
	WebElement transwerAddItems;

	@FindBy(xpath = "//input[@type='search']")
	WebElement searchby;

	@FindBy(xpath = "//button[@id='Transfer_selectBtn']")
	WebElement radiobutton;

	@FindBy(xpath = "//input[@type='radio']")
	WebElement arButton;

	@FindBy(id = "Transfer_selectBtn")
	WebElement addButton;

	@FindBy(xpath = "//input[@id='SelectedDecision_4']")
	WebElement dropRadioBtn;

	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;

	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;

	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationApprovalAction;

	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;
	@FindBy(xpath = "//select[@id='Groups_Left_to']//option[1]")
	WebElement selectedSubgroup;

	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approveLabelText;

	@FindBy(xpath = "//label[text()='Return']")
	WebElement returnLabelText;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callAtEsignInitiationLabel;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callAtEsignApprovalLabel;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;

//	 ConfigureRemarks  configureConfirmation Zero
	public void group_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(configurationMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(groupCnfgMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Config_AC.getGroupStrings(), GroupStrings.Group_Config_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		SelectRadioBtnAndCheckbox(driver, eSignAtRegInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtInitAprCheckBox, "Call E-sign At: Approval Registration");
		click2(statusChangeTabMenu, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(noOfAprReqForRegDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		JavascriptExecutor js3 = (JavascriptExecutor) driver;
		js3.executeScript("arguments[0].scrollIntoView();", remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		highLightElement(driver, confirmationText, "ConfirmationText", test);
		click2(confirmationDone, CommonStrings.Click_DoneatConfig_DC.getCommonStrings(),
				GroupStrings.Click_DoneatGroupConfig_AC.getGroupStrings(),
				GroupStrings.Click_DoneatGroupConfig_AR.getGroupStrings(),
				CommonStrings.Click_DoneatConfig_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();
	}

//	public void group_Configuration_With_AuditTrails(String ConfigureRemarks, String configureConfirmationText1,
//			String esignAt_Initiation_Reg_Value, String esignAt_Approval_Reg_Value,
//			String noOfApprovalsReq_At_Reg_Value) {
//
//		JavascriptExecutor js = (JavascriptExecutor) driver;
//		TimeUtil.shortWait();
//		waitForElementVisibile(menu);
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(userGroups, configurationMenu,
//				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
//				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
//				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
//				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(configurationMenu, groupCnfgMenu,
//				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
//				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
//				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
//				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
//		click2(groupCnfgMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
//				GroupStrings.Group_Config_AC.getGroupStrings(), GroupStrings.Group_Config_AR.getGroupStrings(),
//				GroupStrings.GroupMenu_SS.getGroupStrings());
//		TimeUtil.shortWait();
//		switchToBodyFrame(driver);
//		TimeUtil.shortWait();
//		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
//		TimeUtil.shortWait();
//		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
//		System.out.println(configUniqueCodeValue);
//		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
//		SelectRadioBtnAndCheckboxRegistrationApproval(driver, eSignAtInitAprCheckBox,
//				"Call E-sign At: Regsitration Approval");
//		clickAndWaitforNextElement(noOfAprReqForRegDrpdwn, select1ForInitDropdown,
//				CommonStrings.NoOfApprovals_DC.getCommonStrings(), CommonStrings.NoOfApprovals_AC.getCommonStrings(),
//				CommonStrings.NoOfApprovals_AR.getCommonStrings(), CommonStrings.NoOfApprovals_SS.getCommonStrings());
//		clickAndWaitforNextElement(select1ForInitDropdown, remarks, CommonStrings.One_Approvals_DC.getCommonStrings(),
//				CommonStrings.One_Approvals_AC.getCommonStrings(), CommonStrings.One_Approvals_AR.getCommonStrings(),
//				CommonStrings.One_Approvals_SS.getCommonStrings());
//		js.executeScript("arguments[0].scrollIntoView();", remarks);
//		TimeUtil.shortWait();
//		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), ConfigureRemarks,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CommonStrings.Remarks_SS.getCommonStrings());
//		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
//				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
//				CommonStrings.Submit_Button_SS.getCommonStrings());
//		getCurrentDate();
//		verifyExactCaption(confirmationText, configureConfirmationText1, "Confirmation");
//		switchToDefaultContent(driver);
//		// TimeUtil.shortWait();
//
//		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(systemManagerMenu, configurationAuditTrails,
//				CommonStrings.SYS_Menus_DC.getCommonStrings(), CommonStrings.CM_Menus_AC.getCommonStrings(),
//				CommonStrings.CM_Menus_AR.getCommonStrings(), CommonStrings.CM_Menus_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(configurationAuditTrails, configurationGroupAuditTrails,
//				CommonStrings.ConfigAuditTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.ConfigAuditTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.ConfigAuditTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.ConfigAuditTrailsMenu_SS.getCommonStrings());
//		click2(configurationGroupAuditTrails, GroupStrings.Group_ConfigAudit_DC.getGroupStrings(),
//				GroupStrings.Group_ConfigAudit_AC.getGroupStrings(),
//				GroupStrings.Group_ConfigAudit_AR.getGroupStrings(),
//				GroupStrings.Group_ConfigAudit_SS.getGroupStrings());
//		switchToBodyFrame(driver);
//		highLightElement(driver, highLightScreenTitle, "Groups Configuration Audit Trails", test);
//		TimeUtil.shortWait();
//		verifyExactCaption(auditTrailPageColumn1, configUniqueCodeValue, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn2, initiatorFLName, "User Name");
//		// highLightElement(driver, auditTrailPageColumn3, "Time and Date",test);
//		TimeUtil.shortWait();
//		getExactDate(auditTrailPageColumn3);
//		TimeUtil.shortWait();
//		String modifyNumber = auditTrailPageColumn4.getText();
//		String auditRevisionValue = modifyNumber + " - Modification";
//		highLightElement(driver, auditTrailPageColumn4, "Revision No", test);
//		TimeUtil.shortWait();
//		click2(displayedRecord, GroupStrings.Click_Group_for_ConfigAuditTrails_DC.getGroupStrings(),
//				GroupStrings.Click_Group_for_ConfigAuditTrails_AC.getGroupStrings(),
//				GroupStrings.Click_Group_for_ConfigAuditTrails_AR.getGroupStrings(),
//				GroupStrings.Click_Group_for_ConfigAuditTrails_SS.getGroupStrings());
//		driver.switchTo().frame(0);
//		js.executeScript("arguments[0].scrollIntoView();", modificationLastTab);
//		String revisionTitle = Constants.REVISIONNUM_TITLE;
//		String modifyLastTabRevisionno = revisionTitle + " :" + modifyNumber;
//		verifyExactCaption(modificationLastTab, modifyLastTabRevisionno, "Modify LastTab Revision No");
//		clickAndWaitforNextElement(ModTab, ProceedAudit, CommonStrings.Click_LastesModTab_DC.getCommonStrings(),
//				CommonStrings.Click_LastesModTab_AC.getCommonStrings(),
//				CommonStrings.Click_LastesModTab_AR.getCommonStrings(),
//				CommonStrings.Click_LastesModTab_SS.getCommonStrings());
//		click2(ProceedAudit, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//				GroupStrings.Click_Config_Proceed_AC.getGroupStrings(),
//				GroupStrings.Click_Config_Proceed_AR.getGroupStrings(),
//				GroupStrings.Click_Config_Proceed_SS.getGroupStrings());
//		TimeUtil.shortWait();
//		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
//		verifyExactCaption(revisionNoTitleCompareTRN, revisionTitle, "Revision No.:");
//		verifyExactCaption(ModNumber, auditRevisionValue, "Modification");
//		verifyExactValueInFeild(auditTrailUniqueCode, configUniqueCodeValue, "Unique Code");
//		highLightElement(driver, callAtEsignInitiationLabel, "callAtEsignInitiationLabel", test);
//		verifyExactCaption(auditEsignAt_Initiation_Reg_Value, esignAt_Initiation_Reg_Value,
//				"Call E-sign At: Initiation Registration");
//		highLightElement(driver, callAtEsignApprovalLabel, "callAtEsignApprovalLabel", test);
//		js.executeScript("arguments[0].scrollIntoView();", auditEsignAt_Approval_Reg_Value);
//		verifyExactCaption(auditEsignAt_Approval_Reg_Value, esignAt_Approval_Reg_Value,
//				"Call E-sign At: Approval Registration");
//		verifyExactCaption(noOfApprovalsReqAt_Registration_Value, noOfApprovalsReq_At_Reg_Value,
//				"No. of Approvals Required: Registration");
//		js.executeScript("arguments[0].scrollIntoView();", configureAuditTrailsInitiatedBy);
//		verifyExactCaption(configureAuditTrailsInitiatedBy, initiatorFLName, "Initiated By");
//		getExactDate(configureAuditTrailsInitiatedOn);
//		verifyExactCaption(configureAuditTrailsRemarks, ConfigureRemarks, "Remarks");
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				GroupStrings.Close_ConfigAuditTrails_Group_AC.getGroupStrings(),
//				GroupStrings.Close_ConfigAuditTrails_Group_AR.getGroupStrings(),
//				CommonStrings.Close_Icon_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		switchToDefaultContent(driver);
//
//	}

	public void groupRegistration(HashMap<String, String> testData) {

//		String s = "";
//		prop = ConfigsReader.readProperties("./configs/configuration.properties");
//		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
//			s = TextUtils.randomvalue(3);
//			System.out.println("Generated S Value is: " + s);
//		}
//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";
		RegGroupName1 = testData.get("GroupName") + TextUtils.randomAlphaNumeric(3);
		RegDescription1 = testData.get("Description") + TextUtils.randomAlphaNumeric(3);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(group, GroupStrings.GroupMenu_DC.getGroupStrings(), GroupStrings.Group_Initiate_AC.getGroupStrings(),
				GroupStrings.Group_Initiate_AR.getGroupStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		sendKeys2(groupNameTxt, GroupStrings.GroupName_DC.getGroupStrings(), RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		TimeUtil.shortWait();
		sendKeys2(descriptionTxt, GroupStrings.Description_DC.getGroupStrings(), RegDescription1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.Description_SS.getGroupStrings());
		String currenturl1 = driver.getCurrentUrl();
		if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl320"))) {

			sendKeys2(availableSubgroupSearchNew2, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(),
					SubgroupName, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
		} else if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl310"))) {
			
			TimeUtil.longwait();
			sendKeys2(availableSubgroupSearchNew, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(), SubgroupName,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
		}

		TimeUtil.shortWait();
		click2(availableSubgroup1, GroupStrings.SelectAvailableSubgrp_DC.getGroupStrings(),
				GroupStrings.SelectAvailableSubgrp_AC.getGroupStrings(),
				GroupStrings.SelectAvailableSubgrp_AR.getGroupStrings(),
				GroupStrings.SelectAvailableSubgrp_SS.getGroupStrings());
		click2(rightToggle2, GroupStrings.ClickRightToggle_DC.getGroupStrings(),
				GroupStrings.ClickRightToggle_AC.getGroupStrings(), GroupStrings.ClickRightToggle_AR.getGroupStrings(),
				GroupStrings.ClickRightToggle_SS.getGroupStrings());
		TimeUtil.shortWait();

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
//		initiator = approverName.getDomAttribute("value");

		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
//		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupRegistrationWithAuditTrails(HashMap<String, String> testData) {
		
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
//		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

//		String initiator = SYS_Subgroup.getInitiatorName();

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPREMARKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

//		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
//
//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void verifiModification(HashMap<String, String> testdata) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "FRSubGroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(modifyMenu, modifyGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifyGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());

//		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				GroupStrings.GroupName_SS.getGroupStrings());

		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testdata.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		highlightEle2(nodataAvailabel);
	}

	public void groupRegistrationApproval(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");

//		verifyExactCaption(auditTrailPageColumn3, initiator, "Initiated By");

		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPREMARKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, approveLabelText, "Group Approval", test);
		clickAndWaitforNextElement(approveRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		String ApprovalAction = Constants.APPROVAL_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void groupRegistrationApproveWithAuditTrails_Yes(HashMap<String, String> testData) {

		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionApproveValue,
				auditCompareApproveTRNActionByValue, auditCompareTRNDateTimeApproveValue, auditCompareTRNAppRemarksVal1,
				Constants.APPROVE_ACTIONVAL, initiatorFLNameEmpID, Constants.GROUPAPPROVEREMAKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupRegistrationModApproveWithAuditTrails_Yes(HashMap<String, String> testData) {

		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionApproveValue,
				auditCompareApproveTRNActionByValue, auditCompareTRNDateTimeApproveValue, auditCompareTRNAppRemarksVal1,
				Constants.APPROVE_ACTIONVAL, initiatorFLNameEmpID, Constants.GROUPAPPROVEREMAKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void group_Returns(HashMap<String, String> testdata) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPREMARKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, returnLabelText, "Group Return", test);
		clickAndWaitforNextElement(returnRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testdata.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		String ReturnlAction = Constants.RETUENL_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ReturnlAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void group_ReturnWithAuditTrials_Yes(HashMap<String, String> testdata) {
		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testdata.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
//				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
//				initiatorFLNameEmpID, InitRemarks);

//		verifyAuditTrailsCompareTRNApprovedStatus(auditCompareTRNApproveActionValue,
//				auditCompareTRNApproveActionByValue, auditCompareTRNApproveDateTimeValue,
//				auditCompareTRNApproveRemarksValue, Constants.APPROVAL_ACTION_VALUE, FullNameemployeeID,
//				ApproveRemarks);
//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionReturnValue, auditCompareTRNActionByReturnValue,
				auditCompareTRNDateTimeReturnValue, auditCompareTRNRemarksReturnVal1, Constants.RETURN_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPRetrunRemarks);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void groupReInitiate(HashMap<String, String> testData) {

//		String w = "";
//		prop = ConfigsReader.readProperties("./configs/configuration.properties");
//		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
//			w = TextUtils.randomvalue(3);
//			System.out.println("Generated S Value is: " + w);
//		}
//		String SubgroupName = SYS_Subgroup.getSubGroupval();
//		String SubgroupName = "FRSubGroup";
//		setRegGroupName(RegGroupName2 = testData.get("GroupName") + w);
//		setRegDescription(RegDescription2 = testData.get("Description") + w);

		RegGroupName2 = testData.get("GroupName") + TextUtils.randomAlphaNumeric(3);
		RegDescription2 = testData.get("Description") + TextUtils.randomAlphaNumeric(3);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinigtiateGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Initiate_AC.getGroupStrings(), GroupStrings.Group_Initiate_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);

		click2(searchByNewFilter, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(groupSelection, groupNameLike, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());

		sendKeys2(groupNameTxt, GroupStrings.GroupName_DC.getGroupStrings(), RegGroupName2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		TimeUtil.shortWait();
		sendKeys2(descriptionTxt, GroupStrings.Description_DC.getGroupStrings(), RegDescription2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.Description_SS.getGroupStrings());
//		String currenturl1 = driver.getCurrentUrl();
//		if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl320"))) {
//
//			sendKeys2(availableSubgroupSearchNew2, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(),
//					SubgroupName, CommonStrings.sendKeys_AC.getCommonStrings(),
//					CommonStrings.sendKeys_AR.getCommonStrings(),
//					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
//		} else if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl310"))) {
//
//			sendKeys2(availableSubgroupSearchNew, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(), SubgroupName,
//					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
//		}

		TimeUtil.shortWait();
//		click2(availableSubgroup1, GroupStrings.SelectAvailableSubgrp_DC.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_AC.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_AR.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_SS.getGroupStrings());
//		click2(rightToggle2, GroupStrings.ClickRightToggle_DC.getGroupStrings(),
//				GroupStrings.ClickRightToggle_AC.getGroupStrings(), GroupStrings.ClickRightToggle_AR.getGroupStrings(),
//				GroupStrings.ClickRightToggle_SS.getGroupStrings());
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasons"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
//		String MenuName = Constants.MENUNAME_AS_GROUP;
//		String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
//		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void verifyGroupReInitiate(HashMap<String, String> testData) {

//		String w = "";
//		prop = ConfigsReader.readProperties("./configs/configuration.properties");
//		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
//			w = TextUtils.randomvalue(3);
//			System.out.println("Generated S Value is: " + w);
//		}
//		String SubgroupName = SYS_Subgroup.getSubGroupval();
//		String SubgroupName = "FRSubGroup";
//		setRegGroupName(RegGroupName2 = testData.get("GroupName") + w);
//		setRegDescription(RegDescription2 = testData.get("Description") + w);

		RegGroupName2 = testData.get("GroupName") + TextUtils.randomAlphaNumeric(3);
		RegDescription2 = testData.get("Description") + TextUtils.randomAlphaNumeric(3);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinigtiateGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Initiate_AC.getGroupStrings(), GroupStrings.Group_Initiate_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);

		click2(searchByNewFilter, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(groupSelection, groupNameLike, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		highlightEle2(nodataAvailabel);
//		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
//		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
////		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
//		getExactDate(auditTrailPageColumn4);
//		TimeUtil.shortWait();
//		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
//				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
//				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
//				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
//
//		sendKeys2(groupNameTxt, GroupStrings.GroupName_DC.getGroupStrings(), RegGroupName2,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				GroupStrings.GroupName_SS.getGroupStrings());
//		TimeUtil.shortWait();
//		sendKeys2(descriptionTxt, GroupStrings.Description_DC.getGroupStrings(), RegDescription2,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				GroupStrings.Description_SS.getGroupStrings());
//
//		TimeUtil.shortWait();
//		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasons"),
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CommonStrings.Remarks_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
//				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
//				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
//				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
//		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
//				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//				CommonStrings.Password_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
//				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
//				CommonStrings.Esign_Proceed_SS.getCommonStrings());
////		String MenuName = Constants.MENUNAME_AS_GROUP;
////		String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;
//
//		waitForElementVisibile(confirmationText);
//		saveUniqueCode(driver, confirmationText);
//		getCurrentDate();
////		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
//		highLightElement(driver, confirmationText, "Confirmation Message", test);
//		TimeUtil.shortWait();
//		switchToDefaultContent(driver);
	}

	public void groupReInitiateWithAuditTrails_Yes(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName2 +"%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);
//		String initiator = SYS_Subgroup.getInitiatorName();
		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionReInitiateValue,
				auditCompareTRNActionByReinitiateValue, auditCompareTRNDateTimeReinitiateValue,
				auditCompareTRNRemarksReinitiateVal1, Constants.REINITIATE_ACTIONVAL, initiator,
				Constants.GROUPReInitiateRemarks);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void group_Approval(HashMap<String, String> testdata) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName2, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
//				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
//				initiatorFLNameEmpID, InitRemarks);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);
		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, approveLabelText, "Group Approval", test);
		clickAndWaitforNextElement(approveRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testdata.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		String ApprovalAction = Constants.APPROVAL_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void group_ApprovalWithAuditTrials_Yes(HashMap<String, String> testdata) {
		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName2 + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName2, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
//				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
//				initiatorFLNameEmpID, InitRemarks);

//		verifyAuditTrailsCompareTRNApprovedStatus(auditCompareTRNApproveActionValue,
//				auditCompareTRNApproveActionByValue, auditCompareTRNApproveDateTimeValue,
//				auditCompareTRNApproveRemarksValue, Constants.APPROVAL_ACTION_VALUE, FullNameemployeeID,
//     ApproveRemarks);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionApproveValue,
				auditCompareApproveTRNActionByValue, auditCompareTRNDateTimeApproveValue, auditCompareTRNAppRemarksVal1,
				Constants.APPROVE_ACTIONVAL, initiatorFLNameEmpID, Constants.GROUPAPPROVEREMAKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);

//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void verifiModificationMod(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(modifyMenu, modifyGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(modifyGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1 + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

	}

	public void verifiModificationReAgain(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(modifyMenu, modifyGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(modifyGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName2 + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

	}

	public void verifiModificationReAgainRe(HashMap<String, String> testData) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(modifyMenu, modifyGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(modifyGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

	}

	public void groupRITranswaer(HashMap<String, String> testData) {

//		String s = "";
//		prop = ConfigsReader.readProperties("./configs/configuration.properties");
//		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
//			s = TextUtils.randomvalue(3);
//			System.out.println("Generated S Value is: " + s);
//		}
//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";
//		setRegGroupName(RegGroupName1 = testData.get("GroupName") + s);
//		setRegDescription(RegDescription1 = testData.get("Description") + s);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reTranswerMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(ritrnaswerGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Initiate_AC.getGroupStrings(), GroupStrings.Group_Initiate_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
	//	verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionReturnValue, auditCompareTRNActionByReturnValue,
				auditCompareTRNDateTimeReturnValue, auditCompareTRNRemarksReturnVal1, Constants.RETURN_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPRetrunRemarks);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", transwerAddItems);
		highLightElement(driver, transwerAddItems, "Transfer To", test);
		click2(transwerAddItems,  GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());

		sendKeys2(searchby, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				testData.get("RITransferName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		click2(arButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(addButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());

//		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_TSRITRANSWER_CONFIRMATION_TEXT_ESIGN;
		String ReturnlAction = Constants.TRANSWER_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
//		verifyExactCaption(confirmationApprovalAction, ReturnlAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.GBRITRANSFER_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void groupRITranswaerWithAuditTrails_Yes(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		click2(transwerRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.TRANSWERREVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.TRANSWER_INITIATED);

//		String initiator = SYS_Subgroup.getInitiatorName();

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.TRANSWER_INITIATED);

		highlightEle2(audiTransweTRNRemarksVal1);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionReturnValue, auditCompareTRNActionByReturnValue,
//				auditCompareTRNDateTimeReturnValue, auditCompareTRNRemarksReturnVal1, Constants.RETURN_ACTIONVAL,
//				initiatorFLNameEmpID, Constants.GROUPRetrunRemarks);

//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupRegistrationDrop(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");

//		verifyExactCaption(auditTrailPageColumn3, initiator, "Initiated By");

		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
				initiatorFLNameEmpID, Constants.GROUPREMARKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);

		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, approveLabelText, "Group Approval", test);
		clickAndWaitforNextElement(dropRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		String ApprovalAction = Constants.DROP_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.DROP_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void groupRegistrationDropWithAuditTrails_Yes(HashMap<String, String> testData) {

		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName1, "Group Name");
		verifyExactCaption(auditDescription, RegDescription1, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_DROPPED);

		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionDropValue,
				auditCompareApproveTRNActionByDropValue, auditCompareTRNDateTimeDropValue,
				auditCompareTRNAppRemarksDropVal1, Constants.DROP_ACTIONVAL, initiatorFLNameEmpID,
				Constants.GROUPDROPREMAKS);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void group_TranswerApproval(HashMap<String, String> testdata) {

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "SRSubgroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(approve, approveGroupMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveGroupMenu, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, approveGroup_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName2, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Group Approval", test);
		TimeUtil.shortWait();
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
//				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
//				initiatorFLNameEmpID, InitRemarks);

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_COMPLETED_AS_0);
		JavascriptExecutor js5 = (JavascriptExecutor) driver;
		js5.executeScript("arguments[0].scrollIntoView();", approveRadioBtn);
		highLightElement(driver, approveLabelText, "Group Approval", test);
		clickAndWaitforNextElement(approveRadioBtn, submit, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testdata.get("ApproveRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());
		js5.executeScript("arguments[0].scrollIntoView();", submit);
		clickAndWaitforNextElement(submit, esign_psw, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());
		String ConfirmationTextAtEsign = Constants.GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		String ApprovalAction = Constants.APPROVAL_ACTION_VALUE;
		TimeUtil.shortWait();
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "Esign Confirmation Text");
		verifyExactCaption(confirmationApprovalAction, ApprovalAction, "Approval Action Value");
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_Approval_AR.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_GROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		waitForElementVisibile(confirmationText);
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);
	}

	public void group_TranswerApprovalWithAuditTrials_Yes(HashMap<String, String> testdata) {
		String SubgroupName = "SRSubgroup";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, groupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByGroupNameDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByGroupNameDropdown, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName2 + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		verifyExactCaption(auditTrailPageColumn1, RegGroupName2, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(transwerRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

//		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.TRANSWERREVISION_NUM_0REG, "0 - Registration");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.TRANSWER_INITIATED);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionValue, auditCompareTRNActionByValue,
//				auditCompareTRNDateTimeValue, auditCompareTRNRemarksVal1, Constants.INITIATE_ACTIONVAL,
//				initiatorFLNameEmpID, InitRemarks);

//		verifyAuditTrailsCompareTRNApprovedStatus(auditCompareTRNApproveActionValue,
//				auditCompareTRNApproveActionByValue, auditCompareTRNApproveDateTimeValue,
//				auditCompareTRNApproveRemarksValue, Constants.APPROVAL_ACTION_VALUE, FullNameemployeeID,
//     ApproveRemarks);

//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionApproveValue,
//				auditCompareApproveTRNActionByValue, auditCompareTRNDateTimeApproveValue, auditCompareTRNAppRemarksVal1,
//				Constants.APPROVE_ACTIONVAL, initiatorFLNameEmpID, Constants.GROUPAPPROVEREMAKS);
//
//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_1);

//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.Close_AuditTrails_Group_AC.getGroupStrings(),
				GroupStrings.Close_AuditTrails_Group_AR.getGroupStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void groupRITRInitiate(HashMap<String, String> testData) {

//		String w = "";
//		prop = ConfigsReader.readProperties("./configs/configuration.properties");
//		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
//			w = TextUtils.randomvalue(3);
//			System.out.println("Generated S Value is: " + w);
//		}
//		String SubgroupName = SYS_Subgroup.getSubGroupval();
//		String SubgroupName = "FRSubGroup";
//		setRegGroupName(RegGroupName2 = testData.get("GroupName") + w);
//		setRegDescription(RegDescription2 = testData.get("Description") + w);

		RegGroupName2 = testData.get("GroupName") + TextUtils.randomAlphaNumeric(3);
		RegDescription2 = testData.get("Description") + TextUtils.randomAlphaNumeric(3);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinigtiateGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.Group_Initiate_AC.getGroupStrings(), GroupStrings.Group_Initiate_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);

		click2(searchByNewFilter, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(groupSelection, groupNameLike, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName1 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		verifyExactCaption(auditTrailPageColumn1, RegGroupName1, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
//		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_Group_for_Approve_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_Approve_SS.getGroupStrings());

		sendKeys2(groupNameTxt, GroupStrings.GroupName_DC.getGroupStrings(), RegGroupName2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		TimeUtil.shortWait();
		sendKeys2(descriptionTxt, GroupStrings.Description_DC.getGroupStrings(), RegDescription2,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.Description_SS.getGroupStrings());
//		String currenturl1 = driver.getCurrentUrl();
//		if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl320"))) {
//
//			sendKeys2(availableSubgroupSearchNew2, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(),
//					SubgroupName, CommonStrings.sendKeys_AC.getCommonStrings(),
//					CommonStrings.sendKeys_AR.getCommonStrings(),
//					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
//		} else if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl310"))) {
//
//			sendKeys2(availableSubgroupSearchNew, GroupStrings.SearchAvailableSubgrp_DC.getGroupStrings(), SubgroupName,
//					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//					GroupStrings.SearchAvailableSubgrp_SS.getGroupStrings());
//		}

		TimeUtil.shortWait();
//		click2(availableSubgroup1, GroupStrings.SelectAvailableSubgrp_DC.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_AC.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_AR.getGroupStrings(),
//				GroupStrings.SelectAvailableSubgrp_SS.getGroupStrings());
//		click2(rightToggle2, GroupStrings.ClickRightToggle_DC.getGroupStrings(),
//				GroupStrings.ClickRightToggle_AC.getGroupStrings(), GroupStrings.ClickRightToggle_AR.getGroupStrings(),
//				GroupStrings.ClickRightToggle_SS.getGroupStrings());
		sendKeys2(ReInitiateremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RemarksReasons"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				GroupStrings.SubmitGroupwithEsign_AC.getGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("GroupTRSPwd"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				GroupStrings.Esign_ProceedGroup_AC.getGroupStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
//		String MenuName = Constants.MENUNAME_AS_GROUP;
//		String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;

		waitForElementVisibile(confirmationText);
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
//		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void groupRIReInitiateWithAuditTrails_Yes(HashMap<String, String> testData) {
		String SubgroupName = "SRSubgroup";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(groupAuditTrails, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				GroupStrings.GroupAudittrails_SS.getGroupStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_GroupAudit_AC.getGroupStrings(),
				GroupStrings.SearchBy_GroupAudit_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByGroupNameDropdown, GroupStrings.Select_GroupName_DC.getGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(),
				RegGroupName2 + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.GroupName_SS.getGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		click2(transwerRecord, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		click2(proceedButton, GroupStrings.Click_UniqueCode_for_AuditTrails_DC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AC.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_AR.getGroupStrings(),
				GroupStrings.Click_Group_for_AuditTrails_SS.getGroupStrings());

		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.TRANSWERREVISION_NUM_0REG,
				"0 - Re-initiation Task Transfer (Reg.)");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditGroupName, RegGroupName2, "Group Name");
		verifyExactCaption(auditDescription, RegDescription2, "Description");
		verifyExactCaption(auditSubgroupName, SubgroupName, "Subgroup Name");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.TRANSWER_INITIATED);
//		String initiator = SYS_Subgroup.getInitiatorName();
//		verifyAuditTrailsCompareTRNIntiatedStatus(auditCompareTRNActionReInitiateValue,
//				auditCompareTRNActionByReinitiateValue, auditCompareTRNDateTimeReinitiateValue,
//				auditCompareTRNRemarksReinitiateVal1, Constants.REINITIATETRS_ACTIONVAL, initiator,
//				Constants.GROUPRTRSInitiateRemarks);
//		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
//				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
//				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				GroupStrings.GroupAudittrails_AC.getGroupStrings(), GroupStrings.GroupAudittrails_AR.getGroupStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Status Change')]")
	WebElement statusChyangeMenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN129_SUBMEN08")
	WebElement statusChangeGroup;

	@FindBy(xpath = "//button[text()='Status Change']")
	WebElement statusChangeTabMenu;

	@FindBy(xpath = "//button[text()='Inactive']")
	WebElement statusChangeInActiveTab;

	@FindBy(xpath = "//tr[@class='click-row odd']")
	WebElement dataAvailable;

	public void verifygroup_StatusChange_Inactive() {

//		String SubGroupval = "ATSCSubgroupAMV%";

//		String SubgroupName = SYS_Subgroup.getSubGroupval();
		String SubgroupName = "FRSubGroup";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(statusChyangeMenu, statusChangeGroup,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(statusChangeGroup, GroupStrings.GroupMenu_DC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AC.getGroupStrings(),
				GroupStrings.SYS_UserGroups_groupApproveMenu_AR.getGroupStrings(),
				GroupStrings.GroupMenu_SS.getGroupStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(statusChangeInActiveTab, CommonStrings.SearchBy_DC.getCommonStrings(),
				GroupStrings.SearchBy_Group_AC.getGroupStrings(), GroupStrings.SearchBy_Group_AR.getGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(highLightScreenTitle);
		highLightElement(driver, highLightScreenTitle, "Group Approval", test);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNewFilter, approveGroup_SearchBy,
				CommonStrings.SearchBy_DC.getCommonStrings(), GroupStrings.SearchBy_Group_AC.getGroupStrings(),
				GroupStrings.SearchBy_Group_AR.getGroupStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(approveGroup_SearchBy, groupNameLike,
				GroupStrings.Select_GroupName_DC.getGroupStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
				CommonStrings.dropdown_AR.getCommonStrings(), GroupStrings.GroupMenu_SS.getGroupStrings());
		sendKeys2WithOutClear(groupNameLike, GroupStrings.Like_GroupName_DC.getGroupStrings(), RegGroupName1 + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				GroupStrings.GroupName_SS.getGroupStrings());
		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		highlightEle2(dataAvailable);
		switchToDefaultContent(driver);

	}

}
