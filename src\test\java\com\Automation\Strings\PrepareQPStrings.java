package com.Automation.Strings;

public enum PrepareQPStrings {

	// Question Paper submenu

	PrepareQP_DC("Click on 'Question Paper' submenu."),
	PrepareQP_AC("'Question Paper' screen should be displayed.</div>"
			+ "<div><b>*</b> List of batches for which the Question Paper needs to be prepared should be displayed.</div>"),
	PrepareQP_AR("'Question Paper' screen is getting displayed.</div>"
			+ "<div><b>*</b> List of batches for which the Question Paper needs to be prepared are getting displayed.</div>"),
	PrepareQP_SS("'Question Paper'"),

	// Search By

	SearchBy_AC("Option to search with 'Top 250 Records, Batch Name, Course Name' should be available.</div>"),
	SearchBy_AR("Option to search with 'Top 250 Records, Batch Name, Course Name' are available.</div>"),

	// Search By Course Name

	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR(" Selection is getting accepted.</div>"),
	SearchBy_CourseName_SS("'Search By''Course Name'."),

	// After batch formation

	Like_CourseName_DC("Enter the course name for which the above batch formation is proposed."),
	Like_CourseName_SS("'Course Name'."),

	click_Batch_DC("Click on the above registered batch name.</div>"),
	click_Batch_AC("'Question Paper Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain Course details, Topic details, Session details and Question Paper details accurately.</div>"
			+ "<div><b>*</b> Option to select 'Evaluation Type' should be available.</div>"),

	click_Batch_AR("'Question Paper Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with Course details, Topic details, Session details and Question Paper details accurately.</div>"
			+ "<div><b>*</b> Option to select 'Evaluation Type' is available.</div>"),
	click_Batch_SS("'Question Paper Registration'"),

	// Select System Evaluation

	Select_System_Eval_DC("Select Evaluation Type as 'System Evaluation'."),
	Select_System_Eval_AC("Selection should be accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Question Paper Type' should be available.</div>"),
	Select_System_Eval_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Question Paper Type' is available.</div>"),
	Select_System_Eval_SS("'System Evaluation'."),

	// Select Manual Evaluation

	Select_Manual_Eval_DC("Select Evaluation Type as 'Manual Evaluation'."),
	Select_Manual_Eval_AC("Selection should be accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Question Paper Type' should be available.</div>"),
	Select_Manual_Eval_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Question Paper Type' is available.</div>"),
	Select_Manual_Eval_SS("'Manual Evaluation'."),

	// question paper type

	Click_QPType_DC("Click on 'Question Paper Type'."),
	Click_QPType_AC("Opton to select 'Random, Topic Based, Question Type Based, Manual' should be available.</div>"),
	Click_QPType_AR("Opton to select 'Random, Topic Based, Question Type Based, Manual' is available.</div>"),
	Click_QPType_SS("Option to select 'Question Paper Type'."),

	// Select Manual QP type

	Enter_Manual_DC("Enter 'Manual' in drop down."),
	Enter_Manual_AC("Option to select only 'Manual' should be available."),
	Enter_Manual_AR("Option to select only 'Manual' is available."), Enter_Manual_SS("Manual'"),

	Click_Manual_DC("Click on 'Manual'."), 
	Click_Manual_AC("Selection should be accepted.</div>"+"<div><b>* </b>Option to select the questions should be available.</div>"),
	Click_Manual_AR("Selection is getting accepted.</div>"+"<div><b>* </b>Option to select the questions is available.</div>"), Click_Manual_SS("'Manual'"),

	// Select MC Type

	click_MCType_checkbox_DC("Select Multiple Choice Type question."),
	click_MCType_checkbox_AC("Selection should be accepted."),
	click_MCType_checkbox_AR("Selection is getting accepted."), 
	click_MCType_checkbox_SS("Multiple Choiuce Type."),
	
	// Select T or F Type

		click_TFType_checkbox_DC("Similarly, Select True or False Type question."),
		click_TFType_checkbox_SS("True or False Type."),

	// Compute
	Click_Compute_DC("Click on 'Compute' button."),
	Click_Compute_AC("Option to enter the 'Qualifying Percentage' should be available."),
	Click_Compute_AR("Option to enter the 'Qualifying Percentage' is available."),
	Click_Compute_SS("'Qualifying Percentage'"),

	// Qualifying Percentage

	Enter_QualifyingPecentage_DC(
			"Enter the required Qualifying Percentage value and click mouse by placing cursor anywhere on the screen."),
	Enter_QualifyingPecentage_AC(
			"Qualifying Marks should be displayed based on the 'Maximum Marks' and 'Qualifying Percentage'."),
	Enter_QualifyingPecentage_AR(
			"Qualifying Marks is getting displayed based on the 'Maximum Marks' and 'Qualifying Percentage'."),

	Enter_QualifyingPecentage_SS("'Qualifying Marks'"),

	// Submit

	Submit_AC(
			"'Question Paper Registration Initiated', Unique Code: (Unique Code)' confirmation message should be displayed with ‘Done’ button.</div>"),
	Submit_AR(
			"'Question Paper Registration Initiated', Unique Code: (Unique Code)' confirmation message is getting displayed with ‘Done’ button.</div>"),

	// Submit for Manual evaluation

	SubmitOnlineManual_AC(
			"'This Question Paper Is Eligible for System Evaluation. You May opt for System Evaluation or Continue with Manual Evaluation' alert should be displayed with System Evaluation and Manual Evaluation options with 'OK' button."),
	SubmitOnlineManual_AR(
			"'This Question Paper Is Eligible for System Evaluation. You May opt for System Evaluation or Continue with Manual Evaluation' alert should be displayed with 'System Evaluation' and 'Manual Evaluation' options with 'OK' button."),

	SubmitOfflineManual_AC(
			"'Question Paper  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button."),
	SubmitOfflineManual_AR(
			"'Question Paper  Registration Initiated Unique Code: (Unique Code)' confirmation message is displayed with 'Done' button."),
	Submit_SS("'Submit'"),

	Click_OK("Click 'OK' at alert"), Click_OK_SS("'OK'"),

	// Audit trails list screen QP

	Click_QP_AT_AC("'Question Paper Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b> List of batches for which the Question Paper is prepared should be displayed.</div>"),
	Click_QP_AT_AR("'Question Paper Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>*</b> List of batches for which the Question Paper is prepared are getting displayed .</div>"),
	Click_QP_AT_SS("'Question Paper Audit Trails'."),

	// Search By audit trails

	SearchBy_Audit_AC(
			"Option to search with 'Top 250 Records, Question Paper Name, Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_Audit_AR(
			"Option to search with 'Top 250 Records, Question Paper Name, Unique Code, Initiated Between' are available.</div>"),

	// Search By QP Name

	SearchBy_QPName_DC("Select 'Question Paper Name'."), SearchBy_QPName_AC("Selection should be accepted.</div>"),
	SearchBy_QPName_AR(" Selection is getting accepted.</div>"),
	SearchBy_QPName_SS("'Search By''Question Paper Name'."),

	// QP Name
	Like_QPName_AuditTrails_DC("Ente the above batch name for which the Question Paper is prepared."),
	Like_QPName_SS("'Question Paper Name'"),

	click_QP_AuditTrails_DC("Click on the above batch for which the question paper is registered.</div>"),
	click_QP_AuditTrails_AC(
			"'Question Paper - Audit Trails: Revision No.:Title 0-Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> All the details should be displayed with accurate details in read only mode.</div>"),

	click_QP_AuditTrails_AR(
			"'Question Paper - Audit Trails: Revision No.:Title 0-Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the details are getting displayed with accurate details in read only mode.</div>"),
	click_QP_AuditTrails_SS("'Audit trails'"),

	// Click on 'Add Item' for 'Select Evaluator'

	AddItem_SelectEvaluator_DC("Click on 'Add Item' for 'Select Evaluator' field"),
	AddItem_SelectEvaluator_AC("'Evaluator List' window should be displayed.</div>"),
	AddItem_SelectEvaluator_AR("'Evaluator List' window is getting displayed.</div>"),
	AddItem_SelectEvaluator_SS("'Evaluator List' window.<div>"),

	Find_Evaluator_DC("Enter the 'Evaluator Name' in 'Find' textbox"), Find_Evaluator_SS("'Evaluator Name'"),

	selectEvaluator_DC("Select the required 'Employee Name'"), selectEvaluator_SS("'Employee Name' "),

	Click_AddButton_Evaluator_AC("Selected Employee should be displayed for 'Select Evaluator' field.</div>"),

	Click_AddButton_Evaluator_AR("Selected Employee is getting displayed for 'Select Evaluator' field.</div>"),

	;

	private final String prepareQP;

	PrepareQPStrings(String prepareQP) {

		this.prepareQP = prepareQP;

	}

	public String getPrepareQPStrings() {
		return prepareQP;
	}

}
