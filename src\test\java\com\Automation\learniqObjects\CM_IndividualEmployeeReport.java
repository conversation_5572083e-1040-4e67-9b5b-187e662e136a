package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.Reports;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_IndividualEmployeeReport extends OQActionEngine {

	Properties prop;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Reports']")
	WebElement courseManagerReportsMenu;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Reports_MEN62']")
	WebElement individualEmployeeReportTC;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[2]//input")
	WebElement courseSessionNameNullCheckBox;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[2]//tr//td[1]//input")
	WebElement courseSessionNameTextBox;
	@FindBy(xpath = "//input[@id='ReportViewerControl_ctl04_ctl00']")
	WebElement viewReport;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[1]/div[1]/a[1]/div[1]")
	WebElement employeeName;
	@FindBy(xpath = "//table[1]/tbody[1]/tr/td[1]/div[1]/a[1]/div[1]")
	WebElement CourseNamehyperlink;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[1]/div/a/div")
	WebElement CourseNamehyperlink2;
	@FindBy(xpath = "(//div[text()='Training Certificate'])[2]")
	WebElement ClassroomTypeTrainingCertificate;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[2]//input")
	WebElement employeeIDCheckBox;
	@FindBy(xpath = "//tr[@id='ParametersRowReportViewerControl']//table//table//tr//td[5]//tr//td[1]//input")
	WebElement employeeIDTextBox;
	@FindBy(xpath = "//tr[@valign='top']/td/div/a//div[contains(text(),'Training Certificate')]")
	WebElement RETrainingCertificate;
	@FindBy(xpath = "//table/tbody/tr[7]/td[3]/table/tbody/tr[3]/td[5]/div/div")
	WebElement EmployeeStatus;
	@FindBy(xpath = "//table/tbody/tr[3]/td[3]/table/tbody/tr[3]/td[5]/div/div")
	WebElement EmployeeStatus2;
	@FindBy(xpath = "//table/tbody/tr/td/table/tbody/tr[2]/td[3]/table/tbody/tr[3]/td[4]/div/div")
	WebElement EmployeeStatus3;

	public CM_IndividualEmployeeReport() {
		PageFactory.initElements(driver, this);
	}

	public void individualEmployeeReport(String EmployeeID, String EmployeeStatus, String SessionType) {

		String CourseName = CM_Course.getCourse();

		// String CourseName = "CRSNewBXNZ";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerReportsMenu);
		click2(courseManagerReportsMenu, Reports.ClickReports_DC.getReports(), Reports.ClickReports_AC.getReports(),
				Reports.ClickReports_AR.getReports(), Reports.ClickReports_SS.getReports());
		scrollToViewElement(individualEmployeeReportTC);
		clickForReports(individualEmployeeReportTC, Reports.click_IER_DC.getReports(),
				Reports.click_IER_AC.getReports(), Reports.click_IER_AR.getReports(),
				Reports.click_IER_SS.getReports());
		TimeUtil.mediumWait();
		switchToBodyFrame(driver);
		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
		TimeUtil.mediumWait();
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofMinutes(10));
		// Wait for the element with the specified text to be visible
		WebElement textElement = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[contains(text(),'Individual Employee Report (All)')]")));

		WebElement button = driver.findElement(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]"));
		waitForElementVisibile(button);
		clickAndWaitforNextElement(button, employeeIDCheckBox, Reports.Click_ToggleBtn_DC.getReports(),
				Reports.Click_ToggleBtnIER_AC.getReports(), Reports.Click_ToggleBtnIER_AR.getReports(),
				Reports.Click_ToggleBtn_SS.getReports());
		click2(employeeIDCheckBox, Reports.UnselectEmployeeID_IER_DC.getReports(),
				Reports.UnselectEmployeeID_IER_AC.getReports(), Reports.UnselectEmployeeID_IER_AR.getReports(),
				Reports.UnselectEmployeeID_IER_SS.getReports());
		TimeUtil.shortWait();
		sendKeys2(employeeIDTextBox, Reports.Enter_SelectedEmployeeID_DC.getReports(), EmployeeID,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				Reports.EmployeeName_SS.getReports());
		TimeUtil.shortWait();
		click3(viewReport, Reports.ClickViewReport_DC.getReports(), Reports.ClickViewReport_AC.getReports(),
				Reports.ClickViewReport_AR.getReports(), Reports.ClickViewReport_SS.getReports());
		WebElement textElement1 = wait.until(ExpectedConditions
				.visibilityOfElementLocated(By.xpath("//div[contains(text(),'Individual Employee Report (All)')]")));
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(employeeName, Reports.Click_SelectedUser_DC.getReports(),
				Reports.Click_Skipped_EmployeeName_AC.getReports(), Reports.Click_Skipped_EmployeeName_AR.getReports(),
				Reports.CourseSession_DC.getReports());
		TimeUtil.mediumWait();
		WebDriverWait wait2 = new WebDriverWait(driver, Duration.ofSeconds(30));
		button = wait2.until(ExpectedConditions.visibilityOfElementLocated(
				By.xpath("/html/body/form/table/tbody/tr/td/div[2]/div/table/tbody/tr[3]/td/div/input[1]")));
		click2(button, Reports.Click_ToggleBtn_DC.getReports(), Reports.Click_ToggleBtnIER2_AC.getReports(),
				Reports.Click_ToggleBtnIER2_AR.getReports(), Reports.Click_ToggleBtn_SS.getReports());
		TimeUtil.mediumWait();
		click2(courseSessionNameNullCheckBox, Reports.Unselect_CS_Name_DC.getReports(),
				Reports.Unselect_CS_Name_AC.getReports(), Reports.Unselect_CS_Name_AR.getReports(),
				Reports.Unselect_CS_Name_SS.getReports());

		if (CourseName != "") {
			sendKeys2(courseSessionNameTextBox, Reports.CS_Name_DC.getReports(), CourseName,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					Reports.CS_Name_Like_SS.getReports());

		}

		else {

			CM_RespondDocumentReading.Sessionname = CM_RespondDocumentReading.Sessionname.replaceAll("(/SS/)[^/]+(/)",
					"$1" + EmployeeID + "$2");

			sendKeys2(courseSessionNameTextBox, Reports.CS_Name_DC.getReports(), CM_RespondDocumentReading.Sessionname,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					Reports.CS_Name_Like_SS.getReports());

		}

//		sendKeys2(courseSessionNameTextBox, Reports.CS_Name_DC.getReports(), CourseName,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				Reports.CS_Name_Like_SS.getReports());
		// if (EmployeeStatus.equals("Qualified") &&
		// EmployeeStatus.equals("ITQualifiedEvaluationNA")) {

		if (EmployeeStatus.equals("Qualified")) {

//			WebElement button1 = new WebDriverWait(driver, Duration.ofSeconds(30)).until(
//					ExpectedConditions.elementToBeClickable(By.id("//input[@id='ReportViewerControl_ctl04_ctl00']")));
//			button1.click();
			click3(viewReport, Reports.ClickViewReport_DC.getReports(),
					Reports.ClickViewReport_CSUnderAppr_AC.getReports(),
					Reports.ClickViewReport_CSUnderAppr_AR.getReports(), Reports.ClickViewReport_SS.getReports());
			TimeUtil.mediumWait();
			verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_QUALIFIED, "Qualified");
			// TimeUtil.extralongwait();
			clickAndWaitforNextElement(CourseNamehyperlink, CourseNamehyperlink2,
					Reports.Click_CS_hyperlink_DC.getReports(), Reports.ClickViewReport_Qualfied_AC.getReports(),
					Reports.ClickViewReport_Qualfied_AR.getReports(), Reports.CS1_Name_Like_SS.getReports());
			verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
					"Document Reading Completed");

			click2(CourseNamehyperlink2, Reports.Click_CS_hyperlink_DC.getReports(),
					Reports.ClickViewReport_Qualfied_AC.getReports(), Reports.ClickViewReport_Qualfied_AR.getReports(),
					Reports.CS1_Name_Like_SS.getReports());
			TimeUtil.mediumWait();

//			WebElement button1 = new WebDriverWait(driver, Duration.ofSeconds(20))
//				    .until(ExpectedConditions.elementToBeClickable(By.id("ReportViewerControl_ctl04_ctl00")));
//				button1.click();
//			TimeUtil.mediumWait();
//			TimeUtil.mediumWait();
////			TimeUtil.longwait();
//
//			verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_QUALIFIED, "Qualified");
//			// TimeUtil.extralongwait();
//			click2(CourseNamehyperlink, 
//					Reports.Click_CS_hyperlink_DC.getReports(), Reports.ClickViewReport_Qualfied_AC.getReports(),
//					Reports.ClickViewReport_Qualfied_AR.getReports(), Reports.CS1_Name_Like_SS.getReports());
//			TimeUtil.mediumWait();
//			TimeUtil.mediumWait();
//			verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
//					"Document Reading Completed");
//
//			click2(CourseNamehyperlink2, Reports.Click_CS_hyperlink_DC.getReports(),
//					Reports.ClickViewReport_Qualfied_AC.getReports(), Reports.ClickViewReport_Qualfied_AR.getReports(),
//					Reports.CS1_Name_Like_SS.getReports());
//			TimeUtil.mediumWait();

			if (RETrainingCertificate.isDisplayed() == true) {
				click2(RETrainingCertificate, Reports.ClickDocRead_TC_DC.getReports(),
						Reports.ClickDocRead_TC_AC.getReports(), Reports.ClickDocRead_TC_AR.getReports(),
						Reports.ClickDocRead_TC_SS.getReports());
				TimeUtil.mediumWait();
				TimeUtil.mediumWait();
				switchToDefaultContent(driver);
			} else if (ClassroomTypeTrainingCertificate.isDisplayed() == true) {

				click2(ClassroomTypeTrainingCertificate, Reports.ClickDocRead_TC_DC.getReports(),
						Reports.ClickDocRead_TC_AC.getReports(), Reports.ClickDocRead_TC_AR.getReports(),
						Reports.ClickDocRead_TC_SS.getReports());
				TimeUtil.mediumWait();
				TimeUtil.mediumWait();
				switchToDefaultContent(driver);

			}
		}

//			click2(ClassroomTypeTrainingCertificate, Reports.ClickDocRead_TC_DC.getReports(),
//					Reports.ClickDocRead_TC_AC.getReports(), Reports.ClickDocRead_TC_AR.getReports(),
//					Reports.ClickDocRead_TC_SS.getReports());

		else {
			WebDriverWait wait21 = new WebDriverWait(driver, Duration.ofSeconds(120));
			button = wait21.until(ExpectedConditions
					.visibilityOfElementLocated(By.xpath("//input[@id='ReportViewerControl_ctl04_ctl00']")));

			click3(viewReport, Reports.ClickViewReport_DC.getReports(),
					Reports.ClickViewReport_CSUnderAppr_AC.getReports(),
					Reports.ClickViewReport_CSUnderAppr_AR.getReports(), Reports.ClickViewReport_SS.getReports());
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			// TimeUtil.extralongwait();

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL,
						"Session Under Approvla Status");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED,
						"Course Session Approved");
			}

			if (EmployeeStatus.equals(Constants.DR_COMPLETED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.DR_COMPLETED, "Document Reading Completed");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, "To BeRetrained");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIACCEPT)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_CIACCEPT,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIREJECT)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_CIREJECT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SELFNOMINATED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_SELFNOMINATED, "Self Nominated");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SKIPPED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_SKIPPED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_PRESENT)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_ATTENDANCE_RECORDED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_ABSENT)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_ABSENT,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED)) {
				verifyExactCaption(this.EmployeeStatus, Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED,
						"Course Invitation Accepted");
			}

			clickAndWaitforNextElement(CourseNamehyperlink, CourseNamehyperlink2,
					Reports.Click_CS_hyperlink_DC.getReports(), Reports.ClickViewReport_CSUnderAppr_AC.getReports(),
					Reports.ClickViewReport_CSUnderAppr_AR.getReports(), Reports.CS1_Name_Like_SS.getReports());
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL)) {
				verifyExactCaption(EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL,
						"Session Under Approvla Status");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED,
						"Course Session Approved");
			}
			if (EmployeeStatus.equals(Constants.DR_COMPLETED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.DR_COMPLETED, "Document Reading Completed");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, "To BeRetrained");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIACCEPT)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_CIACCEPT,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SELFNOMINATED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_SELFNOMINATED, "Self Nominated");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIREJECT)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_CIREJECT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SKIPPED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_SKIPPED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_PRESENT)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_ATTENDANCE_RECORDED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_ABSENT)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_ABSENT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED)) {
				verifyExactCaption(this.EmployeeStatus2, Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED,
						"Course Invitation Accepted");
			}

			TimeUtil.mediumWait();
			click2(CourseNamehyperlink2, Reports.Click_CS_hyperlink_DC.getReports(),
					Reports.ClickBatchName_AC.getReports(), Reports.ClickBatchName_AR.getReports(),
					Reports.CS1_Name_Like_SS.getReports());
			TimeUtil.mediumWait();

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL)) {
				if (SessionType.equals("Document Reading")) {
					verifyExactCaption(EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL,
							"Session Under Approvla Status");
				}
				if (SessionType.equals("Internal Classroom")) {
					verifyExactCaption(EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL,
							"Session Under Approvla Status");
				}
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED)) {
				verifyExactCaption(EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED,
						"Session Proposed");
			}

			if (EmployeeStatus.equals(Constants.DR_COMPLETED)) {
				verifyExactCaption(EmployeeStatus3, Constants.DR_COMPLETED, "Document Reading Completed");

			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED)) {
				verifyExactCaption(EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, "To BeRetrained");

			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIACCEPT)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_CIACCEPT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SELFNOMINATED)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_SELFNOMINATED, "Self Nominated");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_CIREJECT)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_CIREJECT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_SKIPPED)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_SKIPPED,
						"Course Invitation Accepted");
			}
			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_PRESENT)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_RESPONSE_NOT_YET_SUBMITTED,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_ABSENT)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_ABSENT,
						"Course Invitation Accepted");
			}

			if (EmployeeStatus.equals(Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED)) {
				verifyExactCaption(this.EmployeeStatus3, Constants.EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED,
						"Course Invitation Accepted");
			}

			TimeUtil.mediumWait();

			driver.navigate().refresh();

			switchToDefaultContent(driver);

		}

	}
}
