package com.Automation.learniqObjects;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.BatchFormationStrings;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_BatchFormation extends OQActionEngine {

	public static int skippedUsersCount;
	public static List<String> listSkippedUSers = new ArrayList<>();
	public static String[] SkippedUsers;

	// Batch Configuration Menu

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[14]/a[1]")
	WebElement configMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']//li//a[text()='Batch Formation']")
	WebElement courseManagerBatchFormationMenu;

	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;

	@FindBy(id = "Config_Remarks")
	WebElement remarks;

	@FindBy(id = "btnSubmit")
	WebElement submit;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	// -----Batch Formation for Online Session----- //

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeMenu;

	@FindBy(id = "TMS_Course Manager_Propose_MEN13")
	WebElement batchFormation;

	@FindBy(xpath = "//span[@class='select2-selection__arrow']")
	WebElement searchByNew;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Session Name']")
	WebElement searchByNewDropDown;

	@FindBy(id = "SessionDesc")
	WebElement courseNameLikeNew;

	@FindBy(id = "displayBtn")
	WebElement display;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//h6[text()='Session Details']")
	WebElement sessionDetailsHeader;

	@FindBy(id = "SelectAllResp")
	WebElement responsedCandidateSelectedAll;
	@FindBy(id = "SelectAllNotResp")
	WebElement notresponsedCandidateSelectedAll;

	@FindBy(xpath = "//h6[text()='Not Responded Candidates']")
	WebElement notResponsedCandidatesHeader;

//	@FindBy(xpath = "//table[@id='NotRespTab']/tbody/tr/td[4]/input[@type='checkbox']")
//	WebElement checkBoxes;
//
//	@FindBy(xpath = "//table[@id='NotRespTab']/tbody/tr/td[1]/input[@type='checkbox']")
//	WebElement employeeName;

	@FindBy(xpath = "//table[@id='NotRespTab']/tbody/tr")
	List<WebElement> rows;

	@FindBy(xpath = "//h6[text()='Self-Nominated Candidates']")
	WebElement selfNominatedCandidateHeader;

	@FindBy(id = "SelectAllSelfNom")
	WebElement selfNominatedCandidatesSelectAll;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	// Propose Batch Formation Audit Trail

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']//li//a[text()='Batch Formation']")
	WebElement batchFormationAudit;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[text()='Batch Formation Name']")
	WebElement searchByBatchFormationNameDropdown;

	@FindBy(id = "BatchFormationName")
	WebElement batchFormationNameLike;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement pageHeaderTitle;

	@FindBy(id = "status_heading")
	WebElement finalStatusHeading;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	// -----Batch Formation for Offline Session----- //

	@FindBy(xpath = "//h6[text()='Off-Line Candidates']")
	WebElement offLineCandidatesHeader;

	@FindBy(xpath = "//table[@id='OfflineTab']/tbody/tr")
	List<WebElement> offLineCandidateRows;

//	String courseName = CM_Course.getCourse();
//	String courseName = "Course_Test_Automation";
	// protected static String courseName = "m sche course1.6
	// (GLBSPPMCRS24259)/IT/1";

	public void batchFormationConfiguration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(configMenu);

		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());

		scrollToViewElement(courseManagerBatchFormationMenu);

		TimeUtil.shortWait();

		click2(courseManagerBatchFormationMenu, BatchFormationStrings.BatchForMenu_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormation_Config_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormation_Config_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchForConfig_SS.getBatchFormationStrings());

		switchToBodyFrame(driver);

		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");

		scrollToViewElement(remarks);

		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.Remarks_AC.getCommonStrings(), CommonStrings.Remarks_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		scrollToViewElement(submit);

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}

	public void proposeBatchFormationAuditTrail() {

		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(courseManagerAuditTrails);

		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());

		scrollToViewElement(batchFormationAudit);

		click2(batchFormationAudit, BatchFormationStrings.BatchForMenu_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFor_AuditTrails_Menu_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFor_AuditTrails_Menu_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFor_AuditTrails_Menu_SS.getBatchFormationStrings());

		TimeUtil.shortWait();

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.AT_SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.AT_SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(searchByBatchFormationNameDropdown,
				BatchFormationStrings.SearchBy_BatchFormationName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_BatchFormationName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_BatchFormationName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_BatchFormationName_SS.getBatchFormationStrings());

		sendKeys2(batchFormationNameLike, BatchFormationStrings.Like_BFName_DC.getBatchFormationStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());

		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(displayedRecord, BatchFormationStrings.Click_BF_for_AuditTrails_DC.getBatchFormationStrings(),
				BatchFormationStrings.Click_BF_for_AuditTrails_AC.getBatchFormationStrings(),
				BatchFormationStrings.Click_BF_for_AuditTrails_AR.getBatchFormationStrings(),
				BatchFormationStrings.Click_BF_for_AuditTrails_SS.getBatchFormationStrings());

		driver.switchTo().frame(0);

		TimeUtil.shortWait();

		scrollToViewElement(finalStatusHeading);

		TimeUtil.shortWait();

		scrollToViewElement(pageHeaderTitle);

		click2(close, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				BatchFormationStrings.BatchFor_AuditTrails_Menu_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFor_AuditTrails_Menu_AR.getBatchFormationStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());

		TimeUtil.shortWait();

		switchToDefaultContent(driver);
	}

	public void batchFormation_Online_NotResponded_Responded_SelfNominatedUsers(HashMap<String, String> testData) {

		// String SkippedUser = "Trainee.75";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());
		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewMSZB" + Constants.PERCENTAGE_SIGN,
				// CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
				"An alert 'Select At Least One Candidate' should be displayed",
				"An alert 'Select At Least One Candidate' is displayed", "Submit button");
		scrollToViewElement(sessionDetailsHeader);
		click2(responsedCandidateSelectedAll, BatchFormationStrings.ResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_SS.getBatchFormationStrings());
		scrollToViewElement(notResponsedCandidatesHeader);
		TimeUtil.shortWait();
		selectMultipleCheckBoxes(rows, BatchFormationStrings.NotResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_SS.getBatchFormationStrings(),
				CM_CourseSession.getSkippedEmployeeName());
		TimeUtil.mediumWait();
		scrollToViewElement(selfNominatedCandidateHeader);
		click2(selfNominatedCandidatesSelectAll, BatchFormationStrings.SelfCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_SS.getBatchFormationStrings());
		selectStartTimeInBatchFormationOQFormat();
		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AR.getBatchFormationStrings(),
				BatchFormationStrings.Submit_SS.getBatchFormationStrings());

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

//		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
//				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//				CommonStrings.Password_SS.getCommonStrings());
//		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//				BatchFormationStrings.Esign_ProceedBatchFor_AC.getBatchFormationStrings(),
//				BatchFormationStrings.Esign_ProceedBatchFor_AR.getBatchFormationStrings(),
//				CommonStrings.Esign_Proceed_SS.getCommonStrings());
	}

	public void batchFormation_Offline_Users(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());

		switchToBodyFrame(driver);

		TimeUtil.shortWait();

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());

		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
//				"CRSNewJMGI" + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());

		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());

		TimeUtil.shortWait();

		waitForElementVisibile(offLineCandidatesHeader);
		scrollToViewElement(offLineCandidatesHeader);

		TimeUtil.shortWait();

		selectMultipleCheckBoxes(offLineCandidateRows,
				BatchFormationStrings.NotResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_SS.getBatchFormationStrings(),
				CM_CourseSession.getSkippedEmployeeName());

		TimeUtil.mediumWait();

		selectStartTimeInBatchFormationOQFormat();

		scrollToViewElement(submit);

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}

	public void batchFormation_Online_NotRespondedUsers_SelectAll(HashMap<String, String> testData) {

		// String SkippedUser = "Trainee.75";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());
		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());
		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		scrollToViewElement(sessionDetailsHeader);
		scrollToViewElement(notResponsedCandidatesHeader);
		click2(notresponsedCandidateSelectedAll, BatchFormationStrings.ResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_SS.getBatchFormationStrings());
//		TimeUtil.shortWait();
//		selectStartTimeInBatchFormationOQFormat();
//		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
//				BatchFormationStrings.Submit_AC.getBatchFormationStrings(),
//				BatchFormationStrings.Submit_AR.getBatchFormationStrings(),
//				BatchFormationStrings.Submit_SS.getBatchFormationStrings());
//		try {
//			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
//				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
//						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
//						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
//				TimeUtil.shortWait();
//				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
//						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
//						CommonStrings.Esign_Proceed_SS.getCommonStrings());
//				TimeUtil.shortWait();
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//	

		TimeUtil.mediumWait();

		selectStartTimeInBatchFormationOQFormat();

		scrollToViewElement(submit);

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
	}

	public void batchFormation_Online_NotResponded_RespondedCandidates(HashMap<String, String> testData) {

		// String SkippedUser = "Trainee.75";

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());
		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewMSZB" + Constants.PERCENTAGE_SIGN,
				// CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
				"An alert 'Select At Least One Candidate' should be displayed",
				"An alert 'Select At Least One Candidate' is displayed", "Submit button");
		scrollToViewElement(sessionDetailsHeader);
		click2(responsedCandidateSelectedAll, BatchFormationStrings.ResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.ResCan_SelectAll_SS.getBatchFormationStrings());
		scrollToViewElement(notResponsedCandidatesHeader);
		TimeUtil.shortWait();
		selectMultipleCheckBoxes(rows, BatchFormationStrings.NotResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_SS.getBatchFormationStrings(),
				CM_CourseSession.getSkippedEmployeeName());
		TimeUtil.mediumWait();
		scrollToViewElement(selfNominatedCandidateHeader);
		click2(selfNominatedCandidatesSelectAll, BatchFormationStrings.SelfCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.SelfCan_SelectAll_SS.getBatchFormationStrings());
		selectStartTimeInBatchFormationOQFormat();
		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AR.getBatchFormationStrings(),
				BatchFormationStrings.Submit_SS.getBatchFormationStrings());

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

	}

	public void batchFormation_NotResponded_Skipped(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());
		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewMSZB" + Constants.PERCENTAGE_SIGN,
				// CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());
		TimeUtil.mediumWait();
//		scrollToViewElement(submit);
//		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
//				"An alert 'Select At Least One Candidate' should be displayed",
//				"An alert 'Select At Least One Candidate' is displayed", "Submit button");
//		scrollToViewElement(sessionDetailsHeader);
//		click2(responsedCandidateSelectedAll, BatchFormationStrings.ResCan_SelectAll_DC.getBatchFormationStrings(),
//				BatchFormationStrings.ResCan_SelectAll_AC.getBatchFormationStrings(),
//				BatchFormationStrings.ResCan_SelectAll_AR.getBatchFormationStrings(),
//				BatchFormationStrings.ResCan_SelectAll_SS.getBatchFormationStrings());
		waitForElementVisibile(notResponsedCandidatesHeader);
		scrollToViewElement(notResponsedCandidatesHeader);
		TimeUtil.shortWait();
		selectMultipleCheckBoxes(rows, BatchFormationStrings.NotResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_SS.getBatchFormationStrings(),
				CM_VerifyCourseSessionScreen.getSkippedEmployeeName());

		TimeUtil.mediumWait();
//		scrollToViewElement(selfNominatedCandidateHeader);
//		click2(selfNominatedCandidatesSelectAll, BatchFormationStrings.SelfCan_SelectAll_DC.getBatchFormationStrings(),
//				BatchFormationStrings.SelfCan_SelectAll_AC.getBatchFormationStrings(),
//				BatchFormationStrings.SelfCan_SelectAll_AR.getBatchFormationStrings(),
//				BatchFormationStrings.SelfCan_SelectAll_SS.getBatchFormationStrings());
		selectStartTimeInBatchFormationOQFormat();
		listSkippedUSers.add(CM_VerifyCourseSessionScreen.SkippedEmployeeID);

		skippedUsersCount = listSkippedUSers.size();
		SkippedUsers = listSkippedUSers.toArray(new String[0]);

		System.out.println("Skipped Users Count is: " + skippedUsersCount);
		click2(submit, BatchFormationStrings.Submit_DC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AC.getBatchFormationStrings(),
				BatchFormationStrings.Submit_AR.getBatchFormationStrings(),
				BatchFormationStrings.Submit_SS.getBatchFormationStrings());

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				
				TimeUtil.shortWait();

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		getCurrentTimeNew();
		switchToDefaultContent(driver);

	}

	public void batchFormation_Offline_Users_CountMisMatch(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, courseManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(courseManagerMenu, proposeMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		click2(proposeMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		click2(batchFormation, BatchFormationStrings.BatchFormationRegistrationScreen_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegistrationScreen_SS.getBatchFormationStrings());

		switchToBodyFrame(driver);

		TimeUtil.shortWait();

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				BatchFormationStrings.SearchBy_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_AR.getBatchFormationStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByNewDropDown, BatchFormationStrings.SearchBy_CourseSessionName_DC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AC.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_AR.getBatchFormationStrings(),
				BatchFormationStrings.SearchBy_CourseSessionName_SS.getBatchFormationStrings());

		sendKeys2(courseNameLikeNew, BatchFormationStrings.Like_CourseSessName_DC.getBatchFormationStrings(),
//				"CRSNewJMGI" + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				BatchFormationStrings.Like_CourseSessName_SS.getBatchFormationStrings());

		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(displayedRecord, BatchFormationStrings.RegisteredCourseSess_DC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AC.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_AR.getBatchFormationStrings(),
				BatchFormationStrings.BatchFormationRegisteredCourseSess_SS.getBatchFormationStrings());

		TimeUtil.shortWait();

		waitForElementVisibile(offLineCandidatesHeader);
		scrollToViewElement(offLineCandidatesHeader);

		TimeUtil.shortWait();

		selectMultipleCheckBoxes(offLineCandidateRows,
				BatchFormationStrings.NotResCan_SelectAll_DC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AC.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_AR.getBatchFormationStrings(),
				BatchFormationStrings.NotResCan_SelectAll_SS.getBatchFormationStrings(),
				CM_VerifyCourseSessionScreen.getSkippedEmployeeName());

		TimeUtil.mediumWait();

		selectStartTimeInBatchFormationOQFormat();

		listSkippedUSers.add(CM_VerifyCourseSessionScreen.SkippedEmployeeID);

		skippedUsersCount = listSkippedUSers.size();
		SkippedUsers = listSkippedUSers.toArray(new String[0]);

		System.out.println("Skipped Users Count is: " + skippedUsersCount);

		scrollToViewElement(submit);

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AC.getCourseSessionStrings(),
				CourseSessionStrings.SubmitCourseSessionwithEsign_AR.getCourseSessionStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		getCurrentTimeNew();
		switchToDefaultContent(driver);
	}

	// This method is for Batch Formation for Single Not Responded Candidates

}
