package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_BatchFormation;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_QuestionPaper;
import com.Automation.learniqObjects.CM_RecordAttendance;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online IT Session without assessment for scheduled course and make
 * atleast one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, present by viewing Individual employee
 * report at each transaction starting from course session, also add at least
 * one user and check IER report and to be retrained and view IER for those
 * employees.
 */

public class SCH_IT_OFF_WITH_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/SCH_IT_OFF_WITH_ASSESSMENT.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/SCH_IT_OFF_WITH_ASSESSMENT.xlsx";

	public SCH_IT_OFF_WITH_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));

	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}

		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Configuration, Modification, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Configuration, Modification, Approve with Audit Trails");
		}

		TrainingShcedule.modifyTrainingScheduled(testData);
	}

	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
		}

		trainer.trainer_Modification_AuditTrails(testData);

	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		verifyCSScreen.courseSession_Offline_WithExam_New(testData);

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();

	}

	// Test Method for BatchFormation Configuration, Registration with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
		}

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Offline_Users_CountMisMatch(testData);

	}

	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionBank")
	public void QuestionBank_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Configuration, Registration, Approve with AuditTrails");
		}

//		
		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
		// Logout.signOutPage();
	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with Manual Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with Manual Evaluation");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOffline, Constants.ITType);

	}
	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}

		RecordAttendance.recordAttendance_OfflineSession_CountMismatch_New(testData);

		driver.navigate().refresh();
	}

	// Test Method for
	// RecordMarks-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordMarksData = new ExcelUtilUpdated(ExcelPath, "Record Marks");

	@DataProvider(name = "Record Marks")
	public Object[][] get_RecordMarks() throws Exception {
		Object[][] obj = new Object[RecordMarksData.getRowCount()][1];
		for (int i = 1; i <= RecordMarksData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordMarksData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 10, dataProvider = "Record Marks", enabled = true)
	public void recordMarks(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Evaluate Answer Paper");
		}

		RecordMarks.RecordMarks(testData);

	}

	@Test(priority = 11, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		verifyCSScreen.checkCourseSession_IT_Offline_With_Assessment_After_Keeping_Employees_In_Different_States();

	}

	@Test(priority = 12, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		CSRReport.TBPCSRReport_IT_Offline_WithAssesment_After_keping_Emplpoyees_In_Diff_States();

	}

	@Test(priority = 13, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest("").assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("");
		}
		CourseRetraining.Scheduled_course_Retraining_IT_Offline_With_Assessment_();

	}

	@Test(priority = 14, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_();
	}

	@Test(priority = 15, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining1() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		verifyCSScreen.scheduled_IT_Offline_With_Assessment_VerifyCourseSession_Screen_After_CourseRetraining();
	}

	@Test(priority = 16, enabled = true)
	public void verifyCSR_AfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
		}
		CSRReport.TBPCSRReport_IT_WithAssesment_After_CourseRetraining();

		Logout.signOutPage();

	}
}
