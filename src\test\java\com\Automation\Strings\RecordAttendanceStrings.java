package com.Automation.Strings;

public enum RecordAttendanceStrings {

//Attendance submenu

	RecrdAttendanceScreen_DC("Click on 'Attendance' submenu."),
	RecrdAttendanceScreen_AC("'Batch List for Attendance' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Batch Name, Course Name, Initiated On, Training Type, Session Type' columns.</div>"),
	RecrdAttendanceScreen_AR("'Batch List for Attendance' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Batch Name, Course Name, Initiated On, Training Type, Session Type' columns.</div>"),
	RecrdAttendanceScreen_SS("'Attendance'"),

//Search By	

	SearchBy_AC(
			"Option to search with 'Top 250 Records, Batch Name, Course Name and Initiated Between' should be displayed."),
	SearchBy_AR(
			"Option to search with 'Top 250 Records, Batch Name, Course Name and Initiated Between' is getting displayed."),

//Search By Course Name
	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_BatchName_DC("Select 'Batch Name'."),
	SearchBy_BatchName_SS("'Batch Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR("Selection is getting accepted.</div>"), SearchBy_CourseName_SS("'Course Name'"),

//Classroom type with Asessment

	Like_WithExam_CourseName_DC(
			"Enter the above 'Course Name' for which the question paper is prepared for the respective batch."),
	RegisteredCourse_DC("Click on the above batch name for which the question paper is prepared."),
	RegisteredCourse_NoExam_DC("Click on the above proposed batch name."), Like_CourseName_SS("Course Name"),

	Like_BatchName_SS("Batch Name"),

//Classroom type without Assessment	
	Like_CourseName_DC("Enter the course name of the above proposed batch."),
	RegisteredCourse_WithoutExam_DC("Click on above proposed Batch Name."),

	RegisteredCourse_AC("Option to select session dates and times should be available under 'Actual' section.</div>"
			+ "<div><b>* </b>Option to select 'Candidates List' should be available.</div>"),
	RegisteredCourse_AR("Option to select session dates and times are available under 'Actual' section.</div>"
			+ "<div><b>* </b>Option to select 'Candidates List' is available.</div>"),
	RegisteredCourse_SS("'CourseSession'"),

	// Manual evaluator strings

	Click_AddItem_Evaluator_DC("Click on 'Add Item' for 'Select Evaluator'."),
	Click_AddItem_Evaluator_AC("'Evaluator List' window should be displayed."),
	Click_AddItem_Evaluator_AR("'Evaluator List' window is getting displayed."),
	Click_AddItem_Evaluator_SS("'Select Evaluator'."),

	// Search Drop Down
	click_Search_Dropdown_DC("Click on the 'Search' dropdown."),
	click_Search_Dropdown_AC("Option to search with 'Employee Name and Employee ID' should be displayed."),
	click_Search_Dropdown_AR("Option to search with 'Employee Name and Employee ID' are getting displayed."),
	click_Search_Dropdown_SS("Search"),

	Select_EmployeeName_DC("Select 'Employee Name'."), Select_EmployeeName_SS("'Employee Name'"),
	Enter_Evaluatorname_DC("Enter the required 'Employee Name' in 'Find' field."),

	Select_Eval_DC("Select the required employee as Evaluator."), Click_Add_DC("Click on 'Add' button."),
	Click_Add_AC("Selected Employee Name should be displayed for 'Select Evaluator' field."),
	Click_Add_AR("Selected Employee Name is getting displayed for 'Select Evaluator' field."),
	Click_Add_SS("'Evaluator'"),

	// Select All candidates

	CandidateList_SelectAll_DC("Click on 'Select All' checkbox."),
	Select_Employee_DC("Select the above employee for whom the batch formation has been proposed."),
	Select_Employee_SS("Select Employee"),
	Selecyt_Employee_Verbal_AC("Selection should be accepted.</div>"
			+ "<div><b>* </b>Option to select Pass and Fail should be enabled.</div>"),
	Selecyt_Employee_Verbal_AR("Selection is getting accepted.</div>"
			+ "<div><b>* </b>Option to select Pass and Fail are getting enabled.</div>"),

	// Select Pass
	Select_Pass_DC("Select Result as 'Pass'"), Select_Pass_SS("'Pass'"),

	CandidateList_SelectAll_AC("Selection  should be accepted."),
	CandidateList_SelectAll_AR("Selection is getting accepted"), CandidateList_SelectAll_SS("'Select All'"),

	// Select Fail
	Select_Fail_DC("Select Result as 'Fail'"), Select_Fail_SS("'Fail'"),
	// Preview button

	previewBtn_DC("Click on 'Preview' Button"),
	previewBtn_AC("'Present' status should be displayed against the selected Employee Name.</div>"
			+ "<div><b>*</b> 'Edit' and 'Submit' button should be displayed.</div>"),
	previewBtn_AR("'Present' status is getting displayed against the selected Employee Name.</div>"
			+ "<div><b>*</b> 'Edit' and 'Submit' buttons are getting displayed.</div>"),
	previewBtnPresentAbsent_AC(
			"'Present' and 'Absent' status should be displayed against the selected Employee Name.</div>"
					+ "<div><b>*</b> 'Edit' and 'Submit' button should be displayed.</div>"),
	previewBtnPresentAbsent_AR(
			"'Present' and 'Absent' status is getting displayed against the selected Employee Name.</div>"
					+ "<div><b>*</b> 'Edit' and 'Submit' buttons are getting displayed.</div>"),
	previewBtn_SS("'Preview'"),

	// Submit button

	Submit_Button_DC("Click on 'Submit' button and click 'OK' at alert"),
	Submit_Button_AC("'Batch Attendance Updated' confirmation message should be displayed with 'Done' button"),
	Submit_Button_AR("'Batch Attendance Updated' confirmation message is getting displayed with 'Done' button"),
	Submit_Button_SS("'Submit'"),

	// Start time and end time strings

	Click_StartHH_DC("Click on the hours dropdown under 'Start Time (HH:MM)' field."),
	Click_StartHH_AC("Option to select hours value should be displayed."),
	Click_StartHH_AR("Option to select hours value is displayed."), Click_StartHH_SS("'Start Time'"),

	Click_HH_DC("Click on the hours dropdown under 'End Time (HH:MM)' field."),
	Click_HH_AC("Option to select hours value should be displayed."),
	Click_HH_AR("Option to select hours value is displayed."), Click_HH_SS("'End Time'"),

	Enter_End_Time_DC("Enter the required hours value."), Enter_End_TimeMin_DC("Enter the required minutes value."),
	Enter_End_Time_SS("'End Time'"),

	Enter_Start_Time_DC("Enter the required hours value."), Enter_Start_TimeMin_DC("Enter the required minutes value."),
	Enter_Start_Time_SS("'Start Time'"),

	Click_HH_Value_DC("Select the required valid hours value."),
	Click_Min_Value_DC("Select the required valid minutes value."),

	Click_StartHH_Value_DC("Select the required valid end hour value."),
	Click_StartMin_Value_DC("Select the required valid end hour value."),

	Click_Min_DC("Click on the minutes dropdown under 'End Time (HH:MM)' field."),
	Click_Min_AC("Option to select minutes value should be displayed."),
	Click_Min_AR("Option to select minutes value is displayed."), Click_Min_SS("'End Time'"),

	Click_StartMin_DC("Click on the minutes dropdown under 'Start Time (HH:MM)' field."),
	Click_StartMin_AC("Option to select minutes value should be displayed."),
	Click_StartMin_AR("Option to select minutes value is displayed."), Click_StartMin_SS("'Start Time'"),

	// Select required user1 to present
	Click_SelectUser_DC("Select the required user."), Select_User("Select User"),

	// Select other user
	Click_SelectUser2_DC("Similarly select the other user and make that atleast one user should 'Absent'."),

	// Evaluation Method Drop Down

	Click_Eval_Method_DC("Click on the Evaluation method drop down."),
	Click_Eval_Method_AC("Option to select 'No' and 'Verbal' should be displayed."),
	Click_Eval_Method_AR("Option to select 'No' and 'Verbal' is getting displayed."),
	Click_Eval_Method_SS("'Evalaution Method'"),

	Select_Verbal_DC("Select Evaluation method as 'Verbal'."),
	Select_Verbal_AR("Selected value should be displayed for Evaluation method."),
	Select_Verbal_AC("Selected value is getting displayed for Evaluation method."), Select_Verbal_SS("'Verbal'"),

	// E Sign

	Esign_sub_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Record Attendance Esign:'.</div>"),
	Esign_sub_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Record Attendance Esign:'.</div>"),

	// Additional Users

	ADDITIONAL_USERS_SD("Click on 'Additional Users'."),
	ADDITIONAL_USERS_AC("'Attendance - Additional Users – Employee List' screen should be displayed."),
	ADDITIONAL_USERS_AR("'Attendance - Additional Users – Employee List' screen is displayed."),
	ADDITIONAL_USERS_SS("'Additional Users'"),

	SEARCH_FILTER_SD("Click on 'Search this Page' icon."),
	SEARCH_FILTER_AC("'Search this Page' search string should be displayed."),
	SEARCH_FILTER_AR("'Search this Page' search string is displayed."), SEARCH_FILTER_SS("'Search this Page'"),

	ENTER_ADDITIONAL_USERS_SD("Enter valid 'Employee ID' into search this page search String."),
	ENTER_ADDITIONAL_USERS_AC("Option to select 'Employee ID' value should be displayed."),
	ENTER_ADDITIONAL_USERS_AR("Option to select 'Employee ID' value is displayed."),
	ENTER_ADDITIONAL_USERS_SS("'Employee ID'"),

	ADD_SD("Click on 'Add' button."),
	ADD_AC("<div>* Selected value should be displayed at 'Selected Items.</div>"
			+ "<div>* option to select 'Remove' and 'Clear All' button should be enabled.</div>"),
	ADD_AR("<div>* Selected value is displayed at 'Selected Items.</div>"
			+ "<div>* option to select 'Remove' and 'Clear All' button is enabled.</div>"),
	ADD_SS("'Add'"),

	FINAL_ADD_AC("<div>* Screen should navigate 'Record Attendance' screen.</div>"
			+ "<div>* Screen should display 'Additional Candidates List' table data with 'Employee Name', 'Employee ID', 'Department', 'Designation' and 'Select All' fields.</div>"),

	FINAL_ADD_AR("<div>* Screen navigate to 'Record Attendance' screen.</div>"
			+ "<div>* Screen displays 'Additional Candidates List' table data with 'Employee Name', 'Employee ID', 'Department', 'Designation' and 'Select All' fields.</div>");

	private final String recordAttendanceStrings;

	RecordAttendanceStrings(String recordAttendanceStrings) {

		this.recordAttendanceStrings = recordAttendanceStrings;

	}

	public String getRecordAttendanceStrings() {
		return recordAttendanceStrings;
	}

}
