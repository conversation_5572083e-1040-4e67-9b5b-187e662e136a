package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Strings.GroupStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.QuestionBankStrings;
import com.Automation.Strings.RecordAttendanceStrings;
import com.Automation.Strings.SetGlobleProfileStrings;
import com.Automation.Strings.SubGroupStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Strings.TrainingScheduleStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;

public class SYS_SubgroupModification extends OQActionEngine {

	Properties prop;
	public static String SubGroupval = "";
	public static String Descriptionval = "";
	public static String JobResponsibilityval = "";

	public static String ModSubGroupval = "";
	public static String ModDescriptionval = "";
	public static String ModJobResponsibilityval = "";

	public static String Initiator = "";
	public static String Approver = "";
	public static String SubgroupUC = "";

	public static String getSubGroupval() {
		return SubGroupval;
	}

	public static void setSubGroupval(String subGroupval) {
		SubGroupval = subGroupval;
	}

	public static String getModSubGroupval() {
		return ModSubGroupval;
	}

	public static void setModSubGroupval(String modsubGroupval) {
		ModSubGroupval = modsubGroupval;
	}

	public static String getSubgroupUC() {
		return SubgroupUC;
	}

	public static void setSubgroupUC(String subGroupUC) {
		SubgroupUC = subGroupUC;
	}

	public static String getDescriptionval() {
		return Descriptionval;
	}

	public static void setDescriptionval(String descriptionval) {
		Descriptionval = descriptionval;
	}

	public static String getModDescriptionval() {
		return ModDescriptionval;
	}

	public static void setModDescriptionval(String moddescriptionval) {
		ModDescriptionval = moddescriptionval;
	}

	public static String getJobResponsibilityval() {
		return JobResponsibilityval;
	}

	public static void setJobResponsibilityval(String jobResponsibilityval) {
		JobResponsibilityval = jobResponsibilityval;
	}

	public static String getModJobResponsibilityval() {
		return ModJobResponsibilityval;
	}

	public static void setModJobResponsibilityval(String modjobResponsibilityval) {
		ModJobResponsibilityval = modjobResponsibilityval;
	}

	public static String getInitiatorName() {
		return Initiator;
	}

	public static void setInitiatorName(String InitiatorName) {
		Initiator = InitiatorName;
	}

	public static String getApproverName() {
		return Approver;
	}

	public String getinitiator() {
		return initiator;
	}

	public void setinitiator(String InitiatiorName) {
		initiator = InitiatiorName;
	}

	public static void setApproverName(String ApproverName) {
		Approver = ApproverName;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Re-Initiation')]")
	WebElement reinitiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approveMenu;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN07_SUBMEN16']")
	WebElement approveSubgroupMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;
	@FindBy(xpath = "(//ul//a[text()='Configuration Audit Trails'])[3]")
	WebElement ConfigurationauditTrailsMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Configure')]")
	WebElement configurationMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN16")
	WebElement subGroupConfig;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(id = "TMS_System Manager_User Groups_MEN18_SUBMEN16")
	WebElement subgroupCnfgMenu;
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationText2;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN16")
	WebElement subgroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN109_SUBMEN16")
	WebElement reniatesubgroup;

	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN16")
	WebElement subgroupAuditTrails;
//	@FindBy(xpath = "//ul[@class='inner-sub'][preceding-sibling::a[text()='Audit Trails']]//li//a[text()='Subgroup']")
//	WebElement subgroupAuditTrails;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Subgroup Name')]")
	WebElement searchBySubgroupNameDropdown;
	@FindBy(id = "Description")
	WebElement subgroupNameLike;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//input[@id='Subgroup_SubgrpDesc']")
	WebElement subGroupNameTxt;
	@FindBy(xpath = "//input[@id='Subgroup_Description']")
	WebElement descriptionText;
	@FindBy(xpath = "//textarea[@id='Subgroup_JDSC']")
	WebElement jobResponsibilityTxt;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDropdown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[1]")
	WebElement searchSel3;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(id = "Config_Remarks")
	WebElement r1emarks;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Subgroup Name']//following-sibling::span")
	WebElement auditSubgroupName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Unique Code')]//following-sibling::span")
	WebElement auditUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Description')]//following-sibling::span")
	WebElement auditDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span")
	WebElement auditJobResponsibility;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'1 -')]")
	WebElement revisionNotransValueCompareTRN;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']")
	WebElement auditCompareReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditCompareReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditCompareReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditCompareReturnTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']")
	WebElement auditCompareDropTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditCompareDropTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditCompareDropTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditCompareDropTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']")
	WebElement auditCompareReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditCompareReinitateTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditCompareReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditCompareReinitateTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditCompareApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareApproveTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;

	@FindBy(xpath = "//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus1;

	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNReurnedFinalStatus;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ModApprovalValue;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtCreate-results']//child::li")
	WebElement clickApprovalCount;

	@FindBy(xpath = "//*[@id='select2-Config_NarAtModify-results']//child::li")
	WebElement clickModApprovalCount;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditCompareTRNUniqueCode;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement initiatedBy;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement initiatedOn;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement revNo;
	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;

	@FindBy(xpath = "//input[@id='SelectedDecision_3']")
	WebElement returnRadioBtn;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approvetext;

	@FindBy(xpath = "//label[text()='Return']")
	WebElement returntext;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement verifyApprovedRemarks;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Subgroup Name')]")
	WebElement searchBySubgroupNameDropdown1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditCompareTRNLevel1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditCompareTRNRole1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement iniatiatedby;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement iniatiatedon;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement revno;
	@FindBy(xpath = "//span[@id='MainTitle']")
	WebElement subroupsreen;
	@FindBy(xpath = "//span[@id='SubTitle']")
	WebElement subroupsreen1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title pb-0 pt-2']")
	WebElement subroupauditsreen;
	@FindBy(xpath = "//span[text()='Subgroup']")
	WebElement subroupauditsreen1;
	@FindBy(xpath = "//span[text()='Audit Trails']")
	WebElement subroupauditsreen2;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Configuration Audit Trails']//li//a[text()='Subgroup']")
	WebElement configurationSubGroupAuditTrails;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callEsignAtInitiaiton;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callEsignAtApproval;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtRegApprCheckBox;
	@FindBy(id = "txtESignPassword")
	WebElement esignPWDTextBox;

	@FindBy(id = "Submit_Esign")
	WebElement proceedBtn;

	@FindBy(xpath = "//textarea[@id='Subgroup_Remarks']")
	WebElement reiniateRemarksReasonTextbox;

	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement returnRemarksReasonTextbox;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::span")
	WebElement remarksandreasons;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement remarksandreasonsapprove;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement remarksandreasonsapprovemain;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Modify']")
	WebElement modifymenu;

	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN16")
	WebElement modifysubgroupmenu;

	@FindBy(xpath = "//td[text()='No data available in table']")
	WebElement nodisplaydata;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='RI Transfer']")
	WebElement ritransfermenu;

	@FindBy(xpath = "//span[text()='Add Item']")
	WebElement additem;

	@FindBy(xpath = "//input[@type='search']")
	WebElement searchby;

	@FindBy(xpath = "//button[@id='Transfer_selectBtn']")
	WebElement radiobutton;

	@FindBy(xpath = "//div[text()='Re-initiation Task Transfer (Reg.)']")
	WebElement reiniatetasktransfer;

	@FindBy(xpath = "//div[text()='Re-initiation Task Transfer (Mod.)']")
	WebElement reiniatemodtasktransfer;

	@FindBy(xpath = "//div[text()='Registration']")
	WebElement reiniatetasktransferregistration;

	@FindBy(xpath = "//button[@id='AuditEventModal_View']")
	WebElement ritransferproceed;

	@FindBy(xpath = "//h6[text()='Final Status : Transferred']")
	WebElement ritransferfinalstatus;
	@FindBy(xpath = "//p[@id='approve-name-0']")
	WebElement ritransferusername;
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[1]")
	WebElement ritransferinitiateddate;
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[2]")
	WebElement ritransferremarks;

	@FindBy(xpath = "//h6[@id='status_heading']")
	WebElement ritransferusernamestatus;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal2;

	@FindBy(xpath = "//p[@id='status-message-1-0']")
	WebElement auditCompareTRNAintActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[1]")
	WebElement auditCompareTRNintActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[2]")
	WebElement auditCompareTRNintDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-0']//following-sibling::p[3]")
	WebElement auditCompareintRemarksVal2;

	@FindBy(xpath = "//input[@type='radio']")
	WebElement arButton;

	@FindBy(id = "Transfer_selectBtn")
	WebElement addButton;

	@FindBy(id = "TransferUserPopUpBtn")
	WebElement transwerAddItems;

	@FindBy(id = "TMS_System Manager_User Groups_MODMEN21_SUBMEN16")
	WebElement ritransfersubgroup;

	@FindBy(xpath = "//span[@id='approve-status-span-old']")
	WebElement transfrom;

	@FindBy(xpath = "//span[@id='approve-status-span-new']")
	WebElement transTo;

	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;

	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approveLabelText;

	@FindBy(xpath = "//input[@id='SelectedDecision_4']")
	WebElement dropRadioBtn;

	@FindBy(id = "TMS_System Manager_User Groups_MEN129_SUBMEN16")
	WebElement statuschangesubgroup;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Status Change')]")
	WebElement statuschange;

	@FindBy(xpath = "//button[text()='Inactive']")
	WebElement statuschangeinactve;

	@FindBy(xpath = "//div[text()='Registration']")
	WebElement regmod;

	@FindBy(xpath = "//div[text()='Modification']")
	WebElement modmod;

	@FindBy(xpath = "//div[@data-target='#MainTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleMainTRN;
	@FindBy(xpath = "//div[@data-target='#MainTRN']//span[contains(text(),'1 -')]")
	WebElement revisionNoValueMainTRN;

	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditMainTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditMainTRNApprovalComVal;

	@FindBy(xpath = "(//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span)[2]")
	WebElement auditMainritransTRNApprovalReqVal;
	@FindBy(xpath = "(//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span)[2]")
	WebElement auditMainritransTRNApprovalComVal;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']")
	WebElement auditMainApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditMainApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditMainApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditMainApproveTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditMainTRNFinalStatus;

	@FindBy(xpath = "(//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading'])[2]")
	WebElement auditMaintransTRNFinalStatus;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::span[1]//following::p")
	WebElement modMainremarksandreasonsapprove;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Subgroup Name']//following-sibling::span")
	WebElement auditmodSubgroupName;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Unique Code')]//following-sibling::span")
	WebElement auditmodUniqueCode;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Description')]//following-sibling::span")
	WebElement auditmodDescription;
	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span")
	WebElement auditmodJobResponsibility;

	@FindBy(xpath = "(//div[@id='MainTRN']//label[text()='Subgroup Name']//following-sibling::span)[2]")
	WebElement auditmodriSubgroupName;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Unique Code')]//following-sibling::span)[2]")
	WebElement auditmodriUniqueCode;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Description')]//following-sibling::span)[2]")
	WebElement auditmodriDescription;
	@FindBy(xpath = "(//div[@id='MainTRN']//label[contains(text(),'Job Responsibility')]//following-sibling::span)[2]")
	WebElement auditmodriJobResponsibility;

	@FindBy(xpath = "//button[text()='Modification']")
	WebElement modificationtab;
	@FindBy(xpath = "//tr[@class='click-row odd']")
	WebElement statusactiveRecord;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']")
	WebElement auditMainReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditMainReinitateTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditMainReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditMainReinitateTRNRemarksVal1;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditMainTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditMainTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditMainTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal2;

	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]")
	WebElement auditMainTRNAintActionValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[1]")
	WebElement auditMainTRNintActionByValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[2]")
	WebElement auditMainTRNintDateTimeValue;
	@FindBy(xpath = "(//div[@id ='MainTRN']//p[@id='status-message-1-0'])[2]//following-sibling::p[3]")
	WebElement auditMainintRemarksVal2;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']")
	WebElement auditMainReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditMainReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditMainReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditMainReturnTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']")
	WebElement auditMainTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditMainTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditMainTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal1;

	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3])[2]")
	WebElement auditMainritransTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']")
	WebElement auditMainDropTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[1]")
	WebElement auditMainDropTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[2]")
	WebElement auditMainDropTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Dropped']//following-sibling::p[3]")
	WebElement auditMainDropTRNRemarksVal1;

	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated'])[2]")
	WebElement auditMainriTRNActionValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1])[2]")
	WebElement auditMainriTRNActionByValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2])[2]")
	WebElement auditMainriTRNDateTimeValue;
	@FindBy(xpath = "(//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3])[2]")
	WebElement auditMainriTRNRemarksVal1;

	@FindBy(xpath = "//button[text()='Active']")
	WebElement statuschangeactve;

	/**
	 * This method is for SubGroup Configuration
	 *
	 */

	public void SubgroupRegistrationApproval_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configurationMenu,
				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configurationMenu, subgroupCnfgMenu,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(subgroupCnfgMenu, SubGroupStrings.SubgroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "Call E-sign At: Approval Registration");
		click2(noOfAprReqForRegDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void SubgroupModificationApproval_Configuration(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configurationMenu,
				CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		clickAndWaitforNextElement(configurationMenu, subgroupCnfgMenu,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(subgroupCnfgMenu, SubGroupStrings.SubgroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AC.getSubGroupStrings(),
				SubGroupStrings.Subgroup_Config_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);
		SelectRadioBtnAndCheckbox(driver, eSignAtModInitCheckBox, "Call E-sign At: Initiation Registration");
		SelectRadioBtnAndCheckbox(driver, eSignAtModAprCheckBox, "Call E-sign At: Approval Registration");
		click2(noOfAprReqForModDropdown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(ModApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("ModificationApprovalCount"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickModApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void subgroup_Registration(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(3);
			System.out.println("Generated S Value is: " + s);
		}

		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		setSubGroupval(SubGroupval = testData.get("SubGroupNameValue") + s);
		setDescriptionval(Descriptionval = testData.get("DescriptionValue") + s);
		setJobResponsibilityval(JobResponsibilityval = testData.get("JobResponsibilityValue") + s);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(initiateMenu, CommonStrings.SYS_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_SS.getCommonStrings());
		click2(subgroup, SubGroupStrings.SubGroup_Initiate_DC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AR.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		sendKeys2(subGroupNameTxt, SubGroupStrings.Subgroup_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Subgroup_SS.getSubGroupStrings());

		sendKeys2(descriptionText, SubGroupStrings.description_DC.getSubGroupStrings(), Descriptionval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.description_SS.getSubGroupStrings());

		sendKeys2(jobResponsibilityTxt, SubGroupStrings.JobRes_DC.getSubGroupStrings(), JobResponsibilityval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.JobRes_SS.getSubGroupStrings());
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_REGISTRATION_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void subgroup_Registration_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(auditTrailPageColumn1, SubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_0,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_Return(HashMap<String, String> testData) {

//		String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		scrollToViewElement(returntext);

		click2(returnRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		scrollToViewElement(returnRemarksReasonTextbox);

		TimeUtil.shortWait();
		sendKeys2(returnRemarksReasonTextbox, CommonStrings.Remarks_DC.getCommonStrings(),
				testData.get("ReturnRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Return_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return Re-iniate
	public void Subgroup_Return_Reinitiate(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(reniatesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		scrollToViewElement(auditCompareTRNApproveActionByValue);
		verifyExactCaption(auditCompareTRNApproveActionByValue, FullNameemployeeID, "Initiated By");
		verifyExactCaption(auditCompareTRNApproveActionValue, Constants.RETURN_ACTIONVAL, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareTRNApproveDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal2, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus1, Constants.FINALSTATUS_RETURNED);
		scrollToViewElement(reiniateRemarksReasonTextbox);

		TimeUtil.shortWait();

		sendKeys2(reiniateRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ReiniatedRemarks"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		String ConfirmationTextAtEsign = Constants.SUBGROUP_REINITIATE_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();

			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REINITIATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

		switchToDefaultContent(driver);
	}

	// Subgroup Return Re-iniate
	public void Subgroup_Return_Reinitiate_AuditTrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_0,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_Approval_AuditTrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		scrollToViewElement(auditCompareTRNRemarksVal1);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		verifyExactCaption(remarksandreasonsapprove, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_0,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void subgroup_Modification(HashMap<String, String> testData) {

		// String SubGroupval="query";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifymenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifysubgroupmenu, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);

		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		verifyExactCaption(nodisplaydata, Constants.nodataavilable, "no data avilable");
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void subgroup_Modification_afterapproval(HashMap<String, String> testData) {

		// String SubGroupval="query";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifymenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifysubgroupmenu, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);

		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_RI_Transfer(HashMap<String, String> testData) {

		// String SubGroupval = "ATSCSubgroupBMO%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, ritransfermenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(ritransfermenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(ritransfersubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

		click2(transwerAddItems, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());

		sendKeys2(searchby, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				testData.get("RITransferName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		click2(arButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(addButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();
		scrollToViewElement(returnRemarksReasonTextbox);
		sendKeys2(returnRemarksReasonTextbox, CommonStrings.Remarks_DC.getCommonStrings(),
				testData.get("RITransferRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_RI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.RITRANSFER_SUBGROUP_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Reiniate_RI_AuditTrails(HashMap<String, String> testData) {

		// String SubGroupval = "ATSCSubgroupBEQ";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(reiniatetasktransfer, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REINITIATION_TASKTRANSFER,
				"0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(ritransferremarks, Constants.RITRANSFER_ACTIONVAL, "Remarks and Reasons");
		validateMultipleDateFormats(ritransferinitiateddate);

		switchToDefaultContent(driver);
	}

	// Subgroup Return Re-iniate
	public void Subgroup_RITranfer_Reiniate(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(reniatesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		scrollToViewElement(auditCompareTRNAintActionValue);
		verifyExactCaption(auditCompareTRNAintActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNintActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNintDateTimeValue);
		verifyExactCaption(auditCompareintRemarksVal2, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareTRNApproveActionValue, Constants.RETURN_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNApproveActionByValue, FullNameemployeeID, "Initiated By");
		scrollToViewElement(auditCompareTRNApproveDateTimeValue);

		validateMultipleDateFormats(auditCompareTRNApproveDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal2, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(reiniateRemarksReasonTextbox);

		TimeUtil.shortWait();

		sendKeys2(reiniateRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ReiniatedRemarks"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_REINITIATE_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REINITIATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_RI_Transfer_Firstinitiator(HashMap<String, String> testData) {

//			String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, ritransfermenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(reinitiateMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(reniatesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());

		verifyExactCaption(nodisplaydata, Constants.nodataavilable, "no data avilable");
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Approval
	public void Subgroup_ritransfer_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		scrollToViewElement(submitBtn);

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, FullNameemployeeID, "Initiated By");
		verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");
		verifyExactCaption(remarksandreasons, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(ritransferusername, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");
		verifyExactCaption(ritransferremarks, Constants.REMARKSANDREASONS, "Remarks and Reasons");
		validateMultipleDateFormats(ritransferinitiateddate);
		verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();

	}

	// Subgroup Approval
	public void Subgroup_Reinitiate_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		scrollToViewElement(auditCompareTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		scrollToViewElement(approvetext);
		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		scrollToViewElement(submitBtn);

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_Reinitiate_Approval_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		scrollToViewElement(auditCompareApproveTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		verifyExactCaption(remarksandreasonsapprove, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Approval
	public void Subgroup_RITransfer_Reinitiate_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		scrollToViewElement(auditCompareTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareReinitateTRNActionByValue, ReinitFullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_RITransfer_Reinitiate_Approval_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(reiniatetasktransferregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditSubgroupName, SubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, Descriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, JobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionByValue, ReinitFullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		scrollToViewElement(auditCompareApproveTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		verifyExactCaption(remarksandreasonsapprove, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_Drop(HashMap<String, String> testData) {

//		String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainTRNActionByValue);
		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		highLightElement(driver, approveLabelText, "Group Approval", test);
		click2(dropRadioBtn, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());
		sendKeys2(groupApproveRemarks, GroupStrings.Approve_Group_Remarks_DC.getGroupStrings(),
				testData.get("DropRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), GroupStrings.Approve_Group_Remarks_SS.getGroupStrings());

		scrollToViewElement(submit);
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				GroupStrings.Submit_Group_Approval_AC.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_AR.getGroupStrings(),
				GroupStrings.Submit_Group_Approval_SS.getGroupStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				String ConfirmationTextEsign = Constants.DROP_ACTION_VALUE;
				verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICATION_DROP_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Drop_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");

		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		scrollToViewElement(auditMainDropTRNActionValue);
		verifyExactCaption(auditMainDropTRNActionValue, Constants.DROP_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainDropTRNActionByValue);
		verifyExactCaption(auditMainDropTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainDropTRNDateTimeValue);
		scrollToViewElement(auditMainDropTRNRemarksVal1);
		verifyExactCaption(auditMainDropTRNRemarksVal1, Constants.DROP_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		scrollToViewElement(auditMainTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_DROPPED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_StatusChange_Inactive(HashMap<String, String> testData) {

//			String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(statuschange, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(statuschangesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();
		click2(statuschangeinactve, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		switchToDefaultContent(driver);

	}

//----------------------------------------------------------------------------------------------------------------------------------------
	// subgroup modification regitsration

	public void subgroup_Modification_afterreg(HashMap<String, String> testData) {
		String s = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(3);
			System.out.println("Generated S Value is: " + s);
		}

		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();

		// String SubGroupval = "ATSCSubgroupPGD";
		setModSubGroupval(ModSubGroupval = testData.get("SubGroupNameValue") + s);
		setModDescriptionval(ModDescriptionval = testData.get("DescriptionValue") + s);
		setModJobResponsibilityval(ModJobResponsibilityval = testData.get("JobResponsibilityValue") + s);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modifymenu, CommonStrings.SYS_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.SYS_InitiateMenu_SS.getCommonStrings());
		click2(modifysubgroupmenu, SubGroupStrings.SubGroup_Initiate_DC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AC.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_AR.getSubGroupStrings(),
				SubGroupStrings.SubGroup_Initiate_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				SubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();

		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		clearField(subGroupNameTxt);
		sendKeys2(subGroupNameTxt, SubGroupStrings.Subgroup_DC.getSubGroupStrings(), ModSubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Subgroup_SS.getSubGroupStrings());
		// descriptionText.clear();
		clearField(descriptionText);
		sendKeys2(descriptionText, SubGroupStrings.description_DC.getSubGroupStrings(), ModDescriptionval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.description_SS.getSubGroupStrings());
		// jobResponsibilityTxt.clear();
		sendKeys2(jobResponsibilityTxt, SubGroupStrings.JobRes_DC.getSubGroupStrings(), ModJobResponsibilityval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.JobRes_SS.getSubGroupStrings());

		sendKeys2(reiniateRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ModifyRemarks"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupwithEsign_AR.getSubGroupStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());

		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_MODIFICATION_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();

		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

//modification audit trails
	public void subgroup_Modification_afterreg_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());

		sendKeys2(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),

				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MODIFICATION, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
//
//	}

	}

	// Subgroup Approval
	public void Subgroup_Modification_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		verifyExactCaption(auditTrailPageColumn1, ModSubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_Modification_Approval_AuditTrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		verifyExactCaption(auditTrailPageColumn1, ModSubGroupval, "Group Name");
		verifyUniqueCode(auditTrailPageColumn2, "Unique Code");
		verifyExactCaption(auditTrailPageColumn3, initiatorFLName, "Initiated By");
		getExactDate(auditTrailPageColumn4);
		String revisionNO = Constants.REVISION_NO_AS_1;
		verifyExactCaption(auditTrailPageColumn5, revisionNO, "Revision No");
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		scrollToViewElement(auditMainTRNRemarksVal1);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainApproveTRNDateTimeValue);
		verifyExactCaption(modMainremarksandreasonsapprove, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_Modification_Return(HashMap<String, String> testData) {

//			String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);

		scrollToViewElement(returntext);

		click2(returnRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		scrollToViewElement(returnRemarksReasonTextbox);

		TimeUtil.shortWait();
		sendKeys2(returnRemarksReasonTextbox, CommonStrings.Remarks_DC.getCommonStrings(),
				testData.get("ReturnRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				String ConfirmationTextEsign = Constants.RETURN_ACTION_VALUE;
				verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.RETURN_MODIFICATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Modification_Return_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");
		scrollToViewElement(auditMainTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

//-------------------------------------------------------------------------------------------------------------	

	// Subgroup Approval
	public void Subgroup_Modification_Reinitiate_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		scrollToViewElement(auditMainTRNDateTimeValue);
		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
		verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		scrollToViewElement(approvetext);
		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());
		scrollToViewElement(submitBtn);

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_Modification_Reinitiate_Approval_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");
		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
		verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyExactCaption(auditMainApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		scrollToViewElement(auditMainApproveTRNDateTimeValue);
		validateMultipleDateFormats(auditMainApproveTRNDateTimeValue);
		verifyExactCaption(modMainremarksandreasonsapprove, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		scrollToViewElement(auditMainTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

//	--------------------------------------------------------------------------------------------------

	// Subgroup Return Re-iniate
	public void Subgroup_Modification_Return_Reinitiate(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManagerMenu, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(reinitiateMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(reniatesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());

		// switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		scrollToViewElement(auditMainTRNApproveActionByValue);
		verifyExactCaption(auditMainTRNApproveActionByValue, FullNameemployeeID, "Initiated By");
		verifyExactCaption(auditMainTRNApproveActionValue, Constants.RETURN_ACTIONVAL, "Remarks and Reasons");
		validateMultipleDateFormats(auditMainTRNApproveDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal2, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
		scrollToViewElement(reiniateRemarksReasonTextbox);

		TimeUtil.shortWait();

		sendKeys2(reiniateRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ReiniatedRemarks"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		String ConfirmationTextAtEsign = Constants.SUBGROUP_MODIFICATION_REINITIATE_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

		try {

			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();

			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICAION_REINITIATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

		switchToDefaultContent(driver);
	}

	// Subgroup Return Re-iniate
	public void Subgroup_Modification_Return_Reinitiate_AuditTrails(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MOD, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		scrollToViewElement(auditMainTRNDateTimeValue);
		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");
		scrollToViewElement(auditMainReturnTRNDateTimeValue);
		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
		verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");
		scrollToViewElement(auditMainTRNApprovalReqVal);
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		scrollToViewElement(auditMainTRNFinalStatus);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);
		switchToDefaultContent(driver);
	}

	// Subgroup Return
	public void Subgroup_Modification_RI_Transfer(HashMap<String, String> testData) {

		// String SubGroupval = "ATSCSubgroupBMO%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, ritransfermenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(ritransfermenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(ritransfersubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		subgroupNameLike.clear();
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditmodriSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodriUniqueCode, "Unique Code");
		verifyExactCaption(auditmodriDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodriJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainriTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainriTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainriTRNDateTimeValue);
		verifyExactCaption(auditMainriTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainritransTRNApprovalReqVal,
				auditMainritransTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMaintransTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

		click2(transwerAddItems, GroupStrings.GroupApproval_DC.getGroupStrings(),
				GroupStrings.GroupApproval_AC.getGroupStrings(), GroupStrings.GroupApproval_AR.getGroupStrings(),
				GroupStrings.GroupApproval_SS.getGroupStrings());

		sendKeys2(searchby, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				testData.get("RITransferName"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		click2(arButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		click2(addButton, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();
		scrollToViewElement(returnRemarksReasonTextbox);
		sendKeys2(returnRemarksReasonTextbox, CommonStrings.Remarks_DC.getCommonStrings(),
				testData.get("RITransferRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants.SUBGROUP_MOD_RI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.RITRANSFER_SUBGROUP_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Modification_RITranfer_Reiniate(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(reinitiateMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(reniatesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		scrollToViewElement(auditMainTRNActionValue);
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");
		scrollToViewElement(auditMainReturnTRNDateTimeValue);

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal2, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(reiniateRemarksReasonTextbox);

		TimeUtil.shortWait();

		sendKeys2(reiniateRemarksReasonTextbox, CommonStrings.ENTER_REMARKS_REASONS_DC.getCommonStrings(),
				testData.get("ReiniatedRemarks"), CommonStrings.ENTER_REMARKS_REASONS_AC.getCommonStrings(),
				CommonStrings.ENTER_REMARKS_REASONS_AR.getCommonStrings(), CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		TimeUtil.shortWait();

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						testData.get("RITransferPassword"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();
				TimeUtil.shortWait();
				String ConfirmationTextAtEsign = Constants._MOD_Reinitiaion_RI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);

		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICAION_REINITIATION_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		switchToDefaultContent(driver);

	}

	public void Subgroup_Modification_Reiniate_RI_AuditTrails(HashMap<String, String> testData) {

		// String SubGroupval = "ATSCSubgroupBEQ";
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(reiniatemodtasktransfer, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNotransValueCompareTRN, Constants.MOD_REVISION_NUM_0REINITIATION_TASKTRANSFER,
				"0 - Registration");
		verifyExactCaption(auditSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditUniqueCode, "Unique Code");
		verifyExactCaption(auditDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditJobResponsibility, ModJobResponsibilityval, "Job Responsibility");

		verifyExactCaption(ritransferusernamestatus, Constants.FINALSTATUS_RI_TRANSFRRED, "Remarks and Reasons");
		verifyExactCaption(ritransferremarks, Constants.RITRANSFER_ACTIONVAL, "Remarks and Reasons");
		validateMultipleDateFormats(ritransferinitiateddate);

		switchToDefaultContent(driver);
	}

	// Subgroup Approval
	public void Subgroup_Modification_RITransfer_Reinitiate_Approval(HashMap<String, String> testData) {

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approveMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(approveMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(approveSubgroupMenu, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(modificationtab, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_ApprovalAuditTrails_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");
		scrollToViewElement(auditMainTRNDateTimeValue);
		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");

		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainReinitateTRNActionByValue, ReinitFullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
		verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		scrollToViewElement(approvetext);

		click2(approveRadioBtn, CourseSessionStrings.CourseSessionApproval_DC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AC.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_AR.getCourseSessionStrings(),
				CourseSessionStrings.CourseSessionApproval_SS.getCourseSessionStrings());

		scrollToViewElement(submitBtn);
		click2(submitBtn, CommonStrings.Submit_Button_DC.getCommonStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AC.getSubGroupStrings(),
				SubGroupStrings.SubmitSubgroupapproval_AR.getSubGroupStrings(),
				CourseSessionStrings.SubmitCourseSessionapp_SS.getCourseSessionStrings());
		String ConfirmationTextAtEsign = Constants.SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
		String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
		verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

		try {
			if (esignPWDTextBox.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignPWDTextBox, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

				TimeUtil.shortWait();

				click2(proceedBtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AC.getTrainingScheduleStrings(),
						TrainingScheduleStrings.ESIGN_MOD_TRAINING_SCHEDULE_AR.getTrainingScheduleStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		saveUniqueCode(driver, confirmationText);
		getCurrentDate();
		String MenuName = Constants.MENUNAME_AS_SUBGROUP;
		String ConfirmationText = Constants.MODIFICATION_APPROVE_CONFIRMATION_TEXT;
		verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void Subgroup_Modification_RITransfer_Reinitiate_Approval_AuditTrails(HashMap<String, String> testData) {
		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, subgroupAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(subgroupAuditTrails, SubGroupStrings.SubGroupMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu1_AR.getSubGroupStrings(),
				SubGroupStrings.SubgroupAudittrails_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_SubgrpAudit_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(),
				ModSubGroupval + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(displayedRecord, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();

		click2(regmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(modmod, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

		TimeUtil.shortWait();

		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);
		verifyExactCaption(revisionNoTitleMainTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueMainTRN, Constants.REVISION_NUM_1MODIFICATION, "0 - Registration");
		verifyExactCaption(auditmodSubgroupName, ModSubGroupval, "Subgroup");
		verifyUniqueCode(auditmodUniqueCode, "Unique Code");
		verifyExactCaption(auditmodDescription, ModDescriptionval, "Subgroup Description");
		verifyExactCaption(auditmodJobResponsibility, ModJobResponsibilityval, "Job Responsibility");
		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, initiatorFLNameEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.ModifyRemarks, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReturnTRNActionByValue);
		verifyExactCaption(auditMainReturnTRNActionByValue, FullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Remarks");
		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Initiated");
		scrollToViewElement(auditMainReinitateTRNActionValue);
		verifyExactCaption(auditMainReinitateTRNActionByValue, ReinitFullNameemployeeID, "Initiated By");

		validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
		verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.REINITIATE_ACTIONVAL, "Remarks");

		verifyExactCaption(auditMainApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainApproveTRNActionByValue, FullNameemployeeID, "Initiated By");

		scrollToViewElement(auditMainApproveTRNDateTimeValue);
		validateMultipleDateFormats(auditMainApproveTRNDateTimeValue);
		verifyExactCaption(remarksandreasonsapprovemain, Constants.CATEGORYTAG_NO, "Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
				Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_1);
		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AC.getSubgroupAssignmentStrings(),
				SubgroupAssignmentStrings.Close_AuditTrailsTopic_AR.getSubgroupAssignmentStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	// Subgroup Return
	public void Subgroup_Modification_StatusChange_active(HashMap<String, String> testData) {

//				String SubGroupval = "ATSCSubgroupAMV%";

		clickAndWaitforNextElement(menu, systemManagerMenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManagerMenu, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(statuschange, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(statuschangesubgroup, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Subgroup Registration Intiation", test);

		click2(statuschangeactve, SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_DC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AC.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_AR.getSubGroupStrings(),
				SubGroupStrings.SYS_UserGroupsSubgroupApproveMenu_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				SubGroupStrings.SearchBy_Subgroup_AC.getSubGroupStrings(),
				SubGroupStrings.SearchBy_Subgroup_AR.getSubGroupStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchBySubgroupNameDropdown, SubGroupStrings.Select_SubgroupName_DC.getSubGroupStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubGroupStrings.SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		subgroupNameLike.clear();
		sendKeys2WithOutClear(subgroupNameLike, SubGroupStrings.Like_SubgroupName_DC.getSubGroupStrings(), SubGroupval,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubGroupStrings.Like_SubgroupName_SS.getSubGroupStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		highLightElement(driver, statusactiveRecord, "Subgroup Registration Intiation", test);

		switchToDefaultContent(driver);

	}
	
	
	
	
	
	
	
	
	
	
	
	
	

}
