package com.Automation.learniqCRITICALScenarios.SelfStudyFlows.OneTimeCourses;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_SelfStudyCourse;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Self_Study_Open_For_All_Yes_MandatorySGP_Yes_Asessment_Yes extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Self_Study_Flows/Self_Study_Open_For_All_Yes_mandatorySGP_Yes_Asessment_Yes.xlsx";
	
	public Self_Study_Open_For_All_Yes_MandatorySGP_Yes_Asessment_Yes() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	
	
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}
	
	
	
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		selfStudyCourse.Self_Study_Course_Registration_OpenForAll_MandatorySGP_yes_Assessment_Yes(testData);
	}
	
	
	ExcelUtilUpdated SSQBData = new ExcelUtilUpdated(ExcelPath, "SSQB");

	@DataProvider(name = "SSQBData")
	public Object[][] getSSQBData() throws Exception {
		Object[][] obj = new Object[SSQBData.getRowCount()][1];
		for (int i = 1; i <= SSQBData.getRowCount(); i++) {
			HashMap<String, String> testData = SSQBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}
	

	@Test(priority = 3, dataProvider = "SSQBData", enabled = true)
	public void SSQB(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		SSQB.selfStudyQuestioBank(testData);
		Logout.signOutPage();
	}
	
	
	@Test(priority = 4,enabled = true)
	public void Initiate_Self_Study_Course() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.QualifieduserID)

					.assignCategory("Initiate Self Study Course");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.QualifieduserID,
				CM_SelfStudyCourse.QualifiedPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}
	@Test(priority = 5,enabled = true)
	public void respondDocumentReading_Completed() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading and complete")
					.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
					.assignCategory("Respond DocumentReading and complete");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed",CM_SelfStudyCourse.QualifiedPSW );
	}
	
	
	@Test(priority = 6, enabled = true)
	public void respond_QP_Qualified() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond QP and Qualified");
		}

		RespondQP.respondSelfStudy_QP("Qualified", CM_SelfStudyCourse.QualifiedPSW);
		Logout.signOutPage();
	}
	
	@Test(priority = 7, enabled = true)
	public void Initiate_Self_Study_Course_By_InProgressUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.RQPPendingEmpID)

					.assignCategory("Initiate Self Study Course");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.RQPPendingUserID,
				CM_SelfStudyCourse.RQPPendingPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}
	@Test(priority = 8,enabled = true)
	public void respondDocumentReading_InProgress() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading and mark as In- Progress")
					.assignAuthor(CM_SelfStudyCourse.RQPPendingEmpID)
					.assignCategory("Respond DocumentReading and mark as In- Progress");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed",CM_SelfStudyCourse.RQPPendingPSW);

	}		
		
	@Test(priority = 9, enabled = true)
	public void respond_QP_ToBeRetrained() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and To Be Retrained")
					.assignAuthor(CM_SelfStudyCourse.RQPPendingEmpID)
					.assignCategory("Respond QP and To Be Retrained");
		}

		RespondQP.respondSelfStudy_QP("To Be Retrained", CM_SelfStudyCourse.RQPPendingPSW);
		Logout.signOutPage();
	}
	
	
	@Test(priority = 10, enabled = true)
	public void respondDRby_SGPQualifiedUer() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DR and Complete by Qualified User")

					.assignAuthor(CM_SelfStudyCourse.SGP_QualifiedEmpID)

					.assignCategory("Respond DR and Complete by Qualified User");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.SGP_QualifieduserID,
				CM_SelfStudyCourse.SGP_QualifiedPSW);
		epiclogin.plant1();
		RespondDR.respondDocReading_Mandatory_Self_Study_Course("Completed",CM_SelfStudyCourse.SGP_QualifiedPSW );
	}
	
	@Test(priority = 11, enabled = true)
	public void respond_QP_SGPQualifiedUer() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and Qualify")
					.assignAuthor(CM_SelfStudyCourse.SGP_QualifiedEmpID)
					.assignCategory("Respond QP and Qualify");
		}

		RespondQP.respondSelfStudy_QP("Qualified", CM_SelfStudyCourse.SGP_QualifiedPSW);
		Logout.signOutPage();
	}
	
	@Test(priority = 12, enabled = true)
	public void respondDRby_SGPToBERetrainedUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DR and Complete by To Be Retrained User")

					.assignAuthor(CM_SelfStudyCourse.SGP_ToBeRetrainedEmpID)

					.assignCategory("Respond DR and Complete by To Be Retrained User");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.SGP_ToBeRetrainedUserID,
				CM_SelfStudyCourse.SGP_ToBeRetrainedPSW);
		epiclogin.plant1();
		RespondDR.respondDocReading_Mandatory_Self_Study_Course("Completed",CM_SelfStudyCourse.SGP_ToBeRetrainedPSW );
	}
	
	@Test(priority = 13, enabled = true)
	public void respondQP_by_SGPToBERetrainedUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and To Be Retrained")
					.assignAuthor(CM_SelfStudyCourse.SGP_ToBeRetrainedEmpID)
					.assignCategory("Respond QP and To Be Retrained");
		}

		RespondQP.respondSelfStudy_QP("To Be Retrained", CM_SelfStudyCourse.SGP_ToBeRetrainedPSW);
		Logout.signOutPage();
	}
	
	@Test(priority = 14, enabled = true)
	public void respondDRby_SGPQPPendongUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DR but pending tp ")

					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)

					.assignCategory("Respond DR and Complete by To Be Retrained User");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.SGP_RQPPendingUserID,
				CM_SelfStudyCourse.SGP_RQPPendingPSW);
		epiclogin.plant1();
		RespondDR.respondDocReading_Mandatory_Self_Study_Course("Completed",CM_SelfStudyCourse.SGP_RQPPendingPSW );
	}
	
	
	@Test(priority = 15, enabled = true)
	public void IERReportForResponDRPendinguser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond DR Employee")
					.assignAuthor(CM_SelfStudyCourse.DRInProgressEmpID)
					.assignCategory("Individual Employee Report for Respond DR Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.DRInProgressEmpID,
					Constants.Session_PROPOSED_FOR_RE, Constants.REType);
	}
	
	@Test(priority = 16, enabled = true)
	public void IERReportForQualifieduser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Qualified Employee")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for Qualified Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID,
					Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);
	}
	@Test(priority = 17, enabled = true)
	public void IERReportForTBRuser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for To BE Retrained Employee")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for To BE Retrained Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.ToBeRetrainedEmpID,
					Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType);
	}
		
	@Test(priority = 18, enabled = true)
	public void IERReportForRespondQPPendinguser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond QP Pending Employee")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for Respond QP Pending Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.RQPPendingEmpID,
					Constants.DR_COMPLETED, Constants.REType);
	}
	
	@Test(priority = 19, enabled = true)
	public void IERReportForRespond_DRpending__mandatory_user() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond DR Employee from mandatory subgroup")
					.assignAuthor(CM_SelfStudyCourse.SGP_DRInProgressEmpID)
					.assignCategory("Individual Employee Report for Respond DR Employee from mandatory subgroup");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.SGP_DRInProgressEmpID,
					Constants.Session_PROPOSED_FOR_RE, Constants.REType);
	}
	@Test(priority = 20, enabled = true)
	public void IERReportForQualified_mandatory_user() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Qualified Employee from mandatory subgroup")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for Qualified Employee from mandatory subgroup");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.SGP_QualifiedEmpID,
					Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);
	}
	

	@Test(priority = 21, enabled = true)
	public void IERReportForToBeRetrained_mandatory_user() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for To Be Retrained Employee from mandatory subgroup")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for To Be Retrained Employee from mandatory subgroup");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.SGP_ToBeRetrainedEmpID,
					Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType);
	}
	
	
	@Test(priority = 22, enabled = true)
	public void IERReportForRespondQPPendingMandaotryuser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond QP Pending Employee")
					.assignAuthor(CM_SelfStudyCourse.SGP_RQPPendingEmpID)
					.assignCategory("Individual Employee Report for Respond QP Pending Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.SGP_RQPPendingEmpID,
					Constants.DR_COMPLETED, Constants.REType);
	}
}
