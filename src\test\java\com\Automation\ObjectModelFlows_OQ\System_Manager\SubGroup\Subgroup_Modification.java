package com.Automation.ObjectModelFlows_OQ.System_Manager.SubGroup;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Subgroup_Modification extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Object_Model_Flows/System_Manager/SubGroup/SubgroupModification.xlsx";

	public Subgroup_Modification() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	// Test Method for subgroup Modification Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subgroupconfigData = new ExcelUtilUpdated(ExcelPath, "SubgroupRegandApproval");

	@DataProvider(name = "subgroupConReg")
	public Object[][] getsubgroupconfigData() throws Exception {
		Object[][] obj = new Object[subgroupconfigData.getRowCount()][1];
		for (int i = 1; i <= subgroupconfigData.getRowCount(); i++) {
			HashMap<String, String> testData = subgroupconfigData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 0, dataProvider = "subgroupConReg", enabled = true)
	public void subGroupConfigReg1(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration Configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup Registration Configuration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Modify_SubGroup.SubgroupRegistrationApproval_Configuration(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Configuration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Configuration");
		}
		Modify_SubGroup.SubgroupModificationApproval_Configuration(testData);

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 1, dataProvider = "subgroupConReg", enabled = true)
	public void subGroupReg111(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}

		Modify_SubGroup.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Registration_AuditTrails(testData);

	}

//Test Method for subgroup modification Registration 
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 2, dataProvider = "subgroupConReg", enabled = true)
	public void modificationreg1(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration");
		}
		Modify_SubGroup.subgroup_Modification_afterreg(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Modification_afterreg_AuditTrails(testdata);

		Logout.signOutPage();
	}

//Test Method for subgroup modification Approve
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 3, dataProvider = "subgroupConReg", enabled = true)
	public void modifiacationapproval(HashMap<String, String> testdata) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Approve")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Approve");
		}
		Modify_SubGroup.Subgroup_Modification_Approval(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Approve AudiTrails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Approve AudiTrails");
		}
		Modify_SubGroup.Subgroup_Modification_Approval_AuditTrails(testdata);

		Logout.signOutPage();
	}

//	subgroup modification return reiniate approve audit trails
//-------------------------------------------------------------------------------------------------//

	// Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subgroupReturnData = new ExcelUtilUpdated(ExcelPath, "SubRegReturnReniatApp");

	@DataProvider(name = "subReturn")
	public Object[][] getsubgroupRegData1() throws Exception {
		Object[][] obj = new Object[subgroupReturnData.getRowCount()][1];
		for (int i = 1; i <= subgroupReturnData.getRowCount(); i++) {
			HashMap<String, String> testData = subgroupReturnData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 4, dataProvider = "subReturn", enabled = true)
	public void subGroupReg1(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();
		Modify_SubGroup.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Registration_AuditTrails(testData);

	}

	// Test Method for subgroup modification Registration Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 5, dataProvider = "subReturn", enabled = true)
	public void modificationreg11(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration");
		}
		Modify_SubGroup.subgroup_Modification_afterreg(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Modification_afterreg_AuditTrails(testdata);

		Logout.signOutPage();
	}

	// Test Method for Subgroup Modification Return and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 6, dataProvider = "subReturn", enabled = true)
	public void subGroupReturn1(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		Modify_SubGroup.getFirstNameLastNameFromProfileIcon();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Return")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Modification Return");
		}
		Modify_SubGroup.Subgroup_Modification_Return(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Return Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Modification Return Audit Trails");
		}
		Modify_SubGroup.Subgroup_Modification_Return_AuditTrails(testData);

		Logout.signOutPage();

	}
	// Test Method for Subgroup Modification Reinitiate and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 7, dataProvider = "subReturn", enabled = true)
	public void subGroupReiniate(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Re-Initiate")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID320"))
					.assignCategory("Subgroup  Modification Re-Initiate");
		}
		Modify_SubGroup.Subgroup_Modification_Return_Reinitiate(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Re-Initiate Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID320"))
					.assignCategory("Subgroup  Modification Re-Initiate Audit Trails");
		}
		Modify_SubGroup.Subgroup_Modification_Return_Reinitiate_AuditTrails(testData);
		Logout.signOutPage();

	}

	// Test Method for Subgroup Modification Approval and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 8, dataProvider = "subReturn", enabled = true)
	public void subapproval1(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Approval")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Modification Approval");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Modify_SubGroup.Subgroup_Modification_Reinitiate_Approval(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Approval Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Modification Approval Audit Trails");
		}

		Modify_SubGroup.Subgroup_Modification_Reinitiate_Approval_AuditTrails(testdata);
		Logout.signOutPage();
	}

//subgroup modification return ri transfer approve audit trails
//-----------------------------------------------------------------------------------------------------//

//Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subRITransferData = new ExcelUtilUpdated(ExcelPath, "SubRegRetRITransferReniatApp");

	@DataProvider(name = "subRITransfer")
	public Object[][] getsubgroupRegData11() throws Exception {
		Object[][] obj = new Object[subRITransferData.getRowCount()][1];
		for (int i = 1; i <= subRITransferData.getRowCount(); i++) {
			HashMap<String, String> testData = subRITransferData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 9, dataProvider = "subRITransfer", enabled = true)
	public void subGroupReg11(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();
		Modify_SubGroup.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Registration_AuditTrails(testData);

	}

	// Test Method for subgroup modification Registration Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 10, dataProvider = "subRITransfer", enabled = true)
	public void modificationreg111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration");
		}
		Modify_SubGroup.subgroup_Modification_afterreg(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Modification_afterreg_AuditTrails(testdata);

		Logout.signOutPage();
	}

	// Test Method for Subgroup Modification Return and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 11, dataProvider = "subRITransfer", enabled = true)
	public void subGroupReturn(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Return")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Modification Return");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Modify_SubGroup.getFirstNameLastNameFromProfileIcon();
		Modify_SubGroup.Subgroup_Modification_Return(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Return Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Modification Return Audit Trails");
		}
		Modify_SubGroup.Subgroup_Modification_Return_AuditTrails(testData);

		Logout.signOutPage();

	}

	// Test Method for Subgroup RI Transfer and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 12, dataProvider = "subRITransfer", enabled = true)
	public void subGroupRITransfer(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification RI Transfer")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup Modification RI Transfer");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Modify_SubGroup.Subgroup_Modification_RI_Transfer(testData);

		Logout.signOutPage();

	}

	// Login with RI TransferUser and Reiniate Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 13, dataProvider = "subRITransfer", enabled = true)
	public void subGroupRITransferUser(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("RITransferUser"),
				testData.get("RITransferPassword"));

		epiclogin.plant1();
		Modify_SubGroup.getFirstNameLastNameFromProfileIconReinitiateuser();

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Re-Initiate")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup Modification Re-Initiate");
		}

		Modify_SubGroup.Subgroup_Modification_RITranfer_Reiniate(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Re-Initiate Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup Modification Re-Initiate Audit Trails");
		}

		Modify_SubGroup.Subgroup_Modification_Reiniate_RI_AuditTrails(testData);

		Logout.signOutPage();

	}

	// Login with RI TransferUser and Approve Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 14, dataProvider = "subRITransfer", enabled = true)
	public void subapproval11(HashMap<String, String> testdata) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Approve")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup Modification Approve");
		}
		Modify_SubGroup.Subgroup_Modification_RITransfer_Reinitiate_Approval(testdata);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Approve  Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup Modification Approve  Audit Trails");
		}

		Modify_SubGroup.Subgroup_Modification_RITransfer_Reinitiate_Approval_AuditTrails(testdata);
		Logout.signOutPage();
	}
//subgroup modification drop
//----------------------------------------------------------------------------------------------------

// Test Method for Subgroup Configuration
// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subRegDropData = new ExcelUtilUpdated(ExcelPath, "SubRegistrationDrop");

	@DataProvider(name = "subRegDrop")
	public Object[][] getsubgroupRegData() throws Exception {
		Object[][] obj = new Object[subRegDropData.getRowCount()][1];
		for (int i = 1; i <= subRegDropData.getRowCount(); i++) {
			HashMap<String, String> testData = subRegDropData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

// Test Method for subgroup Registration ad Audit Trails
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 15, dataProvider = "subRegDrop", enabled = true)
	public void subGroupReg(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Modify_SubGroup.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Registration_AuditTrails(testData);

	}

// Test Method for subgroup modification Registration Audit Trails
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 16, dataProvider = "subRegDrop", enabled = true)
	public void modificationreg(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration");
		}
		Modify_SubGroup.subgroup_Modification_afterreg(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification Registration Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification Registration Audit Trails");
		}
		Modify_SubGroup.subgroup_Modification_afterreg_AuditTrails(testdata);

		Logout.signOutPage();
	}

// Test Method for Subgroup Approval and AuditTrails
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 17, dataProvider = "subRegDrop", enabled = true)
	public void subapproval(HashMap<String, String> testdata) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Modify_SubGroup.getFirstNameLastNameFromProfileIcon();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification  Drop")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification  Drop");
		}
		Modify_SubGroup.Subgroup_Drop(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Modification  Drop Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Modification  Drop Audit Trails");
		}

		Modify_SubGroup.Subgroup_Drop_AuditTrails(testdata);

	}

//Test Method for check status Change inactive
//----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 18, dataProvider = "subRegDrop", enabled = true)
	public void subgroupstatuschange(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verification  of  Modification Subgroup in Status Change Active Tab Tab")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Verification  of  Modification Subgroup in Status Change Active");
		}

		Modify_SubGroup.Subgroup_Modification_StatusChange_active(testdata);

		Logout.signOutPage();
	}

}
