package com.Automation.learniqObjects;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.RespondQPStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_RespondQP extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[8]//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respondMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[18]//a[contains(@class,'sub-menu')][contains(text(),'Release')]")
	WebElement releaseMenu;

	@FindBy(id = "TMS_Course Manager_Release_MEN136")
	WebElement assesmentRetake;

	@FindBy(id = "TMS_Course Manager_Respond_MEN100")
	WebElement questionPaper;

	@FindBy(id = "UniqueCode")
	WebElement courseName;

	@FindBy(id = "ReleaseRemarks")
	WebElement releaseRemarks;

	@FindBy(id = "EmployeeName")
	WebElement employeeName;

	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Name']")
	WebElement searchByCourseName;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[2]")
	WebElement searchByEmployeeName;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;

	@FindBy(id = "displayBtn")
	WebElement display;

	@FindBy(xpath = "//div[10]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]/input[2]")
	WebElement falseAns1;

	@FindBy(xpath = "//div[10]/div[2]/div[3]/div[2]/div[1]/div[1]/div[1]/div[2]/input[2]")
	WebElement falseAns2;

	@FindBy(id = "btnSubmit")
	WebElement submit;

	@FindBy(id = "EXAMINATION_respondExamDetails0_UserAnswer")
	WebElement fillInTheBlank;

	@FindBy(id = "EXAMINATION_respondExamDetails1_UserAnswer")
	WebElement fillInTheBlank2;

	@FindBy(id = "EXAMINATION_respondExamDetails1_UserAnswer")
	WebElement essay1;

	@FindBy(xpath = "//div[@class='panel-collapse online-test-options']//div[@class='col-md-1']/input[@value='2']")
	WebElement multipleChoiceQA2;

	@FindBy(xpath = "//div[2]/div[1]/div[1]/input[1]")
	WebElement multipleChoiceQ1A2;

	@FindBy(xpath = "//div[3]/div[1]/div[1]/input[1]")
	WebElement multipleChoiceQA3;

	@FindBy(xpath = "//div//div[3]//div[2][@class='col-lg-12']")
	WebElement multipleChoiceQ2A2;

	@FindBy(xpath = "//div//div[3]//div[3][@class='col-lg-12']")
	WebElement multipleChoiceQ2A3;

	@FindBy(xpath = "//label[text()='TRUE']//preceding-sibling::input[1]")
	WebElement trueORFalseAnsAsTrue;

	@FindBy(xpath = "//label[text()='FALSE']//preceding-sibling::input[1]")
	WebElement trueORFalseAnsAsFalse;

	@FindBy(id = "txtESignPassword")
	WebElement eSign1;

	@FindBy(id = "Submit_Esign")
	WebElement eSignProceed;

	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;

	@FindBy(xpath = "//button[@class='caliber-button-primary DoneList']")
	WebElement doneButton;

	@FindBy(id = "cfnMsg_Next")
	WebElement done;

	@FindBy(xpath = "//button[@class='caliber-button-primary DoneList']")
	WebElement done1;

	@FindBy(xpath = "//button[@class='caliber-button-primary click-row RetakeNow h-50']")
	WebElement retakeNow;

	@FindBy(xpath = "//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respond;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Respond']/li[2]//a")
	WebElement respondQP;

	@FindBy(id = "UniqueCode")
	WebElement courseName1;

	@FindBy(xpath = "//input[@class='caliber-labeled-option_0'][@value='2']")
	WebElement selectAns1;

	@FindBy(xpath = "//input[@class='caliber-labeled-option_1'][@value='2']")
	WebElement selectAns2;

	@FindBy(xpath = "//label[text()='Result']//following-sibling::span")
	WebElement retakeTraining;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//*[@class='QualfySts']")
	WebElement result;

	@FindBy(xpath = "//*[@class='DisqualfySts']")
	WebElement disQualifyResult;

	@FindBy(xpath = "//label[text()='Course Name']//following::input[1]")
	WebElement CourseName;

	@FindBy(xpath = "//label[text()='Training Type']//following::input[1]")
	WebElement TrainingType;

	@FindBy(xpath = "//label[text()='Course Session Name']//following::input[1]")
	WebElement courseSessionName;

	@FindBy(xpath = "//label[text()='Batch Name']//following::input[1]")
	WebElement batchName;

	@FindBy(xpath = "//label[text()='Course Session Date']//following::input[1]")
	WebElement coursesSesionDate;

	@FindBy(xpath = "//label[text()='Maximum Marks']//following::input[1]")
	WebElement maximumMarks;

	@FindBy(xpath = "//label[text()='Qualifying Mark']//following::input[1]")
	WebElement qualifyingMark;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement VerifyCourseName;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement VerifyTrainingType;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement VerifyBatchName;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[4]")
	WebElement InitiatedOn;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[5]")
	WebElement TargetdateforResponse;

	@FindBy(xpath = "//label[text()='Topic Name ']//following-sibling::span")
	WebElement VerifyTopicName;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;

	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;

	@FindBy(xpath = "//label[text()='Employee Name']//following-sibling::input")
	WebElement EmployeeName;

	@FindBy(xpath = "//label[text()='Course Name']//following-sibling::input")
	WebElement coursename;

	@FindBy(xpath = "//label[text()='Training Type']//following-sibling::input")
	WebElement trainingtype;

	@FindBy(xpath = "//label[text()='Course Session Name']//following-sibling::input")
	WebElement coursesessionname;

	@FindBy(xpath = "//label[text()='Batch Name']//following-sibling::input")
	WebElement batchname;

	@FindBy(xpath = "//label[text()='Course Session Date']//following-sibling::input")
	WebElement coursessiondate;

	@FindBy(xpath = "//label[text()='Maximum Marks']//following-sibling::input")
	WebElement maximummarks;

	@FindBy(xpath = "//label[text()='Qualifying Mark']//following-sibling::input")
	WebElement qualifyingmark;

	@FindBy(xpath = "//label[text()='Qualifying Percentage']//following-sibling::input")
	WebElement qualifyingpercentage;

	@FindBy(xpath = "//label[text()='Acquired Marks']//following-sibling::input")
	WebElement acciquiredmarks;

	@FindBy(xpath = "//label[text()='Acquired Percentage']//following-sibling::input")
	WebElement acciquiredpercentage;

	@FindBy(xpath = "/html/body/form/section/section/div/div/div/div/div[1]/div/div[2]/div/div[9]/div[2]/div[2]/div[2]/div/div[4]/div/div[1]/input")
	WebElement QualifyMQA4;

	@FindBy(xpath = "/html/body/form/section/section/div/div/div/div/div[1]/div/div[2]/div/div[9]/div[2]/div[2]/div[2]/div/div[1]/div/div[1]/input")
	WebElement QualifyMQA1;

	@FindBy(xpath = "/html/body/form/section/section/div/div/div/div/div[1]/div/div[2]/div/div[9]/div[2]/div[3]/div[2]/div/div/div/div[2]/input[2]")
	WebElement QualifyFalse;
	
	@FindBy(xpath = "//button[text()='Self-Study Course(s)']")
	WebElement selfStudyCourses;

	/// html/body/form/section/section/div/div/div/div/div[1]/div/div[2]/div/div[9]/div[2]/div[3]/div[2]/div/div/div/div[2]/input[1]

//	public CM_Respond_QuestionPaper() {
//	}

	/**
	 * This method is for respond Question Paper with All Correct answers System
	 * Evaluation
	 */

	public void respondQP(String status, String trainingMethod, String esignpsw) {

		String CourseNameVal = CM_Course.getCourse();
		String evaluation_Type_Value = CM_QuestionPaper.getEvaluationType();

//		String CourseNameVal = "CRSNewNFZT";
//		String evaluation_Type_Value = "System Evaluation";
		waitForElementVisibile(menu);
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(questionPaper, RespondQPStrings.Respond_QP_menu_DC.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_AC.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_AR.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_SS.getRespondQPStrings());
		switchToBodyFrame(driver);
		// highLightElement(driver, highLightScreenTitle, "Document Registration
		// screen", test);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RespondQPStrings.SearchBy_AC.getRespondQPStrings(), RespondQPStrings.SearchBy_AR.getRespondQPStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByCourseName, RespondQPStrings.SearchBy_CourseName_DC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AR.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_SS.getRespondQPStrings());
		TimeUtil.shortWait();
		if (trainingMethod.equals("Document Reading")) {
			sendKeys2(courseName, RespondQPStrings.Like_CourseNameRE_DC.getRespondQPStrings(), CourseNameVal,
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					RespondQPStrings.Like_CourseName_SS.getRespondQPStrings());
		} else {
			sendKeys2(courseName, RespondQPStrings.Like_CourseNameForQp_DC.getRespondQPStrings(),
					CourseNameVal + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					RespondQPStrings.Like_CourseName_SS.getRespondQPStrings());
		}
		TimeUtil.shortWait();
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		String targetdate = Constants.VALID_TO;
		if (trainingMethod.equals("Document Reading")) {
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			click2(displayedRecord, RespondQPStrings.Click_Course_RE_DC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AR.getRespondQPStrings(),
					RespondQPStrings.Click_Course_SS.getRespondQPStrings());
		} else {
			TimeUtil.mediumWait();
			TimeUtil.mediumWait();
			click2(displayedRecord, RespondQPStrings.Click_Course_DC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AR.getRespondQPStrings(),
					RespondQPStrings.Click_Course_SS.getRespondQPStrings());
		}
		TimeUtil.longwait();
//		String sessionDate = "From:" + Convertedtime + "To: " + Convertedtime;
		if (status.equals("Qualified")) {

			click2(multipleChoiceQ1A2, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());
			scrollToViewElement(trueORFalseAnsAsTrue);
			click2(trueORFalseAnsAsTrue, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());

		} else if (status.equals("Not100%Qualified")) {

			click2(multipleChoiceQ1A2, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());
			scrollToViewElement(trueORFalseAnsAsTrue);
			click2(trueORFalseAnsAsFalse, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());
		}

		else {

			click2(multipleChoiceQA3, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());
			click2(trueORFalseAnsAsFalse, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());

		}

		TimeUtil.shortWait();
		waitForElementVisibile(submit);
		scrollToViewElement(submit);
		click2(submit, TopicStrings.Submit_DC.getTopicStrings(), RespondQPStrings.Submit_AC.getRespondQPStrings(),
				RespondQPStrings.Submit_AR.getRespondQPStrings(), TopicStrings.Submit_SS.getTopicStrings());
		TimeUtil.shortWait();
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), esignpsw,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
//		String ConfirmationTextAtEsign = Constants.RESPONDQUESTIONPAPER_REGISTRATION_CONFIRMATION_TEXT_ESIGN;
		// verifyExactCaption(confirmationText1, ConfirmationTextAtEsign,
		// "ConfirmationTextAtEsign");
		if (evaluation_Type_Value.equals("System Evaluation") && status.equals("Qualified")) {
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondQPProceed_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondQPProceed_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
			TimeUtil.longwait();
		} else if (evaluation_Type_Value.equals("System Evaluation") && status.equals("To Be Re-trained")) {
			TimeUtil.mediumWait();
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondWrongQPProceed_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondWrongQPProceed_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
			TimeUtil.longwait();
		} else if (evaluation_Type_Value.equals("System Evaluation") && status.equals("Not100%Qualified")) {
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
		} else if (evaluation_Type_Value.equals("Manual Evaluation") && status.equals("Qualified")) {
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondQPProceed_ManualEval_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondQPProceed_ManualEval_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
		} else if (evaluation_Type_Value.equals("Manual Evaluation") && status.equals("To Be Re-trained")) {
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondWrongQPProceed_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondWrongQPProceed_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
		} else if (evaluation_Type_Value.equals("Manual Evaluation") && status.equals("Not100%Qualified")) {
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
			TimeUtil.mediumWait();
		}
		switchToDefaultContent(driver);
	}
	
	public void respondSelfStudy_QP(String status, String esignpsw) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(respond);
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(questionPaper, RespondQPStrings.Respond_QP_menu_DC.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_AC.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_AR.getRespondQPStrings(),
				RespondQPStrings.Respond_QP_menu_SS.getRespondQPStrings());
		switchToBodyFrame(driver);
		// highLightElement(driver, highLightScreenTitle, "Document Registration
		// screen", test);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(selfStudyCourses, "Click on 'Self-Study Courses'", "", "", "");

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RespondQPStrings.SearchBy_AC.getRespondQPStrings(), RespondQPStrings.SearchBy_AR.getRespondQPStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByCourseName, RespondQPStrings.SearchBy_CourseName_DC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AR.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_SS.getRespondQPStrings());
		TimeUtil.shortWait();
			sendKeys2(courseName, RespondQPStrings.Like_CourseNameForQp_DC.getRespondQPStrings(),
					CM_SelfStudyCourse.Course + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					RespondQPStrings.Like_CourseName_SS.getRespondQPStrings());
		TimeUtil.mediumWait();
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		String targetdate = Constants.VALID_TO;
			click2(displayedRecord, RespondQPStrings.Click_Course_DC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AC.getRespondQPStrings(),
					RespondQPStrings.Click_Course_AR.getRespondQPStrings(),
					RespondQPStrings.Click_Course_SS.getRespondQPStrings());
		TimeUtil.mediumWait();
//		String sessionDate = "From:" + Convertedtime + "To: " + Convertedtime;
		if (status.equals("Qualified")) {

			click2(multipleChoiceQ1A2, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());
			scrollToViewElement(trueORFalseAnsAsTrue);
			TimeUtil.mediumWait();

			click2(trueORFalseAnsAsTrue, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());

		} else if (status.equals("Not100%Qualified")) {

			click2(multipleChoiceQ1A2, RespondQPStrings.Select_ValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_ValidAns_SS.getRespondQPStrings());
			scrollToViewElement(trueORFalseAnsAsTrue);
			TimeUtil.mediumWait();

			click2(trueORFalseAnsAsFalse, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());
		}

		else {
			TimeUtil.mediumWait();
			click2(multipleChoiceQA3, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());
			TimeUtil.mediumWait();

			click2(trueORFalseAnsAsFalse, RespondQPStrings.Select_InValidAns_DC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AC.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_AR.getRespondQPStrings(),
					RespondQPStrings.Select_InValidAns_SS.getRespondQPStrings());

		}

		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, TopicStrings.Submit_DC.getTopicStrings(), RespondQPStrings.Submit_AC.getRespondQPStrings(),
				RespondQPStrings.Submit_AR.getRespondQPStrings(), TopicStrings.Submit_SS.getTopicStrings());
		TimeUtil.shortWait();
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), esignpsw,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AC.getRespondQPStrings(),
					RespondQPStrings.Esign_RespondQP_JustQualified_AR.getRespondQPStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());
//			TimeUtil.mediumWait();
//		}
		switchToDefaultContent(driver);
	}

}
