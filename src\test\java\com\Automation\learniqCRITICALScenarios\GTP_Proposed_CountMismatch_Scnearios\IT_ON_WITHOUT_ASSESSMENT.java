package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_BatchFormation;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_RecordAttendance;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online IT Session without assessment for scheduled course and make
 * atleast one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, present by viewing Individual employee
 * report at each transaction starting from course session, also add at least
 * one user and check IER report and to be retrained and view IER for those
 * employees.
 */

public class IT_ON_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/IT_ON_WITHOUT_ASSESSMENT.xlsx";

	public IT_ON_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));

	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Configuration, Modification, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Configuration, Modification, Approve with Audit Trails");
		}

		TrainingShcedule.modifyTrainingScheduled(testData);
	}

	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
		}

		trainer.trainer_Modification_AuditTrails(testData);

	}

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		// Compare Outisde and Inside To be Planned count at CS Screen
		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession,
				CM_VerifyCourseSessionScreen.beforeAnySessionInsideCount,
				"Outside To Be Planned Count at CourseSession Before Any Session",
				"Inside To Be Planned Count at CourseSession Before Any Session");

		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession,
				CM_VerifyCourseSessionScreen.totalTraineesCountBeforeSession,
				"Outside To Be Planned Count at CourseSession Before Any Session",
				"Outside Total Traines Count Before Sesion");

		compareCount(CM_VerifyCourseSessionScreen.totalTraineesCountBeforeSession,
				CM_VerifyCourseSessionScreen.totalTraineeInsideCountBeforeSession,
				"Outside Total Traines Count at CourseSession Before Any Session",
				"Inside Total Traines Count at CourseSession Before Any Session");

		CSRReport.TBPCSRReport();
		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore, CM_CSRReport.tobePlannedinsideCountBefore,
				"Outside To Be Planned Count at Course Session Report Before Any Session",
				"Inside To Be Planned Count at CourseSession Report Before Any Session");

		// Data between Course Session Screen and Course Session Report Before Session
		// for To be Planned Count

		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataBeforeSession, CM_CSRReport.combinedArrayBefore,
				"CourseSession To be Planned Count Before", "Course Session Report To be Plannd Count Before Session");

		//verifyCSScreen.courseSession_Online_WithOutExam(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		// Cheeck that Planned users are not displayed in Course Session To b Planned
		// List

		checkCommonElements(CM_VerifyCourseSessionScreen.columnDataAfterSession,
				CM_VerifyCourseSessionScreen.selectedEmployees, "Employees in To Be planned After Session",
				"Selected Employees");

		// Compare the To Be planned Count in Course Session with Previous Count minus
		// Selected Candidates

		compareCount(CM_VerifyCourseSessionScreen.beforeAnySession - CM_VerifyCourseSessionScreen.SessionProposedFor,
				CM_VerifyCourseSessionScreen.AfterAnySession, "Course session before minus Selected employees Count",
				"After Course Session To be Planned Count");

		// Compare Course Session To Be Planned Count with Inside Count After Course
		// Session
		compareCount(CM_VerifyCourseSessionScreen.AfterAnySession,
				CM_VerifyCourseSessionScreen.AfterAnySessionInsideCount,
				"Outside To Be Planned Count at CourseSession After Session Proposal",
				"Inside To Be Planned Count at CourseSession After Session Session");

		// Open Report
		CSRReport.TBPCSRReport();

		// Cheeck that Planned users are not displayed in Course Session Report To b
		// Planned List

		checkCommonElements(CM_CSRReport.combinedArrayAfter, CM_VerifyCourseSessionScreen.selectedEmployees,
				"Employees in To Be planned After Session", "Selected Employees");

		// Compare the To Be planned Count in Course Session Report with Previous Count
		// minus Selected Candidates
		compareCount(CM_CSRReport.toBePlannedOutSideCountBefore - CM_VerifyCourseSessionScreen.SessionProposedFor,
				CM_CSRReport.toBePlannedOutSideCountAfter,
				"Course session Report before minus Selected employees Count",
				"After Course Session To be Planned Count");

		// Compare Course Session Data with Course Session Report Data After Session
		toBePlannedCountData(CM_VerifyCourseSessionScreen.columnDataAfterSession, CM_CSRReport.combinedArrayAfter,
				"CourseSession To be Planned Data After Session",
				"Course Session Report To be Plannd Data After Session");

	}

	// Test Method for BatchFormation Configuration, Registration with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
		}

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_NotResponded_Skipped(testData);

	}
	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}
		RecordAttendance.recordAttendance_OnlineSession_CountMismatch(testData);
		// Logout.signOutPage();
	}

	@Test(priority = 8, dataProvider = "CourseSession", enabled = true)
	public void verifyEmployees(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("").assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("");
		}

		verifyCSScreen.VerifyCountAfterRecordAttendance(testData, "BeforeSession");

		// To be planned Count Outside and Inside.

		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				CM_VerifyCourseSessionScreen.afterRecordAttendance_Inside_ToBePlannedCount,
				"Outside To Be Planned Count at CourseSession After Record Attendance",
				"Inside To Be Planned Count at CourseSession After Record Attendance");

		// Skipped Count

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde, CM_VerifyCourseSessionScreen.SkippedInsideCount,
				"Outside Skipped at CourseSession After Record Attendance",
				"Inside Skipped at CourseSession After Record Attendance");
		// Actual Skipped Users with Skipped Data Displayed in Skipped Column
		toBePlannedCountData(CM_BatchFormation.SkippedUsers, CM_VerifyCourseSessionScreen.SkippedData,
				"Actual Skipped users", "Skipped Users At Course Session Scren");

		// Checking Skipped Employyes are not available in To be planned

		checkCommonElements(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_VerifyCourseSessionScreen.SkippedData, "Employees in To Be planned After Session",
				"Skipped Employees");

		// Absent Count

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount, CM_VerifyCourseSessionScreen.AbsentInsideCount,
				"Outside Absent Count at CourseSession After Record Attendance",
				"Inside Absent Count at CourseSession After Record Attendance");
		// Actual Absent Users with Absent Data Displayed in Skipped Column
		toBePlannedCountData(CM_RecordAttendance.AbsentUsers, CM_VerifyCourseSessionScreen.AbsentData,
				"Actual Absent users", "Absent Users At Course Session Screen");

		// Checking Absent Employees are not available in To be planned
		checkCommonElements(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_VerifyCourseSessionScreen.AbsentData, "Employees in To Be planned After Session",
				"Absent Employees");

		CSRReport.skipped_Absent_Qualified_AfterRecordAttendance();

		compareCount(CM_VerifyCourseSessionScreen.afterRecordAttendanceToBePlannedCount,
				CM_CSRReport.AfterRecordAttendanceToBePlannedCountOutside,
				"Outside To Planned Count at CourseSession After Record Attendance",
				"Outside To Be Planned at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AfterRecordAttendanceToBePlannedData,
				CM_CSRReport.combinedArrayBefore, "CourseSession To be Planned Data After Record Attendance",
				"CSR To be Planned Data After Record Attendance");

		compareCount(CM_VerifyCourseSessionScreen.skippedCountOutisde,
				CM_CSRReport.AfterRecordAttendnaceSkippedCountOurside,
				"Outside Skipped Count at CourseSession After Record Attendance",
				"Outside Skipped at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.SkippedData, CM_CSRReport.SkippedData,
				"CourseSession Skippped Data After Record Attendance", "CSR Skipped Data After Record Attendance");

		compareCount(CM_VerifyCourseSessionScreen.AbsentOutsideCount,
				CM_CSRReport.AfterRecordAttendanceAbsentOutsideCount,
				"Outside Absent Count at CourseSession After Record Attendance",
				"Outside Absent at CSR After Record Attendance");

		toBePlannedCountData(CM_VerifyCourseSessionScreen.AbsentData, CM_CSRReport.AbsentData,
				"CourseSession Absent Data After Record Attendance", "CSR Absent Data After Record Attendance");

		InProgresUsersQualified();
	}

	@Test(priority = 9, dataProvider = "CourseSession", enabled = true)
	public void courseRetraining(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("").assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("");
		}
		CourseRetraining.course_Retraining_Classroom_Type_Without_Assessment();
		CourseRetraining.course_Retraining_AuditTrials_Yes();
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_ScheduledTab(testData, " ");
		Logout.signOutPage();
	}

	@AfterTest
	public void afterTest() {
		MyScreenRecorder.stopRecording();
		driver.quit();
	}
}
