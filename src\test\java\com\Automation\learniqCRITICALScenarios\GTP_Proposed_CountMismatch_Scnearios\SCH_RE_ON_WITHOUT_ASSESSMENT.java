package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;
import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_BatchFormation;
import com.Automation.learniqObjects.CM_CSRReport;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_RecordAttendance;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

/**
 * Verify Online IT Session without assessment for scheduled course and make
 * atleast one employee skipped, course invitation rejected, course invitation
 * accepted, self nominated, absent, present by viewing Individual employee
 * report at each transaction starting from course session, also add at least
 * one user and check IER report and to be retrained and view IER for those
 * employees.
 */

public class SCH_RE_ON_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/SCH_RE_ON_WITHOUT_ASSESSMENT.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/SCH_RE_ONLINE_W_O_EXAM.xlsx";

	public SCH_RE_ON_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));

	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic  Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Configuration_Modification_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Modification");
		}

		TrainingShcedule.modifyTrainingScheduled(testData);
	}

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();
		// Propose Session

		verifyCSScreen.courseSession_Online_DocumentReading_WithOutExamNew(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
		Logout.signOutPage();

	}

	// Test Method for CRespond Document Reading //
	@Test(priority = 5, enabled = true)
	public void respondDocReading() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Respond Document Reading");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.getQualifiedTraineeID(),

				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();
		RespondDR.respondDocReading(CM_VerifyCourseSessionScreen.QualifiedTraineePsw);
	}

	@Test(priority = 6, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Screen_After_RecordDocumentReading(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session screen after keping employees in different states before Course Retraining Screen")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"Course Session screen after keping employees in different states before Course Retraining Screen");
		}

		verifyCSScreen.checkCourseSessionAfterRespondDocumentReading();

	}

	@Test(priority = 7, dataProvider = "CourseSession", enabled = true)
	public void checkCSR_After_Keeping_Employees_In_Different_States(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session report after keping employees in different states before Course Retraining Screen")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"Course Session  report after keping employees in different states before Course Retraining Screen");
		}

		CSRReport.TBPCSRReport_AfterRespondDocumentReading_Without_Assessment();
	}

	@Test(priority = 8, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Retraining")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"Course Retraining");
		}
		
		CourseRetraining.course_Retraining_ScheduledCourse_RE_Type_Without_Assessment();
	}

	@Test(priority = 9, enabled = true)
	public void verifyUnsche_CourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states after Course Retraining Screen in Unscheduled tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states after Course Retraining Screen in Unscheduled tab");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_UnscheduledTab_();
	}

	@Test(priority = 10, enabled = true)
	public void verify_Sche_CourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session  Screen after keping employees in different states after Course Retraining Screen in Scheduled tab")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session  Screen after keping employees in different states after Course Retraining Screen in Scheduled tab");
		}
		verifyCSScreen.verifyCourseSessionScreenAfterCourseRetraining_ScheduledTab_RE_Without_Assessment();
	}

	@Test(priority = 11, dataProvider = "CourseSession", enabled = true)
	public void VerifycourseRetraining_CourseSession_CSR(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Course Session Report Report after keping employees in different states after Course Retraining Screen")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"Course Session Report Screen after keping employees in different states after Course Retraining Screen");
		}

		CSRReport.TBPCSRReport_After_Course_Retraining_DocumentReading_Without_Assessment();

		Logout.signOutPage();
	}

}