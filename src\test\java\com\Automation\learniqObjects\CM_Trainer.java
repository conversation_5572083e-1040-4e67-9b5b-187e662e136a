package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Trainer extends OQActionEngine {

	public static String coursename = "";

	public static String getCoursename() {
		return coursename;
	}

	public static void setCoursename(String coursename) {
		CM_Trainer.coursename = coursename;
	}

	Properties prop;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[14]//a[contains(@class,'sub-menu')][contains(text(),'Configure')]")
	WebElement configMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[2]//a[contains(@class,'sub-menu')][contains(text(),'Initiate')]")
	WebElement initiateMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configure']//li//a[text()='Trainer']")
	WebElement configTrainerMenu;
	@FindBy(id = "TMS_Course Manager_Initiate_MEN137")
	WebElement trainerMenu;
	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Employee Name']")
	WebElement searchBy;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//input[@id='Trainer_TrainerDesc']")
	WebElement trainerName;
	@FindBy(xpath = "//input[@id='Trainer_TrainerCode']")
	WebElement trainerUniqueCode;
	@FindBy(xpath = "//textarea[@id='Trainer_Qualification']")
	WebElement trainerQualification;
	@FindBy(xpath = "//textarea[@id='Trainer_ExpYear']")
	WebElement trainerExperienceValue;
	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(id = "EmployeeName")
	WebElement empName;
	@FindBy(id = "Trainer_Charge")
	WebElement costPerHour;
	@FindBy(xpath = "//span[@id='select2-Trainer_CurrencyType-container']")
	WebElement currency_New;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement currencySearchField;
	@FindBy(xpath = "//ul[@id='select2-Trainer_CurrencyType-results']/li[1]")
	WebElement currencyFieldOption;
	@FindBy(id = "Trainer_ReQualificationDate_btn")
	WebElement requalificationDate;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[1]")
	WebElement mElement;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[2]")
	WebElement yElement;
	@FindBy(id = "Trainer_OtherInfo")
	WebElement externalTrainingAttended_New;
	@FindBy(id = "Trainer_CurResFor")
	WebElement currentlyResponsible_New;
	@FindBy(id = "Trainer_AreaOfExp")
	WebElement areaOfExposure_New;
	@FindBy(xpath = "//button[@id='btnModal_Trainer_coursesList']")
	WebElement course;
	@FindBy(id = "Trainer_coursesList_FindTxt")
	WebElement findText;
	@FindBy(id = "Trainer_coursesList_DisplayBtn")
	WebElement applyNew;
	@FindBy(xpath = "//table[@id='multipopupfilter2']//tbody/tr[1]/td[4]/button[1]")
	WebElement add_New;
	@FindBy(id = "Trainer_coursesList_selectBtn")
	WebElement addNewBtn;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(id = "Trainer_Remarks")
	WebElement modifyTrainerRemarks;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;

	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAppCheckBox;

	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDrpdown;
	@FindBy(id = "select2-Config_NarAtStatusChange-container")
	WebElement noOfAprReqForSTCDrpdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtStatusChange-results']/li[1]")
	WebElement searchSel3;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[2]//a[contains(@class,'sub-menu')][contains(text(),'Initiate')]")
	WebElement initiate;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[11]//a[contains(@class,'sub-menu')][contains(text(),'Modify')]")
	WebElement modify;
	@FindBy(id = "TMS_Course Manager_Modify_MEN137")
	WebElement modifyTrainer;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Trainer Name')]")
	WebElement searchByTrainerNameDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Trainer Name')]")
	WebElement searchByTrainerNameDropDown;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']//li//a[text()='Trainer']")
	WebElement aduitTrailsTrainer;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement trainerNameLike;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;

	@FindBy(xpath = "//div[@data-target='.MainTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoModTitleCompareTRN;

	@FindBy(xpath = "(//span[@class='audit-revision-region-value'])[2]")
	WebElement revisionNoValueModCompareTRN;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='TrainerDesc']/following-sibling::span")
	WebElement auditCompareTRNTrainerDescription;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Unique Code']/following-sibling::span")
	WebElement auditCompareTRNTrainerUniqueCode;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Qualification']/following-sibling::span")
	WebElement auditCompareTRNTrainerQualification;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Experience']/following-sibling::span")
	WebElement auditCompareTRNTrainerExperience;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Cost Per Hour']/following-sibling::span")
	WebElement auditCompareTRNTrainerCostPerHour;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Requalification Date']/following-sibling::span")
	WebElement auditCompareTRNTrainerRequalificationDate;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Any External Training Attended']/following-sibling::span")
	WebElement auditCompareTRNTrainerAnyExternalTrainingAttended;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Currently Responsible for']/following-sibling::span")
	WebElement auditCompareTRNTrainerCurrentlyResponsiblefor;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Area of Exposure']/following-sibling::span")
	WebElement auditCompareTRNTrainerAreaofExposure;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[2]/div[1]//div[10]//table[1]//tbody//tr[1]//td[1]/span")
	WebElement auditCompareTRNCourseName;

	@FindBy(xpath = "//div[@id='CompareTRN']//div[2]/div[1]//div[10]//table[1]//tbody//tr[1]//td[2]/span")
	WebElement auditCompareTRNCourseCode;
	@FindBy(xpath = "//div[@id='MainTRN']//div[2]/div[1]//div[10]//table[1]//tbody//tr[1]//td[2]/span")
	WebElement auditMainTRNCourseCode;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditMainTRNDateTimeValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")

	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading']")

	WebElement auditMainTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByUniqueCodeDropdown;

//	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Trainer Name')]")
//    WebElement 	searchByTrainerNameDropdown;
	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniqueCodeEqualsTo;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement trainerNameValue;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement modApprovalValue;
	@FindBy(xpath = "//*[@id='select2-Config_NarAtModify-results']//child::li")
	WebElement modclickApprovalCount;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDrpDown;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']//li[1]")
	WebElement select0ForModDropdown;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Employee Name')]")
	WebElement searchByEmployeeNameDropDown;
	@FindBy(id = "EmployeeName")
	WebElement employeeNameLike;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement initiatorName;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDropdown;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[2]")
	WebElement select1ForInitDropdown;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configuration Audit Trails']//li//a[text()='Trainer']")
	WebElement configurationTrainerAuditTrails;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[2]//label[contains(text(),'Modification')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Mod_Value;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[2]//label[contains(text(),'Modification')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Mod_Value;

	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;

	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[2]//label[contains(text(),'Modification')]//following-sibling::span")
	WebElement noOfApprovalsModAt_Registration_Value;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;
	@FindBy(xpath = "//input[@id='SelectedDecision_2']")
	WebElement approveRadioBtn;
	@FindBy(xpath = "//*[@id='UserESignUName']")
	WebElement approverName;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationApprovalAction;
	@FindBy(xpath = "//label[text()='Remark(s) / Reason(s)']//following-sibling::textarea")
	WebElement groupApproveRemarks;
	@FindBy(xpath = "//select[@id='Groups_Left_to']//option[1]")
	WebElement selectedSubgroup;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approveLabelText;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callAtEsignInitiationLabel;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callAtEsignApprovalLabel;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Approve']//following-sibling::ul//li//a[text()='Trainer']")
	WebElement approveTrainerMenu;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[contains(text(),'Trainer')]")
	WebElement approveTrainer_SearchBy;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Configuration Audit Trails']/preceding-sibling::a")
	WebElement configurationAuditTrails;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByTrainerUniqueCodeDropdown;
	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement uniqueCodeLike;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Employee Name')]")
	WebElement searchByEmployeeNameDropdown;
	@FindBy(xpath = "//input[@id='EmployeeName']")
	WebElement employeeName_Like;

	@FindBy(xpath = "//*[@id='Trainer_ReQualificationDate']")
	WebElement selected_RequalifyDate;

	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprModForRegDropDown;

	@FindBy(xpath = "//button[text()='Modification']")
	WebElement clickModTab;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='TrainerDesc']/following-sibling::span")
	WebElement auditMainTRNTrainerDescription;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Unique Code']/following-sibling::span")
	WebElement auditMainTRNTrainerUniqueCode;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Qualification']/following-sibling::span")
	WebElement auditMainTRNTrainerQualification;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Experience']/following-sibling::span")
	WebElement auditMainTRNTrainerExperience;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Cost Per Hour']/following-sibling::span")
	WebElement auditMainTRNTrainerCostPerHour;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Requalification Date']/following-sibling::span")
	WebElement auditMainTRNTrainerRequalificationDate;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Any External Training Attended']/following-sibling::span")
	WebElement auditMainTRNTrainerAnyExternalTrainingAttended;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Currently Responsible for']/following-sibling::span")
	WebElement auditMainTRNTrainerCurrentlyResponsiblefor;
	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Area of Exposure']/following-sibling::span")
	WebElement auditMainTRNTrainerAreaofExposure;
	@FindBy(xpath = "//div[@id='MainTRN']//div[2]/div[1]//div[10]//table[1]//tbody//tr[1]//td[1]/span")
	WebElement auditMainTRNCourseName;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']")
	WebElement auditMainTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditMainTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement audiMainTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditMainTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditMainTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditMainTTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditMainTTRNApprovalComVal;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	
	public static String  TrainerName = "";

	public CM_Trainer() {
		PageFactory.initElements(driver, this);
	}


	public void TrainerModificationConfigurations(HashMap<String, String> testData) {
		getInitiatorDetails();
		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(configMenu);
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", configMenu);
		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(configTrainerMenu, TrainerStrings.TrainerConfig_DC.getTrainerStrings(),
				TrainerStrings.TrainerConfig_AC.getTrainerStrings(),
				TrainerStrings.TrainerConfig_AR.getTrainerStrings(),
				TrainerStrings.TrainerConfig_SS.getTrainerStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);

		SelectRadioBtnAndCheckboxModification(driver, eSignAtModInitCheckBox, "Modification");
		SelectRadioBtnAndCheckboxModification(driver, eSignAtModAppCheckBox, "Modification Approval");
		TimeUtil.shortWait();
		click2(noOfAprModForRegDropDown, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());
		sendKeys2(modApprovalValue, CommonStrings.Enter_Approval1_DC.getCommonStrings(),
				testData.get("Approvals Required"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approval_1SS.getCommonStrings());
		click2(modclickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());
		scrollToViewElement(remarks);
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigurationRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void trainer_Modification_AuditTrails(HashMap<String, String> testData) {
		
		TrainerName= testData.get("TrainerName");
		
		
//		TrainerName= "test9.test9";
		
		
		


		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(modify, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		scrollToViewElement(modifyTrainer);
		click2(modifyTrainer, TrainerStrings.Click_TrainerMenu_DC.getTrainerStrings(),
				TrainerStrings.Click_TrainerMenu_AC.getTrainerStrings(),
				TrainerStrings.Click_TrainerMenu_AR.getTrainerStrings(),
				TrainerStrings.Click_TrainerMenu_SS.getTrainerStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, TrainerStrings.SearchByTrainer_Dropdown_DC.getTrainerStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AC.getTrainerStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AR.getTrainerStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_SS.getTrainerStrings());
		TimeUtil.shortWait();
		click2(searchByTrainerNameDropDown, TrainerStrings.Select_UniqueCode_DC.getTrainerStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				TrainerStrings.Select_UniqueCode_SS.getTrainerStrings());
		TimeUtil.mediumWait();
		sendKeys2(trainerNameLike, TrainerStrings.enterTrainerName_DC.getTrainerStrings(),
				TrainerName + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TrainerStrings.Enter_CostPerHour_SS.getTrainerStrings());
		TimeUtil.mediumWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, TrainerStrings.Click_Trainer_for_AuditTrails_DC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_AC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_AR.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_SS.getTrainerStrings());
		saveUniqueCodeInFeild(driver, trainerUniqueCode, "Unique Code");
		sendKeys2(costPerHour, TrainerStrings.Enter_CostPerHour_DC.getTrainerStrings(),
				testData.get("CostPerHourValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TrainerStrings.Enter_CostPerHour_SS.getTrainerStrings());
		click2(currency_New, TrainerStrings.Click_CurrencyDropDown_DC.getTrainerStrings(),
				TrainerStrings.Click_CurrencyDropDown_AC.getTrainerStrings(),
				TrainerStrings.Click_CurrencyDropDown_AR.getTrainerStrings(),
				TrainerStrings.Click_CurrencyDropDown_SS.getTrainerStrings());
		sendKeys2(currencySearchField, TrainerStrings.CurrencyTypeSearchField_DC.getTrainerStrings(),
				testData.get("CurrencyValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.CurrencyTypeSearchField_SS.getTrainerStrings());
		click2(currencyFieldOption, TrainerStrings.SelectCurrencyType_DC.getTrainerStrings(),
				TrainerStrings.SelectCurrencyType_AC.getTrainerStrings(),
				TrainerStrings.SelectCurrencyType_AR.getTrainerStrings(),
				TrainerStrings.SelectCurrencyType_SS.getTrainerStrings());
		TimeUtil.shortWait();
		click2WithScrollDown(requalificationDate, TrainerStrings.Click_RequalificationDate_DC.getTrainerStrings(),
				TrainerStrings.Click_RequalificationDate_AC.getTrainerStrings(),
				TrainerStrings.Click_RequalificationDate_AR.getTrainerStrings(),
				TrainerStrings.Click_RequalificationDate_SS.getTrainerStrings());
		selectDateonReQualification2(mElement, yElement, testData.get("RequalificationMonth"),
				testData.get("RequalificationYear"), testData.get("RequalificationDate"));
		sendKeys2(externalTrainingAttended_New, TrainerStrings.Enter_ExtTraining_DC.getTrainerStrings(),
				testData.get("AnyExtTrngAttendedValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.CurrencyTypeSearchField_SS.getTrainerStrings());
		sendKeys2(currentlyResponsible_New, TrainerStrings.Enter_CurrentlyRespFor_DC.getTrainerStrings(),
				testData.get("CurrentlyResponsibleForValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.Enter_CurrentlyRespFor_SS.getTrainerStrings());
		sendKeys2(areaOfExposure_New, TrainerStrings.Enter_AreaofExposure_DC.getTrainerStrings(),
				testData.get("AreaofExposureValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.Enter_AreaofExposure_SS.getTrainerStrings());
		click2(course, TrainerStrings.Click_CoursesAddItem_DC.getTrainerStrings(),
				TrainerStrings.Click_CoursesAddItem_AC.getTrainerStrings(),
				TrainerStrings.Click_CoursesAddItem_AR.getTrainerStrings(),
				TrainerStrings.Click_CoursesAddItem_SS.getTrainerStrings());
		String CourseName =  CM_Course.getCourse();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		sendKeys2(findText, TrainerStrings.Enter_CourseInFind_DC.getTrainerStrings(), CourseName + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.Enter_CourseInFind_SS.getTrainerStrings());
		TimeUtil.shortWait();
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(add_New, TrainerStrings.Click_AddButton_DC.getTrainerStrings(),
				TrainerStrings.Click_AddButton_AC.getTrainerStrings(),
				TrainerStrings.Click_AddButton_AR.getTrainerStrings(),
				TrainerStrings.Click_AddButton_SS.getTrainerStrings());
		TimeUtil.shortWait();
		scrollToViewElement(addNewBtn);
		click2(addNewBtn, TrainerStrings.Click_AddCoursesButton_DC.getTrainerStrings(),
				TrainerStrings.Click_AddCoursesButton_AC.getTrainerStrings(),
				TrainerStrings.Click_AddCoursesButton_AR.getTrainerStrings(),
				TrainerStrings.Click_AddCoursesButton_SS.getTrainerStrings());
		TimeUtil.shortWait();
		sendKeys2(modifyTrainerRemarks, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
				testData.get("ModificationRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.Enter_AreaofExposure_SS.getTrainerStrings());
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				TrainerStrings.Mod_SubmitTrainerwithEsign_AC.getTrainerStrings(),
				TrainerStrings.Mod_SubmitTrainerwithEsign_AR.getTrainerStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		TimeUtil.longwait();
		switchToDefaultContent(driver);
//		TimeUtil.shortWait();
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
//				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		click2(aduitTrailsTrainer, TrainerStrings.TrainerAudittrails_DC.getTrainerStrings(),
//				TrainerStrings.TrainerAudittrails_AC.getTrainerStrings(),
//				TrainerStrings.TrainerAudittrails_AR.getTrainerStrings(),
//				TrainerStrings.TrainerAudittrails_SS.getTrainerStrings());
//		switchToBodyFrame(driver);
//		clickAndWaitforNextElement(searchByNew, searchByTrainerUniqueCodeDropdown,
//				CommonStrings.SearchByDropdown_DC.getCommonStrings(),
//				TrainerStrings.SearchByTrainer_Dropdown_AC.getTrainerStrings(),
//				TrainerStrings.SearchByTrainer_Dropdown_AR.getTrainerStrings(),
//				CommonStrings.SearchByDropdown_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(searchByTrainerUniqueCodeDropdown, uniqueCodeLike,
//				CommonStrings.Select_UniqueCode_DC.getCommonStrings(), CommonStrings.dropdown_AC.getCommonStrings(),
//				CommonStrings.dropdown_AR.getCommonStrings(), CommonStrings.Select_UniqueCode_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		sendKeys2(uniqueCodeLike, TrainerStrings.Like_TrainerUniqueCode_DC.getTrainerStrings(), trainer_UniqueCode,
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CommonStrings.Select_UniqueCode_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		clickAndWaitforNextElement(searchFilterApplyBtn, auditTrailPageColumn1,
//				CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
//				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		highLightElement(driver, auditTrailPageColumn5, "Revision No", test);
//		TimeUtil.shortWait();
//		click2(displayedRecord, TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_DC.getTrainerStrings(),
//				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_AC.getTrainerStrings(),
//				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_AR.getTrainerStrings(),
//				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_SS.getTrainerStrings());
//		driver.switchTo().frame(0);
//		TimeUtil.shortWait();
//		WebElement modificationNumber = driver.findElement(By.xpath(
//				"//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span"));
//		String text = modificationNumber.getText();
//		System.out.println("text is :" + text);
//		String[] str = text.split(":");
//		String modifyNumber1 = str[1].trim();
//		String auditRevisionValue1 = modifyNumber1 + " - Modification";
//		scrollToViewElement(ModTab);
//		click2(ModTab, TrainerStrings.Click_LastesModTab_DC.getTrainerStrings(),
//				TrainerStrings.Click_LastesModTab_AC.getTrainerStrings(),
//				TrainerStrings.Click_LastesModTab_AR.getTrainerStrings(),
//				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_SS.getTrainerStrings());
//		click2(ProceedAudit, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//				TrainerStrings.Click_Proceed_AC.getTrainerStrings(),
//				TrainerStrings.Click_Proceed_AR.getTrainerStrings(),
//				TrainerStrings.Click_Trainer_for_AuditTrails_SS.getTrainerStrings());
//		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
//				TrainerStrings.Close_AuditTrails_Trainer_AC.getTrainerStrings(),
//				TrainerStrings.Close_AuditTrails_Trainer_AR.getTrainerStrings(),
//				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
//		TimeUtil.shortWait();
//		switchToDefaultContent(driver);

	}

	public void modification_Trainer_Approval_AuditTrials_Yes(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, approve, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(approve, approveTrainerMenu,
				CommonStrings.SYS_UserGroupsApproveMenu_DC.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_AC.getCommonStrings(), CommonStrings.CM_ApproveMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsApproveMenu_SS.getCommonStrings());
		click2(approveTrainerMenu, TrainerStrings.TrainerSubMenu_DC.getTrainerStrings(),
				TrainerStrings.TrainerApproveMenu_AC.getTrainerStrings(),
				TrainerStrings.TrainerApproveMenu_AR.getTrainerStrings(),
				TrainerStrings.TrainerSubMenu_SS.getTrainerStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Trainer Approval", test);
		TimeUtil.shortWait();
		click2(clickModTab, TrainerStrings.TrainerSubMenu_DC.getTrainerStrings(),
				TrainerStrings.TrainerApproveMenu_AC.getTrainerStrings(),
				TrainerStrings.TrainerApproveMenu_AR.getTrainerStrings(),
				TrainerStrings.TrainerSubMenu_SS.getTrainerStrings());

		click2(searchByNew, CommonStrings.SearchByDropdown_DC.getCommonStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AC.getTrainerStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AR.getTrainerStrings(),
				CommonStrings.SearchByDropdown_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByTrainerUniqueCodeDropdown, CommonStrings.Select_UniqueCode_DC.getCommonStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CommonStrings.Select_UniqueCode_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeys2(uniqueCodeLike, TrainerStrings.Like_TrainerUniqueCode_DC.getTrainerStrings(), trainer_UniqueCode,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Select_UniqueCode_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		click2(displayedRecord, TrainerStrings.Click_Trainer_for_Approve_DC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_Approve_AC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_Approve_AR.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_Approve_SS.getTrainerStrings());
		TimeUtil.shortWait();
		highLightElement(driver, approveLabelText, "Course Approval", test);
		click2(approveRadioBtn, CommonStrings.Approval_DC.getCommonStrings(),
				CommonStrings.Approval_AC.getCommonStrings(), CommonStrings.Approval_AR.getCommonStrings(),
				CommonStrings.Approval_SS.getCommonStrings());
		sendKeys2(groupApproveRemarks, CommonStrings.Approve_Remarks_DC.getCommonStrings(),
				testData.get("ModificationApproverRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Approve_Remarks_SS.getCommonStrings());
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				TrainerStrings.Submit_Trainer_Approval_AC.getTrainerStrings(),
				TrainerStrings.Submit_Trainer_Approval_AR.getTrainerStrings(),
				TrainerStrings.Submit_Trainer_Approval_SS.getTrainerStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		click2(menu,  CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(courseManagerMenu, courseManagerAuditTrails,
				CommonStrings.CM_Menus_DC.getCommonStrings(), CommonStrings.CM_Menus_AC.getCommonStrings(),
				CommonStrings.CM_Menus_AR.getCommonStrings(), CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerAuditTrails, 
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(aduitTrailsTrainer, TrainerStrings.TrainerAudittrails_DC.getTrainerStrings(),
				TrainerStrings.TrainerAudittrails_AC.getTrainerStrings(),
				TrainerStrings.TrainerAudittrails_AR.getTrainerStrings(),
				TrainerStrings.TrainerAudittrails_SS.getTrainerStrings());
		switchToBodyFrame(driver);
		highLightElement(driver, highLightScreenTitle, "Audit Trails screen", test);
		click2(searchByNew, CommonStrings.SearchByDropdown_DC.getCommonStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AC.getTrainerStrings(),
				TrainerStrings.SearchByTrainer_Dropdown_AR.getTrainerStrings(),
				CommonStrings.SearchByDropdown_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByTrainerUniqueCodeDropdown, CommonStrings.Select_UniqueCode_DC.getCommonStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				CommonStrings.Select_UniqueCode_SS.getCommonStrings());
		TimeUtil.shortWait();
		sendKeys2(uniqueCodeLike, TrainerStrings.Like_TrainerUniqueCode_DC.getTrainerStrings(), trainer_UniqueCode,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Select_UniqueCode_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, TrainerStrings.Click_Trainer_for_AuditTrails_DC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_Approve_AuditTrails_AC.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_Approve_AuditTrails_AR.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_SS.getTrainerStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		WebElement modificationNumber = driver.findElement(By.xpath(
				"//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span"));
		String text = modificationNumber.getText();
		System.out.println("text is :" + text);
		String[] str = text.split(":");
		String modifyNumber1 = str[1].trim();
		String auditRevisionValue1 = modifyNumber1 + " - Modification";
		scrollToViewElement(ModTab);
		click2(ModTab, TrainerStrings.Click_LastesModTab_DC.getTrainerStrings(),
				TrainerStrings.Click_LastesModTab_AC.getTrainerStrings(),
				TrainerStrings.Click_LastesModTab_AR.getTrainerStrings(),
				TrainerStrings.Click_ModifiedTrainer_for_AuditTrails_SS.getTrainerStrings());
		click2(ProceedAudit, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				TrainerStrings.Click_Proceed_AC.getTrainerStrings(),
				TrainerStrings.Click_Proceed_AR.getTrainerStrings(),
				TrainerStrings.Click_Trainer_for_AuditTrails_SS.getTrainerStrings());

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				TrainerStrings.Close_AuditTrails_Trainer_AC.getTrainerStrings(),
				TrainerStrings.Close_AuditTrails_Trainer_AR.getTrainerStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

}
