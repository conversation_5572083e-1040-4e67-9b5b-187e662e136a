package com.Automation.Strings;

public enum SubGroupStrings {

	SubGroupMenu_DC("Click on 'Subgroup' submenu."),
	SubGroup_Config_AC("'Subgroup Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	SubGroup_Config_AR("'Subgroup Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	SubGroup_Config_SS("'Subgroup'"),

	SubgroupName_SS("'Subgroup Name'."),

	Click_DoneatSubGroupConfig_AC("'Subgroup Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatSubGroupConfig_AR("'Subgroup Configuration Registration' screen is getting displayed.</div>"),
//Subgroup Initiate
	SubGroup_Initiate_DC("Click on 'Subgroup' submenu"),
	SubGroup_Initiate_AC("'Subgroup Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Subgroup Name, Unique Code, Description, Job Responsibility' fields.</div>"),
	SubGroup_Initiate_AR("'Subgroup Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Subgroup Name, Unique Code, Description, Job Responsibility' fields.</div>"),
	SubGroup_Initiate_SS("'Subgroup'"),

	SubGroup_approve2_DC("Click on 'Subgroup' submenu"),
	SubGroup_approve2_AC("‘Subgroup Approval Tasks’ screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain ‘Subgroup Name,Initiated By, and Initiated On’ fields.</div>"
			+ "<div><b>*</b> The screen should contain ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b> Under ‘View Existing’, registered records of ‘Subgroups’ should be displayed (if any).</div>"
			+ "<div><b>*</b> ‘Unique Code’ should be system generated.</div>"),

	SubGroup_approve2_AR("‘Subgroup - Registration Initiation’ screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed ‘Subgroup Name, Initiated By, and Initiated On’ fields.</div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b> Under ‘View Existing’, registered records of ‘Subgroups’ is getting displayed (if any).</div>"
			+ "<div><b>*</b> ‘Unique Code’ is system generated.</div>"),
	SubGroup_approve2_SS("'Subgroup'"),

	SubGroup_Initiate1_DC("Click on 'Subgroup' submenu"),
	SubGroup_Initiate1_AC("‘Subgroup - Registration Initiation’ screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain ‘Subgroup Name, Unique Code, Description and Job Responsibility’ fields.</div>"
			+ "<div><b>*</b> The screen should contain ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b> Under ‘View Existing’, registered records of ‘Subgroups’ should be displayed (if any).</div>"
			+ "<div><b>*</b> ‘Unique Code’ should be system generated.</div>"),

	SubGroup_Initiate1_AR("‘Subgroup - Registration Initiation’ screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘Subgroup Name, Unique Code, Description and Job Responsibility’ fields.</div>"
			+ "<div><b>*</b> The screen is getting displayed with ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b> Under ‘View Existing’, registered records of ‘Subgroups’ is getting displayed (if any).</div>"
			+ "<div><b>*</b> ‘Unique Code’ is system generated.</div>"),
	SubGroup_Initiate1_SS("'Subgroup'"),

	Subgroup_DC("Enter the value less than or equals to 250 characters in 'Subgroup Name' field."),
	Subgroup_SS("'Subgroup Name'"),

	JobRes_DC("Enter the value less than or equals to 250 characters in 'Job Responsibility' field."),
	JobRes_SS("'Job Responsibility'"),

	description_DC("Enter the value less than or equals to 250 characters in 'Description' field."),
	description_SS("'Description'"),

	SubmitSubgroupwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Subgroup: Registration Initiation'.</div>"),
	SubmitSubgroupwithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Subgroup: Registration Initiation'.</div>"),
//Proceed
	Esign_ProceedSubgroup_AC(
			"'Subgroup  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedSubgroup_AR(
			"'Subgroup  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	SubgroupAudittrails_AC("'Subgroup Audit Trails' screen should be displayed.</div>"),
	SubgroupAudittrails_AR("'Subgroup Audit trails' screen is getting displayed.</div>"),
	
	
	
	SubgroupAudittrails_SS("'Subgroup Audit Trails' screen."),

	SearchBy_SubgrpAudit_AC(
			"Option to search with 'Top 250 Records, Subgroup Name, Unique Code, Initiated Between' should be available.</div>"),
	SearchBy_SubgrpAudit_AR(
			"Option to search with 'Top 250 Records, Subgroup Name, Unique Code, Initiated Between' are available.</div>"),

	Select_SubgroupName_DC("Select 'Subgroup Name'."),

	Like_SubgroupName_DC("Enter the above registered 'Subgroup Name' in 'Like' field."), Like_SubgroupName_SS("'Like'"),

	Click_Subgroup_for_AuditTrails_DC("Click on the above registered 'Subgroup' Name'."),

	Click_Subgroup_for_AuditTrails_SS("'Subgroup' - Audit Trails."),
	Click_Subgroup_for_ApprovalAuditTrails_SS("'Subgroup Registration Approval'"),
	
	
	

//subgroup Audit trails for final status iniatited
	Click_Subgroup_for_AuditTrails1_AC(
			"‘Subgroup- Audit Trails - Revision No.: 0 -Registration’ screen should be displayed."
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b> The screen should contain system manager generated subgroups also.<div>"
					+ "<div><b>*</b>The ‘Events’ section should contain only the Registration Initiation’ transaction with ‘Username, Date& Time and Remarks / Reasons’ details.<div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘0’.<div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.<div>"),

	Click_Subgroup_for_AuditTrails1_AR(
			"‘Subgroup- Audit Trails - Revision No.: 0 -Registration’ screen is getting displayed."
					+ "<div><b>*</b>‘Final Status’ is getting displayed as ‘Initiated’.<div>"
					+ "<div><b>*</b> The screen is getting displayed with system manager generated subgroups also.<div>"
					+ "<div><b>*</b>The ‘Events’ section is  contain  only the Registration Initiation’ transaction with ‘Username, Date& Time and Remarks / Reasons’ details.<div>"
					+ "<div><b>*</b>The ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both are  read as ‘1’and '0'.<div>"
					+ "<div><b>*</b>All the particulars is getting  displayed in  read only format.<div>"),

	
	
	//subgroup Audit trails for approval 
	Click_Subgroup_for_AuditTrails_AC("‘Subgroup- Registration Approval’ screen should be displayed.<div>"
			+ "<div><b>*</b> The screen should contain the details of the subgroup entered during registration.<div>"
			+ "<div><b>*</b> ‘Final Status’ should be displayed as ‘Initiated’.<div>"
			+ "<div><b>*</b>The ‘Events’ section should contain the ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.<div>"
			+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as 1 and the ‘No. of Approvals Completed’ should be read as 0.<div>"
			+ "<div><b>*</b>‘Approve, Return & Drop’ options should be available for ‘Decision’ field.<div>"
			+ "<div><b>*</b>The option to enter/ select ‘Remark(s) / Reason(s)’ should be available.<div>"),

	Click_Subgroup_for_AuditTrails_AR("‘Subgroup- Registration Approval’ screen is getting displayed.<div>"
			+ "<div><b>*</b> The screen  contains the details of the subgroup entered during registration.<div>"
			+ "<div><b>*</b> ‘Final Status’ is getting displayed as ‘Initiated’.<div>"
			+ "<div><b>*</b>The ‘Events’ section is getting displayed with the ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.<div>"
			+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ is  read as 1 and the ‘No. of Approvals Completed’ is  read as 0.<div>"
			+ "<div><b>*</b>‘Approve, Return & Drop’ options should be available for ‘Decision’ field.<div>"
			+ "<div><b>*</b>The option to enter/ select ‘Remark(s) / Reason(s)’ are available.<div>"),
//click on subgroup
	SYS_UserGroupsSubgroupApproveMenu_DC("Click on 'Subgroup' submenu."),

	SYS_UserGroupsSubgroupApproveMenu_SS("'Subgroup'"),

	SYS_UserGroupsSubgroupApproveMenu_AC("‘Subgroup Approval Tasks’ screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain ‘Registration, Modification and Status Change’ tabs.<div>"
			+ "<div><b>*</b>By default, the ‘Registration’ tab details should be displayed.<div>"
			+ "<div><b>*</b>All the Subgroups whose registration request is to be approved by the current user should be listed and available for approval under the ‘Registration’ tab.<div>"
			+ "<div><b>*</b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen should contain ‘Subgroup Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Subgroup Name, Unique Code and Initiated Between’ should be available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’ should be displayed as ‘20’.<div>"),
	SYS_UserGroupsSubgroupApproveMenu_AR("‘Subgroup Approval Tasks’ screen is getting displayed.<div>"
			+ "<div><b>*</b> Screen is contain ‘Registration, Modification and Status Change’ tabs.<div>"
			+ "<div><b>*</b>By default, the ‘Registration’ tab details is getting displayed.<div>"
			+ "<div><b>*</b>All the Subgroups whose registration request is to be approved by the current user is listed and available for approval under the ‘Registration’ tab.<div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Subgroup Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Subgroup Name, Unique Code and Initiated Between’ are available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’is  getting displayed  as ‘20’.<div>"),

	SYS_UserGroupsSubgroupApproveMenu1_AC("‘Subgroup Audit Trails’ screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen should contain ‘Subgroup Name’, ‘Unique Code’, ‘Initiated By’,‘Initiated On’ and ‘Revision No.’columns.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Subgroup Name, Unique Code and Initiated Between’ should be available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’ should be displayed as ‘20’.<div>"),
	SYS_UserGroupsSubgroupApproveMenu1_AR("‘Subgroup Audit Trails’ screen is getting displayed.."
			+ "<div><b>*</b>The screen contains ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Subgroup Name’, ‘Unique Code’, ‘Initiated By’,‘Initiated On’ and ‘Revision No.’columns.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Subgroup Name, Unique Code and Initiated Between’ are available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’ is getting displayed as ‘20’.<div>"),

	SYS_UserGroupsSubgroupApproveAudittrails_DC(
			"Click on the 'Subgroup Name' of the above registered 'Subgroup Name.'"),
	SYS_UserGroupsSubgroupApproveAudittrails_AC("‘Final Status’ should be displayed as  ‘Approved’.<div>"
			+ "<div><b>*</b> The ‘Events’ section should contain Initiated and Approved transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.<div>"
			+ "<div><b>*</b>By default, the ‘Registration’ tab details should be displayed.<div>"
			+ "<div><b>*</b>Also, ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both should be read as 1.<div>"
			+ "<div><b>*</b>All the particulars should be displayed in read only format.<div>"),

	SYS_UserGroupsSubgroupApproveAudittrails_AR("‘Final Status’ is getting displayed as  ‘Approved’.<div>"
			+ "<div><b>*</b> The ‘Events’ section  contains Initiated and Approved transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details.<div>"
			+ "<div><b>*</b>By default, the ‘Registration’ tab details is getting displayed.<div>"
			+ "<div><b>*</b>Also, ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both  are read as 1.<div>"
			+ "<div><b>*</b>All the particulars is getting displayed in read only format.<div>"),

	Click_Subgroup_for_AuditTrails1_SS("'Subgroup Audit Trails'."),

	// search by
	SearchBy_Subgroup_AC(
			"Option to search with 'Top 250 Records, Subgroup Name, Unique Code ,Initiated Between should be available.</div>"),
	SearchBy_Subgroup_AR(
			"Option to search with 'Top 250 Records,Subgroup Name,Unique Code ,Initiated Between is available.</div>"),
	// submit at Subgroup approval
	SubmitSubgroupapproval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Subgroup: Registration Approval: Approve'.</div>"),
	SubmitSubgroupapproval_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Subgroup: Registration Approval: Approve'.</div>"),
	SubmitSubgroupapproval_SS("'E-Sign window'"),

	// esign- approval
	Esign_SubgroupProceedapproval_AC(
			"'Subgroup Registration Approved' Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_SubgroupProceedapproval_AR(
			"'Subgroup Registration Approved' Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),
//Search by subgroup Name
	SearchBy_Subgroupname1_DC("Select 'Subgroup Name'."),
	SearchBy_Subgroupname1_AC("Selection should be accepted.</div>"),
	SearchBy_Subgroupname1_AR(" Selection is getting accepted.</div>"),
	SearchBy_Subgroupname1_SS("'Subgroup Name'."),
	Like_Subgroup_DC("Enter the above registered 'Subgroup Name' in 'Like' text box."),
	Like_Subgroup_SS("'Subgroup Name'."),
//click subgroup
	Subgroup_for_AuditTrails_DC("Click on the above registered 'Subgroup Name'."),
	TP_for_AuditTrails_SS("'Topic Audit Trails'"),
	// search by subgroup name
	SearchBy_Subgroupname_DC("Click on 'Search By' dropdown."),
	SearchBy_Subgroupname_AC(
			"Option to search with 'Top 250 Records, Subgroup Name, Unique Code, Initiated Between' should be diplayed.<div>"),
	SearchBy_Subgroupname_AR(
			"Option to search with 'Top 250 Records, Subgroup Name, Unique Code,  Initiated Between' is getting diplayed.<div>"),
	SearchBy_Subgroupname_SS("'Search By'."),
	// subgroup configuration Audit trails
	Click_Subgroup_for_ConfigAuditTrails_DC("Click on the above registered 'Unique Code'."),
	Click_Subgroup_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Subgroup_for_ConfigAuditTrails_AR("'Transactions' screen is getting.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Subgroup_for_ConfigAuditTrails_SS("'Subgroup Configuration' - Audit Trails."),

	// subgroup configuration proceed
	Click_SubgroupConfig_Proceed_AC(
			"'Subgroup Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Subgroup Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_SubgroupConfig_Proceed_AR(
			"'Subgroup - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Subgroup Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_SubgroupConfig_Proceed_SS("'Subgroup Configuration - Audit Trails'."),
	Close_SubgroupConfigAuditTrails_Group_AC("'Subgroup Configuration Audit Trails' screen should be displayed.</div>"),
	Close_SubgroupConfigAuditTrails_Group_AR(
			"'Subgroup Configuration Audit Trails' screen is getting displayed.</div>"),

	SubgroupMenu_DC("Click on 'Subgroup' submenu."),
	
	// SubGroup submenu
	Subgroup_Config_DC("Click on 'Subgroup' submenu."),
	Subgroup_Config_AC("'Subgroup Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	Subgroup_Config_AR("'Subgroup Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	SubgroupMenu_SS("'Subgroup' submenu"),

	// SubGroup configuration Audit trails

	Subgroup_ConfigAudit_DC("Click on 'Subgroup' submenu."),
	Subgroup_ConfigAudit_AC("'Subgroup Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Subgroup_ConfigAudit_AR("'Subgroup Configuration Registration' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Subgroup_ConfigAudit_SS("'Subgroup' submenu"),;

	private final String subGroupStrings;

	SubGroupStrings(String subGroupStrings) {

		this.subGroupStrings = subGroupStrings;

	}

	public String getSubGroupStrings() {
		return subGroupStrings;
	}

}
