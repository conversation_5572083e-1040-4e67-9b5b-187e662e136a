package com.Automation.learniqCRITICALScenarios.PreRequisites310;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.SSO_UserRegistration;

public class Induction_Training extends OQActionEngine{
	
	String ExcelPath = "./learnIQTestData/INDUCTION_TRAINING.xlsx";

	public Induction_Training() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "Induction Training");
	@DataProvider(name = "InductionTraining")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[excel1.getRowCount()][1];
		for (int i = 1; i <= excel1.getRowCount(); i++) {
			HashMap<String, String> testData = excel1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}
	/**
	 * Method is for Induction Training
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "InductionTraining", enabled = true)
	public void proposeIT(HashMap<String, String> testData) {
		test = extent.createTest("Propose Induction Training")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Induction Training Propose");
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		ProposeInduction.proposeInductionTraining(testData);
		ProposeInduction.proposeInductionTrainingAuditTrails(testData);
		Logout.signOutPage();
	}
	@Test(priority = 2, dataProvider = "InductionTraining", enabled = true)
	public void recordIT(HashMap<String, String> testData) {
		test = extent.createTest("Record Induction Training")
				.assignAuthor(testData.get("trainerUserID"))
				.assignCategory("Record Induction Training");
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("trainerUserID"),
				testData.get("trainerPassword"));
		epiclogin.plant1();
		RecordInduction.recordInductionTraining(testData);
		Logout.signOutPage();
	}
	
	@Test(priority = 3, dataProvider = "InductionTraining", enabled = true)
	public void respondIT(HashMap<String, String> testData) {
		test = extent.createTest("Respond Induction Training")
				.assignAuthor(SSO_UserRegistration.getUserID())
						.assignCategory("Induction Training Respond");
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), SSO_UserRegistration.getUserID(),
				ConfigsReader.getPropValue("SSONewUserRegPassword"));
		epiclogin.plant1();
		RespondInduction.respondInductionTraining(testData);
		Logout.signOutPage();
	}
	
	@Test(priority = 4, dataProvider = "InductionTraining", enabled = true)
	public void completionIT(HashMap<String, String> testData) {
		test = extent.createTest("Induction Training Completion").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Induction Training Respond");
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		completionIT.completionInductionTraining(testData);
		Logout.signOutPage();
	}
	
//	ExcelUtilUpdated excel2 = new ExcelUtilUpdated(ExcelPath, "Jobresponsibility");
//
//	@DataProvider(name = "JobResp")
//	public Object[][] getRegistrationUser() throws Exception {
//		Object[][] obj = new Object[excel2.getRowCount()][1];
//		for (int i = 1; i <= excel2.getRowCount(); i++) {
//			HashMap<String, String> testData = excel2.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 5, dataProvider = "JobResp", enabled = true)
//	public void JR_Registration(HashMap<String, String> testData) {
//
//		test = extent.createTest("Pre Requisite: 4.1 Job Responsibility Configuration")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Pre Requisite: 4.1 Job Responsibility  Configuration");
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//	//s	JobResponsibility.jobresponsbility_Configuration(testData);
//		test = extent.createTest("Pre Requisite: 4.2  Job Responsibility Registration with AuditTrails")
//
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//
//				.assignCategory("Pre Requisite: 4.2  Job Responsibility Registration with AuditTrails");
//
//		JobResponsibility.jobResponsibiltyRegWith1Approval(testData);
//		Logout.signOutPage();
//	}
//	
//	@Test(priority = 6, dataProvider = "JobResp", enabled = true)
//	public void JR_Line_Of_Approval(HashMap<String, String> testData) {
//		test = extent.createTest("Pre Requisite: 4.3  Job Responsibility Line of Approvers Approve with AuditTrails")
//
//				.assignAuthor(ConfigsReader.getPropValue("EPIQDefaultID"))
//
//				.assignCategory("Pre Requisite: 4.3  Job Responsibility Line of Approvers Approve with AuditTrails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData);
//
//		Logout.signOutPage();
//		
//	}
//	@Test(priority = 7, dataProvider = "JobResp", enabled = true)
//	public void JR_UserAcceptance(HashMap<String, String> testData) {
//		test = extent.createTest("Pre Requisite: 4.4  Job Responsibility User Acceptance Approve with AuditTrails")
//
//				.assignAuthor(SSO_UserRegistration.getEmployeeID())
//
//				.assignCategory("Pre Requisite: 4.4  Job Responsibility User Acceptance Approve with AuditTrails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), SSO_UserRegistration.getUserID(),
//				ConfigsReader.getPropValue("SSONewUserRegPassword"));
//		
////		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), "userJCUZ",
////				ConfigsReader.getPropValue("SSONewUserRegPassword"));
//
//		epiclogin.plant1();
//
//		JobResponsibility.jobResponsibilityUserAcceptanceApprove(testData);
//
//		Logout.signOutPage();
//	}
//	@Test(priority = 8, dataProvider = "JobResp", enabled = true)
//	public void JR_AD_Approval(HashMap<String, String> testData) {
//		test = extent
//				.createTest(
//						"Pre Requisite: 4.5  Job Responsibility Authorized Deputy Acceptance Approve with AuditTrails")
//
//				.assignAuthor(ConfigsReader.getPropValue("EPIQADUserID"))
//
//				.assignCategory(
//						"Pre Requisite: 4.5  Job Responsibility Authorized Deputy Acceptance Approve with AuditTrails");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EPIQADUserID"),
//				ConfigsReader.getPropValue("EPIQADUserPSW"));
//
//		epiclogin.plant1();
//
//		JobResponsibility.jobResponsibility_AuthorizedDeputyAcceptance_ApproveWithAudiTrails(testData);
//		test = extent.createTest("Pre Requisite: 4.6  Print Job Responsibility")
//
//				.assignAuthor(ConfigsReader.getPropValue("EPIQADUserID"))
//
//				.assignCategory("Pre Requisite: 4.6  Print Job Responsibility");
//
//		JRReport.jobResponsibilityReport();
//		
//
//		Logout.signOutPage();
//
//		n = 0;
//		screenshotCounter = 0;
//		
//
//	}
	
	
	
	@AfterTest
	public void afterTest() {
		extent.flush();
		MyScreenRecorder.stopRecording();
		driver.quit();
	}

}
