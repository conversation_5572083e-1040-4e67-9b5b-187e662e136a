package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Online IT Session with assessment for scheduled course with MQA as
 * 'Yes' and make sure that at at least user should be qualified but not 100%
 * and view the MQA report before and after responding to MQA.
 */

public class UNSCH_IT_ON_WITH_ASSESSMENT_MQA_YES extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/UnscheduledScenariosData/UNSCH_IT_ON_WITH_ASSESSMENT_MQA_YES.xlsx";

	public UNSCH_IT_ON_WITH_ASSESSMENT_MQA_YES() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic  Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//					epiclogin.masterPlant();
		//
//					InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//							Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//					Logout.signOutPage();

//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//					epiclogin.plant1();
		//
//					InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//					Logout.signOutPage();

	}

	// Test Method for Course  Registration
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));

//					epiclogin.masterPlant();
		//
//					Initiate_Course.courseConfiguration_Reg(testData);

//					epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//					Logout.signOutPage();

//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//					epiclogin.plant1();
		//
//					Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
//					Logout.signOutPage();

	}

	// Test Method for Trainer  Modification

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer  Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}
//						epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//								ConfigsReader.getPropValue("EpicUserPWD"));
//						epiclogin.plant1();
//						trainer.TrainerModificationConfigurations(testData);
		trainer.trainer_Modification_AuditTrails(testData);
//						Logout.signOutPage();
//						epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//								ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//						epiclogin.plant1();
//						trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//						Logout.signOutPage();

	}

	// Test Method for CourseSession  Registration

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData)
			throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Online_WithExam_MQA_Yes(testData);

//		CourseSession.courseSessionAuditTrails();
//
//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//
//		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Proposed Status");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);

		// Logout.signOutPage();
	}

	// Test Method for BatchFormation  Registration 

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation  Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Online_NotRespondedUsers_SelectAll(testData);

		// BatchFormation.proposeBatchFormationAuditTrail();
		if (isReportedRequired == true) {
		test = extent.createTest("Individual Employee Report For Employee selected in batch")
				.assignAuthor(CM_CourseSession.getSkippedTraineeID())
				.assignCategory("Individual Employee Report For Employee selected in batch");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);

	//	Logout.signOutPage();

	}

	// Test Method for QuestionBank  Registration

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionBank", enabled = true)
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.masterPlant();
//
//		PrepareQB.QBRegistrationApproval_Configuration(testData);
//
//		epiclogin.navigateTolearnIQPlant();

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);

//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);
//
//		Logout.signOutPage();

	}

	// Test Method for QuestionPaper Registration
	// -------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration");
		}
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);

		// Logout.signOutPage();

	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Record Attendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Attendance");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		RecordAttendance.recordAttendance_CandidatesList_SelectAll(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for selected Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for selected Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_ATTENDANCE_RECORDED, Constants.ITType);

		Logout.signOutPage();
	}

	// Test Method for 'Respond Question paper'
	// and 'Missed Question
	// Analysis'-------------------------------------------------------------------------------------------

	@Test(priority = 10, enabled = true)
	public void respond_QuestionPaper_Respond_MQA() {
		if (isReportedRequired == true) {
		test = extent.createTest("Respond Question paper (Not100%Qualified)")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("Respond Question paper (Not100%Qualified)");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.mqaTraineeID,
				CM_CourseSession.mqaTraineePassword);

		epiclogin.plant1();
//
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_Not_100_QUALIFIED, Constants.ITType,
				CM_CourseSession.mqaTraineePassword);
		if (isReportedRequired == true) {
		test = extent.createTest("MissedQuestionAnalysisLog before responding MQA")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("MissedQuestionAnalysisLog before responding MQA");
		}
		reports.missedQuestionAnalysisLog_Report(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_PENDING_LIST);
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Missed Question Analysis").assignAuthor(CM_CourseSession.mqaTraineeID)
					.assignCategory("Respond Missed Question Analysis");
		}
		respondMQA.respondMissedQuestionAnalysis(CM_CourseSession.mqaTraineePassword);
		if (isReportedRequired == true) {
		test = extent.createTest("MissedQuestionAnalysisLog after responding MQA")
				.assignAuthor(CM_CourseSession.mqaTraineeID)
				.assignCategory("MissedQuestionAnalysisLog after responding MQA");
		}
		reports.missedQuestionAnalysisLog_Report(CM_CourseSession.mqaTraineeEmployeeID,
				Constants.EMPLOYEESTATUS_AS_COMPLETED_LIST);
//		
//		test = extent.createTest("Check Trainees at Course Session Screen")
//				.assignAuthor( ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Check Trainees at Course Session Screen");
//
//		CourseSession.verify_Employees_At_Coursesession();

		Logout.signOutPage();

	}

}
