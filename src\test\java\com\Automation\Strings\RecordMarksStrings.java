package com.Automation.Strings;

public enum RecordMarksStrings {

	RecordMarks_DC("Click on 'Marks' submenu."),
	RecordMarks_AC("'Records Marks' screen should be displayed.</div>"
			+ "<div><b>* </b> List of batches for which record marks are pending should be displayed."),
	RecordMarks_AR("'Records Marks' screen is getting displayed.</div>"
			+ "<div><b>* </b> List of batches for which record marks are pending are getting displayed."),
	RecordMarks_SS("'Record Marks'"),

	Click_Record_DC("Click on the above batch name for which the record attendance is updated."),
	Click_Record_AC("'Record Marks Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>* </b>Option to enter 'AQ Marks' should be available under 'User(s) List' section.</div>"),
	Click_Record_AR("'Record Marks Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>* </b>Option to enter 'AQ Marks' is available under 'User(s) List' section.</div>"),
	Click_Record_SS("'Record Marks Registration Initiation'"),

	SearchBy_AC("Option to search with 'Top 250 Records, Batch Name and Initiated Between' should be available."),
	SearchBy_AR("Option to search with 'Top 250 Records, Batch Name and Initiated Between' are available."),

	SelectBatchName_DC("Select 'Batch Name'."), SelectBatchName_SS("'Batch Name'"),

	Like_batchname_DC("Enter the above batch name for which the record attendance is updated."),
	Like_batchname_SS("'Batch Name'"),

	Enter_AQMarks_DC(
			"Enter the value greater than 'QM' and less than 'MM' in 'AQ Marks' for the required user and click mouse by placing the cursor anywhere on the screen."),
	Enter_AQMarks_AC("Percentage value should be updated based on 'AQ Marks'.</div>"
			+ "<div><b>* </b>'Qualified' status should be updated under 'Result' column in green colour.</div>"),
	Enter_AQMarks_AR("Percentage value is getting updated based on 'AQ Marks'.</div>"
			+ "<div><b>* </b>'Qualified' status is getting updated under 'Result' column in green colour.</div>"),
	Enter_AQMarks_SS("'Acquired Marks'"),

	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Record Marks: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Record Marks: Registration Initiation'.</div>"),

	Esign_RecordMarks_Proceed_AC(
			"'Record Marks Updated' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_RecordMarks_Proceed_AR(
			"'Record Marks Updated' confirmation message is getting displayed with 'Done' button.</div>"),

	//To be re-trained
	
	Enter_AQMarks1_DC(
			"Enter the value less than 'QM' and less than 'MM' in 'AQ Marks' for the required user and click mouse by placing the cursor anywhere on the screen."),
	Enter_AQMarks1_AC("Percentage value should be updated based on 'AQ Marks'.</div>"
			+ "<div><b>* </b>'To Be Re-trained' status should be updated under 'Result' column in red colour.</div>"),
	Enter_AQMarks1_AR("Percentage value is getting updated based on 'AQ Marks'.</div>");

	private final String recordmarksStrings;

	RecordMarksStrings(String recordMarksStrings) {

		this.recordmarksStrings = recordMarksStrings;

	}

	public String getRecordMarksStrings() {
		return recordmarksStrings;
	}

}
