package com.Automation.learniqCRITICALScenarios.Unscheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Online RE type Session without assessment for scheduled course and
 * make at least one employee should be pending at respond document reading and
 * one should be qualified by viewing Individual employee report at each
 * transaction starting from course session.
 */

public class UNSCH_RE_ON_WITHOUT_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/UnscheduledScenariosData/UNSCH_RE_ON_WITHOUT_ASSESSMENT.xlsx";

	public UNSCH_RE_ON_WITHOUT_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));

//			epiclogin.masterPlant();
//			InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//					Constants.CONFIGURE_CONFIRMATION_TEXT);

			epiclogin.plant1();

			InitiateTopic.topic_Registration(testData);

//			Logout.signOutPage();
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//			epiclogin.plant1();
//			InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
//			Logout.signOutPage();
	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
			}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//			ConfigsReader.getPropValue("EpicUserPWD"));

	//epiclogin.masterPlant();
	//
	//Initiate_Course.courseConfiguration_Reg(testData);

	//epiclogin.plant1();

	Initiate_Course.Course_Registration(testData);

	//Logout.signOutPage();

	//epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//			ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
	//
	//epiclogin.plant1();
	//
	//Initiate_Course.course_Approval_AuditTrials_Yes(testData);
	//
	//Logout.signOutPage();

	}

	

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails
	ExcelUtilUpdated excel = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegDOC");

	@DataProvider(name = "courseSessionRegistration")
	public Object[][] getdocument_preparation_Request() throws Exception {
		Object[][] obj = new Object[excel.getRowCount()][1];
		for (int i = 1; i <= excel.getRowCount(); i++) {
			HashMap<String, String> testData = excel.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "courseSessionRegistration", enabled = true)
	public void CourseSessionConfigReg(HashMap<String, String> testData) {

//		// CourseSession Configuration 
//		// --------------------------	
		if (isReportedRequired == true) {
		test = extent.createTest("Course Session  Configration & Registration")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Course Session  Configration & Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();

	//	CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Online_DocumentReading_WithOutExam(testData);

		//CourseSession.courseSessionAuditTrails();
//		if (isReportedRequired == true) {
//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");
//		}
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL, Constants.REType);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
		if (isReportedRequired == true) {
		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Individual Employee Report for Course Session Proposed Status");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
				Constants.Session_PROPOSED_FOR_RE, Constants.REType);

		Logout.signOutPage();
	}

	// Test Method for CRespond Document Reading //
	@Test(priority = 5, enabled = true)
	public void respondDocReading() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Respond Document Reading");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getQualifiedTraineeID(),

					CM_CourseSession.QualifiedTraineePsw);

			epiclogin.plant1();
			RespondDR.respondDocReading(CM_CourseSession.QualifiedTraineePsw);
			if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Qualified Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Qualified Trainee");
			}
			//IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED);
			IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report of Document Reading Pending Trainee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report of Document Reading Pending Trainee");
			}
			IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedTraineeID, Constants.Session_PROPOSED_FOR_RE,
					Constants.REType);
//			test = extent.createTest("Check Trainees at Course Session Screen")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Check Trainees at Course Session Screen");
	//
//			CourseSession.verify_Employees_At_Coursesession();

			Logout.signOutPage();
	}
}
