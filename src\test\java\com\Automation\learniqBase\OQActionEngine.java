package com.Automation.learniqBase;

import java.awt.Robot;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.awt.event.KeyEvent;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Stream;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openqa.selenium.Alert;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Keys;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.UnhandledAlertException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Assert;
import org.testng.util.TimeUtils;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.DocumentRegistrationStrings;
import com.Automation.Strings.MDM_Department;
import com.Automation.Strings.RecordAttendanceStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqObjects.CM_CSRReport;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.MediaEntityBuilder;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.Markup;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import java.util.HashMap;
import java.util.HashSet;

public class OQActionEngine extends PageInitializer {
	public static String firstName = "";
	public static String lastName = "";
	public static String employeeID = "";
	public static String FullName = "";
	public static String FullNameemployeeID = "";
	public static int n = 0;
	static String modifiedMarkup = null;
	public static double screenshotCounter = 0;
	public static String tempPwd = "";
	public static String uniqueCode = "";
	public static String masterUniqueCode = "";
	public static String plantUniqueCode = "";
	public static String uniqueCodeTopic = "";
	public static String text = "";
	public static String text1 = "";
	public static String text2 = "";
	public static String text3 = "";
	public static String text4 = "";
	public static String text5 = "";
	public static String text6 = "";
	public static String currentDate = "";
	public static String CourseSessionUniqueCode = "";
	public static String BatchFormationUniqueCode = "";
	public static String sessionNum = "";
	public static String batchsessionNum = "";
	public static String sessionstartDate = "";
	public static String sessionendDate = "";
	public static String sessionStartTime = "";
	public static String sessionEndTime = "";
	public static String sessionLastDateofResponse = "";
	public static String courseUniqueCode = "";
	public static String roleName = "";
	public static String batchFormationEndTime = "";
	public static String initiatorFLName = "";
	public static String UserAcceptinitiatorFLName = "";
	public static String ADAcceptinitiatorFLName = "";
	public static String initiatorFLNameEmpID = "";
	public static String UserAcceptinitiatorFLNameEmpID = "";
	public static String ADAcceptinitiatorFLNameEmpID = "";
	public static String saveIniatiated = "";
	public static String Trainer_Name = "";
	public static String trainer_UniqueCode = "";
	public static String trainer_Qualification = "";
	public static String Trainer_Experience = "";
	public static String trimmedApprroverSubGroup = "";
	public static String sessionTrainingHours = "";
	public static String batchFormationStartTime = "";
	public static String InitiatorFullName = "";
	public static String initiator = "";
	public static String DocEffDate = "";
	public static String nextReviewDate = "";
	public static String DocumentName = "";
	public static String VersionNo = "";
	public static String UserAcceptanceFullName = "";
	public static String UserAcceptanceFullNameEmployeeID = "";
	public static String ExternalCode = "";
	public static String courseSessionTrainingHours = "";
	public static String SessionName = "";
	public static String BatchFormationName = "";
	public static String ADFullName = "";
	public static String ADFullNameEmployeeID = "";
	public static String SessionUniqueCode = "";
	public static String QPName = "";
	public static String QPUniqueCode = "";
	public static String batchUniqueCode = "";
	public static String Convertedtime = "";
	public static String currentWeekDay = "";
	public static String currenttimenew = "";
	public static Duration duration;
	public static int stringToInt;
	public static String[] InProgresUsers;
	public static int InProgresUsersActualCount;
	public static String[] InsideData;
	public static String OutputPath;
	public static String ReinitfirstName = "";
	public static String ReinitlastName = "";
	public static String Empname = "";
	public static String EmpID = "";
	public static String ReinitemployeeID = "";
	public static String ReinitFullName = "";
	public static String EmpNameEmpID = "";
	public static String ReinitFullNameemployeeID = "";
	
	public static String IntiatorNameInitiatorEmpID = "";
	public static String InitiatorEmployeeID = "";
	public static String InitiatorName = "";
 
	public static String ApproverNameInitiatorEmpID = "";
	public static String ApproverEmployeeID = "";
	public static String ApproverName = "";
 
	public static String Re_Int_NameInitiatorEmpID = "";
	public static String Re_Int_EmployeeID = "";
	public static String Re_Int_Name = "";
 
	public static String Mod_Initiator_Name_EmpID = "";
	public static String Mod_Initiator_EmployeeID = "";
	public static String Mod_Initiator_Name = "";
	
	
	public static String UserAcceptanceApproverNameInitiatorEmpID = "";
	public static String UserAcceptanceApproverEmployeeID = "";
	public static String UserAcceptanceApproverName = "";
 
 
 
	JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;

	static Properties prop = ConfigsReader.readProperties("./configs/configuration.properties");

	public OQActionEngine(String url) {

		super(url);
	}

	public OQActionEngine() {

	}

	// ---------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Method for Element Visibile
	 * 
	 */
	public static void waitForElementVisibile(WebElement ele) {

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));
		wait.until(ExpectedConditions.visibilityOf(ele));
	}
	// --------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Methods wait for page Load
	 * 
	 * @param pixel
	 */
	public static void waitForPageToLoad(int timeInMilliSeconds) {
		try {
			Thread.sleep(timeInMilliSeconds);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * this is to capture screen shot as base64 type
	 */

	public static String captureScreen(WebDriver driver, boolean testCaseResult, String elementName) {
		screenshotCounter = screenshotCounter + 1;
		String scrBase64 = null;
		TakesScreenshot ts = (TakesScreenshot) driver;
		scrBase64 = ts.getScreenshotAs(OutputType.BASE64);
		if (testCaseResult) {
			if (prop.getProperty("ScreenCapture").equalsIgnoreCase("YES")) {
				test.pass(MediaEntityBuilder
						.createScreenCaptureFromBase64String(scrBase64, screenshotCounter + " - " + elementName)
						.build());
			}
		} else {
			test.fail(MediaEntityBuilder.createScreenCaptureFromBase64String(scrBase64, elementName).build());
		}
		return scrBase64;
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Highlights a web element by changing its background color to yellow, then
	 * removes the highlight after a short delay.
	 **/
	public static void highLightElement(WebDriver driver, WebElement element, String elementname, ExtentTest test) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		try {
			if (prop.getProperty("HighlightElement").equalsIgnoreCase("YES")) {
				js.executeScript("arguments[0].style.background='yellow'", element);
				Thread.sleep(300);
			}
			Thread.sleep(100);
		} catch (InterruptedException e) {
			System.out.println(e.getMessage());
		} catch (org.openqa.selenium.NoSuchElementException e) {
			System.out.println("");
		}
		js.executeScript("arguments[0].removeAttribute('style','')", element);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Highlights a WebElement on the web page by temporarily changing its border
	 * style to red. This is useful for visually indicating the element in test
	 * reports or screenshots.
	 * 
	 * @param driver  The WebDriver instance used to execute JavaScript on the
	 *                browser.
	 * @param element The WebElement to be highlighted.
	 * @param test    The ExtentTest instance for logging test information.
	 */
	public static void highLightEle1(WebDriver driver, WebElement element, ExtentTest test) {

		JavascriptExecutor js = (JavascriptExecutor) driver;

		js.executeScript("arguments[0].setAttribute('style',' border: 2px solid red;');", element);

		try {

			Thread.sleep(500);

		} catch (InterruptedException e) {

			System.out.println(e.getMessage());
		} catch (org.openqa.selenium.NoSuchElementException e) {

			System.out.println("");

		}

		// js.executeScript("arguments[0].setAttribute('style','border: solid

		// 2px white');", element);

		js.executeScript("arguments[0].setAttribute('style','border: solid 2px transparent');", element);

	}

	// ---------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Highlights the specified web element by changing its background color and
	 * border.
	 **/

	public void highlightEle2(WebElement element) {

		try {
			if (prop.getProperty("ExecutionMode").equalsIgnoreCase("slow")) {
				jsExecutor.executeScript("arguments[0].style.background='yellow'", element);
				jsExecutor.executeScript("arguments[0].style.border='2px solid red'", element);
				jsExecutor.executeScript("arguments[0].style.background=''", element);
				jsExecutor.executeScript("arguments[0].style.border=''", element);
			} else {
				jsExecutor.executeScript("arguments[0].style.background='yellow'", element);
				jsExecutor.executeScript("arguments[0].style.border='2px solid red'", element);
				jsExecutor.executeScript("arguments[0].style.background=''", element);
				jsExecutor.executeScript("arguments[0].style.border=''", element);
			}

		} catch (Exception e) {
			test.log(Status.FAIL, "Exception :" + e.getMessage());

		}
	}

	// ---------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Scrolls the web page to bring the specified web element into view.
	 **/

	public static void scrollToViewElement(WebElement element) {

		JavascriptExecutor js = (JavascriptExecutor) driver;

		js.executeScript("arguments[0].scrollIntoView();", element);

		TimeUtil.shortWait();
	}

	// ---------------------------------------------------------------------------------------------------------------------------------

//	/**
//	 * Method to modify the column width
//	 **/
//
//	private void String modifyColumnWidth(String originalMarkup) {
//
//		String modifiedMarkup = originalMarkup.replaceAll("<table>", "<table style=\"width: 100px;\">");
//
//		modifiedMarkup = modifiedMarkup.replaceAll("<th>", "<th style=\"width: 25px\"; >");
//
//		modifiedMarkup = modifiedMarkup.replaceAll("<td>",
//
//				"<td style=\"max-width: 25px; vertical-align:top!important; word-break: break-all; white-space: normal; word-break: keep-all\";>");
//
//		return modifiedMarkup;
//
//	}

	/**
	 * Method to modify the column width
	 **/

	protected static String modifyColumnWidth(String originalMarkup) {

		String modifiedMarkup = originalMarkup.replaceAll("<table>", "<table style=\"width: 100px;\">");

		modifiedMarkup = modifiedMarkup.replaceAll("<th>", "<th style=\"width: 25px\"; >");

		modifiedMarkup = modifiedMarkup.replaceAll("<td>",

				"<td style=\"max-width: 25px; vertical-align:top!important; word-break: break-all; white-space: normal; word-break: keep-all\";>");

		return modifiedMarkup;

	}

	// -----------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method for switchTo BodyFrame
	 * 
	 */
	public static void switchToBodyFrame(WebDriver driver) {

		driver.switchTo().frame("bodyFrame");
		TimeUtil.shortWait();
	}

	// --------------------------------------------------------------------------------------------------------------------
	/**
	 * Switches the WebDriver context to the default content of the page.
	 **/
	public static void switchToDefaultContent(WebDriver driver) {

		driver.switchTo().defaultContent();
		TimeUtil.shortWait();
	}

	// -----------------------------------------------------------------------------------------------------------------------------
	/**
	 * Switches the WebDriver context to the second window/tab
	 **/
	public static void switchToWindowTwo() {
		Set<String> allWindows = driver.getWindowHandles();
		String windowTwo = (String) allWindows.toArray()[1];
		driver.switchTo().window(windowTwo);
		TimeUtil.shortWait();
	}

	// -----------------------------------------------------------------------------------------------------------------------------
	/**
	 * Switches the WebDriver context to the first window/tab
	 **/
	public static void switchtoWidowOne() {
		Set<String> allwidows = driver.getWindowHandles();
		Object[] windows = allwidows.toArray();
		String win1 = windows[0].toString();
		driver.switchTo().window(win1);
		TimeUtil.shortWait();
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method for isAlertPresent
	 * 
	 * @return
	 */
	public static void isAlertPresent(WebDriver driver) {

		try {
			Alert alert = driver.switchTo().alert();
			alert.accept();
			TimeUtil.shortWait();
		} catch (org.openqa.selenium.NoAlertPresentException e) {
		} catch (UnhandledAlertException e) {
			e.printStackTrace();
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a radio button or checkbox if it is not already selected.
	 * 
	 * @param driver      The WebDriver instance used to interact with the browser.
	 * @param element     The WebElement representing the radio button or checkbox
	 *                    to be selected.
	 * @param elementName A descriptive name for the element, used for reporting
	 *                    purposes.
	 */
	public void SelectRadioBtnAndCheckbox(WebDriver driver, WebElement element, String elementName) {

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		boolean status = element.isSelected();
		if (status == true) {
		} else {
			click2(element, CommonStrings.Call_ESign_RegInitiation_DC.getCommonStrings(),
					CommonStrings.Call_ESign_RegInitiation_AC.getCommonStrings(),
					CommonStrings.Call_ESign_RegInitiation_AR.getCommonStrings(),
					CommonStrings.Call_ESign_RegInitiation_SS.getCommonStrings());

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a radio button or checkbox if it is not already selected.
	 * 
	 * @param driver      The WebDriver instance used to interact with the browser.
	 * @param element     The WebElement representing the radio button or checkbox
	 *                    to be selected.
	 * @param elementName A descriptive name for the element, used for reporting
	 *                    purposes.
	 */
	public void SelectRadioBtnAndCheckboxModification(WebDriver driver, WebElement element, String elementName) {

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		boolean status = element.isSelected();
		if (status == true) {
		} else {
			click2(element, CommonStrings.Call_ESign_Modification_DC.getCommonStrings(),
					CommonStrings.Call_ESign_Modification_AC.getCommonStrings(),
					CommonStrings.Call_ESign_Modification_AR.getCommonStrings(),
					CommonStrings.Call_ESign_Modification_SS.getCommonStrings());

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the confirmation text in a given WebElement matches the
	 * expected format.
	 * 
	 * @param confirmationText The WebElement containing the confirmation text.
	 * @param MenuName         The name of the menu to be included in the expected
	 *                         text.
	 * @param ConfirmationText The text that should appear in the confirmation
	 *                         message.
	 */
	public void verifyConfirmationText(WebElement confirmationText, String MenuName, String ConfirmationText) {

		String text = confirmationText.getText();
		int startIndex = text.indexOf(ConfirmationText);
		String uniqueCodeText = text.substring(startIndex + ConfirmationText.length()).trim();
		verifyExactCaption(confirmationText, MenuName + "\n" + ConfirmationText + "\n" + uniqueCodeText,
				"ConfirmationText");
		System.out
				.println("Expected confirmation text = " + MenuName + "\n" + ConfirmationText + "\n" + uniqueCodeText);
		System.out.println("Actual confirmation text = " + text);

	}

	// --------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a given WebElement exactly matches the expected
	 * caption.
	 * 
	 * @param element         The WebElement whose text is to be verified.
	 * @param expectedCaption The expected caption that the WebElement's text should
	 *                        exactly match.
	 * @param elementName     A descriptive name for the WebElement, used in
	 *                        reporting.
	 */
	 
		public static void verifyExactCaption(WebElement element, String expectedCaption, String elementName) {

			waitForElementVisibile(element);
			highLightElement(driver, element, "elementName", test);
			String actualCaptionValue = element.getText().trim();
			System.out.println(actualCaptionValue);
			System.out.println(expectedCaption);
			try {

				if (actualCaptionValue.equals(expectedCaption)) {

				} else {
					test.fail("Expected Result: " + "<b>" + expectedCaption + "</b>" + " and actual Result was: " + "<b>"
							+ actualCaptionValue + "</b>" + "<b><font color=red> Test failed </font></b>");
					captureScreen(driver, true, expectedCaption);
				}
			} catch (TimeoutException e) {
				test.fail(
						"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
			} catch (AssertionError e) {
				test.fail(elementName + " does not contain the expected caption: " + expectedCaption);
				throw e;
			} catch (Exception e) {
				test.fail("Step failed due to an unexpected exception: " + e.getMessage());
				captureScreen(driver, true, elementName);
			}

		}


	// --------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a given WebElement matches the expected caption
	 * after processing.
	 * 
	 * @param element         The WebElement whose text is to be verified.
	 * @param expectedCaption The expected caption that the WebElement's text should
	 *                        match.
	 * @param elementName     A descriptive name for the WebElement, used in
	 *                        reporting.
	 */
	public static void verifyQuestionAnswers(WebElement element, String expectedCaption, String elementName) {

		waitForElementVisibile(element);

		String actualCaptionValue = element.getText();

		String trimmedValue = actualCaptionValue.substring(actualCaptionValue.lastIndexOf(")") + 1).trim();
		highLightElement(driver, element, "QAText", test);

		System.out.println(trimmedValue);
		System.out.println(expectedCaption);
		try {

			if (trimmedValue.equals(expectedCaption)) {
			} else {
				test.fail("Expected Result: " + "<b>" + expectedCaption + "</b>" + " and actual Result was: " + "<b>"
						+ trimmedValue + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, expectedCaption);
				Assert.assertEquals(trimmedValue, expectedCaption,
						"The actual Value does not match the expected Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + expectedCaption);
			throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}

	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the value of a given WebElement matches the expected value.
	 * 
	 * @param element            The WebElement whose value is to be verified.
	 * @param expectedFeildValue The expected value that the WebElement should
	 *                           contain.
	 * @param elementName        A descriptive name for the WebElement, used in
	 *                           reporting.
	 */
	public static void verifyExactValueInFeild(WebElement element, String expectedFeildValue, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, "elementName", test);
		String actualCaptionValue = element.getAttribute("value");
		System.out.println(actualCaptionValue);
		System.out.println(expectedFeildValue);
		try {
			if (actualCaptionValue.equals(expectedFeildValue)) {
			} else {
				test.fail("Expected Result: " + "<b>" + expectedFeildValue + "</b>" + " and actual Result was: " + "<b>"
						+ actualCaptionValue + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, expectedFeildValue);
//				Assert.assertEquals(actualCaptionValue, expectedFeildValue,
//						"The actual Value does not match the expected Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + expectedFeildValue);
			throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method saves the unique code from the given WebElement
	 * 
	 * @param driver
	 * @param element
	 */
	public static void saveUniqueCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = element.getText().trim();
		String[] str = message.split(":");
		String saveuniqueCode = str[1].trim();
		uniqueCode = saveuniqueCode;
		System.out.println("Unique code value=" + saveuniqueCode);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts and saves the unique code from the specified web element for a
	 * master entity.
	 **/
	public static void saveMasterUniqueCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = element.getText().trim();
		String[] str = message.split(":");
		String saveMasteruniqueCode = str[1].trim();
		masterUniqueCode = saveMasteruniqueCode;
		System.out.println("Master Unique code value=" + saveMasteruniqueCode);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts and saves the unique code from the specified web element for a
	 * plant.
	 **/
	public static void savePlantUniqueCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = element.getText().trim();
		String[] str = message.split(":");
		String savePlantuniqueCode = str[1].trim();
		plantUniqueCode = savePlantuniqueCode;
		System.out.println("Plant Unique code value=" + savePlantuniqueCode);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts and saves the unique code from the specified web element for a
	 * topic.
	 **/
	public static void saveTopicUniqueCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = "";
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		message = element.getText();
		String[] str = message.split(":");
		String saveuniqueCode = str[1];
		saveuniqueCode = saveuniqueCode.trim();
		uniqueCodeTopic = saveuniqueCode;
		System.out.println("Unique code value=" + saveuniqueCode);

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts and saves the unique code from the specified web element for course
	 * session.
	 **/
	public static void saveCourseSessionUniqueCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = element.getText().trim();
		String[] str = message.split(":");
		String saveuniqueCode = str[1].trim();
		CourseSessionUniqueCode = saveuniqueCode;
		String trimSessionNum = saveuniqueCode.substring(saveuniqueCode.lastIndexOf("/") + 1);
		sessionNum = trimSessionNum;
		System.out.println("Unique code value=" + saveuniqueCode);
	}

	// --------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the unique code displayed in a web element matches the expected
	 * value.
	 **/
	public static void verifyUniqueCode(WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String UniqueCode = uniqueCode;
		String actualUniqueCodeValue = element.getText();
		System.out.println(actualUniqueCodeValue);
		System.out.println(UniqueCode);
		try {
			if (actualUniqueCodeValue.equals(UniqueCode)) {
			} else {
				test.fail("Expected result: " + "<b>" + UniqueCode + "</b>" + ", and actual result was: " + "<b>"
						+ actualUniqueCodeValue + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualUniqueCodeValue, UniqueCode,
						"The actual Feild Value does not match the expected Feild Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + UniqueCode);
			// throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts and saves the unique course code from a specified web element.
	 **/
	public static void saveCourseUniqueCode(WebDriver driver, WebElement element) {
		String message = "";
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		message = element.getText();
		String[] str = message.split(":");
		String saveuniqueCode = str[1];
		saveuniqueCode = saveuniqueCode.trim();
		courseUniqueCode = saveuniqueCode;
		System.out.println("Unique code value=" + saveuniqueCode);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the unique course code displayed in a specified web element
	 * matches the expected value.
	 */
	public static void verifyCourseUniqueCode(WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, "elementName", test);
		// String UniqueCode = uniqueCode2;
		String UniqueCode = courseUniqueCode;
		String actualUniqueCodeValue = element.getText();
		System.out.println(actualUniqueCodeValue);
		System.out.println(UniqueCode);
		try {
			if (actualUniqueCodeValue.equals(UniqueCode)) {
			} else {
				test.fail("Expected result: " + "<b>" + UniqueCode + "</b>" + ", and actual result was: " + "<b>"
						+ actualUniqueCodeValue + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualUniqueCodeValue, UniqueCode,
						"The actual Feild Value does not match the expected Feild Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + UniqueCode);
			// throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the trainer's name from a specified web element and logs
	 * its value.
	 */
	public static void saveTrainerName(WebDriver driver, WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		System.out.println("FeildValueis=" + actualFeildValue);
		Trainer_Name = actualFeildValue;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the session name from a specified web element and logs
	 * its value.
	 * 
	 */
	public static void saveSessionName(WebDriver driver, WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		System.out.println("FeildValueis=" + actualFeildValue);
		SessionName = actualFeildValue;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the unique code from a specified web element and logs its
	 * value.
	 **/
	public static void saveSessionUniqueCode(WebDriver driver, WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		System.out.println("FeildValueis=" + actualFeildValue);
		SessionUniqueCode = actualFeildValue;

	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Enters a trainer's name into the specified input field, appending a percent
	 * sign to the name.
	 * 
	 */
	public void enterTrainerName(WebDriver driver, WebElement element) {

		waitForElementVisibile(element);
		// element.sendKeys(text + "%");
		// element.sendKeys(Trainer_Name);
		sendKeys2(element, TrainerStrings.enterTrainerName_DC.getTrainerStrings(), Trainer_Name + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TrainerStrings.enterTrainerName_SS.getTrainerStrings());
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the trainer's qualification from a specified field.
	 */
	public static void save_Trainer_Qualification(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		trainer_Qualification = actualFeildValue;
		System.out.println("actualFeildValue=" + actualFeildValue);

	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the trainer's experience from a specified field.
	 **/
	public static void save_Trainer_Experience(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		Trainer_Experience = actualFeildValue;
		System.out.println("actualFeildValue=" + actualFeildValue);

	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the unique code from a specified field.
	 **/
	public static void saveUniqueCodeInFeild(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		trainer_UniqueCode = actualFeildValue;
//		System.out.println("trainerUniqueCodeInFeild=" + actualFeildValue);

	}

	// --------------------------------------------------------------------------------------------------------------------------
	/**
	 * Enters an electronic signature password if the e-signature modal is
	 * displayed.
	 **/
	public void Esign(String eSignpsw) {
		try {
			boolean esign = driver.findElement(By.xpath(
					"//div[@class=\"modal-content esign-body\"]//div//span[contains(text(),'Meaning of This Electronic Signature')]"))
					.isDisplayed();
			if (esign == true) {
				WebElement esign_psw = driver.findElement(By.xpath("//input[@id='txtESignPassword']"));
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), eSignpsw,
						CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
						CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (NoSuchElementException e) {
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the current date from a calendar or date picker.
	 **/
	public static void selectCurrentdate() {
		try {
			WebElement currentDate = driver.findElement(By.xpath("//td[contains(@class,'today')]"));
			currentDate.click();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the start date for the current date based on whether it's a weekend
	 * or a weekday.
	 **/
	public void selectStartdateCurrentdate() {

		try {

			if (!currentWeekDay.equals("Saturday") || !currentWeekDay.equals("Sunday")) {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("//td[contains(@class,'today active start-date active end-date available')]"));

				WebElement startDate = from_date_tr.get(0);
				click2(startDate, CourseSessionStrings.SelectStartDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectStartDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectStartDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.StartDate_SS.getCourseSessionStrings());
			}

			else {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("//td[@class='today weekend active start-date active end-date available']"));

				WebElement startDate = from_date_tr.get(0);
				click2(startDate, CourseSessionStrings.SelectStartDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectStartDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectStartDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.StartDate_SS.getCourseSessionStrings());

			}

		} catch (Exception e1) {

			e1.printStackTrace();
		}

	}

	// -----------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the end date for the current date based on whether it's a weekend or
	 * a weekday.
	 */
	public void selectEnddateCurrentdate() {

		try {

			if (!currentWeekDay.equals("Saturday") || !currentWeekDay.equals("Sunday")) {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("(//td[contains(@class,'today active start-date active end-date available')])[2]"));

				WebElement enddate = from_date_tr.get(0);

				click2(enddate, CourseSessionStrings.SelectEndDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectEndDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectEndDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.EndDate_SS.getCourseSessionStrings());
			} else {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("(//td[@class='today weekend active start-date active end-date available'])[2]"));

				WebElement enddate = from_date_tr.get(0);

				click2(enddate, CourseSessionStrings.SelectEndDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectEndDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectEndDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.EndDate_SS.getCourseSessionStrings());

			}

		} catch (Exception e1) {

			e1.printStackTrace();
		}

	}
//}}
	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the last available date for the current date based on whether it's a
	 * weekend or a weekday.
	 **/
	public void selectlastdateCurrentdate() {
//
		try {

			if (!currentWeekDay.equals("Saturday") || !currentWeekDay.equals("Sunday")) {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("(//td[contains(@class,'today active start-date active end-date available')])[3]"));

				WebElement lastDateofResp = from_date_tr.get(0);

				click2(lastDateofResp, CourseSessionStrings.SelectLastDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectLastDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectLastDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.Response_SS.getCourseSessionStrings());
			} else {

				List<WebElement> from_date_tr = driver.findElements(
						By.xpath("(//td[@class='today weekend active start-date active end-date available'])[3]"));

				WebElement lastDateofResp = from_date_tr.get(0);

				click2(lastDateofResp, CourseSessionStrings.SelectLastDate_DC.getCourseSessionStrings(),
						CourseSessionStrings.SelectLastDate_AC.getCourseSessionStrings(),
						CourseSessionStrings.SelectLastDate_AR.getCourseSessionStrings(),
						CourseSessionStrings.Response_SS.getCourseSessionStrings());

			}
		} catch (Exception e1) {

			e1.printStackTrace();
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a specific effective date from a calendar widget based on month,
	 * year, and day.
	 **/
	public void selectEffectiveFrom2(String month, String year, String day) {

		DocEffDate = day + " " + "May" + " " + year;

		WebElement XpathYear = driver
				.findElement(By.xpath("//div[@class='drp-calendar left single']//select[@class='yearselect']"));

		click2(XpathYear, DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());

		List<WebElement> availableYears = driver
				.findElements(By.xpath("//div[@class='drp-calendar left single']//select[@class='yearselect']/option"));
		for (WebElement requiredyear : availableYears) {

			String requiredYearText = requiredyear.getText();

			if (requiredYearText.equals(year)) {

				click2(requiredyear,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}
		}

		List<WebElement> availableMonths = driver
				.findElements(By.xpath("//body/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[1]/option"));
		for (WebElement requiredmonth : availableMonths) {

			String requiredmonthText = requiredmonth.getText();

			if (requiredmonthText.equals(month)) {

				click2(requiredmonth,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}
		}

		List<WebElement> availableDates = driver.findElements(By.xpath(
				"//body[1]/div[2]/div[2]/div[1]/table[1]//tbody//tr//td[@class='available' or @class='weekend available']"));
		for (WebElement requireDate : availableDates) {

			String requireDateText = requireDate.getText();

			if (requireDateText.equals(day)) {

				click2(requireDate,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a specific review date from a calendar widget based on month, year,
	 * and day.
	 */
	public void selectReviewFrom2(String month, String year, String day) {

		WebElement XpathYear = driver
				.findElement(By.xpath("//div[@class='drp-calendar left single']//select[@class='yearselect']"));

		nextReviewDate = day + " " + "May" + " " + year;
		click2(XpathYear, DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
				DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());

		List<WebElement> availableYears = driver
				.findElements(By.xpath("//div[@class='drp-calendar left single']//select[@class='yearselect']/option"));
		for (WebElement requiredyear : availableYears) {

			String requiredYearText = requiredyear.getText();

			if (requiredYearText.equals(year)) {

				click2(requiredyear,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}
		}

		List<WebElement> availableMonths = driver
				.findElements(By.xpath("//body/div[3]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[1]/option"));
		for (WebElement requiredmonth : availableMonths) {

			String requiredmonthText = requiredmonth.getText();

			if (requiredmonthText.equals(month)) {

				click2(requiredmonth,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}
		}

		List<WebElement> availableDates = driver.findElements(By.xpath(
				"//body[1]/div[3]/div[2]/div[1]/table[1]//tbody//tr//td[@class='available' or @class='weekend available']"));
		for (WebElement requireDate : availableDates) {

			String requireDateText = requireDate.getText();

			if (requireDateText.equals(day)) {

				click2(requireDate,
						DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;
			}

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a specific date on a qualification or requalification calendar.
	 */
	private static final String AVAILABLE_DATES_XPATH1 = "//body[1]/div[2]/div[2]/div[1]/table[1]//tbody//tr//td[contains(@class,'available')]";

	public void selectDateonReQualification2(WebElement Melement, WebElement Yelement, String month, String year,

			String day) {

		waitForElementVisibile(Melement);

		String actualMonth = Melement.getText();

		String actualYear = Yelement.getText();

		if (!actualYear.equals(year)) {

			selectOptionFromDropdownByName(Yelement, year, "year");

			if (!actualMonth.equals(month)) {

				selectOptionFromDropdownByName(Melement, month, "month");

			}

		}

		List<WebElement> availableDates = driver.findElements(By.xpath(AVAILABLE_DATES_XPATH1));

		for (WebElement date : availableDates) {

			String dayValue = date.getText();

			if (dayValue.equals(day)) {

				click2(date, TrainerStrings.Select_RequalificationDate_DC.getTrainerStrings(),
						TrainerStrings.Select_RequalificationDate_AC.getTrainerStrings(),
						TrainerStrings.Select_RequalificationDate_AR.getTrainerStrings(),
						TrainerStrings.Select_RequalificationDate_SS.getTrainerStrings());
				break;

			}

		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects an option from a dropdown menu by its visible text
	 **/

	public static void selectOptionFromDropdownByName(WebElement element, String option, String elementname) {

		waitForElementVisibile(element);

		highLightElement(driver, element, elementname, test);

		Select select = new Select(element);

		try {

			select.selectByVisibleText(option);

			String selectedOption = select.getFirstSelectedOption().getText();

			if (selectedOption.equals(option)) {

			} else {

			}

		} catch (NoSuchElementException e) {

			// throw e;

		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Removes focus from the specified web element by sending a TAB key press.
	 **/
	public static void removeFocus(WebElement element) {
		try {
			waitForElementVisibile(element);
			WebElement webElement = element;
			webElement.sendKeys(Keys.TAB);
			// test.log(Status.PASS, "Focus has been changed successfully.");
			if (prop.getProperty("ScreenCapture").equalsIgnoreCase("YES")) {
			}
		} catch (org.openqa.selenium.NoSuchElementException e) {
		} catch (org.openqa.selenium.TimeoutException e) {
			try {
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} catch (org.openqa.selenium.WebDriverException e) {

			try {

			} catch (Exception e1) {

				e1.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the value of a temporary password from a specified web
	 * element.
	 **/
	public static void saveTempPassword(WebDriver driver, WebElement element, ExtentTest test) {
		String tempPwd = "";
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		tempPwd = element.getAttribute("value");
		System.out.println("temporary password is:" + tempPwd);
		// test.pass("Successfully saved the value (" + tempPwd + ").");
		OQActionEngine.tempPwd = tempPwd;

	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method scrolls a PDF viewer element within an iframe up and down to
	 * simulate user interaction.
	 **/
	public static void documentScrollUpDown() {

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
		driver.switchTo().frame(driver.findElement(By.xpath("//iframe[@class='w-100 h-100']")));
		WebElement pdf_element = driver.findElement(By.xpath("//div[@id='viewerContainer']"));
		JavascriptExecutor js = (JavascriptExecutor) driver;
		try {
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop += 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop += 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop += 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop += 100", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop -= 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop -= 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop -= 300", pdf_element);
			Thread.sleep(400);
			js.executeScript("arguments[0].scrollTop -= 100", pdf_element);
			switchToDefaultContent(driver);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * Verifies Initiated status in audit trails
	 */

	public void verifyAuditTrailsCompareTRNIntiatedStatus(WebElement auditCompareTRNActionValue,
			WebElement auditCompareTRNActionByValue, WebElement auditCompareTRNDateTimeValue,
			WebElement auditCompareTRNRemarksVal1, String InitActionVal, String InitActionByVal, String InitRemarks) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNActionValue);
		verifyExactCaption(auditCompareTRNActionValue, InitActionVal, "InitiationActionValue");
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNRemarksVal1, InitRemarks, "Remarks / Reasons");
	}

	// Modification remarks
	public void verifyAuditTrailsMainTRNIntiatedStatus(WebElement auditCompareTRNActionValue,
			WebElement auditCompareTRNActionByValue, WebElement auditCompareTRNDateTimeValue,
			WebElement auditCompareTRNRemarksVal1, String InitActionVal, String InitActionByVal, String ModRemarks) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNActionValue);
		verifyExactCaption(auditCompareTRNActionValue, InitActionVal, "InitiationActionValue");
		verifyExactCaption(auditCompareTRNActionByValue, InitActionByVal, "InitiationActionValue");
		verifyExactCaption(auditCompareTRNActionByValue, InitActionByVal, "InitiationActionByValue");
		getExactDate(auditCompareTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNRemarksVal1, ModRemarks, "Remarks / Reasons");
	}

	public void verifyAuditTrailsCompareTRNApprovedStatus(WebElement auditCompareTRNApproveActionValue,
			WebElement auditCompareTRNApproveActionByValue, WebElement auditCompareTRNApproveDateTimeValue,
			WebElement auditCompareTRNApproveRemarksValue, String AppActionVal, String AppActionByVal,
			String ApproveRemarks) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNApproveActionValue);
		verifyExactCaption(auditCompareTRNApproveActionValue, AppActionVal, "ApproveActionValue");
		verifyExactCaption(auditCompareTRNApproveActionByValue, AppActionByVal, "ApproveActionByValue");
		validateMultipleDateFormats(auditCompareTRNApproveDateTimeValue);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNApproveRemarksValue, ApproveRemarks, "Remarks / Reasons");
	}

	public void verifyAuditTrailsCompareTRNAcceptApprovedStatus(WebElement auditCompareTRNUserAccepApproveActionValue,
			WebElement auditCompareTRNUserAccepApproveActionByValue,
			WebElement auditCompareTRNUserAccepApproveDateTimeValue,
			WebElement auditCompareTRNUserAccepApproveRemarksValue, String useracceptAppActionVal,
			String useracceptAppActionByVal, String ApproveRemarks) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNUserAccepApproveActionValue);
		verifyExactCaption(auditCompareTRNUserAccepApproveActionValue, useracceptAppActionVal, "ApproveActionValue");
		verifyExactCaption(auditCompareTRNUserAccepApproveActionByValue, useracceptAppActionByVal,
				"ApproveActionByValue");
		validateMultipleDateFormats(auditCompareTRNUserAccepApproveDateTimeValue);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNUserAccepApproveRemarksValue, ApproveRemarks, "Remarks / Reasons");
	}

	public void verifyAuditTrailsCompareTRNAcceptADApprovedStatus(WebElement auditCompareTRNADApproveActionValue,
			WebElement auditCompareTRNADApproveActionByValue, WebElement auditCompareTRNADApproveDateTimeValue,
			WebElement auditCompareTRNADApproveRemarksValue, String acceptADAppActionVal, String acceptADAppActionByVal,
			String ApproveRemarks) {

		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNADApproveActionValue);
		verifyExactCaption(auditCompareTRNADApproveActionValue, acceptADAppActionVal, "ApproveActionValue");
		verifyExactCaption(auditCompareTRNADApproveActionByValue, acceptADAppActionByVal, "ApproveActionByValue");
		validateMultipleDateFormats(auditCompareTRNADApproveDateTimeValue);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNADApproveRemarksValue, ApproveRemarks, "Remarks / Reasons");
	}

	public void verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(WebElement auditCompareTRNApprovalReqVal,
			WebElement auditCompareTRNApprovalComVal, String NoofApprovalsRequired, String NoofApprovalsCompleted) {
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNApprovalReqVal);
		verifyExactCaption(auditCompareTRNApprovalReqVal, NoofApprovalsRequired, "No of Apprvals Required");
		verifyExactCaption(auditCompareTRNApprovalComVal, NoofApprovalsCompleted, "No of Apprvals Completed");
	}

//

	/*
	 * // *Modification Verifies Approvals Required and Completed values in audit
	 * trails //
	 */
//
	public void verifyAuditTrailsMainTRNApprovalsRequiredCompleted(WebElement auditMainTRNApprovalReqVal,
			WebElement auditMainTRNApprovalComVal, String NoofApprovalsRequired, String NoofApprovalsCompleted) {
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditMainTRNApprovalReqVal);
		verifyExactCaption(auditMainTRNApprovalReqVal, NoofApprovalsRequired, "No of Apprvals Required");
		verifyExactCaption(auditMainTRNApprovalComVal, NoofApprovalsCompleted, "No of Apprvals Completed");
	}

//	// ---------------------------------------------------------------------------------------------------------------------------------
//
//	/*
//	 * Verifies Final Status in audit trails
//	 */
//
	public void verifyAuditTrailsCompareTRNFinalStatus(WebElement auditCompareTRNFinalStatus, String FinalStatusValue) {
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditCompareTRNFinalStatus);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNFinalStatus, FinalStatusValue, "Final Status:Value");
		TimeUtil.shortWait();
	}

	// Modification Final Status
	public void verifyAuditTrailsMainTRNFinalStatus(WebElement auditMainTRNFinalStatus, String FinalStatusValue) {
		JavascriptExecutor js = (JavascriptExecutor) driver;
		js.executeScript("arguments[0].scrollIntoView();", auditMainTRNFinalStatus);
		TimeUtil.shortWait();
		verifyExactCaption(auditMainTRNFinalStatus, FinalStatusValue, "Final Status:Value");
		TimeUtil.shortWait();
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * This method will add +1 to minutes the current time
	 */

	public static void selectStartTimeInCourseSession() {

		LocalDateTime currentTime = LocalDateTime.now();
		TimeUtil.shortWait();
		LocalDateTime updatedTime = currentTime.plusMinutes(2);
		TimeUtil.shortWait();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		TimeUtil.shortWait();
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		TimeUtil.shortWait();
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		TimeUtil.shortWait();
		WebElement startTimeHourDropDown = driver.findElement(By.id("StartTime_Hrs_0"));
		TimeUtil.shortWait();
		// startTimeHourDropDown.click();

		Select se = new Select(startTimeHourDropDown);

		se.selectByVisibleText(currentTimeHoursValue);
		removeFocus(startTimeHourDropDown);
		WebElement startTimeMinuteDropDown = driver.findElement(By.id("StartTime_Mins_0"));
		Select se1 = new Select(startTimeMinuteDropDown);
		se1.selectByVisibleText(currentTimeMinutesValue);
		TimeUtil.shortWait();
		removeFocus(startTimeMinuteDropDown);
		text3 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}

	/**
	 * This method sets the start time for a course session in a web application
	 * using a specific format. It calculates the start time by adding 4 minutes to
	 * the current time, formats it to "HH:mm", and then selects the corresponding
	 * hour and minute values from dropdown menus on the webpage.
	 **/
	public void selectStartTimeInCourseSessionOQFormat() {

		LocalDateTime currentTime = LocalDateTime.now();
		TimeUtil.shortWait();
		LocalDateTime updatedTime = currentTime.plusMinutes(4);
		TimeUtil.shortWait();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		TimeUtil.shortWait();
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		TimeUtil.shortWait();
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		sessionStartTime = currentTimeHoursValue + ":" + currentTimeMinutesValue;
		TimeUtil.shortWait();
		WebElement startTimeHourDropDown = driver.findElement(By.id("StartTime_Hrs_0"));
		TimeUtil.shortWait();

		click2(startTimeHourDropDown, CourseSessionStrings.Click_StartTime_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_SS.getCourseSessionStrings());

		List<WebElement> opt = driver.findElements(By.xpath("//*[@id='StartTime_Hrs_0']/option"));
		for (int j = 0; j < opt.size(); j++) {
			// if the option is By Subject click that option
			if (opt.get(j).getText().equals(currentTimeHoursValue)) {
				WebElement hr = opt.get(j);

				clickwithRemoveFocus(hr, CourseSessionStrings.selectHH_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_SS.getCourseSessionStrings());

			}
		}

		WebElement startTimeMinuteDropDown = driver.findElement(By.id("StartTime_Mins_0"));

		TimeUtil.shortWait();

		click2(startTimeMinuteDropDown, CourseSessionStrings.Click_StartTime_Min_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_SS.getCourseSessionStrings());

		List<WebElement> optmin = driver.findElements(By.xpath("//*[@id='StartTime_Mins_0']/option"));
		for (int k = 0; k < optmin.size(); k++) {
			// if the option is By Subject click that option
			if (optmin.get(k).getText().equals(currentTimeMinutesValue)) {
				WebElement min = optmin.get(k);

				clickwithRemoveFocus(min, CourseSessionStrings.selectMIN_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_SS.getCourseSessionStrings());

			}
		}

		text3 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method sets the start time for a course session in a web application. It
	 * calculates the start time by adding 2 minutes to the current time, formats it
	 * to "HH:mm", and then selects the corresponding hour and minute values from
	 * dropdown menus on the webpage.
	 **/
	public void selectStartTimeInCourseSession2() {

		LocalDateTime currentTime = LocalDateTime.now();
		TimeUtil.shortWait();
		LocalDateTime updatedTime = currentTime.plusMinutes(2);
		TimeUtil.shortWait();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		TimeUtil.shortWait();
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		TimeUtil.shortWait();
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		TimeUtil.shortWait();
		WebElement startTimeHourDropDown = driver.findElement(By.id("StartTime_Hrs_0"));
		TimeUtil.shortWait();
		click2(startTimeHourDropDown, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		Select se = new Select(startTimeHourDropDown);
		se.selectByVisibleText(currentTimeHoursValue);
		removeFocus(startTimeHourDropDown);
		WebElement startTimeMinuteDropDown = driver.findElement(By.id("StartTime_Mins_0"));
		Select se1 = new Select(startTimeMinuteDropDown);
		se1.selectByVisibleText(currentTimeMinutesValue);
		TimeUtil.shortWait();
		removeFocus(startTimeMinuteDropDown);
		text3 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * This method will add +30 to minutes the current time
	 */

	public static void selectEndTimeInCourseSession() {

		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime updatedTime = currentTime.plusMinutes(20);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		WebElement endTimeHourDropDown = driver.findElement(By.id("EndTime_Hrs_0"));

		Select se = new Select(endTimeHourDropDown);
		se.selectByVisibleText(currentTimeHoursValue);
		removeFocus(endTimeHourDropDown);
		WebElement endTimeMinuteDropDown = driver.findElement(By.id("EndTime_Mins_0"));
		TimeUtil.shortWait();
		Select se1 = new Select(endTimeMinuteDropDown);
		se1.selectByVisibleText(currentTimeMinutesValue);
		TimeUtil.shortWait();
		removeFocus(endTimeMinuteDropDown);
		text4 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method sets the end time for a course session in a web application using
	 * a specific format. It calculates the end time by adding 37 minutes to the
	 * current time, formats it to "HH:mm", and then selects the corresponding hour
	 * and minute values from dropdown menus on the webpage.
	 **/
	public void selectEndTimeInCourseSessionOQFormat() {

		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime updatedTime = currentTime.plusMinutes(37);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		sessionEndTime = currentTimeHoursValue + ":" + currentTimeMinutesValue;
		WebElement endTimeHourDropDown = driver.findElement(By.id("EndTime_Hrs_0"));

		click2(endTimeHourDropDown, CourseSessionStrings.Click_EndTime_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_EndTime_SS.getCourseSessionStrings());

		List<WebElement> opt = driver.findElements(By.xpath("//*[@id='EndTime_Hrs_0']/option"));

		int s = opt.size();

		for (int j = 0; j < opt.size(); j++) {
			// if the option is By Subject click that option
			if (opt.get(j).getText().equals(currentTimeHoursValue)) {
				WebElement hr = opt.get(j);

				clickwithRemoveFocus(hr, CourseSessionStrings.selectHH_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_SS.getCourseSessionStrings());

			}
		}

		WebElement endTimeMinuteDropDown = driver.findElement(By.id("EndTime_Mins_0"));
		click2(endTimeMinuteDropDown, CourseSessionStrings.Click_EndTime_Min_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_EndTime_SS.getCourseSessionStrings());

		List<WebElement> optmin = driver.findElements(By.xpath("//*[@id='EndTime_Mins_0']/option"));
		int m = optmin.size();
		for (int k = 0; k < optmin.size(); k++) {
			// if the option is By Subject click that option
			if (optmin.get(k).getText().equals(currentTimeMinutesValue)) {
				WebElement min = optmin.get(k);
				clickwithRemoveFocus(min, CourseSessionStrings.selectMIN_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_SS.getCourseSessionStrings());

			}
		}

		TimeUtil.shortWait();
		text4 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * This method updates the end time of a course session in a web application. It
	 * retrieves the current time, adds 20 minutes to it, and formats it as "HH:mm".
	 * Then, it sets the end time for the course session using dropdown menus for
	 * hours and minutes.
	 * 
	 * 1. Fetches the current date and time. 2. Adds 20 minutes to the current time.
	 * 3. Formats the updated time to "HH:mm". 4. Extracts hours and minutes from
	 * the formatted time. 5. Selects the corresponding hour and minute values from
	 * dropdown menus on the webpage. 6. Updates the class variable `text5` with the
	 * new end time in "HH:mm" format.
	 */
	public static void modifyEndTimeInCourseSession() {

		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime updatedTime = currentTime.plusMinutes(20);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		String currentTimeHoursValue = formattedTime.substring(0, 2);
		String currentTimeMinutesValue = formattedTime.substring(3, 5);
		WebElement endTimeHourDropDown = driver.findElement(By.id("EndTime_Hrs_0"));
		Select se = new Select(endTimeHourDropDown);
		se.selectByVisibleText(currentTimeHoursValue);
		removeFocus(endTimeHourDropDown);
		WebElement endTimeMinuteDropDown = driver.findElement(By.id("EndTime_Mins_0"));
		Select se1 = new Select(endTimeMinuteDropDown);
		se1.selectByVisibleText(currentTimeMinutesValue);
		TimeUtil.shortWait();
		removeFocus(endTimeMinuteDropDown);
		text5 = currentTimeHoursValue + ":" + currentTimeMinutesValue;
	}

	// ---------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the training hours from a specific field in the course
	 * session.
	 * 
	 * This method extracts the training hours from the specified WebElement,
	 * formats it, and stores it in a class-level variable.
	 * 
	 * @param driver      The WebDriver instance used to interact with the browser.
	 * @param element     The WebElement representing the field containing the
	 *                    training hours.
	 * @param elementName A descriptive name for the element, used for reporting
	 *                    purposes.
	 */
	public static void saveTrainingHoursInCourseSession(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		String timeValue = actualFeildValue;
		String[] textValueArray = timeValue.split(":");
		int hours = Integer.parseInt(textValueArray[0]);
		String modifiedHours = String.valueOf(hours);
		String TextValue = modifiedHours + ":" + textValueArray[1];
		courseSessionTrainingHours = TextValue;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the start time for batch formation in the "Course Session" form using
	 * the OQ format.
	 * 
	 * This method sets the start time to the current time plus two minutes and
	 * updates both hours and minutes using dropdown menus.
	 */
	public void selectStartTimeInBatchFormationOQFormat() {

		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime updatedTime = currentTime.plusMinutes(2);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		String currentTimeHrsValue = formattedTime.substring(0, 2);
		String currentTimeMinValue = formattedTime.substring(3, 5);
		WebElement startTimeHourDropDown = driver.findElement(By.id("StartTime_Hrs"));
		JavascriptExecutor js2 = (JavascriptExecutor) driver;
		js2.executeScript("arguments[0].scrollIntoView();", startTimeHourDropDown);
		click2(startTimeHourDropDown, CourseSessionStrings.Click_StartTime_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_SS.getCourseSessionStrings());
		List<WebElement> opt = driver.findElements(By.xpath("//*[@id='StartTime_Hrs']/option"));
		int s = opt.size();
		for (int j = 0; j < opt.size(); j++) {
			// if the option is By Subject click that option
			if (opt.get(j).getText().equals(currentTimeHrsValue)) {
				WebElement hr = opt.get(j);
				clickwithRemoveFocus(hr, CourseSessionStrings.selectHH_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectHH_SS.getCourseSessionStrings());

			}
		}
		WebElement endTimeMinuteDropDown = driver.findElement(By.id("StartTime_Mins"));
		js2.executeScript("arguments[0].scrollIntoView();", endTimeMinuteDropDown);
		click2(endTimeMinuteDropDown, CourseSessionStrings.Click_StartTime_Min_DC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AC.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_AR.getCourseSessionStrings(),
				CourseSessionStrings.Click_StartTime_Min_SS.getCourseSessionStrings());
		List<WebElement> optmin = driver.findElements(By.xpath("//*[@id='StartTime_Mins']/option"));
		int m = optmin.size();
		for (int k = 0; k < optmin.size(); k++) {
			if (optmin.get(k).getText().equals(currentTimeMinValue)) {
				WebElement min = optmin.get(k);
				clickwithRemoveFocus(min, CourseSessionStrings.selectMIN_DC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AC.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_AR.getCourseSessionStrings(),
						CourseSessionStrings.selectMIN_SS.getCourseSessionStrings());
			}
		}
		TimeUtil.shortWait();
		batchFormationStartTime = currentTimeHrsValue + ":" + currentTimeMinValue;
		// TimeUtil.waitHalfmin();
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects the end time for attendance in the "Record Attendance" form using the
	 * OQ format.
	 * 
	 * This method sets the end time to the current time and updates both hours and
	 * minutes using dropdown menus.
	 */
	public void selectEndTimeInRecordAttendanceOQFormat() {

		TimeUtil.extralongwait();
		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime updatedTime = currentTime.plusMinutes(0);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = updatedTime.format(formatter);
		String currentTimeHrsValue = formattedTime.substring(0, 2);
		String currentTimeMinValue = formattedTime.substring(3, 5);
		WebElement endTimeHourDropDown = driver.findElement(By.xpath("//span[@id='select2-EndTime_Hrs-container']"));
		// endTimeHourDropDown.click();
		click2(endTimeHourDropDown, RecordAttendanceStrings.Click_HH_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		WebElement endTimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		sendKeys2(endTimeDropDownSendText, RecordAttendanceStrings.Enter_End_Time_DC.getRecordAttendanceStrings(),
				currentTimeHrsValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		// endTimeDropDownSendText.sendKeys(currentTimeHrsValue);
		WebElement selecthrs1 = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs1.click();
		TimeUtil.shortWait();
		click2(selecthrs1, RecordAttendanceStrings.Click_HH_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		WebElement endTimeMinutesDropDown = driver.findElement(By.id("select2-EndTime_Mins-container"));
		// endTimeMinutesDropDown.click();
		click2(endTimeMinutesDropDown, RecordAttendanceStrings.Click_Min_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_SS.getRecordAttendanceStrings());

		WebElement endtimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		// endtimeDropDownSendText.sendKeys(currentTimeMinValue);

		sendKeys2(endtimeDropDownSendText, RecordAttendanceStrings.Enter_End_TimeMin_DC.getRecordAttendanceStrings(),
				currentTimeMinValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		WebElement selecthrs2 = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs2.click();

		click2(selecthrs2, RecordAttendanceStrings.Click_Min_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Attempts to click on a specified WebElement. If the initial click fails, the
	 * method retries up to 3 times using JavaScript click and then falls back to
	 * the Actions class for the click operation if necessary. Logs the result of
	 * each step and captures screenshots for validation.
	 * 
	 * @param element            The WebElement to be clicked.
	 * @param stepDescription    A description of the step being performed.
	 * @param acceptanceCriteria The criteria that must be met for the test to be
	 *                           considered successful.
	 * @param actualResult       The actual result observed after performing the
	 *                           action.
	 * @param scName             The name used for screenshot files.
	 */
	public void click2(WebElement element, String stepDescription, String acceptanceCriteria, String actualResult,
			String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;
				highlightEle2(element);

				String[][] data1 = {
						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
								"<b>Actual Result</b>" },
						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);
				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
				test.log(Status.PASS, modifiedMarkup);
				attachScreenshot(driver, true, scName, "click2");
				jsExecutor.executeScript("arguments[0].style.border=''", element);
//			jsExecutor.executeScript("arguments[0].scrollIntoView();",element );

				boolean clickSuccessful = false;
				int attempts = 0;
				while (!clickSuccessful && attempts < 3) {
					try {
						// Wait for the element to be clickable
						new WebDriverWait(driver, Duration.ofSeconds(30))
								.until(ExpectedConditions.elementToBeClickable(element));

						// Perform the click operation using JavascriptExecutor
//					((JavascriptExecutor) driver).executeScript("arguments[0].click();", element);
						waitForElementVisibile(element);
						element.click();
						isAlertPresent(driver);
						clickSuccessful = true; // Set to true if the click is successful
					} catch (Exception e) {
						attempts++;
						if (attempts == 3) {
							// Fallback to Actions Class click if Simple click fails after 3 attempts
							Actions actions = new Actions(driver);
							actions.moveToElement(element).click().perform();
							clickSuccessful = true;
						}
					}
				}

				if (!clickSuccessful) {
					throw new Exception("Failed to click the element after multiple attempts.");
				}

				// TimeUtil.shortWait();
				--screenshotCounter;
				attachScreenshot(driver, true, scName, "click2");
				screenshotCounter = Math.round(screenshotCounter);

			}

			else {
				waitForElementVisibile(element);
				element.click();
				isAlertPresent(driver);

			}
		} catch (Exception e) {

			if (isReportedRequired == true) {
				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
				test.log(Status.FAIL, "Exception :" + e.getMessage());
				attachScreenshot(driver, true, scName, "click2");
			}

			else {
				System.out.println(e.getMessage());
			}
		}
	}

	public void clickandHandleAlert(WebElement element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		waitForElementVisibile(element);
		element.click();
		isAlertPresent(driver);

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Performs a click action on a specified WebElement using JavaScript, then
	 * waits for the visibility of another WebElement. Logs the test steps and
	 * handles any exceptions.
	 * 
	 * @param element            The WebElement to be clicked using JavaScript.
	 * @param nextWebElement     The WebElement whose visibility will be waited for
	 *                           after the click action.
	 * @param stepDescription    A description of the step being performed.
	 * @param acceptanceCriteria The criteria that must be met for the test to be
	 *                           considered successful.
	 * @param actualResult       The actual result observed after performing the
	 *                           action.
	 * @param scName             The name used for screenshot files.
	 */
	public void jsClick2(WebElement element, WebElement nextWebElement, String stepDescription,
			String acceptanceCriteria, String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			// { String.valueOf(n), stepDescription, acceptanceCriteria,
			//
			// "<div><b>*</b> Successfully clicked on " + "</br>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].style.border=''", element);

			jsExecutor.executeScript("arguments[0].click();", element);

			isAlertPresent(driver);

			waitForElementVisibile(nextWebElement);

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Performs a click action on a specified WebElement, logs the test steps, and
	 * handles any exceptions.
	 * 
	 * @param element            The WebElement to be clicked.
	 * @param stepDescription    A description of the step being performed.
	 * @param acceptanceCriteria The criteria that must be met for the test to be
	 *                           considered successful.
	 * @param actualResult       The actual result observed after performing the
	 *                           action.
	 * @param scName             The name used for screenshot files.
	 */
//	public void click3(WebElement element, String stepDescription, String acceptanceCriteria, String actualResult,
//			String scName) {
//
//		try {
//			if (isReportedRequired == true) {
//				n = n + 1;
//
//				highlightEle2(element);
//
//				String[][] data1 = {
//
//						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
//
//								"<b>Actual Result</b>" },
//
//						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
//								"<div><b>* </b>" + actualResult } };
//
//
//
//				Markup tableMarkup = MarkupHelper.createTable(data1);
//
//				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
//
//				test.log(Status.PASS, modifiedMarkup);
//
//				attachScreenshot(driver, true, scName, "click2");
//
//				jsExecutor.executeScript("arguments[0].style.border=''", element);
//
//				element.click();
//				waitForElementVisibile(element);
//				TimeUtil.mediumWait();
//				element.click();
//
//				isAlertPresent(driver);
//
//				TimeUtil.mediumWait();
//
//				--screenshotCounter;
//
//				attachScreenshot(driver, true, scName, "click2");
//
//				screenshotCounter = Math.round(screenshotCounter);
//
//			} else {
//				waitForElementVisibile(element);
//				element.click();
//				TimeUtil.mediumWait();
//				TimeUtil.mediumWait();
//				element.click();
//
//			}
//
//		}
//
//		catch (Exception e) {
//
//			if (isReportedRequired == true) {
//				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
//				test.log(Status.FAIL, "Exception :" + e.getMessage());
//				attachScreenshot(driver, true, scName, "click2");
//			}
//
//			else {
//				System.out.println(e.getMessage());
//			}
//
//		}
//	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * 
	 * These methods are for report enhancement
	 *
	 * 
	 * 
	 */

	public void clickForReports(WebElement element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

//			{ String.valueOf(n), stepDescription, acceptanceCriteria,
//
//				"<div><b>*</b> Successfully clicked on " + "</br>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				jsExecutor.executeScript("arguments[0].style.border=''", element);

				attachScreenshot(driver, true, scName, "click2");

				element.click();

				TimeUtil.mediumWait();

				windowsPopUp();

				TimeUtil.mediumWait();
				TimeUtil.mediumWait();
				TimeUtil.mediumWait();

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);
			} else {
				jsExecutor.executeScript("arguments[0].style.border=''", element);
				element.click();

				TimeUtil.mediumWait();

				windowsPopUp();

				TimeUtil.mediumWait();
				TimeUtil.mediumWait();
				TimeUtil.mediumWait();
			}

		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");
			} else {
				System.out.println(e.getMessage());
			}

		}

		TimeUtil.longwait();
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * 
	 * These methods are for report enhancement
	 *
	 * 
	 * 
	 */

	public void clickwithRemoveFocus(WebElement element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				// { String.valueOf(n), stepDescription, acceptanceCriteria,
				//
				// "<div><b>*</b> Successfully clicked on " + "</br>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].style.border=''", element);

				element.click();

				removeFocus(element);

				isAlertPresent(driver);

				TimeUtil.mediumWait();

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);

			}

			else {
				element.click();

				removeFocus(element);

				isAlertPresent(driver);

			}
		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");
			} else {

				System.out.println(e.getMessage());
			}
		}
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	/*
	 * 
	 * These methods are for report enhancement
	 *
	 * 
	 * 
	 */

	public void attachScreenshot(WebDriver driver, boolean testCaseResult, String classname, String methodType) {
		List<String> listClassName = Arrays
				.asList(Thread.currentThread().getStackTrace()[2].getClassName().split("\\."));
		String className = listClassName.get(listClassName.size() - 1);

		screenshotCounter = screenshotCounter + 1.0;

		try {
			if (testCaseResult) {
				if (prop.getProperty("ScreenCapture").equalsIgnoreCase("YES")) {

					if ("sendKeys".equalsIgnoreCase(methodType)) {
						String screenshotDescription = String.format("%.1f - %s", screenshotCounter,
								"<span style=\"word-break: keep-all;\">" + classname + "</span>");
						test.pass(MediaEntityBuilder.createScreenCaptureFromBase64String(
								takeScreenshot(driver, className), screenshotDescription).build());
					} else if ("click2".equalsIgnoreCase(methodType)) {
						String screenshotDescription = String.format("%.1f - %s", screenshotCounter,
								"<span style=\"word-break: keep-all;\">" + classname + "</span>");
						test.pass(MediaEntityBuilder.createScreenCaptureFromBase64String(
								takeScreenshot(driver, className), screenshotDescription).build());
						screenshotCounter += 0.1;
					} else if ("selectMultipleCheckBoxes".equalsIgnoreCase(methodType)) {
						String screenshotDescription = String.format("%.1f - %s", screenshotCounter,
								"<span style=\"word-break: keep-all;\">" + classname + "</span>");
						test.pass(MediaEntityBuilder.createScreenCaptureFromBase64String(
								takeScreenshot(driver, className), screenshotDescription).build());
						screenshotCounter += 0.1;
					}
				}
			} else {
				String screenshotDescription = String.format("%.1f - %s", screenshotCounter, classname);
				if ("sendKeys".equalsIgnoreCase(methodType)) {
					test.fail(MediaEntityBuilder.createScreenCaptureFromBase64String(takeScreenshot(driver, className),
							screenshotDescription).build());
				} else if ("click2".equalsIgnoreCase(methodType)) {
					test.fail(MediaEntityBuilder.createScreenCaptureFromBase64String(takeScreenshot(driver, className),
							screenshotDescription).build());
				} else if ("selectMultipleCheckBoxes".equalsIgnoreCase(methodType)) {
					test.fail(MediaEntityBuilder.createScreenCaptureFromBase64String(takeScreenshot(driver, className),
							screenshotDescription).build());
				}
			}
		} catch (Exception e) {
			test.log(Status.FAIL, "Exception at Screen Capturing :" + e.getMessage());
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Captures a screenshot of the current state of the WebDriver, saves it as a
	 * PNG file in the specified directory, and returns the screenshot as a Base64
	 * encoded string. The filename includes a timestamp and the provided class
	 * name.
	 *
	 * @param driver    The WebDriver instance used to capture the screenshot.
	 * @param classname The class name to be included in the screenshot file name.
	 * @return The screenshot as a Base64 encoded string.
	 */

	public static String takeScreenshot(WebDriver driver, String classname) {

		String scrBase64 = null;

		try {

			String dateName = new SimpleDateFormat("dd_MMM_yyyy_hh_mm_ss").format(new Date());

			TakesScreenshot ts = (TakesScreenshot) driver;

			scrBase64 = ts.getScreenshotAs(OutputType.BASE64);

			File source = OutputType.FILE.convertFromBase64Png(scrBase64);

			String screenshotFolder = ConfigsReader.getPropValue("Screenshots");

			String destination = screenshotFolder + screenshotCounter + " - "

					+ classname + " - " + dateName + ".png";

			File finalDestination = new File(destination);

			FileUtils.copyFile(source, finalDestination);

		} catch (IOException e) {

			System.out.println(e.getMessage());

		}

		return scrBase64;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Waits for the specified WebElement to become visible, clears it, sends the
	 * provided text, and logs the step details. It also captures a screenshot and
	 * highlights the element after sending the keys. If an exception occurs, it
	 * logs the failure and captures a screenshot for further analysis.
	 *
	 * @param element            The WebElement where text will be entered.
	 * @param stepDescription    A description of the step being performed.
	 * @param text               The text to be entered into the WebElement.
	 * @param acceptanceCriteria The expected result or acceptance criteria for the
	 *                           action.
	 * @param actualResult       The actual result of the action performed.
	 * @param scName             The name for the screenshot to be captured.
	 */

	public void sendKeys2(WebElement element, String stepDescription, String text, String acceptanceCriteria,

			String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;
				waitForElementVisibile(element);
				element.clear();
				highlightEle2(element);

				String[][] data = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>", },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult

						} };

				Markup tableMarkup = MarkupHelper.createTable(data);

				modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				element.sendKeys(text);

				attachScreenshot(driver, true, scName, "sendKeys");

				jsExecutor.executeScript("arguments[0].style.border=''", element);

			}

			else {

				waitForElementVisibile(element);
				element.clear();
				element.sendKeys(text);
			}

		} catch (Exception e) {

			if (isReportedRequired == true) {

				n = n + 1;

				attachScreenshot(driver, true, scName, "sendKeys");

				test.log(Status.FAIL,

						" <b>Step No. " + n + "</b> " + " Failed to enter data in " + "</br>" + actualResult
								+ " textBox");

				test.log(Status.FAIL, "Exception :" + e.getMessage());
			} else {
				System.out.println(e.getMessage());
			}
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clears the specified WebElement, sends the provided text to it, removes focus
	 * from the element, and logs the step details. It captures screenshots before
	 * and after sending keys and highlights the element. If an exception occurs, it
	 * logs the failure and captures a screenshot.
	 *
	 * @param element            The WebElement where text will be entered.
	 * @param stepDescription    A description of the step being performed.
	 * @param text               The text to be entered into the WebElement.
	 * @param acceptanceCriteria The expected result or acceptance criteria for the
	 *                           action.
	 * @param actualResult       The actual result of the action performed.
	 * @param scName             The name for the screenshot to be captured.
	 */
	public void sendKeysAndRemoveFocus(WebElement element, String stepDescription, String text,
			String acceptanceCriteria,

			String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				element.clear();
				highlightEle2(element);

				String[][] data = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>", },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult

						} };

				Markup tableMarkup = MarkupHelper.createTable(data);

				modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				element.sendKeys(text);

				removeFocus(element);

				TimeUtil.shortWait();

				attachScreenshot(driver, true, scName, "sendKeys");

				jsExecutor.executeScript("arguments[0].style.border=''", element);
			}

			else {
				element.clear();
				element.sendKeys(text);

				removeFocus(element);
				TimeUtil.shortWait();
				TimeUtil.shortWait();
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				attachScreenshot(driver, true, scName, "sendKeys");

				test.log(Status.FAIL,

						" <b>Step No. " + n + "</b> " + " Failed to enter data in " + "</br>" + actualResult
								+ " textBox");

				test.log(Status.FAIL, "Exception :" + e.getMessage());
			}

			else {

				System.out.println(e.getMessage());

			}
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks on a specified WebElement and scrolls down the page. It logs the step
	 * details and acceptance criteria, captures a screenshot before and after the
	 * click action, and highlights the element. If an exception occurs, it logs the
	 * failure and captures a screenshot.
	 *
	 * @param element            The WebElement to be clicked.
	 * @param stepDescription    A description of the step being performed.
	 * @param acceptanceCriteria The expected result or acceptance criteria for the
	 *                           action.
	 * @param actualResult       The actual result of the action performed.
	 * @param scName             The name for the screenshot to be captured.
	 */
	public void click2WithScrollDown(WebElement element, String stepDescription, String acceptanceCriteria,

			String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };
				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].style.border=''", element);

				element.click();

				isAlertPresent(driver);

				TimeUtil.mediumWait();

				--screenshotCounter;

				jsExecutor.executeScript("window.scrollBy(0, 250);");

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);

			}

			else {
				element.click();

				isAlertPresent(driver);

				TimeUtil.mediumWait();
				jsExecutor.executeScript("window.scrollBy(0, 250);");

			}
		} catch (Exception e) {

			if (isReportedRequired == true) {

				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");
			}

			else {
				System.out.println(e.getMessage());

			}
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Sends keys to a specified WebElement without clearing any existing text. It
	 * logs the step details and acceptance criteria, captures a screenshot, and
	 * highlights the element. If an exception occurs, it logs the failure and
	 * captures a screenshot.
	 * 
	 * @param element            The WebElement to which the text will be sent.
	 * @param stepDescription    A description of the step being performed.
	 * @param text               The text to be sent to the WebElement.
	 * @param acceptanceCriteria The expected result or acceptance criteria for the
	 *                           action.
	 * @param actualResult       The actual result of the action performed.
	 * @param scName             The name for the screenshot to be captured.
	 */
	public void sendKeys2WithOutClear(WebElement element, String stepDescription, String text,
			String acceptanceCriteria,

			String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>", },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult

					} };

			Markup tableMarkup = MarkupHelper.createTable(data);

			modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			element.sendKeys(text);

			attachScreenshot(driver, true, scName, "sendKeys");

			jsExecutor.executeScript("arguments[0].style.border=''", element);

		} catch (Exception e) {

			n = n + 1;

			attachScreenshot(driver, true, scName, "sendKeys");

			test.log(Status.FAIL,

					" <b>Step No. " + n + "</b> " + " Failed to enter data in " + "</br>" + actualResult + " textBox");

			test.log(Status.FAIL, "Exception :" + e.getMessage());

		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Handles authentication in a Windows popup dialog using the Robot class. The
	 * method: 1. Waits for the popup to appear. 2. Simulates keyboard input to
	 * paste the username and password into the popup. 3. Uses the TAB key to
	 * navigate between fields. 4. Submits the form by pressing the ENTER key. 5.
	 * Waits for the authentication process to complete.
	 * 
	 * @throws Exception if any unexpected issues occur during the Robot operations
	 *                   or thread sleeping
	 */
	public void windowsPopUp() throws Exception {

		Thread.sleep(4000);

		Robot rb = new Robot();

		StringSelection username = new StringSelection(ConfigsReader.getPropValue("ReportUser"));
		Thread.sleep(4000);
		Toolkit.getDefaultToolkit().getSystemClipboard().setContents(username, null);
		rb.keyPress(KeyEvent.VK_CONTROL);
		rb.keyPress(KeyEvent.VK_V);
		rb.keyRelease(KeyEvent.VK_V);
		rb.keyRelease(KeyEvent.VK_CONTROL);
		Thread.sleep(2000);

		rb.keyPress(KeyEvent.VK_TAB);
		rb.keyRelease(KeyEvent.VK_TAB);
		Thread.sleep(2000);

		StringSelection pwd = new StringSelection(ConfigsReader.getPropValue("ReportPassword"));
		Toolkit.getDefaultToolkit().getSystemClipboard().setContents(pwd, null);
		rb.keyPress(KeyEvent.VK_CONTROL);
		rb.keyPress(KeyEvent.VK_V);
		rb.keyRelease(KeyEvent.VK_V);
		rb.keyRelease(KeyEvent.VK_CONTROL);
		Thread.sleep(2000);

		// tab to password entry field
		rb.keyPress(KeyEvent.VK_TAB);
		rb.keyRelease(KeyEvent.VK_TAB);
		Thread.sleep(2000);

		// press enter
		rb.keyPress(KeyEvent.VK_ENTER);
		rb.keyRelease(KeyEvent.VK_ENTER);

		// wait
		Thread.sleep(5000);
	}
	// --------------------------------------------------------------------------------------------------------------------------------
	// Get Current Date

	public void getCurrentDate() {
		SimpleDateFormat formatter = null;
		Date date = null;
		try {
			formatter = new SimpleDateFormat("dd-MMM-yyyy");
			date = new Date();
			currentDate = formatter.format(date);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			formatter = new SimpleDateFormat("dd MMM yyyy");
			currentDate = formatter.format(date);
		} catch (Exception ex) {
			ex.printStackTrace();
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and verifies the exact date displayed by the provided web element.
	 * This method highlights the specified web element, extracts its text, and
	 * formats it into a date string. It then compares this formatted date with the
	 * expected `currentDate` and asserts that they are equal. If any exception
	 * occurs during the process, it prints the stack trace for debugging.
	 * 
	 * @param dateWebElement the web element containing the date to be verified
	 */
	public void getExactDate(WebElement dateWebElement) {
		try {

			highLightElement(driver, dateWebElement, "Transaction Date", test);

			String dateText = dateWebElement.getText();
			String[] dateArray = StringUtils.split(dateText, " ");

			StringBuffer actualDateBuffer = new StringBuffer();
			actualDateBuffer.append(dateArray[0]).append(" ").append(dateArray[1]).append(" ").append(dateArray[2]);
			String actualDate = actualDateBuffer.toString();
			System.out.println(actualDate);
			System.out.println(currentDate);
			Assert.assertEquals(actualDate, currentDate);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a web element matches the expected start time. This
	 * method waits for the element to be visible, highlights it, and compares its
	 * text with the expected start time. It checks if the actual text from the
	 * element contains the expected start time, and logs the results. If the text
	 * does not match, it logs a failure message, captures a screenshot, and asserts
	 * equality. It handles various exceptions, including timeout, assertion errors,
	 * and general exceptions.
	 * 
	 * @param driver      the WebDriver instance used to interact with the web page
	 * @param element     the web element containing the start time to verify
	 * @param elementName a description of the element for logging and screenshot
	 *                    purposes
	 */
	public static void verifyStartTimeInCourseSession(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String expectedStartTime = text3;
		String actualStartTime = element.getText().trim();
		System.out.println(actualStartTime);
		System.out.println(expectedStartTime);
		try {
			if (actualStartTime.contains(expectedStartTime)) {

			} else {
				test.fail("Expected result: " + "<b>" + expectedStartTime + "</b>" + ", and actual result was: " + "<b>"
						+ actualStartTime + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualStartTime, expectedStartTime,
						"The actual Feild Value does not match the expected Feild Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + expectedStartTime);
			// throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			// captureScreen(driver, true, elementName);
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a web element matches the expected end time. This
	 * method waits for the element to be visible, highlights it, and compares its
	 * text with the expected end time. It checks if the actual text from the
	 * element contains the expected end time, and logs the results. If the text
	 * does not match, it logs a failure message, captures a screenshot, and asserts
	 * equality. It handles various exceptions, including timeout, assertion errors,
	 * and general exceptions.
	 * 
	 * @param driver      the WebDriver instance used to interact with the web page
	 * @param element     the web element containing the end time to verify
	 * @param elementName a description of the element for logging and screenshot
	 *                    purposes
	 */
	public static void verifyEndTimeInCourseSession(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String expectedStartTime = text4;
		String actualStartTime = element.getText().trim();
		try {
			if (actualStartTime.contains(expectedStartTime)) {

			} else {
				test.fail("Expected result: " + "<b>" + expectedStartTime + "</b>" + ", and actual result was: " + "<b>"
						+ actualStartTime + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualStartTime, expectedStartTime,
						"The actual Feild Value does not match the expected Feild Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + expectedStartTime);
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a web element matches the expected training hours
	 * format. This method waits for the element to be visible, highlights it, and
	 * compares its text with the expected time value. It formats the expected time
	 * value from a string, compares it with the actual text from the element, and
	 * logs the results. If the text does not match, it logs a failure message,
	 * captures a screenshot, and asserts equality. It handles various exceptions,
	 * including timeout, assertion errors, and general exceptions.
	 * 
	 * @param driver      the WebDriver instance used to interact with the web page
	 * @param element     the web element containing the training hours to verify
	 * @param elementName a description of the element for logging and screenshot
	 *                    purposes
	 */

	public static void verifyTrainingHoursInCourseSession(WebDriver driver, WebElement element, String elementName) {

		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String timeValue = text2;
		String[] textValueArray = timeValue.split(":");
		int hours = Integer.parseInt(textValueArray[0]);
		String modifiedHours = String.valueOf(hours);
		String TextValue = modifiedHours + ":" + textValueArray[1];
		String actualTextValue = element.getText();
		System.out.println(actualTextValue);
		System.out.println("Expected time= " + TextValue);
		try {
			if (actualTextValue.equals(TextValue)) {
			} else {
				test.fail("Expected result: " + "<b>" + TextValue + "</b>" + ", and actual result was: " + "<b>"
						+ actualTextValue + "</b>" + "<b><font color=red> Test failed </font></b>");

				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualTextValue, TextValue,
						"The actual Feild Value does not match the expected Feild Value.");

			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail("</b>" + elementName + "</b>" + " does not contain the expected caption: " + "<b>" + TextValue
					+ "</b>");
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies that the text of a web element matches the expected trainer name.
	 * This method waits for the element to be visible, highlights it, and then
	 * compares its text with the expected trainer name. If the text does not match,
	 * it logs a failure message, captures a screenshot, and asserts equality. It
	 * handles various exceptions, including timeout, assertion errors, and general
	 * exceptions.
	 * 
	 * @param element     the web element containing the trainer name to verify
	 * @param elementName a description of the element for logging and screenshot
	 *                    purposes
	 */

	public static void verifyTrainerName(WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String TrainerName = text;
		String actualTrainerName = element.getText().trim();
		System.out.println(actualTrainerName);
		System.out.println(TrainerName);
		try {
			if (actualTrainerName.contains(TrainerName)) {

			} else {
				test.fail("Expected result: " + "<b>" + TrainerName + "</b>" + ", and actual result was: " + "<b>"
						+ actualTrainerName + "</b>" + "<b><font color=red> Test failed </font></b>");
				captureScreen(driver, true, elementName);
				Assert.assertEquals(actualTrainerName, TrainerName,
						"The actual Feild Value does not match the expected Feild Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + TrainerName);
			// throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and formats the session start date. This method extracts the start
	 * date value from a specific web element, parses it from "MM/dd/yyyy" format,
	 * and then reformats it to "dd MMM yyyy" format for further use.
	 * 
	 * @throws ParseException if the date cannot be parsed from the input format.
	 */
	public static void sessionStartDate() {

		String startdate = driver.findElement(By.xpath("//*[@id='CMSESSIONS_StartDate_0']")).getAttribute("value");
		SimpleDateFormat format1 = new SimpleDateFormat("MM/dd/yyyy");
		SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy");
		Date date = null;
		try {
			date = format1.parse(startdate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		sessionstartDate = format2.format(date);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and formats the session end date. This method extracts the end date
	 * value from a specific web element, parses it from "MM/dd/yyyy" format, and
	 * then reformats it to "dd MMM yyyy" format for further use.
	 * 
	 * @throws ParseException if the date cannot be parsed from the input format.
	 */
	public static void sessionEndDate() {

		String enddate = driver.findElement(By.xpath("//*[@id='CMSESSIONS_EndDate_0']")).getAttribute("value");
		SimpleDateFormat format1 = new SimpleDateFormat("MM/dd/yyyy");
		SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy");
		Date date = null;
		try {
			date = format1.parse(enddate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		sessionendDate = format2.format(date);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and formats the last response date for a session. This method
	 * extracts the date value from a specific web element, parses it from
	 * "MM/dd/yyyy" format, and then reformats it to "dd MMM yyyy" format for
	 * further use.
	 * 
	 * @throws ParseException if the date cannot be parsed from the input format.
	 */
	public static void sessionLastDateOfResponse() {

		String lastdate = driver.findElement(By.xpath("//*[@id='CMSESSIONS_LastRespDate_0']")).getAttribute("value");
		SimpleDateFormat format1 = new SimpleDateFormat("MM/dd/yyyy");
		SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy");
		Date date = null;
		try {
			date = format1.parse(lastdate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		sessionLastDateofResponse = format2.format(date);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Trims the approver subgroup from a given approver name string. This method
	 * extracts the substring following the last "/" character in the `ApproverName`
	 * string.
	 * 
	 * @param ApproverName The full name of the approver, which includes the
	 *                     subgroup information.
	 * @return The trimmed approver subgroup, extracted from the `ApproverName`.
	 */
	public static void trimApproversSubgroup(String ApproverName) {

		trimmedApprroverSubGroup = ApproverName.substring(ApproverName.lastIndexOf("/") + 1);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Calculates the training hours for a course session based on its start and end
	 * dates and times. This method computes the difference in days between the
	 * start and end dates, and the difference in hours and minutes between the
	 * start and end times. It then calculates the total training hours considering
	 * whether the session spans multiple days or is contained within a single day.
	 *
	 * The calculated training hours are formatted as "HH:mm" and stored in the
	 * static variable `sessionTrainingHours`.
	 *
	 * @throws ParseException if there is an error parsing the date or time strings.
	 */
	public static void getTrainingHoursatCourseSession() {

		SimpleDateFormat dateFormat2 = new SimpleDateFormat("dd MMM yyyy");
		String startDate = sessionstartDate;
		String endDate = sessionendDate;

		Date date1 = null;
		try {
			date1 = dateFormat2.parse(startDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		Date date2 = null;
		try {
			date2 = dateFormat2.parse(endDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		long difftimedaysinMillisecs = date2.getTime() - date1.getTime();
		long diffDays = difftimedaysinMillisecs / (24 * 60 * 60 * 1000);
		String differencedays = String.format("%02d", diffDays);
		int differencedaysCount = Integer.parseInt(differencedays) + 1;
		SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
		String startTime = sessionStartTime;
		String EndTime = sessionEndTime;

		Date time1 = null;
		try {
			time1 = dateFormat.parse(startTime);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		Date time2 = null;
		try {
			time2 = dateFormat.parse(EndTime);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		long difftime = time2.getTime() - time1.getTime();
		long diffSeconds = difftime / 1000 % 60;
		long diffMinutes = difftime / (60 * 1000) % 60;
		long diffHours = difftime / (60 * 60 * 1000) % 24;

		if (differencedaysCount > 0) {
			long diffCalHoursinMS = diffHours * 60 * 60 * differencedaysCount * 1000;
			long diffCalfinalHrs = diffCalHoursinMS / (60 * 60 * 1000) % 24;

			long diffCalMinsinMS = diffMinutes * 60 * differencedaysCount * 1000;
			long diffCalfinalMins = diffCalMinsinMS / (60 * 1000) % 60;
			String actualCalhrs = (String.format("%02d", diffCalfinalHrs));
			String actualCalMins = (String.format("%02d", diffCalfinalMins));
			sessionTrainingHours = actualCalhrs + ":" + actualCalMins;
		} else {
			String Uncalhrs = String.format("%02d", diffHours);
			String UncalMins = String.format("%02d", diffMinutes);
			sessionTrainingHours = Uncalhrs + ":" + UncalMins;
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates to the Form Workflow Modification menu within the MDM (Master Data
	 * Management) system. This method sequentially clicks through the MDM menu,
	 * System Configuration menu, Form Workflow Map, and finally the Modification
	 * menu. It ensures that each menu is visible and clickable before proceeding to
	 * the next one, and performs a click action on the final Modification menu.
	 *
	 * @param MDMMenu          WebElement representing the MDM menu to be clicked.
	 * @param SystemConfigMenu WebElement representing the System Configuration menu
	 *                         to be clicked.
	 * @param FrmWorkFlowMap   WebElement representing the Form Workflow Map menu to
	 *                         be clicked.
	 * @param Modifcation      WebElement representing the Modification menu to be
	 *                         clicked.
	 */
	public void MDMConfigurationMenu(WebElement MDMMenu, WebElement SystemConfigMenu, WebElement FrmWorkFlowMap,
			WebElement Modifcation) {
		clickAndWaitforNextElement(MDMMenu, SystemConfigMenu,
				MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(SystemConfigMenu, FrmWorkFlowMap,
				MDM_Department.Click_MDM_SytemConfigurationMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_SytemConfigurationMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_SytemConfigurationMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_SytemConfigurationMenu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(FrmWorkFlowMap, Modifcation,
				MDM_Department.Click_FormWorkflow_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflow_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflow_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflow_Menu_SS.getMDM_DepartmentStrings());
		click2(Modifcation, MDM_Department.Click_FormWorkflowModificiation_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflowModificiation_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflowModificiation_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_FormWorkflowModificiation_Menu_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates to the Department Registration menu within the MDM (Master Data
	 * Management) system. This method ensures that each menu (MDM, Master,
	 * Department, and Department Registration) is visible and clickable in
	 * sequence, waiting for the next element to be visible before proceeding. It
	 * clicks through each menu to reach the final Department Registration menu.
	 *
	 * @param MDMMenu                    WebElement representing the MDM menu to be
	 *                                   clicked.
	 * @param MasterMenu                 WebElement representing the master menu to
	 *                                   be clicked.
	 * @param DepartmentMenu             WebElement representing the department menu
	 *                                   to be clicked.
	 * @param DepartmentRegistrationMenu WebElement representing the department
	 *                                   registration menu to be clicked.
	 */
	public void MDMDepartmentRegistrationMenu(WebElement MDMMenu, WebElement MasterMenu, WebElement DepartmentMenu,
			WebElement DepartmentRegistrationMenu) {

		waitForElementVisibile(MDMMenu);
		clickAndWaitforNextElement(MDMMenu, MasterMenu, MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(MasterMenu, DepartmentMenu,
				MDM_Department.Click_MasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(DepartmentMenu, DepartmentRegistrationMenu,
				MDM_Department.Click_Department_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_SS.getMDM_DepartmentStrings());
		click2(DepartmentRegistrationMenu, MDM_Department.Click_Registration_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentRegistration_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentRegistration_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentRegistration_Menu_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates to the Department Audit Trails menu within the MDM (Master Data
	 * Management) system. This method clicks through the MDM menu, master menu,
	 * department menu, and finally the audit trails menu, ensuring that the next
	 * element is visible before proceeding with each click.
	 *
	 * @param MDMMenu         WebElement representing the MDM menu to be clicked.
	 * @param MasterMenu      WebElement representing the master menu to be clicked.
	 * @param DepartmentMenu  WebElement representing the department menu to be
	 *                        clicked.
	 * @param AuditTrailsMenu WebElement representing the audit trails menu to be
	 *                        clicked.
	 */
	public void MDMDepartmentAudiTrailsMenu(WebElement MDMMenu, WebElement MasterMenu, WebElement DepartmentMenu,
			WebElement AuditTrailsMenu) {
		clickAndWaitforNextElement(MDMMenu, MasterMenu, MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(MasterMenu, DepartmentMenu,
				MDM_Department.Click_MasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(DepartmentMenu, AuditTrailsMenu,
				MDM_Department.Click_Department_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_Department_Menu_SS.getMDM_DepartmentStrings());
		click2(AuditTrailsMenu, MDM_Department.Click_DepartmentAuditTrails_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentAuditTrails_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentAuditTrails_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_DepartmentAuditTrails_Menu_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates through the MDM (Master Data Management) common masters mapping
	 * modification menu by sequentially clicking on the MDM menu, master menu,
	 * common masters menu, and finally the modification menu. This method ensures
	 * that after each click, the next expected element is visible and waits for it
	 * before proceeding.
	 *
	 * @param MDMMenu           WebElement representing the MDM menu to click.
	 * @param MasterMenu        WebElement representing the master menu to click.
	 * @param CommomMastersMenu WebElement representing the common masters menu to
	 *                          click.
	 * @param ModificationMenu  WebElement representing the modification menu to
	 *                          click.
	 */

	public void MDMCommonMastersMappingModificationMenu(WebElement MDMMenu, WebElement MasterMenu,
			WebElement CommomMastersMenu, WebElement ModificationMenu) {
		clickAndWaitforNextElement(MDMMenu, MasterMenu, MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		clickAndWaitforNextElement(MasterMenu, CommomMastersMenu,
				MDM_Department.Click_MasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_SS.getMDM_DepartmentStrings());

		clickAndWaitforNextElement(CommomMastersMenu, ModificationMenu,
				MDM_Department.Click_CommonMasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_SS.getMDM_DepartmentStrings());

		click2(ModificationMenu, MDM_Department.Click_Modification_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMaster_Modification_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMaster_Modification_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMaster_Modification_Menu_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates through the MDM (Master Data Management) common masters mapping
	 * audit trails menu by clicking on the specified menu options in sequence. This
	 * involves clicking on the MDM menu, the master menu, the common masters menu,
	 * and finally the audit trails menu.
	 *
	 * @param MDMMenu           WebElement representing the MDM menu to click.
	 * @param MasterMenu        WebElement representing the master menu to click.
	 * @param CommomMastersMenu WebElement representing the common masters menu to
	 *                          click.
	 * @param AuditTrailsMenu   WebElement representing the audit trails menu to
	 *                          click.
	 */
	public void MDMCommonMastersMappingAuditTrailsMenu(WebElement MDMMenu, WebElement MasterMenu,
			WebElement CommomMastersMenu, WebElement AuditTrailsMenu) {
		click2(MDMMenu, MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		click2(MasterMenu, MDM_Department.Click_MasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_SS.getMDM_DepartmentStrings());

		click2(CommomMastersMenu, MDM_Department.Click_CommonMasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_SS.getMDM_DepartmentStrings());

		click2(AuditTrailsMenu, MDM_Department.Click_DepartmentAuditTrails_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Navigates through the document registration menu by clicking on the specified
	 * menu options. It first clicks on the main menu, then the master menu,
	 * followed by the common masters menu, and finally selects the audit trails
	 * menu.
	 *
	 * @param menu              WebElement representing the main menu to click.
	 * @param MasterMenu        WebElement representing the master menu to click.
	 * @param CommomMastersMenu WebElement representing the common masters menu to
	 *                          click.
	 * @param AuditTrailsMenu   WebElement representing the audit trails menu to
	 *                          click.
	 */
	public void DocumentRegistrationMenu(WebElement menu, WebElement MasterMenu, WebElement CommomMastersMenu,
			WebElement AuditTrailsMenu) {
		click2(menu, MDM_Department.Click_MDM_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MDM_Menu_SS.getMDM_DepartmentStrings());
		click2(MasterMenu, MDM_Department.Click_MasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_MasterMenu_SS.getMDM_DepartmentStrings());

		click2(CommomMastersMenu, MDM_Department.Click_CommonMasterMenu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMasterMenu_SS.getMDM_DepartmentStrings());

		click2(AuditTrailsMenu, MDM_Department.Click_DepartmentAuditTrails_Menu_DC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_AC.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_AR.getMDM_DepartmentStrings(),
				MDM_Department.Click_CommonMastersPlantMap_SS.getMDM_DepartmentStrings());

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a specific date from the department effective dates calendar. It
	 * first ensures the correct month and year are selected in the dropdowns. Then,
	 * it searches for the available dates on the calendar and selects the specified
	 * day.
	 *
	 * @param Melement WebElement representing the month dropdown.
	 * @param Yelement WebElement representing the year dropdown.
	 * @param month    The month to select.
	 * @param year     The year to select.
	 * @param day      The day to select from the calendar.
	 */
//	private static final String EffectiveDates = "//html/body/div[2]/div[2]/div[1]/table/tbody/tr/td[@class='today active start-date active end-date available' or @class='today available']";
	private static final String EffectiveDates = "//body/div[2]/div[2]/div[1]/table/tbody/tr/td[@class='weekend available' or @class='today weekend active start-date active end-date available' or @class= 'available' or @class= 'today active start-date active end-date available']";

	public void selectDateonDepartmentEffectiveDates(WebElement Melement, WebElement Yelement, String month,
			String year,

			String day) {

		waitForElementVisibile(Melement);

		String actualMonth = Melement.getText();

		String actualYear = Yelement.getText();

		if (!actualYear.equals(year)) {

			selectOptionFromDropdownByName(Yelement, year, "year");

			if (!actualMonth.equals(month)) {

				selectOptionFromDropdownByName(Melement, month, "month");

			}

		}

		List<WebElement> availableDates = driver.findElements(By.xpath(EffectiveDates));

		for (WebElement date : availableDates) {

			String dayValue = date.getText();

			if (dayValue.equals(day)) {

				waitForElementVisibile(date);

				click2(date, MDM_Department.Select_EffectiveDate_DC.getMDM_DepartmentStrings(),
						MDM_Department.Select_EffectiveDate_AC.getMDM_DepartmentStrings(),
						MDM_Department.Select_EffectiveDate_AR.getMDM_DepartmentStrings(),
						MDM_Department.Select_EffectiveDate_SS.getMDM_DepartmentStrings());

			}
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves the user's first name, last name, and employee ID from the profile
	 * icon menu. The method performs the following steps: 1. Clicks on the user
	 * profile icon to open the menu. 2. Selects "Personal Particulars" from the
	 * menu. 3. Switches to the appropriate frame to access user details. 4.
	 * Retrieves and highlights the user's first name, last name, and employee ID.
	 * 5. Constructs a full name and employee ID string in the format
	 * "FirstName.LastName (EmployeeID)". 6. Switches back to the default content.
	 * 
	 * This method assumes the presence of certain elements and frame-switching
	 * based on the provided XPaths.
	 */
	public void getFirstNameLastNameFromProfileIcon() {

		WebElement userProfileIcon = driver.findElement(By.xpath("//ul[@class='navbar-nav']/li[2]/a"));

		waitForElementVisibile(userProfileIcon);
		TimeUtil.mediumWait();
		click2(userProfileIcon, CommonStrings.Click_ProfileIconofUser_DC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AR.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_SS.getCommonStrings());
		WebElement personalParticulars = driver.findElement(By.xpath("//a[@id='RefreshA']//following-sibling::a[2]"));
		click2(personalParticulars, CommonStrings.Click_PersonalParticulars_DC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AR.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		WebElement firstNameText = driver
				.findElement(By.xpath("//label[contains(text(),'First Name')]//following-sibling::span"));
		waitForElementVisibile(firstNameText);
		highLightElement(driver, firstNameText, "First Name", test);
		firstName = firstNameText.getText().trim();
		System.out.println(firstName);
		WebElement lastNameText = driver
				.findElement(By.xpath("//label[contains(text(),'Last Name')]//following-sibling::span"));

		lastName = lastNameText.getText().trim();
		highLightElement(driver, lastNameText, "Last Name", test);
		FullName = firstName + "." + lastName;
		System.out.println(lastName);
		WebElement employeeIDText = driver
				.findElement(By.xpath("//label[contains(text(),'Employee ID')]//following-sibling::span"));
		highLightElement(driver, employeeIDText, "Employee ID", test);
		employeeID = employeeIDText.getText().trim();
		FullNameemployeeID = FullName + "(" + employeeID + ")";
		System.out.println(employeeID);
		switchToDefaultContent(driver);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Sets the initiator's full name and employee ID to class variables. This
	 * method assigns the values of `FullName` and `FullNameemployeeID` to
	 * `initiatorFLName` and `initiatorFLNameEmpID` respectively.
	 */
	public void getInitiatorDetails() {

		initiatorFLName = FullName;
		initiatorFLNameEmpID = FullNameemployeeID;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Extracts the first name and last name of the initiator from a WebElement.
	 * Assumes that the WebElement's value contains the full name followed by an
	 * identifier in parentheses. The method assigns the extracted full name
	 * (excluding the identifier) to `InitiatorFullName`.
	 *
	 * @param initiatorName The WebElement containing the initiator's full name with
	 *                      an identifier in parentheses.
	 */

	public void getInitiatorFirstNameLastName(WebElement initiatorName) {

		initiator = initiatorName.getAttribute("value");

		InitiatorFullName = StringUtils.substringBefore(initiator, "(");
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks on a specified WebElement and waits for a subsequent element to become
	 * visible. Logs the action details and attaches screenshots before and after
	 * the click operation.
	 *
	 * @param element            The WebElement to be clicked.
	 * @param nextWebElement     The WebElement to wait for after the click
	 *                           operation.
	 * @param stepDescription    A description of the step being performed.
	 * @param acceptanceCriteria The criteria that define the expected outcome of
	 *                           the step.
	 * @param actualResult       The actual result observed after performing the
	 *                           step.
	 * @param scName             The name used for screenshot files.
	 */
	public void clickAndWaitforNextElement(WebElement element, WebElement nextWebElement, String stepDescription,
			String acceptanceCriteria, String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].style.border=''", element);

				element.click();

				isAlertPresent(driver);

				waitForElementVisibile(nextWebElement);

				TimeUtil.extrashortWait();

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);

			} else {
				waitForElementVisibile(element);
				element.click();
				isAlertPresent(driver);
				waitForElementVisibile(nextWebElement);

			}
		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");

			}

			else {

				System.out.println(e.getMessage());
			}

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a radio button or checkbox if it is not already selected. Waits for
	 * the element to be visible and checks its selection status. If the element is
	 * not selected, it performs a click action and logs the step details.
	 */
	public void SelectRadioBtnAndCheckboxRegistrationApproval(WebDriver driver, WebElement element,
			String elementName) {

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
		wait.until(ExpectedConditions.visibilityOf(element));
		boolean status = element.isSelected();
		if (status == true) {
		} else {
			click2(element, CommonStrings.Call_ESign_RegApproval_DC.getCommonStrings(),
					CommonStrings.Call_ESign_RegInitiation_AC.getCommonStrings(),
					CommonStrings.Call_ESign_RegInitiation_AR.getCommonStrings(),
					CommonStrings.Call_ESign_RegApproval_SS.getCommonStrings());

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks on the specified login button and performs additional actions based on
	 * the current URL. Highlights the button element, logs detailed step
	 * information and result, and attaches screenshots. If the current URL
	 * indicates a specific application state, it performs additional actions such
	 * as clicking on a button for multi-session scenarios.
	 */
	public void clickLoginButton(WebElement element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].style.border=''", element);

			element.click();

			isAlertPresent(driver);

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Validates if the date format of a given WebElement matches any of a
	 * predefined list of date formats. Iterates through a list of date formats,
	 * using the `validateDateFormat` method to check each format. If a valid format
	 * is found, it highlights the WebElement, prints the valid date format and its
	 * value, and stops further checks. If no valid format is found, no action is
	 * taken.
	 *
	 * @param dateInput The WebElement containing the date to be validated.
	 */
	public static void validateMultipleDateFormats(WebElement dateInput) {

		List<String> dateFormats = Arrays.asList(

				"yyyy-MM-dd", "MM/dd/yyyy", "dd-MM-yyyy", "yyyy/MM/dd", "dd/MM/yyyy", "dd MMM yyyy", "dd-MM-yy",
				"yyyy MMM dd", "MMM dd, yyyy", "dd-MMM-yyyy", "dd MMMM yyyy", "MMMM dd, yyyy", "yy-MM-dd", "MM/dd/yy",
				"yy/MM/dd", "dd/MM/yy", "yy MMM dd", "MMM yy dd", "yy dd MMM", "dd yy MMM", "MMM dd yyyy", "dd.MM.yyyy",
				"yyyy dd MMM", "dd MMMM yy", "yy MMMM dd", "MMMM dd yy", "dd/MMM/yyyy", "dd-MMM-yy", "MMM dd, yy",
				"dd MMMM, yyyy", "yyyy/MMM/dd", "dd.MM.yy"
		// Add other formats as needed
		);

		for (String dateFormat : dateFormats) {

			boolean isValid = validateDateFormat(dateInput, dateFormat);

			if (isValid) {

				System.out.println("Date format: " + dateFormat + " is valid.");
				waitForElementVisibile(dateInput);
				highLightElement(driver, dateInput, dateFormat, test);
				String actualCaptionValue = dateInput.getText().trim();
				System.out.println("Date format:" + actualCaptionValue);
				break;
			} else {
			}
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Validates if the date format of a given WebElement matches the expected date
	 * format. Compares the current system date in the specified format with the
	 * text of the WebElement. Returns true if the formats match, false otherwise.
	 * Handles exceptions and prints stack trace in case of errors.
	 *
	 * @param dateInput  The WebElement containing the date to be validated.
	 * @param dateFormat The expected date format to validate against.
	 * @return True if the date format is valid, false otherwise.
	 */
	public static boolean validateDateFormat(WebElement dateInput, String dateFormat) {
		try {

			String currentDateStr = new SimpleDateFormat(dateFormat).format(new Date());

			String enteredDateStr = dateInput.getText().trim();

			if (enteredDateStr.contains(currentDateStr)) {
				System.out.println("Date format: " + dateFormat + " is valid.");
				return true;

			} else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Verifies if the "value" attribute of a given WebElement matches the expected
	 * caption. Highlights the element, retrieves its attribute value, and compares
	 * it with the expected caption. Logs the result of the verification, handles
	 * exceptions, and captures screenshots in case of failures.
	 *
	 * @param element         The WebElement whose "value" attribute needs to be
	 *                        verified.
	 * @param expectedCaption The expected value of the "value" attribute.
	 * @param elementName     The name of the element, used for logging and failure
	 *                        reporting.
	 */
	public static void getAttributofValueandVerify(WebElement element, String expectedCaption, String elementName) {

		waitForElementVisibile(element);

		highLightElement(driver, element, "Elements", test);
		String actualCaptionValue = element.getAttribute("value").trim();
		System.out.println(actualCaptionValue);
		System.out.println(expectedCaption);
		try {

			if (actualCaptionValue.equals(expectedCaption)) {
			} else {
				test.fail("Expected Result: " + "<b>" + expectedCaption + "</b>" + " and actual Result was: " + "<b>"
						+ actualCaptionValue + "</b>" + "<b><font color=red> Test failed </font></b>");
//				captureScreen(driver, true, expectedCaption);
				Assert.assertEquals(actualCaptionValue, expectedCaption,
						"The actual Value does not match the expected Value.");
			}
		} catch (TimeoutException e) {
			test.fail(
					"The specified locator WebElement not found in the given time interval. Please check the provided locator value.");
		} catch (AssertionError e) {
			test.fail(elementName + " does not contain the expected caption: " + expectedCaption);
			throw e;
		} catch (Exception e) {
			test.fail("Step failed due to an unexpected exception: " + e.getMessage());
			captureScreen(driver, true, elementName);
		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and stores the first name, last name, and employee ID of an
	 * accepted user from their profile. Clicks on the profile icon, navigates to
	 * personal particulars, extracts and prints the first name, last name, and
	 * employee ID, and formats them into global variables for further use.
	 * Highlights elements and switches between frames as needed.
	 */
	public void getFirstNameLastNameFromProfileIconForAcceptUser() {

		WebElement userProfileIcon = driver.findElement(By.xpath("//ul[@class='navbar-nav']/li[2]/a"));
		// waitForElementVisibile(userProfileIcon);

		waitForElementVisibile(userProfileIcon);
		TimeUtil.mediumWait();
		click2(userProfileIcon, CommonStrings.Click_ProfileIconofUser_DC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AR.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_SS.getCommonStrings());
		WebElement personalParticulars = driver.findElement(By.xpath("//a[@id='RefreshA']//following-sibling::a"));
		click2(personalParticulars, CommonStrings.Click_PersonalParticulars_DC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AR.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		WebElement firstNameText = driver
				.findElement(By.xpath("//label[contains(text(),'First Name')]//following-sibling::span"));
		waitForElementVisibile(firstNameText);
		highLightElement(driver, firstNameText, "First Name", test);
		firstName = firstNameText.getText().trim();
		System.out.println(firstName);
		WebElement lastNameText = driver
				.findElement(By.xpath("//label[contains(text(),'Last Name')]//following-sibling::span"));

		lastName = lastNameText.getText().trim();
		highLightElement(driver, lastNameText, "Last Name", test);
		UserAcceptanceFullName = firstName + "." + lastName;
		System.out.println(lastName);
		WebElement employeeIDText = driver
				.findElement(By.xpath("//label[contains(text(),'Employee ID')]//following-sibling::span"));
		highLightElement(driver, employeeIDText, "Employee ID", test);
		employeeID = employeeIDText.getText().trim();
		UserAcceptanceFullNameEmployeeID = UserAcceptanceFullName + "(" + employeeID + ")";
		System.out.println(employeeID);
		switchToDefaultContent(driver);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Assigns the values of the global variables UserAcceptanceFullName and
	 * UserAcceptanceFullNameEmployeeID to the local variables
	 * UserAcceptinitiatorFLName and UserAcceptinitiatorFLNameEmpID respectively.
	 */
	public void getUserAcceptanceDetails() {

		UserAcceptinitiatorFLName = UserAcceptanceFullName;
		UserAcceptinitiatorFLNameEmpID = UserAcceptanceFullNameEmployeeID;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and stores the first name, last name, and employee ID of the user
	 * from their profile. Clicks on the profile icon, navigates to personal
	 * particulars, extracts and prints the first name, last name, and employee ID,
	 * and formats them into global variables for further use. Highlights elements
	 * and switches between frames as needed.
	 */
	public void getFirstNameLastNameFromProfileIconForADUser() {

		WebElement userProfileIcon = driver.findElement(By.xpath("//ul[@class='navbar-nav']/li[2]/a"));

		waitForElementVisibile(userProfileIcon);
		TimeUtil.mediumWait();
		click2(userProfileIcon, CommonStrings.Click_ProfileIconofUser_DC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AR.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_SS.getCommonStrings());
		WebElement personalParticulars = driver.findElement(By.xpath("//a[@id='RefreshA']//following-sibling::a"));
		click2(personalParticulars, CommonStrings.Click_PersonalParticulars_DC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AR.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		WebElement firstNameText = driver
				.findElement(By.xpath("//label[contains(text(),'First Name')]//following-sibling::span"));
		waitForElementVisibile(firstNameText);
		highLightElement(driver, firstNameText, "First Name", test);
		firstName = firstNameText.getText().trim();
		System.out.println(firstName);
		WebElement lastNameText = driver
				.findElement(By.xpath("//label[contains(text(),'Last Name')]//following-sibling::span"));

		lastName = lastNameText.getText().trim();
		highLightElement(driver, lastNameText, "Last Name", test);
		ADFullName = firstName + "." + lastName;
		System.out.println(lastName);
		WebElement employeeIDText = driver
				.findElement(By.xpath("//label[contains(text(),'Employee ID')]//following-sibling::span"));
		highLightElement(driver, employeeIDText, "Employee ID", test);
		employeeID = employeeIDText.getText().trim();
		ADFullNameEmployeeID = ADFullName + "(" + employeeID + ")";
		System.out.println(employeeID);
		switchToDefaultContent(driver);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Assigns the values of the global variables ADFullName and
	 * ADFullNameEmployeeID to the local variables ADAcceptinitiatorFLName and
	 * ADAcceptinitiatorFLNameEmpID respectively.
	 */
	public void getADAcceptanceDetails() {

		ADAcceptinitiatorFLName = ADFullName;
		ADAcceptinitiatorFLNameEmpID = ADFullNameEmployeeID;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Retrieves and saves the external code from the text of the specified
	 * WebElement. It splits the text to extract the code, trims it, and assigns it
	 * to a global variable. Also prints the extracted external code to the console.
	 */
	public static void saveExternalCode(WebDriver driver, WebElement element) {
		waitForElementVisibile(element);
		String message = element.getText().trim();
		String[] str = message.split(":");
		String saveExternalCode = str[1].trim();
		ExternalCode = saveExternalCode;
		System.out.println("ExternalCode value=" + saveExternalCode);
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement to open a file chooser dialog, then uses Robot to paste a
	 * file path and confirm the selection. Logs detailed test information including
	 * step description, acceptance criteria, and actual result. Takes a screenshot
	 * before and after the action, handles potential alerts, and manages exceptions
	 * by logging failures and capturing a screenshot.
	 */
	public void clickChooseFile(WebElement element, String DocUpload, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };
			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", element);

			element.click();

			isAlertPresent(driver);

			TimeUtil.mediumWait();

			Robot rb = new Robot();

			TimeUtil.mediumWait();

			StringSelection str = new StringSelection(DocUpload);

			Toolkit.getDefaultToolkit().getSystemClipboard().setContents(str, null);

			rb.keyPress(KeyEvent.VK_CONTROL);
			rb.keyPress(KeyEvent.VK_V);

			rb.keyRelease(KeyEvent.VK_CONTROL);
			rb.keyRelease(KeyEvent.VK_V);

			rb.keyPress(KeyEvent.VK_ENTER);
			rb.keyRelease(KeyEvent.VK_ENTER);

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement and logs detailed test information, including step
	 * description, acceptance criteria, and actual results. Takes multiple
	 * screenshots at different stages, highlights the element, scrolls to specified
	 * elements, and manages potential alerts. Logs failures and captures a
	 * screenshot if an exception occurs.
	 */
	public void click_Four_SS(WebElement element, WebElement ScreenshotElement, WebElement ScreenshotElement2,
			String stepDescription, String acceptanceCriteria, String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", element);

			element.click();

			isAlertPresent(driver);

			TimeUtil.mediumWait();
			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement);

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement2);

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement and logs detailed test information, including step
	 * description, acceptance criteria, and actual results. Takes screenshots at
	 * various stages, highlights the element, scrolls to multiple elements, and
	 * handles potential alerts. Logs failures and captures screenshots if an
	 * exception occurs.
	 */
	public void click_Five_SS(WebElement element, WebElement ScreenshotElement, WebElement ScreenshotElement2,
			WebElement ScreenshotElement3, String stepDescription, String acceptanceCriteria, String actualResult,
			String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", element);

				element.click();

				isAlertPresent(driver);

				TimeUtil.mediumWait();
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement);

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement2);

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement3);

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);
			}

			else {

				element.click();

				isAlertPresent(driver);

			}
		} catch (Exception e) {

			if (isReportedRequired == true) {

				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");
			}

			else {

				System.out.println(e.getMessage());
			}
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement and logs detailed test information, including step
	 * description, acceptance criteria, and actual results. Takes multiple
	 * screenshots at various stages, highlights the element, scrolls to multiple
	 * elements, switches to an iframe, and handles potential alerts. Logs failure
	 * details and captures a screenshot if an exception occurs.
	 */
	public void click_Five_SS_AuditTrails(WebElement element, WebElement ScreenshotElement,
			WebElement ScreenshotElement2, WebElement ScreenshotElement3, String stepDescription,
			String acceptanceCriteria, String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", element);
				element.click();

				driver.switchTo().frame(0);

				isAlertPresent(driver);

				TimeUtil.mediumWait();
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement);
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement2);
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement3);
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);
			} else {
				element.click();

				driver.switchTo().frame(0);

				isAlertPresent(driver);

			}
		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");
			} else {

				System.out.println(e.getMessage());
			}
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement and logs detailed test information, including step
	 * description, acceptance criteria, and actual results. Takes multiple
	 * screenshots at different stages, highlights the element, scrolls to various
	 * elements, switches to an iframe, and handles potential alerts. In case of an
	 * exception, logs the failure and captures a screenshot.
	 */
	public void click_Four_SS_AuditTrails(WebElement element, WebElement ScreenshotElement,
			WebElement ScreenshotElement2, String stepDescription, String acceptanceCriteria, String actualResult,
			String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", element);
			element.click();

			driver.switchTo().frame(0);

			isAlertPresent(driver);

			TimeUtil.mediumWait();
			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement);
			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement2);
			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Clicks a WebElement while logging detailed test information, including step
	 * description, acceptance criteria, and actual results. Takes screenshots
	 * before and after the click, highlights the element, handles any potential
	 * alerts, and manages exceptions by logging failures and capturing screenshots.
	 */

	public void clickScrollShot(WebElement element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName) {

		try {

			n = n + 1;

			highlightEle2(element);

			String[][] data1 = {

					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

							"<b>Actual Result</b>" },

					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
							"<div><b>* </b>" + actualResult } };

			Markup tableMarkup = MarkupHelper.createTable(data1);

			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

			test.log(Status.PASS, modifiedMarkup);

			attachScreenshot(driver, true, scName, "click2");

			jsExecutor.executeScript("arguments[0].style.border=''", element);

			element.click();
			Thread.sleep(2000);
			element.click();

			isAlertPresent(driver);

			TimeUtil.mediumWait();

			--screenshotCounter;

			attachScreenshot(driver, true, scName, "click2");

			attachScreenshot(driver, true, scName, "click2");

			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {

			n = n + 1;

			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

			test.log(Status.FAIL, "Exception :" + e.getMessage());

			attachScreenshot(driver, true, actualResult, "click2");

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Handles the process of clicking a WebElement while logging detailed test
	 * information, including step description, acceptance criteria, and actual
	 * results. Takes screenshots before and after the click, highlights the
	 * element, and manages any potential alerts. If an exception occurs, logs the
	 * failure and captures a screenshot.
	 */
	public void click_Three_SS(WebElement element, WebElement ScreenshotElement, String stepDescription,
			String acceptanceCriteria, String actualResult, String scName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				highlightEle2(element);

				String[][] data1 = {

						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",

								"<b>Actual Result</b>" },

						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data1);

				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());

				test.log(Status.PASS, modifiedMarkup);

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", element);

				element.click();

				isAlertPresent(driver);

				TimeUtil.mediumWait();
				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				jsExecutor.executeScript("arguments[0].scrollIntoView();", ScreenshotElement);

				--screenshotCounter;

				attachScreenshot(driver, true, scName, "click2");

				screenshotCounter = Math.round(screenshotCounter);

			} else {

				element.click();

				isAlertPresent(driver);
			}
		} catch (Exception e) {
			if (isReportedRequired == true) {
				n = n + 1;

				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + "</br>" + actualResult);

				test.log(Status.FAIL, "Exception :" + e.getMessage());

				attachScreenshot(driver, true, actualResult, "click2");

			} else {

				System.out.println(e.getMessage());
			}
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a document registration review date by setting the specified day,
	 * month, and year, adjusting the dropdown selections if necessary, and then
	 * clicking the appropriate date in the calendar. The selected date is stored in
	 * a global variable in the format "dd MMM yyyy".
	 */
	private static final String NETREVIEWDATE = "//body[1]/div[3]/div[2]/div[1]/table[1]//tbody//tr//td[contains(@class,'available')]";

	public void selectDocRegReviewDate(WebElement Melement, WebElement Yelement, String month, String year,

			String day) {

		waitForElementVisibile(Melement);

		Month Capitalmonth = Month.valueOf(month.toUpperCase());
		String abbreviatedMonth = Capitalmonth.toString().substring(0, 3);
		String result = abbreviatedMonth.toLowerCase();
		result = result.substring(0, 1).toUpperCase() + result.substring(1);

		int daySize = day.length();

		if (daySize > 1) {

			String modifiedDay = "0" + day;

			nextReviewDate = modifiedDay + " " + result + " " + year;
		}

		else {

			nextReviewDate = day + " " + result + " " + year;
		}

		nextReviewDate = day + " " + result + " " + year;

		String actualMonth = Melement.getText();

		String actualYear = Yelement.getText();

		if (!actualYear.equals(year)) {

			selectOptionFromDropdownByName(Yelement, year, "year");

			if (!actualMonth.equals(month)) {

				selectOptionFromDropdownByName(Melement, month, "month");

			}

		}

		List<WebElement> availableDates = driver.findElements(By.xpath(NETREVIEWDATE));

		for (WebElement date : availableDates) {

			String dayValue = date.getText();

			if (dayValue.equals(day)) {

				click2(date, DocumentRegistrationStrings.Select_NextReviewDate_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_NextReviewDate_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_NextReviewDate_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_NextReviewDate_SS.getDocumentRegistrationStrings());
				break;

			}

		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Selects a document registration effective date by setting the specified day,
	 * month, and year, adjusting the dropdown selections if necessary, and then
	 * clicking the appropriate date in the calendar. The date is stored in a global
	 * variable in the format "dd MMM yyyy".
	 */

	private static final String EFFECTIVEDATES = "//body[1]/div[2]/div[2]/div[1]/table[1]//tbody//tr//td[contains(@class,'available')]";

	public void selectDocRegEffectiveDate(WebElement Melement, WebElement Yelement, String month, String year,

			String day) {

		waitForElementVisibile(Melement);

		Month Capitalmonth = Month.valueOf(month.toUpperCase());
		String abbreviatedMonth = Capitalmonth.toString().substring(0, 3);
		String result = abbreviatedMonth.toLowerCase();
		result = result.substring(0, 1).toUpperCase() + result.substring(1);

		int daySize = day.length();

		if (daySize > 1) {

			String modifiedDay = "0" + day;

			DocEffDate = modifiedDay + " " + result + " " + year;

		}

		else {

			DocEffDate = day + " " + result + " " + year;
		}
		String actualMonth = Melement.getText();

		String actualYear = Yelement.getText();

		if (!actualYear.equals(year)) {

			selectOptionFromDropdownByName(Yelement, year, "year");

			if (!actualMonth.equals(month)) {

				selectOptionFromDropdownByName(Melement, month, "month");

			}

		}

		List<WebElement> availableDates = driver.findElements(By.xpath(EFFECTIVEDATES));

		for (WebElement date : availableDates) {

			String dayValue = date.getText();

			if (dayValue.equals(day)) {

				click2(date, DocumentRegistrationStrings.Select_EffectiveFrom_DC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AC.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_AR.getDocumentRegistrationStrings(),
						DocumentRegistrationStrings.Select_EffectiveFrom_SS.getDocumentRegistrationStrings());
				break;

			}

		}

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Saves the question paper name by retrieving the value of the specified
	 * WebElement, highlighting the element, and storing the value in a global
	 * variable. Also prints the retrieved value to the console.
	 */

	public static void saveQuestionPaperName(WebDriver driver, WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		System.out.println("FeildValueis=" + actualFeildValue);
		QPName = actualFeildValue;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Saves the question paper code by retrieving the value of the specified
	 * WebElement, highlighting the element, and storing the value in a global
	 * variable. Also prints the retrieved value to the console.
	 */

	public static void saveQuestionPaperCode(WebDriver driver, WebElement element, String elementName) {
		waitForElementVisibile(element);
		highLightElement(driver, element, elementName, test);
		String actualFeildValue = element.getAttribute("value");
		System.out.println("FeildValueis=" + actualFeildValue);
		QPUniqueCode = actualFeildValue;

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Converts the date from "MM/dd/yyyy" format to "dd MMM yyyy" format for the
	 * value of the provided WebElement.
	 */

	public static void ConvertSelectedDateFormatToDDMMMYYYY(WebElement element) {

		String actualCaptionValue = element.getAttribute("value");
		SimpleDateFormat format1 = new SimpleDateFormat("MM/dd/yyyy");
		SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy");
		Date date = null;
		try {
			date = format1.parse(actualCaptionValue);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		Convertedtime = format2.format(date);

	}

	// --------------------------------------------------------------------------------------------------------------------------------
	/**
	 * Prints the current day of the week (e.g., "Monday", "Tuesday") in English. It
	 * uses the Calendar class to get the current date and time, formats the date to
	 * retrieve the day of the week, and then prints it to the console.
	 */
	public static void currentWeekDay() {
		Calendar calendar = Calendar.getInstance();
		Date date = calendar.getTime();
		currentWeekDay = new SimpleDateFormat("EEEE", Locale.ENGLISH).format(date.getTime());
		System.out.println(currentWeekDay);

	}

	public void selectMultipleCheckBoxes(List<WebElement> element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName, String SkippedemployeeName) {

		try {
			if (isReportedRequired == true) {
				n = n + 1;

				String[][] data = {
						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
								"<b>Actual Result</b>" },
						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data);
				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
				test.log(Status.PASS, modifiedMarkup);
				attachScreenshot(driver, true, scName, "click2");

				for (WebElement row : element) {

					WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));

					WebElement employeeNameElement = row.findElement(By.xpath(".//td[1]"));

					String employeeName = employeeNameElement.getText();
					if (employeeName.equals(SkippedemployeeName)) {
						continue;
					}
					waitForElementVisibile(checkbox);
					checkbox.click();
				}

				// TimeUtil.shortWait();
				--screenshotCounter;
				attachScreenshot(driver, true, scName, "click2");
				screenshotCounter = Math.round(screenshotCounter);
			}

			else {
				for (WebElement row : element) {

					WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));

					WebElement employeeNameElement = row.findElement(By.xpath("./td[1]"));

					String employeeName = employeeNameElement.getText();
					if (employeeName.equals(SkippedemployeeName)) {
						continue;
					}

					checkbox.click();
				}

			}
		} catch (Exception e) {

			if (isReportedRequired == true) {
				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
				test.log(Status.FAIL, "Exception :" + e.getMessage());
				attachScreenshot(driver, true, scName, "click2");
			}

			else {

				System.out.println(e.getMessage());
			}
		}
	}

	/**
	 * Method to upload the File Using SendKeys
	 *
	 * @param element
	 * @param Path
	 */
	public void FileUploadWithSendKeys(WebElement element, String Path) {

		File uploadFile = new File(System.getProperty("user.dir") + Path);

		element.sendKeys(uploadFile.getAbsolutePath());
	}

	public void authenticationPopup(String password) {

		// create robot for keyboard operations
		Robot rb;
		try {
			rb = new Robot();

			// Enter user name by ctrl-v
			StringSelection username = new StringSelection(currentSysUsername);
			TimeUtil.shortWait();
			Toolkit.getDefaultToolkit().getSystemClipboard().setContents(username, null);
			Thread.sleep(1000);
			rb.keyPress(KeyEvent.VK_CONTROL);
			Thread.sleep(1000);
			rb.keyPress(KeyEvent.VK_V);
			Thread.sleep(1000);
			rb.keyRelease(KeyEvent.VK_CONTROL);
			rb.keyRelease(KeyEvent.VK_V);
			Thread.sleep(1000);

			// tab to password entry field
			rb.keyPress(KeyEvent.VK_TAB);
			rb.keyRelease(KeyEvent.VK_TAB);
			TimeUtil.shortWait();

			// Enter password by ctrl-v
			StringSelection pwd = new StringSelection(password);
			TimeUtil.shortWait();
			Toolkit.getDefaultToolkit().getSystemClipboard().setContents(pwd, null);
			Thread.sleep(1000);
			rb.keyPress(KeyEvent.VK_CONTROL);
			Thread.sleep(1000);
			rb.keyPress(KeyEvent.VK_V);
			Thread.sleep(1000);
			rb.keyRelease(KeyEvent.VK_CONTROL);
			rb.keyRelease(KeyEvent.VK_V);
			Thread.sleep(1000);

			// tab to password entry field
			rb.keyPress(KeyEvent.VK_TAB);
			rb.keyRelease(KeyEvent.VK_TAB);
			TimeUtil.shortWait();

			// press enter
			rb.keyPress(KeyEvent.VK_ENTER);
			rb.keyRelease(KeyEvent.VK_ENTER);

			TimeUtil.longwait();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void toBePlannedCountBefore_Session(String[] array1, String[] array2, String CourseSessionCoulumn,
			String CSRCoulumn) {

		System.out.println(CourseSessionCoulumn + ": " + array1.length);
		System.out.println(CSRCoulumn + ": " + array2.length);

		System.out.println(array2.length);
		// Comparing count of both CSR and Course Session To be Planned Column
		if (array1.length == array2.length) {
			System.out.println("Both " + CourseSessionCoulumn + " and " + CSRCoulumn + " have the same length.");
		} else if (array1.length > array2.length) {
			System.out.println(CourseSessionCoulumn + " longer than" + CSRCoulumn);
		} else {
			System.out.println(CSRCoulumn + " longer than" + CourseSessionCoulumn);
		}

		for (String str1 : array1) {
			boolean found = false;
			for (String str2 : array2) {
				if (str1.equals(str2)) {
					found = true;
					break;
				}
			}
			if (!found) {
				System.out.println(str1);
			}
		}

		// Print elements in array2 that are not in array1
		for (String str2 : array2) {
			boolean found = false;
			for (String str1 : array1) {
				if (str2.equals(str1)) {
					found = true;
					break;
				}
			}
			if (!found) {
				System.out.println(str2);
			}
		}

	}

//	public void toBePlannedCountData(String[] array1, String[] array2, String CourseSessionColumn, String CSRCoulumn) {
//
//// Print the lengths of the arrays
////		System.out.println(CourseSessionColumn + ": " + array1.length);
////		System.out.println(CSRCoulumn + ": " + array2.length);
//
//// Comparing the lengths of both arrays
//		if (array1.length == array2.length) {
//
//			test.pass(CourseSessionColumn + " and " + CSRCoulumn + "<b><font color='green'> are equal </font></b>");
//		} else if (array1.length > array2.length) {
//			System.out.println(CourseSessionColumn + " is longer than " + CSRCoulumn);
//			test.fail(CourseSessionColumn + "<b><font color=red> is greater than </font></b>" + CSRCoulumn);
//			appendToExcel(OutputPath, CourseSessionColumn + " is longer than " + CSRCoulumn);
//		} else {
//			System.out.println(CSRCoulumn + " is longer than " + CourseSessionColumn);
//			test.fail(CSRCoulumn + "<b><font color=red> is greather than </font></b>" + CourseSessionColumn);
//			appendToExcel(OutputPath, CSRCoulumn + " is longer than " + CourseSessionColumn);
//		}
//
//// Convert both arrays to Sets for faster lookup
//		Set<String> set1 = new HashSet<>();
//		Set<String> set2 = new HashSet<>();
//
//// Add elements to Set
//		for (String str1 : array1) {
//			set1.add(str1);
//
//		}
//		test.pass(CourseSessionColumn + "<b>" + set1 + "</b>");
//		for (String str2 : array2) {
//			set2.add(str2);
//
//		}
//		test.pass(CSRCoulumn + "<b>" + set2 + "</b>");
//// Print elements in array1 that are not in array2
//		System.out.println("Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ":");
//		appendToExcel(OutputPath, "Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ":");
//		for (String str1 : set1) {
//			if (!set2.contains(str1)) {
//				System.out.println(str1);
//				appendToExcel(OutputPath, str1);
//				test.fail("Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ": " + str1);
//			}
//
//		}
//
//// Print elements in array2 that are not in array1
//		System.out.println("Employees in " + CSRCoulumn + " not in " + CourseSessionColumn + ":");
//		appendToExcel(OutputPath,
//				"Employees in " + CSRCoulumn + "<b><font color=red> not in </font></b>" + CourseSessionColumn + ":");
//		for (String str2 : set2) {
//			if (!set1.contains(str2)) {
//				System.out.println(str2);
//				appendToExcel(OutputPath, str2);
//				test.fail("Employees in " + CSRCoulumn + "<b><font color=red> not in </font></b>" + CourseSessionColumn
//						+ ": " + str2);
//			}
//		}
//		System.out.println();
//		System.out.println();
//		appendToExcel(OutputPath, "---------------------");
//	}

//	public void compareCount(int num1, int num2, String Num1, String Num2) {
////		System.out.println(Num1 + ": " + num1);
////		System.out.println(Num2 + ": " + num2);
//		if (num1 > num2) {
//			test.fail(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color=red> is greated than </font></b>" + Num2
//					+ "  " + "<b>" + num2 + "</b>");
//			System.out.println(Num1 + "  " + num1 + " is greater than " + Num2 + "  " + num2);
//			appendToExcel(OutputPath, Num1 + "  " + num1 + " is greater than " + Num2 + "  " + num2);
//		} else if (num1 < num2) {
//
//			System.out.println(Num1 + "  " + num1 + " is less than " + Num2 + "  " + num2);
//			appendToExcel(OutputPath, Num1 + "  " + num1 + " is less than " + Num2 + "  " + num2);
//			test.fail(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color=red> is less than </font></b>" + Num2 + "  "
//					+ "<b>" + num2 + "</b>");
//		}
//
//		else if (num1 == num2) {
//
//			test.pass(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color='green'> is equal with </font></b>" + Num2
//					+ "  " + "<b>" + num2 + "</b>");
//
//		}
//
//	}

	
	
	
	public void compareCount(int num1, int num2, String Num1, String Num2) {
//		System.out.println(Num1 + ": " + num1);
//		System.out.println(Num2 + ": " + num2);
		if (num1 > num2) {
			test.fail(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color=red> is greated than </font></b>" + Num2
					+ "  " + "<b>" + num2 + "</b>");
			System.out.println(Num1 + "  " + num1 + " is greater than " + Num2 + "  " + num2);
			appendToExcel(OutputPath, Num1 + "  " + num1 + " is greater than " + Num2 + "  " + num2);
		} else if (num1 < num2) {
 
			System.out.println(Num1 + "  " + num1 + " is less than " + Num2 + "  " + num2);
			appendToExcel(OutputPath, Num1 + "  " + num1 + " is less than " + Num2 + "  " + num2);
			test.fail(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color=red> is less than </font></b>" + Num2 + "  "
					+ "<b>" + num2 + "</b>");
		}
 
		else if (num1 == num2) {
 
			test.pass(Num1 + "  " + "<b>" + num1 + "</b>" + "<b><font color='green'> is equal with </font></b>" + Num2
					+ "  " + "<b>" + num2 + "</b>");
 
		}
 
	}
	
	
	
	
	
	
	
	public void checkCommonElements(String[] array1, String[] array2, String array1name, String array2name) {
		// Convert the second array into a HashSet for fast lookup
		HashSet<String> set2 = new HashSet<>();
		for (String s : array2) {
			set2.add(s);
		}

		// Iterate through the first array and check if elements are in the HashSet
		for (String element : array1) {
			if (set2.contains(element)) {
				System.out.println(
						"Element " + element + " is available in both " + array1name + "and " + array2name + "arrays.");

				appendToExcel(OutputPath,
						"Element " + element + " is available in both " + array1name + "and " + array2name + "arrays.");
			}
		}

	}

	public void getCurrentTimeNew() {
		// Create a SimpleDateFormat object with the desired format "HH:mm"
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

		// Get the current date and time
		Date now = new Date();

		// Format the current date and time into the desired format
		currenttimenew = sdf.format(now);
	}

	public void selectTimeInRecordAttendanceOQFormat() throws Throwable {

		DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("HH:mm");

		// Parse the input time string to a LocalTime object
		LocalTime time = LocalTime.parse(currenttimenew, formatter1);

		// Add one minute to the time
		LocalTime newTime = time.plusMinutes(1);

		String batchSubmissionPlusOnemin = newTime.format(formatter1);
		String StartcurrentTimeHrsValue = batchSubmissionPlusOnemin.substring(0, 2);
		String StartcurrentTimeMinValue = batchSubmissionPlusOnemin.substring(3, 5);

		WebElement startTimeHourDropDown = driver
				.findElement(By.xpath("//span[@id='select2-StartTime_Hrs-container']"));
		click2(startTimeHourDropDown, RecordAttendanceStrings.Click_HH_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		WebElement startTimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		sendKeys2(startTimeDropDownSendText, RecordAttendanceStrings.Enter_End_Time_DC.getRecordAttendanceStrings(),
				StartcurrentTimeHrsValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		WebElement startselecthrs1 = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs1.click();
		TimeUtil.shortWait();
		click2(startselecthrs1, RecordAttendanceStrings.Click_HH_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		WebElement startTimeMinutesDropDown = driver.findElement(By.id("select2-StartTime_Mins-container"));
		// endTimeMinutesDropDown.click();
		click2(startTimeMinutesDropDown, RecordAttendanceStrings.Click_Min_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_SS.getRecordAttendanceStrings());

		WebElement starttimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		// endtimeDropDownSendText.sendKeys(currentTimeMinValue);

		sendKeys2(starttimeDropDownSendText, RecordAttendanceStrings.Enter_End_TimeMin_DC.getRecordAttendanceStrings(),
				StartcurrentTimeMinValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		WebElement startTimeSelected = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs2.click();

		click2(startTimeSelected, RecordAttendanceStrings.Click_Min_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		LocalTime newTime2 = time.plusMinutes(2);
//
//		LocalDateTime currentTime = LocalDateTime.now();
//		LocalDateTime updatedTime = currentTime.plusMinutes(0);
//		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
		String formattedTime = newTime2.format(formatter1);

		getCurrentTimeNew();

		duration(currenttimenew, formattedTime);

		if (duration.toMillis() > 0) {
			long du = duration.toMillis();
			Thread.sleep(duration.toMillis());
		}
		String currentTimeHrsValue = formattedTime.substring(0, 2);
		String currentTimeMinValue = formattedTime.substring(3, 5);

		WebElement endTimeHourDropDown = driver.findElement(By.xpath("//span[@id='select2-EndTime_Hrs-container']"));
		// endTimeHourDropDown.click();
		click2(endTimeHourDropDown, RecordAttendanceStrings.Click_HH_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_HH_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		WebElement endTimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		sendKeys2(endTimeDropDownSendText, RecordAttendanceStrings.Enter_End_Time_DC.getRecordAttendanceStrings(),
				currentTimeHrsValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		// endTimeDropDownSendText.sendKeys(currentTimeHrsValue);
		WebElement selecthrs1 = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs1.click();
		TimeUtil.shortWait();
		click2(selecthrs1, RecordAttendanceStrings.Click_HH_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		WebElement endTimeMinutesDropDown = driver.findElement(By.id("select2-EndTime_Mins-container"));
		// endTimeMinutesDropDown.click();
		click2(endTimeMinutesDropDown, RecordAttendanceStrings.Click_Min_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Min_SS.getRecordAttendanceStrings());

		WebElement endtimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
		// endtimeDropDownSendText.sendKeys(currentTimeMinValue);

		sendKeys2(endtimeDropDownSendText, RecordAttendanceStrings.Enter_End_TimeMin_DC.getRecordAttendanceStrings(),
				currentTimeMinValue, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		WebElement selecthrs2 = driver
				.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
		// selecthrs2.click();

		click2(selecthrs2, RecordAttendanceStrings.Click_Min_Value_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());

//		}
//		
//		else {
//			
//			
//			Thread.sleep(duration.toMillis());
//			LocalDateTime currentTimeAfterwait = LocalDateTime.now();
//			LocalDateTime updatedTimeAfterwait = currentTimeAfterwait.plusMinutes(0);
//			DateTimeFormatter formatterAfterwait = DateTimeFormatter.ofPattern("HH:mm");
//			String formattedTimeAfterwait = updatedTimeAfterwait.format(formatterAfterwait);
//			String currentTimeHrsValueAfterwait = formattedTimeAfterwait.substring(0, 2);
//			String currentTimeMinValueAfterwait = formattedTimeAfterwait.substring(3, 5);
//			
//			WebElement endTimeHourDropDown = driver.findElement(By.xpath("//span[@id='select2-EndTime_Hrs-container']"));
//			// endTimeHourDropDown.click();
//			click2(endTimeHourDropDown, RecordAttendanceStrings.Click_HH_DC.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_HH_AC.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_HH_AR.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_HH_SS.getRecordAttendanceStrings());
//
//			TimeUtil.shortWait();
//			WebElement endTimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
//			sendKeys2(endTimeDropDownSendText, RecordAttendanceStrings.Enter_End_Time_DC.getRecordAttendanceStrings(),
//					currentTimeHrsValueAfterwait, CommonStrings.sendKeys_AC.getCommonStrings(),
//					CommonStrings.sendKeys_AR.getCommonStrings(),
//					RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());
//
//			// endTimeDropDownSendText.sendKeys(currentTimeHrsValue);
//			WebElement selecthrs1 = driver
//					.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
//			// selecthrs1.click();
//			TimeUtil.shortWait();
//			click2(selecthrs1, RecordAttendanceStrings.Click_HH_Value_DC.getRecordAttendanceStrings(),
//					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//					RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());
//
//			WebElement endTimeMinutesDropDown = driver.findElement(By.id("select2-EndTime_Mins-container"));
//			// endTimeMinutesDropDown.click();
//			click2(endTimeMinutesDropDown, RecordAttendanceStrings.Click_Min_DC.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_Min_AC.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_Min_AR.getRecordAttendanceStrings(),
//					RecordAttendanceStrings.Click_Min_SS.getRecordAttendanceStrings());
//
//			WebElement endtimeDropDownSendText = driver.findElement(By.xpath("//input[@class='select2-search__field']"));
//			// endtimeDropDownSendText.sendKeys(currentTimeMinValue);
//
//			sendKeys2(endtimeDropDownSendText, RecordAttendanceStrings.Enter_End_TimeMin_DC.getRecordAttendanceStrings(),
//					currentTimeMinValueAfterwait, CommonStrings.sendKeys_AC.getCommonStrings(),
//					CommonStrings.sendKeys_AR.getCommonStrings(),
//					RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());
//
//			TimeUtil.shortWait();
//			WebElement selecthrs2 = driver
//					.findElement(By.xpath("//li[@class='select2-results__option select2-results__option--highlighted']"));
//			// selecthrs2.click();
//
//			click2(selecthrs2, RecordAttendanceStrings.Click_Min_Value_DC.getRecordAttendanceStrings(),
//					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//					RecordAttendanceStrings.Enter_End_Time_SS.getRecordAttendanceStrings());
//			
//		}
//		

	}

	// Duration Between two times in Milliseconds

	public void duration(String endTimeStr, String startTimeStr) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

		// Parse the input time strings into LocalTime objects
		LocalTime startTime = LocalTime.parse(startTimeStr, formatter);
		LocalTime endTime = LocalTime.parse(endTimeStr, formatter);

		// Calculate the duration between the two times
		duration = Duration.between(endTime, startTime);

		// Return the difference in milliseconds
		// return duration.toMillis();

	}

	public void convertString_To_Integer(String text) {

		stringToInt = Integer.parseInt(text);
	}

	public void enterToSSRSShadowRoot() {

		String strt = "return document.querySelector('ssrs-reportviewer').shadowRoot.querySelector('div > iframe')";
		JavascriptExecutor js = (JavascriptExecutor) driver;
		WebElement element3 = (WebElement) js.executeScript(strt);
		driver.switchTo().frame(element3);
	}

	public void InProgresUsersQualified() {

//		InProgresUsers = Stream.concat(Arrays.stream(CM_CSRReport.QualifiedEmployeesData), // Convert the first array to
//																							// Stream<String>
//				Arrays.stream(CM_CSRReport.RetakeEvaluation)) // Convert the second array to Stream<String>
//				.toArray(String[]::new);
		InProgresUsers = CM_CSRReport.QualifiedEmployeesData;

		InProgresUsersActualCount = InProgresUsers.length;

	}

	public void InsideData() {
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(30));

		// Define the locator for the element you want to wait for
		By elementLocator = By.xpath("//*[@id='ListTabUser']/tbody");

		// Wait until the element is visible on the page
		WebElement element = wait.until(ExpectedConditions.visibilityOfElementLocated(elementLocator));

//		WebElement courseRetrainingInsideCount= driver.findElement(By.xpath("//*[@id='ListTabUser']/tbody"));
//		waitForElementVisibile(courseRetrainingInsideCount);
		List<WebElement> insideCountrows = element.findElements(By.tagName("tr"));

		// Return the number of rows
		int CountInside = insideCountrows.size();

		InsideData = new String[CountInside];

		for (int i = 0; i < CountInside; i++) {
			WebElement cell = insideCountrows.get(i).findElements(By.tagName("td")).get(1);
			InsideData[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}
	}

	public void InsideDataAfterSGP_Selection() {

		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(30));

		// Define the locator for the element you want to wait for
		By elementLocator = By.xpath("//div[@id='modalBody_Transfer2']/div/table/tbody");

		// Wait until the element is visible on the page
		WebElement element = wait.until(ExpectedConditions.visibilityOfElementLocated(elementLocator));

//		WebElement courseRetrainingInsideCount= driver.findElement(By.xpath("//*[@id='ListTabUser']/tbody"));
//		waitForElementVisibile(courseRetrainingInsideCount);
		List<WebElement> insideCountrows = element.findElements(By.tagName("tr"));

		// Return the number of rows
		int CountInside = insideCountrows.size();

		InsideData = new String[CountInside];

		for (int i = 0; i < CountInside; i++) {
			WebElement cell = insideCountrows.get(i).findElements(By.tagName("td")).get(1);
			InsideData[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}
	}

	public void AddTrainees() {
		TimeUtil.mediumWait();
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));

		// Define the locator for the element you want to wait for
		By elementLocator = By.xpath("//*[@id='multipopupfilter2']/tbody");

		// Wait until the element is visible on the page
		WebElement element = wait.until(ExpectedConditions.visibilityOfElementLocated(elementLocator));

//			WebElement courseRetrainingInsideCount= driver.findElement(By.xpath("//*[@id='multipopupfilter2']/tbody"));
//			waitForElementVisibile(courseRetrainingInsideCount);
		List<WebElement> insideCountrows = element.findElements(By.tagName("tr"));

		// Return the number of rows
		int CountInside = insideCountrows.size();

		InsideData = new String[CountInside];

		for (int i = 0; i < CountInside; i++) {
			WebElement cell = insideCountrows.get(i).findElements(By.tagName("td")).get(1);
			InsideData[i] = cell.getText().trim();

			// System.out.println(InsideData[i]);

		}

	}

	public void getOutPutPath(String excelOutputPath) {

		OutputPath = excelOutputPath;

	}

	public static void appendToExcel(String filePath, String text) {
		FileInputStream fis = null;
		FileOutputStream fos = null;
		Workbook workbook = null;

		try {
			File file = new File(filePath);

//	            // If file exists, open it. If not, create a new workbook.
//	            if (file.exists()) {
			fis = new FileInputStream(file);
			workbook = new XSSFWorkbook(fis);
//	            } else {
//	                workbook = new XSSFWorkbook(); // Create a new workbook
//	            }
//
			Sheet sheet;
//	            if (workbook.getNumberOfSheets() == 0) {
//	                sheet = workbook.createSheet("Sheet1");
//	            } else {
			sheet = workbook.getSheetAt(0);
//	            }

			// Create a new row at the end of the sheet
			int lastRowNum = sheet.getLastRowNum();
			Row row = sheet.createRow(lastRowNum + 1);

			// Add data to the new row

			Cell cell = row.createCell(0);
			cell.setCellValue(text);

			// Write the changes to the file
			fos = new FileOutputStream(file);
			workbook.write(fos);

//	            System.out.println("Row appended successfully to: " + filePath);

		} catch (IOException e) {
			System.err.println("Error while handling the Excel file: " + e.getMessage());
		} finally {
			// Close resources properly
			try {
//	                if (workbook != null) {
//	                    ((Closeable) workbook).close(); // Explicitly use close() to release resources
//	                }
				if (fis != null) {
					fis.close();
				}
				if (fos != null) {
					fos.close();
				}
			} catch (IOException e) {
				System.err.println("Error while closing resources: " + e.getMessage());
			}
		}
	}

	public static void clearSheetData(String filePath, int sheetIndex) {
		FileInputStream fis = null;
		FileOutputStream fos = null;
		Workbook workbook = null;

		try {

			File file = new File(filePath);
			if (file.exists()) {
				fis = new FileInputStream(file);
				workbook = new XSSFWorkbook(fis);

				// Access the sheet
				Sheet sheet = workbook.getSheetAt(sheetIndex);

				// Remove all rows
				for (int i = sheet.getLastRowNum(); i >= 0; i--) {
					Row row = sheet.getRow(i);
					if (row != null) {
						sheet.removeRow(row);
					}
				}

				// Write the updated workbook back to the file
				fos = new FileOutputStream(file);
				workbook.write(fos);

				System.out.println("Sheet data cleared successfully!");

			}

			else {
				workbook = new XSSFWorkbook();
				Sheet sheet = workbook.createSheet("Sheet1");
				fos = new FileOutputStream(filePath);
				workbook.write(fos);

			}

		} catch (IOException e) {
			System.err.println("Error handling the Excel file: " + e.getMessage());
		} finally {
			// Close resources
			try {
//	                if (workbook != null) {
//	                    workbook.close();
//	                }
				if (fis != null) {
					fis.close();
				}
				if (fos != null) {
					fos.close();
				}
			} catch (IOException e) {
				System.err.println("Error closing resources: " + e.getMessage());
			}
		}
	}

	public void selectMultipleCheckBoxesAndSelectRadioButton(List<WebElement> element, String stepDescription, String acceptanceCriteria,
			String actualResult, String scName, String skippedUser, String qualifiedUsersStr,
			String toBeRetrainedUsersStr) {

		try {
			if (isReportedRequired) {
				n = n + 1;

				String[][] data = {
						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
								"<b>Actual Result</b>" },
						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
								"<div><b>* </b>" + actualResult } };

				Markup tableMarkup = MarkupHelper.createTable(data);
				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
				test.log(Status.PASS, modifiedMarkup);
				attachScreenshot(driver, true, scName, "click2");
			}

// Convert comma-separated user lists to arrays
			List<String> qualifiedUsers = Arrays.asList(qualifiedUsersStr.split(","));
			List<String> toBeRetrainedUsers = Arrays.asList(toBeRetrainedUsersStr.split(","));

			for (WebElement row : element) {
				WebElement employeeNameElement = row.findElement(By.xpath(".//td[1]"));
				String empName = employeeNameElement.getText().trim();

				if (empName.equals(skippedUser)) {
					continue;
				}

				WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));
				if (!checkbox.isSelected()) {
					checkbox.click();
				}

// Select the appropriate radio button based on qualification
				WebElement passRadioButton = row
						.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTPass_')]"));
				WebElement failRadioButton = row
						.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTFail_')]"));

				for (String qualifiedEmp : qualifiedUsers) {
					if (empName.contains(qualifiedEmp.trim())) {
						((JavascriptExecutor) driver).executeScript("arguments[0].click();", passRadioButton);
						break;
					}
				}

				for (String retrainEmp : toBeRetrainedUsers) {
					if (empName.contains(retrainEmp.trim())) {
						((JavascriptExecutor) driver).executeScript("arguments[0].click();", failRadioButton);
						break;
					}
				}
			}

			--screenshotCounter;
			attachScreenshot(driver, true, scName, "click2");
			screenshotCounter = Math.round(screenshotCounter);

		} catch (Exception e) {
			if (isReportedRequired) {
				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
				test.log(Status.FAIL, "Exception :" + e.getMessage());
				attachScreenshot(driver, true, scName, "click2");
			} else {
				System.out.println(e.getMessage());
			}
		}
	}
	
	public void clearField(WebElement element) {
		try {
			element.clear();
			if (!element.getAttribute("value").isEmpty()) {
				JavascriptExecutor js = (JavascriptExecutor) driver;
				js.executeScript("arguments[0].value = '';", element);
			}
		} catch (Exception e) {
			System.out.println("Failed to clear the field: " + e.getMessage());
		}
	}
	public void getFirstNameLastNameFromProfileIconReinitiateuser() {

		WebElement userProfileIcon = driver.findElement(By.xpath("//ul[@class='navbar-nav']/li[2]/a"));

		waitForElementVisibile(userProfileIcon);
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		click2(userProfileIcon, CommonStrings.Click_ProfileIconofUser_DC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AC.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_AR.getCommonStrings(),
				CommonStrings.Click_ProfileIconofUser_SS.getCommonStrings());
		WebElement personalParticulars = driver.findElement(By.xpath("//a[@id='RefreshA']//following-sibling::a[2]"));
		click2(personalParticulars, CommonStrings.Click_PersonalParticulars_DC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AC.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_AR.getCommonStrings(),
				CommonStrings.Click_PersonalParticulars_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		WebElement firstNameText = driver
				.findElement(By.xpath("//label[contains(text(),'First Name')]//following-sibling::span"));
		waitForElementVisibile(firstNameText);
		highLightElement(driver, firstNameText, "First Name", test);
		ReinitfirstName = firstNameText.getText().trim();
		System.out.println(ReinitfirstName);
		WebElement lastNameText = driver
				.findElement(By.xpath("//label[contains(text(),'Last Name')]//following-sibling::span"));

		ReinitlastName = lastNameText.getText().trim();
		highLightElement(driver, lastNameText, "Last Name", test);
		ReinitFullName = ReinitfirstName + "." + ReinitlastName;
		System.out.println(ReinitlastName);
		WebElement employeeIDText = driver
				.findElement(By.xpath("//label[contains(text(),'Employee ID')]//following-sibling::span"));
		highLightElement(driver, employeeIDText, "Employee ID", test);
		ReinitemployeeID = employeeIDText.getText().trim();
		ReinitFullNameemployeeID = ReinitFullName + "(" + ReinitemployeeID + ")";
		System.out.println(ReinitemployeeID);
		switchToDefaultContent(driver);
	}
	
	public void getUserNameFromEsgin() {
	    WebElement e = driver.findElement(By.id("UserESignUName"));
	    EmpNameEmpID = e.getAttribute("value").trim();
	    Empname = EmpNameEmpID.substring(0, EmpNameEmpID.indexOf('('));
	    EmpID = EmpNameEmpID.substring(EmpNameEmpID.indexOf('(') + 1, EmpNameEmpID.indexOf(')'));
	    
	
		}
	
	
	
	
	
	
	
	
	
	
	public void toBePlannedCountData(String[] array1, String[] array2, String CourseSessionColumn, String CSRCoulumn) {
		 
		// Print the lengths of the arrays
//				System.out.println(CourseSessionColumn + ": " + array1.length);
//				System.out.println(CSRCoulumn + ": " + array2.length);
 
		// Comparing the lengths of both arrays
				if (array1.length == array2.length) {
 
					test.pass(CourseSessionColumn + " and " + CSRCoulumn + "<b><font color='green'> are equal </font></b>");
				} else if (array1.length > array2.length) {
					System.out.println(CourseSessionColumn + " is longer than " + CSRCoulumn);
					test.fail(CourseSessionColumn + "<b><font color=red> is greater than </font></b>" + CSRCoulumn);
					appendToExcel(OutputPath, CourseSessionColumn + " is longer than " + CSRCoulumn);
				} else {
					System.out.println(CSRCoulumn + " is longer than " + CourseSessionColumn);
					test.fail(CSRCoulumn + "<b><font color=red> is greather than </font></b>" + CourseSessionColumn);
					appendToExcel(OutputPath, CSRCoulumn + " is longer than " + CourseSessionColumn);
				}
 
		// Convert both arrays to Sets for faster lookup
				Set<String> set1 = new HashSet<>();
				Set<String> set2 = new HashSet<>();
 
		// Add elements to Set
				for (String str1 : array1) {
					set1.add(str1);
 
				}
				test.pass(CourseSessionColumn + "<b>" + set1 + "</b>");
				for (String str2 : array2) {
					set2.add(str2);
 
				}
				test.pass(CSRCoulumn + "<b>" + set2 + "</b>");
		// Print elements in array1 that are not in array2
				System.out.println("Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ":");
				appendToExcel(OutputPath, "Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ":");
				for (String str1 : set1) {
					if (!set2.contains(str1)) {
						System.out.println(str1);
						appendToExcel(OutputPath, str1);
						test.fail("Employees in " + CourseSessionColumn + " not in " + CSRCoulumn + ": " + str1);
					}
 
				}
 
		// Print elements in array2 that are not in array1
				System.out.println("Employees in " + CSRCoulumn + " not in " + CourseSessionColumn + ":");
				appendToExcel(OutputPath,
						"Employees in " + CSRCoulumn + "<b><font color=red> not in </font></b>" + CourseSessionColumn + ":");
				for (String str2 : set2) {
					if (!set1.contains(str2)) {
						System.out.println(str2);
						appendToExcel(OutputPath, str2);
						test.fail("Employees in " + CSRCoulumn + "<b><font color=red> not in </font></b>" + CourseSessionColumn
								+ ": " + str2);
					}
				}
				System.out.println();
				System.out.println();
				appendToExcel(OutputPath, "---------------------");
			}
	
	
	
	
	
	
	
	
	
	public void click3(WebElement element, String stepDescription, String acceptanceCriteria, String actualResult,
            String scName) {
try {
 if (isReportedRequired) {
     n++;

     highlightEle2(element);

     String[][] data1 = {
         { "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>", "<b>Actual Result</b>" },
         { String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria, "<div><b>* </b>" + actualResult }
     };

     Markup tableMarkup = MarkupHelper.createTable(data1);
     String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
     test.log(Status.PASS, modifiedMarkup);

     attachScreenshot(driver, true, scName, "click2");

     jsExecutor.executeScript("arguments[0].style.border=''", element);

     safeClick(element);

     waitForElementVisibile(element);
     TimeUtil.mediumWait();

     safeClick(element);

     isAlertPresent(driver);

     TimeUtil.mediumWait();

     --screenshotCounter;

     attachScreenshot(driver, true, scName, "click2");

     screenshotCounter = Math.round(screenshotCounter);

 } else {
     waitForElementVisibile(element);

     safeClick(element);

     TimeUtil.mediumWait();
     TimeUtil.mediumWait();

     safeClick(element);
 }

} catch (Exception e) {
 if (isReportedRequired) {
     test.log(Status.FAIL, "<b>Step No. " + n + "</b> Failed to click " + actualResult);
     test.log(Status.FAIL, "Exception: " + e.getMessage());
     attachScreenshot(driver, true, scName, "click2");
 } else {
     System.out.println(e.getMessage());
 }
}
}

/**
* Attempts to click on the element and retries once if a StaleElementReferenceException occurs.
*/
private void safeClick(WebElement element) {
try {
 element.click();
} catch (StaleElementReferenceException e) {
 element.click();
}
}

}