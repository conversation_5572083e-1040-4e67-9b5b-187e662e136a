<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="SuiteForCountMimatch">
  <test thread-count="5" name="Test">
    <classes>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION"/>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_OFF_WITHOUT_ASSESSMENT"/>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_ON_WITHOUT_ASSESSMENT"/>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION"/>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_OFF_WITHOUT_ASSESSMENT"/>
      <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITHOUT_ASSESSMENT"/>
    </classes>
  </test> <!-- Test -->
</suite> <!-- SuiteForCountMimatch -->
