package com.Automation.Strings;

public enum CourseSessionStrings {

	// Common titles for Course session

	CourseSession_Menu("Click on 'Course Session' submenu."),

	Add_Required_AC("Selected record should be moved to 'Selected Items' column.</div>"),
	Add_Required_AR("Selected record is getting moved to 'Selected Items' cloumn.</div>"),
	Add_Required_SS("'Selected Items'"),

	Click_AddButton_DC("Click on 'Add' button and click 'OK' at alert(if any)."), Click_AddButton_SS("'Add' button"),

	// Course Session Configuration menus

	CourseSessionConfig_AC("'Course Session' Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),

	CourseSessionConfig_AR("'Course Session Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' are avialable.</div>"),

	CourseSessionConfig_SS("'Configuration'"),

	Click_DoneatCSConfig_AC("'Course Session Configuration Registration' screen should be displayed.</div>"),

	Click_DoneatCSConfig_AR("'Course Session Configuration Registration' screen is getting displayed.</div>"),

	// Course Session Registration Titles

	Propose_CoursSession_AC("'Course Session Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'Scheduled Courses, Unscheduled Courses and Interim Courses' tabs.</div>"
			+ "<div><b>*</b> The screen should contain 'Course Name' and 'Unique Code' columns.</div>"
			+ "<div><b>*</b> By default 'Scheduled Courses' tab should be selected.</div>"),
	Propose_CoursSession_AR("'Course Session Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Scheduled Courses', Unscheduled Courses and Interim Courses' tabs.</div>"
			+ "<div><b>*</b> The screen contains 'Course Name' and 'Unique Code' columns.</div>"
			+ "<div><b>*</b> By default 'Scheduled Courses' tab is getting selected.</div>"),
	Propose_Scheduled_CoursSession_AC("'Course Session Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'Scheduled Courses, Unscheduled Courses and Interim Courses' tabs.</div>"
			+ "<div><b>*</b> The screen should contain 'Course Name' and 'Unique Code' columns.</div>"
			+ "<div><b>*</b> By default 'Scheduled Courses' tab should be selected.</div>"),
	Propose_Scheduled_CoursSession_AR("'Course Session Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Scheduled Courses', Unscheduled Courses and Interim Courses' tabs.</div>"
			+ "<div><b>*</b> The screen contains 'Course Name' and 'Unique Code' columns.</div>"
			+ "<div><b>*</b> By default 'Scheduled Courses' tab is getting selected.</div>"),
	Propose_CoursSession_SS("'Course Session Registration Initiation' screen"),

	// Unschedule tab

	UnscheduledTab_DC("Click on 'Unscheduled Courses' tab."),
	UnscheduledTab_AC("List of the unscheduled courses should be displayed.</div>"),
	UnscheduledTab_AR("List of the unscheduled courses are getting displayed.</div>"),
	UnscheduledTab_SS("'Unscheduled Courses'"),

	// InterimTab

	InterimTab_DC("Click on 'Interim Courses' tab."),
	InterimTab_AC("List of the courses for which the Interim GTP is proposed should be displayed.</div>"),
	InterimTab_AR("List of the courses for which the Interim GTP is proposed are getting displayed.</div>"),
	InterimTab_SS("'Interim Courses'"),

	// Search By

	SearchBy_AC("Option to search with 'Top 250 Records, Course Name and Course Code' should be displayed.</div>"),
	SearchBy_AR("Option to search with 'Top 250 Records, Course Name and Course Code' are getting displayed.</div>"),

	// Search Field

	Like_CourseName_DC("Enter the above registered 'Course Name' in 'Like' field."),
	Like_InterimCourseName_DC(
			"Enter the above registered course name in 'Like' field for which the Interim GTP is proposed."),
	Like_CourseName_SS("Course Name"),

	// Click on displayed Course

	RegisteredCourse_DC("Click on above registred course."),
	Registered_InterimCourse_DC("Click on above registered course for which the Interim GTP is proposed."),
	RegisteredInterimCourse_AC(
			"Employee count should be displayed under 'To Be Planned, Skipped, Absentees, To Be Retrained, Total Trainees, To Retake Evaluation' columns."),
	RegisteredInterimCourse_AR(
			"Employee count is getting displayed under 'To Be Planned, Skipped, Absentees, To Be Retrained, Total Trainees, To Retake Evaluation' columns."),

	// Select SubGroups

	RegisteredCourse_AC("'Course Sessions Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The selected 'Course Name' should be displayed under 'Course Name' column.</div>"
			+ "<div><b>*</b> The screen should contain 'To Be Planned', 'Skipped', 'Absentees', 'To Be Re-Trained', and 'Total Trainees' and 'To Retake Evaluation' columns.</div>"
			+ "<div><b>*</b> Group Training Plan proposed Employee(s) count for respective course should be displayed under 'To Be Planned' column.</div>"
			+ "<div><b>*</b> 'Add Item' button should be available for 'Selected Subgroups List' field.</div>"
			+ "<div><b>*</b> The screen should contain 'Back' and 'Next' buttons.</div>"),
	RegisteredCourse_AR("'Course Sessions Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> The selected 'Course Name' is getting displayed under 'Course Name' column.</div>"
			+ "<div><b>*</b> The screen contains 'To Be Planned', 'Skipped', 'Absentees', 'To Be Re-Trained', and 'Total Trainees' and 'To Retake Evaluation' columns.</div>"
			+ "<div><b>*</b> Group Training Plan proposed Employee(s) count for respective course are getting under 'To Be Planned' column.</div>"
			+ "<div><b>*</b> 'Add Item' button is available for 'Selected Subgroups List' field.</div>"
			+ "<div><b>*</b> The screen contains 'Back' and 'Next' buttons.</div>"),
	RegisteredScheduledCourse_AC("'Course Sessions Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The selected 'Course Name' should be displayed under 'Course Name' column.</div>"
			+ "<div><b>*</b> The screen should contain 'To Be Planned', 'Skipped', 'Absentees', 'To Be Re-Trained', and 'Total Trainees' and 'To Retake Evaluation' columns.</div>"
			+ "<div><b>*</b> Training Schedule proposed Employee(s) count for respective course should be displayed under 'To Be Planned' column.</div>"
			+ "<div><b>*</b> 'Add Item' button should be available for 'Selected Subgroups List' field.</div>"
			+ "<div><b>*</b> The screen should contain 'Back' and 'Next' buttons.</div>"),
	RegisteredScheduledCourse_AR("'Course Sessions Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b> The selected 'Course Name' is getting displayed under 'Course Name' column.</div>"
			+ "<div><b>*</b> The screen contains 'To Be Planned', 'Skipped', 'Absentees', 'To Be Re-Trained', and 'Total Trainees' and 'To Retake Evaluation' columns.</div>"
			+ "<div><b>*</b> Group Training Plan proposed Employee(s) count for respective course are getting under 'To Be Planned' column.</div>"
			+ "<div><b>*</b> 'Add Item' button is available for 'Selected Subgroups List' field.</div>"
			+ "<div><b>*</b> The screen contains 'Back' and 'Next' buttons.</div>"),
	RegisteredCourse_SS("'Course Session Registration Initiation' screen"),
	RegisteredInterimCourse_SS("'Course Session Registration Initiation' screen"),

	// Click Add item for Selected Subgroup List.

	AddItem_Subgroup_DC("Click 'Add Item' button for 'Selected Subgroup List' field."),
	AddItem_Subgroup_AC("'Subgroup List' window should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', Advanced Search' & 'Close' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Subgroup Name' 'Subgroup Code', 'Previous Schedule' & 'Next Schedule' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Subgroup Name' and 'Subgroup Code' should be available.</div>"
			+ "<div><b>*</b> List of Subgroup(s) for which Group Training Plan is proposed for the selected course should be displayed for selection.</div>"
			+ "<div><b>*</b> The option to select multiple subgroups should be available.</div>"
			+ "<div><b>*</b> By default, 'Records Per Limit' should be displayed as '50'</div>"
			+ "<div><b>*</b> 'Cancel' and 'Add' button should be available.</div>"
			+ "<div><b>*</b> 'Add' button should be displayed in disabled mode.</div>"),
	AddItem_Subgroup_AR("'Subgroup List' window is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', Advanced Search' & 'Close' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Subgroup Name' 'Subgroup Code', 'Previous Schedule' & 'Next Schedule' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Subgroup Name' and 'Subgroup Code' are available.</div>"
			+ "<div><b>*</b> List of Subgroup(s) for which Group Training Plan is proposed for the selected course is getting displayed for selection.</div>"
			+ "<div><b>*</b> The option to select multiple subgroups are available.</div>"
			+ "<div><b>*</b> By default, 'Records Per Limit' is displayed as '50'</div>"
			+ "<div><b>*</b> 'Cancel' and 'Add' button are available.</div>"
			+ "<div><b>*</b> 'Add' button is getting displayed in disabled mode.</div>"),
	AddItem_Subgroup_SS("'Subroup List' window.<div>"),

	Find_Subgroup_DC("Enter the 'Subgroup Name' which is linked to the above registered course in 'Find' textbox"),
	Find_Subgroup_SS("'Subgroup Name'"),

	Add_Required_Subgroup_DC("Click on 'Add' against the subgroup which is selected at course registration."),

	Click_AddButton_AC("Selected subgroup name(s) should be displayed for 'Selected Subgroup List' field.</div>"),
	Click_AddButton_AR("Selected subgroup name(s) is/are getting displayed for 'Selected Subgroup List' field.</div>"),

	Click_NextButton_AC(
			"The screen should contain selected course details like 'Course Name-Session No', 'Course Code', 'Course Description'.</div>"
					+ "<div><b>*</b> List of Topic(s) which are mapped for selected course should be displayed under 'Topics Covered section with 'Topic', 'Subject' & 'Category' details.</div>"
					+ "<div><b>*</b> The screen should contain 'Session Information Management' & 'Select Trainees' fields.</div>"),
	Click_NextButton_AR(
			"The screen contains selected course details like 'Course Name-Session No', 'Course Code', 'Course Description'.</div>"
					+ "<div><b>*</b> List of Topic(s) which are mapped for selected course are displayed under 'Topics Covered section with 'Topic', 'Subject' & 'Category' details.</div>"
					+ "<div><b>*</b> The screen contains 'Session Information Management' & 'Select Trainees' fields.</div>"),

	// Click Session Information Management

	SessionInfo_DC("Click the drop down for 'Session Information Management' field."),
	SessionInfo_AC("The dropdown list should contain 'On-Line' and 'Off-Line' options.</div>"),
	SessionInfo_AR("The dropdown list contains 'On-Line' and 'Off-Line' options.</div>"),
	SessionInfo_SS("Option to select Online and Offline"),

	// Select Session Information Management

	SIM_Online_DC("Select 'Session Information Management' as 'On-Line'"),
	SIM_Online_AC("Selection should be accepted.</div>"), SIM_Online_AR("Selection is getting accepted.</div>"),
	SIM_Online_SS("'On-Line'"),

	AddItem_ITTypeTrainee_DC("Click on 'Add Item' for Select Trainees List' field."),
	AddItem_Trainee_DC("Click on 'Add Item' for Select Trainees List field and click 'OK' at alert."),
	AddItem_Trainee_AC("'Trainee List' window should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page' and 'Close' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Employee Name', 'Employee ID', 'Department', 'Last Attended On' columns.</b> </div>"
			+ "<div><b>*</b> List of Employee Name(s) who have been assigned under 'Login' category for the selected subgroup(s) should be displayed.</div>"
			+ "<div><b>*</b> The Employee(s) for whom Job Responsibility is not yet completed also should be listed with the 'Add' button in disabled mode and there should be no option to select the Employees whose job responsibility is not completed (if any).</div>"
			+ "<div><b>*</b> The screen should contain 'Load More Details', 'Add All', 'Cancel' & 'Add' buttons.</div>"
			+ "<div><b>*</b> 'Add' button should be displayed in disabled mode.</div>"
			+ "<div><b>*</b> Note: 'Disabled Button Against Employee Selection Indicates Pending Job Responsibility' and 'Blue Color Fill Indicates OJT Completion' should be displayed at bottom of the screen.</div>"),
	AddItem_Trainee_AR("'Trainee List' window is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page' and 'Close' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Employee Name', 'Employee ID', 'Department', 'Last Attended On' columns.</b> </div>"
			+ "<div><b>*</b> List of Employee Name(s) who have been assigned under 'Login' category for the selected subgroup(s) are getting displayed.</div>"
			+ "<div><b>*</b> The Employee(s) for whom Job Responsibility is not yet completed also listed with the 'Add' button in disabled mode and there is no option to select the Employees whose job responsibility is not completed (if any).</div>"
			+ "<div><b>*</b> The screen contains 'Load More Details', 'Add All', 'Cancel' & 'Add' buttons.</div>"
			+ "<div><b>*</b> 'Add' button is getting displayed in disabled mode.</div>"
			+ "<div><b>*</b> Note: 'Disabled Button Against Employee Selection Indicates Pending Job Responsibility' and 'Blue Color Fill Indicates OJT Completion' is getting displayed at bottom of the screen.</div>"),
	AddItem_Trainee_SS("'Trainee List' window"),

	Enter_TraineeName_DC("Enter the required valid 'Employee Name' in 'Search This Page' textbox"),
	Enter_OJT_TraineeName_DC("Enter the above employee registered name for whom the On Job Training is required."),

	Enter_TraineeName_SS("'Employee Name'"),

	// Click Add item Trainee

	Add_RequiredTrainee_DC("Click on 'Add' against the required trainee."),
	Add_OJT_Trainee_DC("Click on 'Add' against the required trainee for whom the On Job Training is required."),

	Click_AddButton_Trainee_AC(
			"The selected value for 'Session Information Management' should be displayed in read only format.</div>"
					+ "<div><b>*</b> Selected Employee(s) count should be displayed for 'Total Selected Trainees' field</div>"
					+ "<div><b>*</b> The option to select 'Training Method' with 'Internal Classroom', 'External', 'Shop Floor', 'Document Reading' and 'Completed Trainings options should be displayed.</div>"),
	Click_AddButton_Trainee_AR(
			"The selected value for 'Session Information Management' is getting displayed in read only format.</div>"
					+ "<div><b>*</b> Selected Employee(s) count is getting displayed for 'Total Selected Trainees' field</div>"
					+ "<div><b>*</b> The option to select 'Training Method' with 'Internal Classroom', 'External', 'Shop Floor', 'Document Reading' and 'Completed Trainings options are displayed.</div>"),
	Click_AddButton_Trainee_SS("'Add' button"),

	// select document reading as training method

	Select_TM_DR_DC("Select 'Document Reading' as 'Training Method'"),
	Select_TM_DR_AC(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options should be available.</div>"),
	Select_TM_DR_AR(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options are available.</div>"),
	Select_TM_DR_SS("'Documemnt Reading'"),

	// Assessment required as No

	Assessment_No_DC("Select 'Assessment Required' as 'No/Verbal'."),
	Assessment_No_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	Assessment_No_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"),
	Assessment_No_SS("'Assessment Required'"),

	// Next Button

	Next2_AC("All the selected/entered particulars should be displayed.</div>"
			+ "<div><b>*</b> The option to select documents should be available.</div>"),
	Next2_AR("All the selected/entered particulars are getting displayed.</div>"
			+ "<div><b>*</b> The option to select documents are available.</div>"),
	Next2_SS("'Next' button"),

	// E-sign

	CourseSession_Esign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Course Session: Registration Initiation'.</div>"),
	CourseSession_Esign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Course Session: Registration Initiation'.</div>"),

	// CourseSession_Esign_SS("'Meaning of This Electronic Signature' window is
	// getting displayed as 'Course: Registration Initiation'.</div>"),
	// Proceed Button

	Proceed_CourseSession_AC(
			"'Course Sessions Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Proceed_CourseSession_AR(
			"Course Sessions Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),
	Proceed_CourseSession_SS("'Proceed'"),

	CourseSession_AuditTrails_Menu_AC("'Course Sessions Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Course Session Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.</div>"
			+ "<div><b>*</b> The screen should contain a list of all the registered Course Sessions.</div>"),
	CourseSession_AuditTrails_Menu_AR("'Course Sessions Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Course Session Name Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is displayed as '20'.</div>"
			+ "<div><b>*</b> The screen contains a list of all the registered Course Sessions.</div>"),
	CourseSession_AuditTrails_Menu_SS("'Course Session Audit Trails' screen"),

	AT_SearchBy_AC(
			"Option to search with 'Top 250 Records, Course Session Name, Unique Code and Initiated Between' should be available."),
	AT_SearchBy_AR(
			"Option to search with 'Top 250 Records, Course Session Name, Unique Code and Initiated Between' are available.</div>"),

	// Search by Course Session Name

	SearchBy_CoursSessionName_DC("Select 'Course Session Name'."),
	SearchBy_CoursSessionName_AC("Selection should be accepted.<div>"),
	SearchBy_CoursSessionName_AR("Selection is getting accepted.<div>"),
	SearchBy_CoursSessionName_SS("'Course Session Name'."),

	// Enter Course Session Name

	Like_CSName_DC("Enter the above registered 'Course Session Name' in 'Like' field."),
	Like_CSName_SS("'Course Session Name'"),

	Click_CS_for_AuditTrails_DC("Click on the above registered 'Course Session Name'."),
	Click_CS_for_AuditTrails_AC(
			"'Course Sessions - Audit Trails Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should contain the details of the Course Session entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Initiated'.</div>"
					+ "<div><b>*</b> The 'Events' section should contain registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be read as '1' and the 'No. of Approvals Completed' should be read as '0'.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_CS_for_AuditTrails_AR(
			"'Course Sessions - Audit Trails Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen contains the details of the Course Session entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed displayed as 'Initiated'.</div>"
					+ "<div><b>*</b> The 'Events' section contains registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' reads as '1' and the 'No. of Approvals Completed'  reads as '0'.</div>"
					+ "<div><b>*</b> All the particulars are displayed in read only format.</div>"),
	Click_CS_for_AuditTrails_SS("'Course Sessions-Audit Trails"),

	// Offline

	SIM_Offline_DC("Select 'Off-Line'"), SIM_Offline_AC("Selected value should be accepted.</div>"),
	SIM_Offline_AR("Selected value is getting accepted."), SIM_Offline_SS("'Off-Line'"),

	OJTRemarks_DC("Enter the required value in 'Resons for OJT Pending' text box."), OJTRemarks_SS("'OJT Remarks'"),

	RTCB_NO_DC("Select the 'Reading To Be Completed' as 'Not Required'."),
	RTCB_NO_AC("Selected option should be accepted for 'Reading To Be Completed' field.</div>"),
	RTCB_NO_AR("Selected option is getting accepted for 'Reading To Be Completed' field.</div>"), RTCB_NO_SS("'RTCB'"),

	Assessment_Yes_DC("Select 'Assessment Required' as 'Yes'."),

	// Enter OJT Remarks

	Enter_OJTRemarks_DC(
			"Enter the value less than or equals to 250 characters in 'Reasons for Selecting OJT Pending Employee(s)' field."),
	Enter_OJTRemarks_SS("'Reasons for Selecting OJT Pending Employee(s)'"),

	// Select internal classroom as Training Method

	Select_TM_IntClassRoom_DC("Select 'Internal Classroom' or 'External' option for 'Training Method' field."),
	Select_TM_IntClassRoom_AC("The option to enter 'Average Session Size' field should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Total Number of Sessions' and should be displayed in read only format.</div>"
			+ "<div><b>*</b> The option to select 'Content Delivery', 'Evaluation Required', 'Feedback Required' and 'Assessment Required' fields should be available.</div>"),
	Select_TM_IntClassRoom_AR("The option to enter 'Average Session Size' field is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Total Number of Sessions' and is getting displayed in read only format.</div>"
			+ "<div><b>*</b> The option to select 'Content Delivery', 'Evaluation Required', 'Feedback Required' and 'Assessment Required' fields are available.</div>"),
	Select_TM_IntClassRoom_SS("'Internal Classroom'"),

	// Enter Average session Size

	Enter_AvgSessSize_DC("Enter the value greater than '1 ' and less than '99' in 'Average Session Size' feild."),
	Enter_AvgSessSize_AC("Entered value should be displayed for the field.</div>"
			+ "<div><b>*</b> 'Total Number of Sessions' value should be updated based on the entered 'Average Session Size'value.</div>"),
	Enter_AvgSessSize_AR("Entered value is getting displayed for the field.</div>"
			+ "<div><b>*</b> 'Total Number of Sessions' value is getting updated based on the entered 'Average Session Size' value.</div>"),
	Enter_AvgSessSize_SS("'Average Session Size'"),

	// Content delivery as No

	Select_ContentDeliveryNo_DC("Select 'No' for 'Content Delivery' field."),
	Select_ContentDeliveryNo_AC("Selected option should be accepted for 'Content Delivery' field."),
	Select_ContentDeliveryNo_AR("Selected option is getting accepted for 'Content Delivery' field."),
	Select_ContentDeliveryNo_SS("'Content Delivery'."),

	Select_ContentDeliveryYes_DC("Select 'Yes' for 'Content Delivery' field."),
	Select_ContentDeliveryYes_AC("Selected option should be accepted for 'Content Delivery' field."),
	Select_ContentDeliveryYes_AR("Selected option is getting accepted for 'Content Delivery' field."),
	Select_ContentDeliveryYes_SS("'Content Delivery'."),

	Select_EvaluationRequiredNo_DC("Select 'No' for 'Evaluation Required' field."),
	Select_EvaluationRequiredNo_AC("Selected option should be accepted for 'Evaluation Required' field."),
	Select_EvaluationRequiredNo_AR("Selected option is getting accepted for 'Evaluation Required' field."),
	Select_EvaluationRequiredNo_SS("'Evaluation Required'."),

	// Feedback required as No

	Select_FeedbackRequiredNo_DC("Select 'No' for 'Feedback Required' field."),
	Select_FeedbackRequiredNo_AC("Selected option should be accepted for 'Feedback Required' field."),
	Select_FeedbackRequiredNo_AR("Selected option is getting accepted for 'Feedback Required' field."),
	Select_FeedbackRequiredNo_SS("'Feedback Required'."),

	Select_AssessmentRequiredYes_DC("Select 'Yes' for 'Assessment Required' field."),
	Select_AssessmentRequiredNo_DC("Select 'No' for 'Assessment Required' field."),
	Select_AssessmentRequiredYes_AC("Selected option should be accepted for 'Assessment Required' field.</div>"
			+ "<div><b>*</b> Missed Question Analysis' field should be displayed with 'Yes' and 'No' options.</div>"),

	Select_OfflineAssessmentRequiredYes_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	Select_OfflineAssessmentRequiredYes_AR(
			"Selected option is getting accepted for 'Assessment Required' field.</div>"),
	Select_AssessmentRequiredYes_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"
			+ "<div><b>*</b> Missed Question Analysis' field are getting displayed with 'Yes' and 'No' options.</div>"),
	Select_AssessmentRequiredYes_SS("'Assessment Required'."),

	// Missed Question Analysis

	MQA_NO_DC("Select 'No' for 'Missed Question Analysis' field."),
	MQA_NO_AC("Selected option should be accepted for 'Missed Question Analysis' field.</div>"),
	MQA_NO_AR("Selected option is getting accepted for 'Missed Question Analysis' field.</div>"),
	MQA_NO_SS("'Missed Question Analysis'."),

	MQA_Yes_DC("Select 'Yes' for 'Missed Question Analysis' field."),

	// Next Button

	UserSelectionScreenNextBtn_DC("Click on 'Next' button"),
	UserSelectionScreenNextBtnWithApprove_AC(
			"The screen should contain 'Course Name', 'Course Code', 'Course Description', 'Training Method', 'Feedback Required', 'Feedback Template', 'Evaluation Required', 'Long-term Evaluation Template', 'Short-term Evaluation Template', 'Assessment Required', 'Missed Question Analysis', 'View Selected Employee(s)', 'Average Session Size', 'Total Number of Sessions', 'Course Sessions Name', 'Unique Code', 'Session Information Management' and 'Training Type' fields should be displayed read only format."
					+ "<div><b>*</b> 'Course Session Name' and 'Unique Code' should be system generated.</div>"
					+ "<div><b>*</b> 'Course Session Name' should be generated by using '<Course Name>/ <Type of Training Method>/ <Number of Session></div>"
					+ "<div><b>*</b> 'Course Session Unique Code' should be generated by using '<Course Code> / <Year> / <Number of Session>'.</div>"
					+ "<div><b>*</b> The option to enter / select session details should be available (based on Average Session size the Number of sessions should be created).</div>"
					+ "<div><b>*</b> Start Date, End Date and Last Date for Response fields should be displayed with date picker.</div>"
					+ "<div><b>*</b> Start Time and End Time fields with HH (Hours), MM (Minutes) dropdown controls and 'Number of Training Hours' fields should be displayed.</div>"
					+ "<div><b>*</b> Based on 'Start Date', 'End Date' and 'Start time', 'End Time' the Total Number of Hours for Training should be displayed at 'Number of Training Hours' field as read only format</div>"
					+ "<div><b>*</b> The option to select the 'Trainer' and 'Training Venue' should be available.</div>"
					+ "<div><b>*</b> The option to select the 'Approver 1' should be available with dropdown under 'Number of Approvals Required For Initiation: <Number>' section.</div>"),
	UserSelectionScreenNextBtnWithApprove_AR(
			"The screen contains 'Course Name', 'Course Code', 'Course Description', 'Training Method', 'Feedback Required', 'Feedback Template', 'Evaluation Required', 'Long-term Evaluation Template', 'Short-term Evaluation Template', 'Assessment Required', 'Missed Question Analysis', 'View Selected Employee(s)', 'Average Session Size', 'Total Number of Sessions', 'Course Sessions Name', 'Unique Code', 'Session Information Management' and 'Training Type' fields should be displayed read only format."
					+ "<div><b>*</b> 'Course Session Name' and 'Unique Code' are system generated.</div>"
					+ "<div><b>*</b> 'Course Session Name' is generated by using '<Course Name>/ <Type of Training Method>/ <Number of Session></div>"
					+ "<div><b>*</b> 'Course Session Unique Code' is generated by using '<Course Code> / <Year> / <Number of Session>'.</div>"
					+ "<div><b>*</b> The option to enter / select session details are available (based on Average Session size the Number of sessions should be created).</div>"
					+ "<div><b>*</b> Start Date, End Date and Last Date for Response fields are getting displayed with date picker.</div>"
					+ "<div><b>*</b> Start Time and End Time fields with HH (Hours), MM (Minutes) dropdown controls and 'Number of Training Hours' fields are getting displayed.</div>"
					+ "<div><b>*</b> Based on 'Start Date', 'End Date' and 'Start time', 'End Time' the Total Number of Hours for Training are displayed at 'Number of Training Hours' field as read only format</div>"
					+ "<div><b>*</b> The option to select the 'Trainer' and 'Training Venue' are available.</div>"
					+ "<div><b>*</b> The option to select the 'Approver 1' is available with dropdown under 'Number of Approvals Required For Initiation: <Number>' section.</div>"),
	UserSelectionScreenNextBtn_AC(
			"The screen should contain 'Course Name', 'Course Code', 'Course Description', 'Training Method', 'Feedback Required', 'Feedback Template', 'Evaluation Required', 'Long-term Evaluation Template', 'Short-term Evaluation Template', 'Assessment Required', 'Missed Question Analysis', 'View Selected Employee(s)', 'Average Session Size', 'Total Number of Sessions', 'Course Sessions Name', 'Unique Code', 'Session Information Management' and 'Training Type' fields should be displayed read only format."
					+ "<div><b>*</b> 'Course Session Name' and 'Unique Code' should be system generated.</div>"
					+ "<div><b>*</b> 'Course Session Name' should be generated by using '<Course Name>/ <Type of Training Method>/ <Number of Session></div>"
					+ "<div><b>*</b> 'Course Session Unique Code' should be generated by using '<Course Code> / <Year> / <Number of Session>'.</div>"
					+ "<div><b>*</b> The option to enter / select session details should be available (based on Average Session size the Number of sessions should be created).</div>"
					+ "<div><b>*</b> Start Date, End Date and Last Date for Response fields should be displayed with date picker.</div>"
					+ "<div><b>*</b> Start Time and End Time fields with HH (Hours), MM (Minutes) dropdown controls and 'Number of Training Hours' fields should be displayed.</div>"
					+ "<div><b>*</b> Based on 'Start Date', 'End Date' and 'Start time', 'End Time' the Total Number of Hours for Training should be displayed at 'Number of Training Hours' field as read only format</div>"
					+ "<div><b>*</b> The option to select the 'Trainer' and 'Training Venue' should be available.</div>"),
	UserSelectionScreenNextBtn_AR(
			"The screen contains 'Course Name', 'Course Code', 'Course Description', 'Training Method', 'Feedback Required', 'Feedback Template', 'Evaluation Required', 'Long-term Evaluation Template', 'Short-term Evaluation Template', 'Assessment Required', 'Missed Question Analysis', 'View Selected Employee(s)', 'Average Session Size', 'Total Number of Sessions', 'Course Sessions Name', 'Unique Code', 'Session Information Management' and 'Training Type' fields should be displayed read only format."
					+ "<div><b>*</b> 'Course Session Name' and 'Unique Code' are system generated.</div>"
					+ "<div><b>*</b> 'Course Session Name' is generated by using '<Course Name>/ <Type of Training Method>/ <Number of Session></div>"
					+ "<div><b>*</b> 'Course Session Unique Code' is generated by using '<Course Code> / <Year> / <Number of Session>'.</div>"
					+ "<div><b>*</b> The option to enter / select session details are available (based on Average Session size the Number of sessions should be created).</div>"
					+ "<div><b>*</b> Start Date, End Date and Last Date for Response fields are getting displayed with date picker.</div>"
					+ "<div><b>*</b> Start Time and End Time fields with HH (Hours), MM (Minutes) dropdown controls and 'Number of Training Hours' fields are getting displayed.</div>"
					+ "<div><b>*</b> Based on 'Start Date', 'End Date' and 'Start time', 'End Time' the Total Number of Hours for Training are displayed at 'Number of Training Hours' field as read only format</div>"
					+ "<div><b>*</b> The option to select the 'Trainer' and 'Training Venue' are available.</div>"),
	UserSelectionScreenNextBtn_SS("'Next'"),

	// Start Date

	StartDate_DC("Click on the date picker icon for the 'Start Date' field."),
	StartDate_AC("By default, current month of current year calendar should be displayed.</div>"
			+ "<div><b>*</b> By default, current date should be highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date should not be available.</div>"),
	StartDate_AR("By default, current month of current year calendar is getting displayed.</div>"
			+ "<div><b>*</b> By default, current date is getting highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date is not be available.</div>"),
	StartDate_SS("'Start Date'"),

	SelectStartDate_DC("Select the date which is equal to current date for 'Start Date' field."),
	SelectStartDate_AC("Selected date should be displayed for 'Start Date' field."),
	SelectStartDate_AR("Selected date is getting displayed for 'Start Date' field."),

	EndDate_DC("Click on the date picker icon for the 'End Date' field."),
	EndDate_AC("By default, current month of current year calendar should be displayed.</div>"
			+ "<div><b>*</b> By default, current date should be highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date should not be available.</div>"),
	EndDate_AR("By default, current month of current year calendar is getting displayed.</div>"
			+ "<div><b>*</b> By default, current date is getting highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date is not be available.</div>"),
	EndDate_SS("'End Date'"),

	SelectEndDate_DC("Select the date which is equal to Start Date for 'End Date' field."),
	SelectEndDate_AC("Selected date should be displayed for 'End Date' field."),
	SelectEndDate_AR("Selected date is getting displayed for 'End Date' field."),

	Response_DC("Click on the date picker icon for the 'Last Date of Response' field."),
	Response_AC("By default, current month of current year calendar should be displayed.</div>"
			+ "<div><b>*</b> By default, current date should be highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date should not be available.</div>"),
	Response_AR("By default, current month of current year calendar is getting displayed.</div>"
			+ "<div><b>*</b> By default, current date is getting highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select less than the current date is not be available.</div>"),
	Response_SS("'Last Date of Response'"),

	SelectLastDate_DC("Select the date equal to Start Date for 'Last Date of Response' field."),
	SelectLastDate_AC("Selected date should be displayed for 'Last Date of Response' field."),
	SelectLastDate_AR("Selected date is getting displayed for 'Last Date of Response' field."),

	Click_StartTime_DC("Click on the hours field under 'Start Time (HH:MM)'"),
	Click_StartTime_AC("Option to select hours from '00' to '24' should be available."),
	Click_StartTime_AR("Option to select hours from '00' to '24' is available."),

	Click_StartTime_SS("'Start Time'"),

	Click_StartTime_Min_DC("Click on the minutes field under 'Start Time (HH:MM)'"),
	Click_StartTime_Min_AC("Option to select minutes should be available."),
	Click_StartTime_Min_AR("Option to select minutes is available."),

	Click_StartTime_Min_SS("'Start Time'"),

	Click_EndTime_DC("Click on the hours field under 'End Time (HH:MM)'"),

	Click_EndTime_SS("'End Time'"),

	Click_EndTime_Min_DC("Click on the minutes field under 'End Time (HH:MM)'"),

	selectHH_DC("Select the required hours value."), selectHH_AC("Selected value should be accepted for the field"),
	selectHH_AR("Selected value is getting accepted for the field"), selectHH_SS("'HH'"),

	selectMIN_DC("Select the required minutes value."),
	selectMIN_AC("Selected value should be accepted for the field.</div>"
			+ "<div><b>* </b>Total Hours field value should be updated based on the selected dates and times.</div>"),
	selectMIN_AR("Selected value should be accepted for the field.</div>"
			+ "<div><b>* </b>Total Hours field value is getting updated based on the selected dates and times.</div>"),
	selectMIN_SS("'HH'"),

	selectEndHH_DC("Select the valid hours value."),

	selectEndMIN_DC("Select the valid minutes value."),

	// Evaluation as No

	Evaluation_No_DC("Select 'Evaluation Required' as 'No'."),
	Evaluation_No_AC("Selected option should be accepted for 'Evaluation Required' field.</div>"),
	Evaluation_No_AR("Selected option is getting accepted for 'Evaluation Required' field.</div>"),
	Evaluation_No_SS("'Evaluation Required'"),

	// Content Delivery as No

	ContentDelivery_No_DC("Select 'ContentDelivery' as 'No'."),
	ContentDelivery_No_AC("Selected option should be accepted for 'ContentDelivery' field.</div>"),
	ContentDelivery_No_AR("Selected option is getting accepted for 'ContentDelivery' field.</div>"),
	ContentDelivery_No_SS("'ContentDelivery'"),

	// Feed Back as No

	Feedback_No_DC("Select 'Feedback Required' as 'No'."),
	Feedback_No_AC("Selected option should be accepted for 'Feedback Required' field.</div>"),
	Feedback_No_AR("Selected option is getting accepted for 'Feedback Required' field.</div>"),
	Feedback_No_SS("'Feedback Required'"),

	// Add Trainer

	TrainerAddItem_DC("Click on 'Add Item' button for 'Trainer' field."),
	TrainerAddItem_AC("'Course Sessions' window should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page' & 'Close' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Trainer', 'Trainer Code', 'Occupancy Details' columns.</div>"
			+ "<div><b>*</b> List of active Trainers assigned to the respective course should be displayed.</div>"
			+ "<div><b>*</b> The option to select one or more Trainers should be available.</div>"
			+ "<div><b>*</b> '- -' should be displayed under 'Occupancy Details' column if any trainer doesn't have any time conflicts.</div>"
			+ "<div><b>*</b> If any trainer is occupied with some other training in the selected date & time slot, then 'View' button should be displayed under 'Occupancy Details' column (if any).</div>"
			+ "<div><b>*</b> 'Cancel' and 'Add' buttons should be displayed.</div>"
			+ "<div><b>*</b> 'Add' button should be displayed in disabled mode.</div>"),
	TrainerAddItem_AR("'Course Sessions' window is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page' & 'Close' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Trainer', 'Trainer Code', 'Occupancy Details' columns.</div>"
			+ "<div><b>*</b> List of active Trainers assigned to the respective course are getting displayed.</div>"
			+ "<div><b>*</b> The option to select one or more Trainers are available.</div>"
			+ "<div><b>*</b> '- -' is getting displayed under 'Occupancy Details' column if any trainer doesn't have any time conflicts.</div>"
			+ "<div><b>*</b> If any trainer is occupied with some other training in the selected date & time slot, then 'View' button is getting displayed under 'Occupancy Details' column (if any).</div>"
			+ "<div><b>*</b> 'Cancel' and 'Add' buttons are getting displayed.</div>"
			+ "<div><b>*</b> 'Add' button is getting displayed in disabled mode.</div>"),
	TrainerAddItem_SS("'Add Item' button"),

	AddButtonAgainstTrainer_DC("Click 'Add' button against required Trainer(s)."),
	AddButtonAgainstTrainer_AC("'Trainer Code' of selected Trainer should be displayed for 'Selected Items' field."),
	AddButtonAgainstTrainer_AR("'Trainer Code' of selected Trainer is getting displayed for 'Selected Items' field."),
	AddButtonAgainstTrainer_SS("'Add' button"),

	// Trainer Add Button

	TrainerAddButton_DC("Click on 'Add' button."),
	TrainerAddButton_AC("Selected Trainer code should be displayed for 'Trainer' field."),
	TrainerAddButton_AR("Selected Trainer code is getting displayed for 'Trainer' field."),
	TrainerAddButton_SS("'Add' button"),

	// Training Venue add item

	TrainingVenueAddItem_DC("Click on 'Add Item' button for 'Training Venue' field."),
	TrainingVenueAddItem_AC("'Course Sessions' window should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Training Venue and Occupancy Details' columns.</div>"
			+ "<div><b>*</b> List of active Training Venues should be displayed.</div>"
			+ "<div><b>*</b> The green color background indicates that the training venue should be available in the selected date and time for that course.</div>"
			+ "<div><b>*</b> If no color should be displayed, then the training venue is occupied with some other training in the selected date & time slot with 'View' button (if any).</div>"
			+ "<div><b>*</b> '- -' should be displayed if training venue doesn't have any time conflicts under 'Occupancy Details' column.</div>"),
	TrainingVenueAddItem_AR("'Course Sessions' window is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Training Venue and Occupancy Details' columns.</div>"
			+ "<div><b>*</b> List of active Training Venues are getting displayed.</div>"
			+ "<div><b>*</b> The green color background indicates that the training venue is available in the selected date and time for that course.</div>"
			+ "<div><b>*</b> If no color should be displayed, then the training venue is occupied with some other training in the selected date & time slot with 'View' button (if any).</div>"
			+ "<div><b>*</b> '- -' is getting displayed if training venue doesn't have any time conflicts under 'Occupancy Details' column.</div>"),
	TrainingVenueAddItem_SS("'Add Item' button"),

	// Add Button against Training Venue

	AddButtonAgainstTrainingVenue_DC("Click on 'Add' against required Training Venue."),
	AddButtonAgainstTrainingVenue_AC("'Selected Training Venue should be displayed in 'Selected Items' field."),
	AddButtonAgainstTrainingVenue_AR("'Selected Training Venue is getting displayed in 'Selected Items' field."),
	AddButtonAgainstTrainingVenue_SS("'Add' button"),

	// Training Venue

	TrainingVenueAddButton_DC("Click on 'Add' button."),
	TrainingVenueAddButton_AC("Selected Training Venue should be displayed for the field."),
	TrainingVenueAddButton_AR("Selected Training Venue is getting displayed for the field."),
	TrainingVenueAddButton_SS("'Add' button"),

	// Un-schedule Remarks

	UnsheduleReamarks_DC(
			"Enter the value less than or equals to 250 characters in 'Remarks for Unscheduled Training' field."),
	UnsheduleReamarks_AC("Entered value should be displayed for the field.</div>"),
	UnsheduleReamarks_AR("'Remarks for Unscheduled Training' entered value is getting displayed for the field.</div>"),
	UnsheduleReamarks_SS("'Remarks for Unscheduled Training'"),

	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR("Selection is getting accepted.</div>"), SearchBy_CourseName_SS("'Course Name'"),

	// Assessment No

	Select_AssessmentRequiredNo_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	Select_AssessmentRequiredNo_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"),
	Select_AssessmentRequiredNo_SS("'Assessment Required'."),

	// Reading to be completed by as not required

	ReadToBeComBy_NotReq_DC("Select 'Reading To Be Completed By' as 'Not Required'"),
	ReadToBeComBy_NotReq_SS("'Not Required'"),

	// Feedback Yes

	Select_FeedbackRequiredYes_DC("Select 'Feedback Required' as 'Yes'."),

	Select_FeedbackRequiredYes_SS("'Feedback Required'."),

	Select_FeedbackRequiredYes_AC("Selection should be accepted.</div>"),

	Select_FeedbackRequiredYes_AR("Selection is getting accepted.</div>"),

	FeedbackTemplateSelect_DC("Click on 'Select'."),
	FeedbackTemplateSelect_AC("Option to select 'Feedback Template' should be available.</div>"),
	FeedbackTemplateSelect_AR("Option to select 'Feedback Template' is available.</div>"),
	FeedbackTemplateSelect_SS("'Select'."),

	Click_Newfeedback_DC("Click the required 'Feedback Template.' "),
	Click_Newfeedback_AC("Selection should be accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Preview' should be available.</div>"),
	Click_Newfeedback_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Option to select the 'Preview' is available.</div>"),

	Click_Newfeedback_SS("'Feedback Template'"),

	Enter_Newfeedback_DC("Enter the required 'Feedback Template' in drop down."),
	Enter_Newfeedback_SS("Feedback Template'"),

	feedbackPreview_DC("Click on 'Preview' button."),
	feedbackPreview_AC("'Course Sessions' window should be displayed.</div>"
			+ "<div><b>*</b> 'Feedback Template Preview' should be displayed.</div>"),
	feedbackPreview_AR("'Course Sessions' is getting displayed.</div>"
			+ "<div><b>*</b> 'Feedback Template Preview' is getting displayed.</div>"),
	feedbackPreview_SS("'Preview"),

	feedbackPreviewClose_DC("Click on 'Close' button."),

	feedbackPreviewClose_AC(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options should be available.</div>"),
	feedbackPreviewClose_AR(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options are available.</div>"),
	feedbackPreviewClose_SS("'Close"),

//	OJT Document reading

	_OJT_AssesmentNO_DC("Select 'Assessment Required' as 'No'."),
	_OJT_AssesmentNO_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	_OJT_AssesmentNO_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"),
	_OJT_AssesmentNO_SS("'Assessment Required'."),

//	OJT Document reading

	Select_OJT_DR_DC("Select 'Document Reading' as 'Training Method'"),
	Select_OJT_DR_AC(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options should be available.</div>"),
	Select_OJT_DR_AR(
			"The option to select the 'Assessment Required' with 'Yes and No/Verbal' options are available.</div>"),
	Select_OJT_DR_SS("'Documemnt Reading'"),

	OJT_AssesmentYes_DC("Select 'Assessment Required' as 'Yes'."),
	OJT_AssesmentYes_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	OJT_AssesmentYes_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"),
	OJT_AssesmentYes_SS("'Assessment Required'."),

	// Line of Approvers

	Click_Approval1_DC("Click the dropdown for 'Approval 1' field."),
	Click_Approval1_AC(
			"All active Group(s), along with their respective mapped Subgroup(s), should be displayed in the dropdown menu."),
	Click_Approval1_AR(
			"All active Group(s), along with their respective mapped Subgroup(s), are getting displayed in the dropdown menu."),
	Click_Approval1_SS("'Groups/Subgroups'"),

	Enter_Approval1_DC("Enter the required 'Groups/Subgroups' Name."),

	Select_Apprval1_DC("Select 'Approvers Group/ Approvers Subgroup' from the dropdown list of 'Approval 1' field."),

	// Course Session Approval

	CourseSessionApproveMenu_DC("Click on 'Course Session' submenu."),
	CourseSessionApproveMenu_AC("'Course Sessions Approval Tasks' screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain 'Registration and Modification' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details should be displayed.<div>"
			+ "<div><b>*</b> All the Course Sessions whose registration request is to be approved by the current user should be listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen should contain 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen should contain 'Course Session Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.<div>"),
	CourseSessionApproveMenu_AR("'Course Sessions Approval Tasks' screen is getting displayed."
			+ "<div><b>*</b> Screen contains 'Registration and Modification' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details are getting displayed.<div>"
			+ "<div><b>*</b> All the Course Sessions whose registration request is to be approved by the current user is listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen contains 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen contains 'Course Session Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is getting displayed as '20'.<div>"),
	CourseSessionApproveMenu_SS("'Course Session' submenu"),

	SearchBy_CourseSessionApproval_AC(
			"Option to search with 'Top 250 Records, Course Session Name, Unique Code, Initiated Between should be displayed.</div>"),
	SearchBy_CourseSessionApproval_AR(
			"Option to search with 'Top 250 Records, Course Session Name, Unique Code, Initiated Between are getting displayed.</div>"),

	// approve radio button
	CourseSessionApproval_DC("Select 'Decision' as 'Approve'."),
	CourseSessionApproval_AC("Selected option should be accepted for 'Decision' field.</div>"),
	CourseSessionApproval_AR("Selected option is getting accepted for 'Decision' field.</div>"),
	CourseSessionApproval_SS("'Approve'"),

	// submit at CourseSession approval
	SubmitCourseSessionapp_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Course Sessions: Registration Approval: Approve'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> should be displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' should be available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>*</b> 'Proceed' button should be displayed in disabled mode.</div>"),
	SubmitCourseSessionapp_AR(
			"'Meaning of This Electronic Signature' is getting displayed as 'Course Sessions: Registration Approval: Approve'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' is available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>*</b> 'Proceed' button is getting displayed in disabled mode.</div>"),
	SubmitCourseSessionapp_SS("'E-Sign window'"),

	// esign- approval
	Esign_CourseSessionProceedapp_AC(
			"'Course Sessions Registration Approved' Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_CourseSessionProceedapp_AR(
			"'Course Sessions Registration Approved' Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	viewEmployeeButton_DC("Click on 'View Employee' button."),
	viewEmployeeButton_AC("Selected Employee List screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should be displayed with Employee Name, Employee ID, Designation, Department and Reporting To Details.</div>"
			+ "<div><b>*</b> All the Details should be displayed in Read Only Format.</div>"
			+ "<div><b>*</b> Screen should contain cancel button.</div>"),
	viewEmployeeButton_AR("Selected Employee List screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should is getting displayed with Employee Name, Employee ID, Designation, Department and Reporting To Details.</div>"
			+ "<div><b>*</b> All the Details are getting displayed in Read Only Format.</div>"
			+ "<div><b>*</b> Screen contains cancel button.</div>"),
	viewEmployeeButton_SS("'View Employee'"),

	CancelButton_DC("Click on 'Cancel' button."),
	CancelButton_AC(" 'Course Sessions Registration Initiation' screen should be displayed"),
	CancelButton_AR(" 'Course Sessions Registration Initiation' screen is getting displayed"),
	CancelButton_SS("'Cancel' button."),

	CourseConfig_DC("Click on 'Course' submenu."),
	CourseConfig_AC("'Course Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and No of Approvals Required should be available.</div>"),
	CourseConfig_AR("'Course Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and No of Approvals Required are available.</div>"),
	CourseConfig_SS("'Course' submenu"),

	Coursesession_ConfigAudit_AC("'Course Session Configuration Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Coursesession_ConfigAudit_AR("'Course Session Configuration Audit Trails' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),

	Click_Coursesession_for_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_Coursesession_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Coursesession_for_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Coursesession_for_ConfigAuditTrails_SS("'Transactions'"),

	Click_Config_Proceed_AC(
			"'Course Session Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Course Session Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Course Session Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Course Session Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Course Session Configuration - Audit Trails'."),

	Close_ConfigAuditTrails_Coursesession_AC(
			"'Course Session Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_Coursesession_AR(
			"'Course Session Configuration Audit Trails' screen is getting displayed.</div>"),

	SubmitCourseSessionwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Course Sessions: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> should be displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' should be available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>*</b> 'Proceed' button should be displayed in disabled mode.</div>"),
	SubmitCourseSessionwithEsign_AR(
			"'Meaning of This Electronic Signature' is getting displayed as 'Course Sessions: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' is available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>*</b> 'Proceed' button is getting displayed in disabled mode.</div>"),

	Esign_ProceedCourseSession_AC(
			"'Course Sessions  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedCourseSession_AR(
			"'Course Sessions  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Close_AuditTrails_CourseSession_AC("'Course Sessions Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_CourseSession_AR("'Course Sessions Audit trails' screen  is getting displayed.</div>"),

	Click_CS_for_Approve_AC(" 'Course Sessions Registration Approval' screen should be displayed</div>"
			+ "<div><b>*</b>"
			+ " The screen should display the details of the Course Sessions entered at the time of registration.</div>"
			+ "<div><b>*</b> 'Final Status' should be displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section should display the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' should be reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options should be available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' should be available.</div>"),
	Click_CS_for_Approve_AR(" 'Course Sessions Registration Approval' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " The screen is getting displayed with details of the Course Sessions entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' is getting displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section is displaying the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options are available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' are available.</div>"),
	Click_CS_for_Approve_SS(" 'Course Sessions Registration Approval' screen"),

	Click_CS_for_ApproveAuditTrails_AC(
			"'Course Sessions - Audit Trails Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should contain the details of the Course Session entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Approved'.</div>"
					+ "<div><b>*</b> The 'Events' section should contain registration 'Initiated and Approved' transaction's with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be read as '1' and the 'No. of Approvals Completed' should be read as '1'.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_CS_for_ApproveAuditTrails_AR(
			"'Course Sessions - Audit Trails Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen contains the details of the Course Session entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Approved'.</div>"
					+ "<div><b>*</b> The 'Events' section contains registration 'Initiated and Approved' transaction's with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' reads as '1' and the 'No. of Approvals Completed'  reads as '1'.</div>"
					+ "<div><b>*</b> All the particulars are displayed in read only format.</div>"),

	Confirm_CheckBox_DC("Select 'I Confirm I Understood the Missed Content.' check box."),
	Confirm_CheckBox_AC("Selected 'I Confirm I Understood the Missed Content.' check box..</div>"),
	Confirm_CheckBox_AR("Selected 'I Confirm I Understood the Missed Content.' check box.</div>"),
	Confirm_CheckBox_SS("'Confirm check box'"),

	MQARemarks_DC("Enter the value less than or equals to 250 characters in 'Remarks' field."),
	MQARemarks_AC("Entered value should be displayed for the field.</div>"),
	MQARemarks_AR("'Remarks' entered value is getting displayed for the field.</div>"), MQARemarks_SS("'Remarks'"),

	;

	private final String courseSessionStrings;

	CourseSessionStrings(String CourseSessionStrings) {

		this.courseSessionStrings = CourseSessionStrings;

	}

	public String getCourseSessionStrings() {
		return courseSessionStrings;

	}

}
