package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.PrepareQPStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;

public class CM_QuestionPaper extends OQActionEngine {

	public static String sessionInfoValue = "";
	public static String EvaluationType = "";
	public static String qualify_Mark = "";
	public static String max_Mark = "";
	public static String Course = "";
	public static String Evaluator = "";
	public static String EvaluatorUserID = "";
	public static String EvaluatorPassword = "";
	public static String EvaluatorEmployeeID = "";

	public static String getQualify_Mark() {
		return qualify_Mark;
	}

	public static void setQualify_Mark(String qualify_Mark) {
		CM_QuestionPaper.qualify_Mark = qualify_Mark;
	}

	public static String getMax_Mark() {
		return max_Mark;
	}

	public static void setMax_Mark(String max_Mark) {
		CM_QuestionPaper.max_Mark = max_Mark;
	}

	public static String getEvaluationType() {
		return EvaluationType;
	}

	public static void setEvaluationType(String evaluationType) {
		EvaluationType = evaluationType;
	}

	public static String getSessionInfoValue() {
		return sessionInfoValue;
	}

	public static String getCourse() {
		return Course;
	}

	public static void setCourse(String course) {
		Course = course;
	}

	public static void setSessionInfoValue(String sessionInfoValue) {
		//CM_CourseSession.sessionInfoValue = sessionInfoValue;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[6]//a[contains(@class,'sub-menu')][contains(text(),'Prepare')]")
	WebElement prepareMenu;
	@FindBy(id = "TMS_Course Manager_Prepare_MEN100")
	WebElement questionPaperMenu;
	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Question Paper Name')]")
	WebElement searchByQPNameDropdown;
	@FindBy(id = "Description")
	WebElement like;
	@FindBy(id = "displayBtn")
	WebElement apply;
	@FindBy(xpath = "/html[1]/body[1]/span[1]/span[1]/span[2]/ul[1]/li[3]")
	WebElement searchByNewDropdown;
	@FindBy(id = "CrsDesc")
	WebElement courseName;
//		@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
//		WebElement displayedRecord;
	@FindBy(xpath = "//table[@id='ListTab']//td[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//table[@id='ListTab']//td[2]")
	WebElement SessionType;

	@FindBy(xpath = "//table[@id='ListTab']//td[3]")
	WebElement CoursName;

	@FindBy(xpath = "//table[@id='ListTab']//td[4]")
	WebElement TrainingTypeEle;

	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(id = "EvalTypeRD_0")
	WebElement systemEvaluation;
	@FindBy(id = "EvalTypeRD_1")
	WebElement manualEvaluation;
	@FindBy(xpath = "//input[@id='EvalTypeRD_1']//following-sibling::label")
	WebElement manualEvaluation1;
	@FindBy(xpath = "//div[1]/span[2]/span[1]/span[1]//span[@id='select2-QuestionPaper_QnpType-container']")
	WebElement questionPaperType;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement questionPaperTypeSearchField;
	@FindBy(xpath = "//ul[@id='select2-QuestionPaper_QnpType-results']/li[1]")
	WebElement questionPaperTypeOption;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[2]/td[4]/input[1]")
	WebElement questionPaperTFChkbox1;
	@FindBy(xpath = "//table[1]/tbody[1]/tr[3]/td[4]/input[1]")
	WebElement questionPaperTFChkbox2;
	@FindBy(id = "ComputeBtn")
	WebElement computeBtn;
	@FindBy(id = "QuestionPaper_QlfyPerc")
	WebElement qualifyPercentage;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//button[@id='Transfer_selectBtn']")
	WebElement okButton;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Audit Trails_MEN100']")
	WebElement questionPaperAudit;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//label[contains(text(),'Question Paper Name')]//following-sibling::span")
	WebElement auditQuestionPaperName;
	@FindBy(xpath = "//label[contains(text(),'Unique Code')]//following-sibling::span")
	WebElement auditUniqueCode;
	@FindBy(xpath = "//label[contains(text(),'Batch Name')]//following-sibling::span")
	WebElement auditBatchName;
	@FindBy(xpath = "//label[contains(text(),'Evaluation Type')]//following-sibling::span")
	WebElement auditEvaluationType;
	@FindBy(xpath = "//label[contains(text(),'Evaluator')]//following-sibling::span")
	WebElement auditEvaluator;
	@FindBy(xpath = "//label[contains(text(),'Total Questions Available')]//following-sibling::span")
	WebElement auditTotalQuestionsAvailable;
	@FindBy(xpath = "//label[contains(text(),'Total Marks Available')]//following-sibling::span")
	WebElement auditTotalMarksAvailable;
	@FindBy(xpath = "//label[contains(text(),'Question Paper Type')]//following-sibling::span")
	WebElement auditQuestionPaperType;
	@FindBy(xpath = "//label[contains(text(),'Maximum Marks')]//following-sibling::span")
	WebElement auditMaximumMarks;
	@FindBy(xpath = "//label[contains(text(),'Qualifying Percentage')]//following-sibling::span")
	WebElement auditQualifyingPercentage;
	@FindBy(xpath = "//label[contains(text(),'Qualifying Mark')]//following-sibling::span")
	WebElement auditQualifyingMark;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(id = "select2-QuestionPaper_QnpType-container")
	WebElement selectQuestionPaperType;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//div[@class='table-responsivem breakWord pl-0']//td[1]")
	WebElement displayedRecord320;
	@FindBy(xpath = "//label[@for='EvalTypeRD_1']")
	WebElement manualEvaluation1320;
	@FindBy(xpath = "//button[@id='Transfer_selectBtn']")
	WebElement optManualOK;
	@FindBy(id = "btnModal_QuestionPaper_EvalPopUpVC")
	WebElement selectEvaluatorAddItem;
	@FindBy(id = "QuestionPaper_EvalPopUpVC_FindTxt")
	WebElement findEvaluator;
	@FindBy(id = "QuestionPaper_EvalPopUpVC_DisplayBtn")
	WebElement findEvaluatorApplyBtn;
	@FindBy(xpath = "//table[@id='ListTab']//tbody//tr[1]//td[1]//input")
	WebElement employeeNameRadioBtn;
	@FindBy(id = "QuestionPaper_EvalPopUpVC_selectBtn")
	WebElement evaluatorAddBtn;
	@FindBy(xpath = "//label[contains(text(),'Target Date for Question Paper Response')]//following-sibling::span")
	WebElement auditTargetDateQuestionPaper;
	@FindBy(xpath = "//input[@id='EvalTypeRD_0']//following-sibling::label")
	WebElement systemEvaluation1;

	@FindBy(xpath = "//label[text()='Course Name']/following-sibling::input")
	WebElement verifCourseNameQPScreen;

	@FindBy(xpath = "//label[text()='Description']/following-sibling::input")
	WebElement verifCourseDescriptionQPScreen;

	@FindBy(xpath = "//label[text()='Training Type']/following-sibling::input")
	WebElement verifyTrainingTypeQPScreen;

	@FindBy(xpath = "//label[text()='Batch Name']/following-sibling::input")
	WebElement verifyBatchNameQPScreen;

	@FindBy(xpath = "//input[@id='QuestionPaper_QualifyingMarks']")
	WebElement getQP;

	@FindBy(xpath = "//label[text()='System Evaluation ']")
	WebElement SysEval;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement VerifyQPNameATList;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement VerifyUniqueCodeATList;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement InitiatedBy;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[4]")
	WebElement InitiatedOn;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[5]")
	WebElement Revisionnum;

	@FindBy(xpath = "//*[@id='ListTab2']/tbody/tr/td[1]")
	WebElement TopicName;

	@FindBy(xpath = "//*[@id='ListTab2']/tbody/tr/td[2]")
	WebElement SubName;

	@FindBy(xpath = "//*[@id='ListTab2']/tbody/tr/td[3]")
	WebElement CatNme;

	@FindBy(xpath = "//*[@id='ListTab2']/tbody/tr/td[4]")
	WebElement NoOfQuesAvailable;

	@FindBy(xpath = "//label[@for='QuestionPaper_SessionName']/parent::div/div/span")
	WebElement SessionNameEle;

	@FindBy(xpath = "//label[text()='Trainer']//parent::div/div/div/span")
	WebElement TrainerName;

	@FindBy(xpath = "//label[text()='Venue Name']//parent::div/div/div/span")
	WebElement VenueName;

	@FindBy(xpath = "//label[text()='Question Paper Name']//following-sibling::input")
	WebElement questionPaperName;

	@FindBy(xpath = "//label[text()='Unique Code']//following-sibling::input")
	WebElement qpUniqueCode;

	@FindBy(xpath = "//label[text()='Questions Picked']//following-sibling::input")
	WebElement questionsPicked;

	@FindBy(xpath = "//label[text()='Maximum Marks']//following-sibling::input")
	WebElement maximummarksDisplayed;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//h6[text()='Questions']")
	WebElement questions;

	public CM_QuestionPaper() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * This method is for Prepare Question Paper System Evaluation
	 */

	public void QPRegistration_AuditTrails_Yes(HashMap<String, String> testData, String sessionInfo,
			String trainingMethod) {

		String CourseNameVal = CM_Course.getCourse();
		// String CourseNameVal = "CRSNewFOZQ";

		Evaluator = testData.get("EvaluatorName");
		EvaluatorUserID = testData.get("EvaluatorUserID");
		EvaluatorPassword = testData.get("EvaluatorPassword");
		EvaluatorEmployeeID = testData.get("EvaluatorEmployeeID");

		setEvaluationType(EvaluationType = testData.get("EvaluationType"));
		clickAndWaitforNextElement(menu, courseManagermenu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(courseManagermenu, prepareMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(prepareMenu, questionPaperMenu, CommonStrings.Prepare_DC.getCommonStrings(),
				CommonStrings.Prepare_AC.getCommonStrings(), CommonStrings.Prepare_AR.getCommonStrings(),
				CommonStrings.Prepare_SS.getCommonStrings());
		click2(questionPaperMenu, PrepareQPStrings.PrepareQP_DC.getPrepareQPStrings(),
				PrepareQPStrings.PrepareQP_AC.getPrepareQPStrings(),
				PrepareQPStrings.PrepareQP_AR.getPrepareQPStrings(),
				PrepareQPStrings.PrepareQP_SS.getPrepareQPStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				PrepareQPStrings.SearchBy_AC.getPrepareQPStrings(), PrepareQPStrings.SearchBy_AR.getPrepareQPStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByNewDropdown, PrepareQPStrings.SearchBy_CourseName_DC.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_CourseName_AC.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_CourseName_AR.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_CourseName_SS.getPrepareQPStrings());
		sendKeys2(courseName, PrepareQPStrings.Like_CourseName_DC.getPrepareQPStrings(),
				CourseNameVal + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TopicStrings.Like_TopicName_SS.getTopicStrings());
		waitForElementVisibile(display);
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, PrepareQPStrings.click_Batch_DC.getPrepareQPStrings(),
				PrepareQPStrings.click_Batch_AC.getPrepareQPStrings(),
				PrepareQPStrings.click_Batch_AR.getPrepareQPStrings(),
				PrepareQPStrings.click_Batch_SS.getPrepareQPStrings());
		TimeUtil.mediumWait();
//		saveQuestionPaperName(driver, questionPaperName, "questionPaperName");
//		saveQuestionPaperCode(driver, qpUniqueCode, "qpUniqueCode");
		scrollToViewElement(systemEvaluation1);

		if (testData.get("EvaluationType").equals("System Evaluation")) {
			click2(systemEvaluation1, PrepareQPStrings.Select_System_Eval_DC.getPrepareQPStrings(),
					PrepareQPStrings.Select_System_Eval_AC.getPrepareQPStrings(),
					PrepareQPStrings.Select_System_Eval_AR.getPrepareQPStrings(),
					PrepareQPStrings.Select_System_Eval_SS.getPrepareQPStrings());
			TimeUtil.shortWait();

		} else if (testData.get("EvaluationType").equals("Manual Evaluation")) {
			click2(manualEvaluation1, PrepareQPStrings.Select_Manual_Eval_DC.getPrepareQPStrings(),
					PrepareQPStrings.Select_Manual_Eval_AC.getPrepareQPStrings(),
					PrepareQPStrings.Select_Manual_Eval_AR.getPrepareQPStrings(),
					PrepareQPStrings.Select_Manual_Eval_SS.getPrepareQPStrings());
			if (trainingMethod.equals("Document Reading")) {
				click2(selectEvaluatorAddItem, PrepareQPStrings.AddItem_SelectEvaluator_DC.getPrepareQPStrings(),
						PrepareQPStrings.AddItem_SelectEvaluator_AC.getPrepareQPStrings(),
						PrepareQPStrings.AddItem_SelectEvaluator_AR.getPrepareQPStrings(),
						PrepareQPStrings.AddItem_SelectEvaluator_SS.getPrepareQPStrings());
				waitForElementVisibile(findEvaluator);
				TimeUtil.mediumWait();
				sendKeys2(findEvaluator, PrepareQPStrings.Find_Evaluator_DC.getPrepareQPStrings(),
						CM_QuestionPaper.Evaluator + Constants.PERCENTAGE_SIGN,
						CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
						PrepareQPStrings.Find_Evaluator_DC.getPrepareQPStrings());
				TimeUtil.mediumWait();
				waitForElementVisibile(findEvaluatorApplyBtn);
				TimeUtil.mediumWait();
			
				click2(findEvaluatorApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
						CommonStrings.ApplyButton_AC.getCommonStrings(),
						CommonStrings.ApplyButton_AR.getCommonStrings(),
						CommonStrings.ApplyButton_SS.getCommonStrings());
				TimeUtil.mediumWait();
				
				click2(employeeNameRadioBtn, PrepareQPStrings.selectEvaluator_DC.getPrepareQPStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						PrepareQPStrings.selectEvaluator_SS.getPrepareQPStrings());
				TimeUtil.mediumWait();
				click2(evaluatorAddBtn, CommonStrings.Click_AddButton_DC.getCommonStrings(),
						PrepareQPStrings.Click_AddButton_Evaluator_AC.getPrepareQPStrings(),
						PrepareQPStrings.Click_AddButton_Evaluator_AR.getPrepareQPStrings(),
						CommonStrings.Click_AddButton_SS.getCommonStrings());
			}
		}

		scrollToViewElement(questionPaperType);

		click2(questionPaperType, PrepareQPStrings.Click_QPType_DC.getPrepareQPStrings(),
				PrepareQPStrings.Click_QPType_AC.getPrepareQPStrings(),
				PrepareQPStrings.Click_QPType_AR.getPrepareQPStrings(),
				PrepareQPStrings.Click_QPType_SS.getPrepareQPStrings());
		TimeUtil.shortWait();
		scrollToViewElement(questionPaperTypeSearchField);
		sendKeys2(questionPaperTypeSearchField, PrepareQPStrings.Enter_Manual_DC.getPrepareQPStrings(), "Manual",
				PrepareQPStrings.Enter_Manual_AC.getPrepareQPStrings(),
				PrepareQPStrings.Enter_Manual_AR.getPrepareQPStrings(),
				PrepareQPStrings.Enter_Manual_SS.getPrepareQPStrings());
		click2(questionPaperTypeOption, PrepareQPStrings.Click_Manual_DC.getPrepareQPStrings(),
				PrepareQPStrings.Click_Manual_AC.getPrepareQPStrings(),
				PrepareQPStrings.Click_Manual_AR.getPrepareQPStrings(),
				PrepareQPStrings.Click_Manual_SS.getPrepareQPStrings());
		waitForElementVisibile(questions);
		scrollToViewElement(questions);
		List<WebElement> numberOfQuestions = driver.findElements(
				By.xpath("//th[text()='Questions']//parent::tr//parent::thead//following-sibling::tbody//tr//td[2]"));
		for (int i = 0; i <= numberOfQuestions.size(); i++) {
			String questionType = driver.findElement(
					By.xpath("//th[text()='Questions']//parent::tr//parent::thead//following-sibling::tbody//tr["
							+ (i + 2) + "]//td[2]"))
					.getText();
			if (questionType.equals("Multiple Choice")) {
				WebElement element = driver
						.findElement(By.xpath("//table[1]/tbody[1]/tr[" + (i + 2) + "]/td[4]/input[1]"));
				scrollToViewElement(element);
				element.click();
				break;
			}
		}
		TimeUtil.shortWait();
		List<WebElement> numberOfQuestions1 = driver.findElements(
				By.xpath("//th[text()='Questions']//parent::tr//parent::thead//following-sibling::tbody//tr//td[2]"));
		for (int j = 0; j <= numberOfQuestions1.size(); j++) {
			String questionType = driver.findElement(
					By.xpath("//th[text()='Questions']//parent::tr//parent::thead//following-sibling::tbody//tr["
							+ (j + 2) + "]//td[2]"))
					.getText();
			if (questionType.equals("True or False")) {
				WebElement element = driver
						.findElement(By.xpath("//table[1]/tbody[1]/tr[" + (j + 2) + "]/td[4]/input[1]"));
				scrollToViewElement(element);
				element.click();
				break;
			}
		}
		scrollToViewElement(computeBtn);
		click2(computeBtn, PrepareQPStrings.Click_Compute_DC.getPrepareQPStrings(),
				PrepareQPStrings.Click_Compute_AC.getPrepareQPStrings(),
				PrepareQPStrings.Click_Compute_AR.getPrepareQPStrings(),
				PrepareQPStrings.Click_Compute_SS.getPrepareQPStrings());
		scrollToViewElement(qualifyPercentage);
		sendKeysAndRemoveFocus(qualifyPercentage, PrepareQPStrings.Enter_QualifyingPecentage_DC.getPrepareQPStrings(),

				testData.get("QualifyPercentageval"),
				PrepareQPStrings.Enter_QualifyingPecentage_AC.getPrepareQPStrings(),
				PrepareQPStrings.Enter_QualifyingPecentage_AR.getPrepareQPStrings(),
				PrepareQPStrings.Enter_QualifyingPecentage_SS.getPrepareQPStrings());
		highLightElement(driver, getQP, "Qualified Percnetage", test);
		scrollToViewElement(submit);
		if (sessionInfo.equals("Off-Line") || testData.get("EvaluationType").equals("Manual Evaluation")) {
			click2(submit, TopicStrings.Submit_DC.getTopicStrings(),
					PrepareQPStrings.SubmitOfflineManual_AC.getPrepareQPStrings(),
					PrepareQPStrings.SubmitOfflineManual_AR.getPrepareQPStrings(),
					PrepareQPStrings.Submit_SS.getPrepareQPStrings());
			TimeUtil.shortWait();
			if (!sessionInfo.equals("Off-Line")) {
				click2(optManualOK, PrepareQPStrings.Click_OK.getPrepareQPStrings(),
						PrepareQPStrings.Submit_AC.getPrepareQPStrings(),
						PrepareQPStrings.Submit_AR.getPrepareQPStrings(),
						PrepareQPStrings.Submit_SS.getPrepareQPStrings());
			}
		} else {
			click2(submit, TopicStrings.Submit_DC.getTopicStrings(), PrepareQPStrings.Submit_AC.getPrepareQPStrings(),
					PrepareQPStrings.Submit_AR.getPrepareQPStrings(), PrepareQPStrings.Submit_SS.getPrepareQPStrings());

		}
		TimeUtil.longwait();
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(questionPaperAudit, PrepareQPStrings.PrepareQP_DC.getPrepareQPStrings(),
				PrepareQPStrings.Click_QP_AT_AC.getPrepareQPStrings(),
				PrepareQPStrings.Click_QP_AT_AR.getPrepareQPStrings(),
				PrepareQPStrings.Click_QP_AT_SS.getPrepareQPStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				PrepareQPStrings.SearchBy_Audit_AC.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_Audit_AR.getPrepareQPStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByQPNameDropdown, PrepareQPStrings.SearchBy_QPName_DC.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_QPName_AC.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_QPName_AR.getPrepareQPStrings(),
				PrepareQPStrings.SearchBy_QPName_SS.getPrepareQPStrings());
		sendKeys2(like, PrepareQPStrings.Like_QPName_AuditTrails_DC.getPrepareQPStrings(),
				CourseNameVal + Constants.PERCENTAGE_SIGN

				, CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				PrepareQPStrings.Like_QPName_SS.getPrepareQPStrings());

		click2(apply, CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, PrepareQPStrings.click_QP_AuditTrails_DC.getPrepareQPStrings(),
				PrepareQPStrings.click_QP_AuditTrails_AC.getPrepareQPStrings(),
				PrepareQPStrings.click_QP_AuditTrails_AR.getPrepareQPStrings(),
				PrepareQPStrings.click_QP_AuditTrails_SS.getPrepareQPStrings());
		TimeUtil.mediumWait();
		driver.switchTo().frame(0);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				PrepareQPStrings.Click_QP_AT_AC.getPrepareQPStrings(),
				PrepareQPStrings.Click_QP_AT_AR.getPrepareQPStrings(),
				PrepareQPStrings.Click_QP_AT_SS.getPrepareQPStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

}
