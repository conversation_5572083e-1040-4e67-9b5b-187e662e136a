package com.Automation.ObjectModelFlows_OQ.System_Manager.SubGroup;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Subgroup_StatusChange extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Object_Model_Flows/System_Manager/SubGroup/SubgroupStatusChange.xlsx";

	public Subgroup_StatusChange() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subStatusChangeData = new ExcelUtilUpdated(ExcelPath, "StatuschangeActInact");

	@DataProvider(name = "subStatusChange")
	public Object[][] getsubgroupRegData() throws Exception {
		Object[][] obj = new Object[subStatusChangeData.getRowCount()][1];
		for (int i = 1; i <= subStatusChangeData.getRowCount(); i++) {
			HashMap<String, String> testData = subStatusChangeData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 0, dataProvider = "subStatusChange", enabled = true)
	public void subGroupConfigReg(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration Configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup Registration Configuration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		subGroup_StatusChange.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		subGroup_StatusChange.SubgroupRegistrationApproval_Configuration(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Modification Configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup  Modification Configuration");
		}

		subGroup_StatusChange.SubgroupModificationApproval_Configuration(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Status Change Configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup Status Change Configuration");
		}

		subGroup_StatusChange.SubgroupStatusChangeApproval_Configuration(testData);

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 1, dataProvider = "subStatusChange", enabled = true)
	public void subGroupReg(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}

		subGroup_StatusChange.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		subGroup_StatusChange.subgroup_Registration_AuditTrails(testData);

	}

// Test Method for check status Change inactive
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 2, dataProvider = "subStatusChange", enabled = true)
	public void subgroupstatuschange(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Active to Inactive")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Active to Inactive");
		}

		subGroup_StatusChange.Subgroup_StatusChange_activetoInactive(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Active to Inactive Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Active to Inactive Audit Trails");
		}

		subGroup_StatusChange.Subgroup_StatusChange_activetoInactive_AuditTrails(testdata);

	}

	@Test(priority = 3, dataProvider = "subStatusChange", enabled = true)
	public void subgroupstatuschange11(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("verify  Modification  before approval")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("verify  Modification  before approval");
		}

		subGroup_StatusChange.subgroup_Modification(testdata);

		Logout.signOutPage();
	}

	// Test Method for Verification of subgroup Approve and Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 4, dataProvider = "subStatusChange", enabled = true)
	public void subgroupapp11(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		subGroup_StatusChange.getFirstNameLastNameFromProfileIcon();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Status Change  Drop")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Status Change  Drop");
		}
		subGroup_StatusChange.Subgroup_statuschange_drop(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Status Change  Drop Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Status Change  Drop Audit Trails");
		}
		subGroup_StatusChange.Subgroup_statuschange_drop_auditrails(testData);

	}

	@Test(priority = 5, dataProvider = "subStatusChange", enabled = true)
	public void subgroupapp1(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("verify  Modification  After Drop")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("verify  Modification  After Drop");
		}

		subGroup_StatusChange.subgroup_Modification_afterapproval(testData);
		Logout.signOutPage();
	}

	// Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subStatusChangeApproveData = new ExcelUtilUpdated(ExcelPath, "StatuschangeApprove");

	@DataProvider(name = "subStatusChangeApprove")
	public Object[][] getsubgroupRegData1() throws Exception {
		Object[][] obj = new Object[subStatusChangeApproveData.getRowCount()][1];
		for (int i = 1; i <= subStatusChangeApproveData.getRowCount(); i++) {
			HashMap<String, String> testData = subStatusChangeApproveData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 6, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subGroupReg1(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		subGroup_StatusChange.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();
		subGroup_StatusChange.subgroup_Registration(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Registration  Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup Registration Audit Trails");
		}
		subGroup_StatusChange.subgroup_Registration_AuditTrails(testData);

	}

// Test Method for check status Change active to inactive
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 7, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subgroupstatuschange1111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Active to Inactive")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Active to Inactive");
		}

		subGroup_StatusChange.Subgroup_StatusChange_activetoInactive(testdata);

		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Active to Inactive Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Active to Inactive Audit Trails");
		}

		subGroup_StatusChange.Subgroup_StatusChange_activetoInactive_AuditTrails(testdata);
		Logout.signOutPage();
	}

	// Test Method for check status Change active to inactive approve and audit
	// trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 8, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subgroupapp(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		subGroup_StatusChange.getFirstNameLastNameFromProfileIcon();
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup 	Status Change Approval")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup 	Status Change Approval");
		}
		subGroup_StatusChange.Subgroup_statuschange_Approve(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Status Change Approval Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Status Change Approval Audit Trails");
		}
		subGroup_StatusChange.Subgroup_statuschange_Approve_auditrails(testData);
		Logout.signOutPage();
	}

	// Test Method for check status Change inactive to active
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 9, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subgroupstatuschange111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Inactive to Active")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Inactive to Active");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		subGroup_StatusChange.Subgroup_StatusChange_InactivetoActive(testdata);
		if (isReportedRequired == true) {
			test = extent.createTest("Status Change Inactive to Active Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Status Change Inactive to Active Audit Trails");
		}

		subGroup_StatusChange.Subgroup_StatusChange_InactivetoActive_AuditTrails(testdata);

	}

	// Test Method for check modification before drop
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 10, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subgroupstatuschange1(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("verify  Modification  before Drop")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("verify  Modification  before Drop");
		}

		subGroup_StatusChange.subgroup_Modification(testdata);

		Logout.signOutPage();
	}

	// Test Method for Verification of subgroup drop and Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 11, dataProvider = "subStatusChangeApprove", enabled = true)
	public void subgroupapp111(HashMap<String, String> testData) {

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Staus Change Drop")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Staus Change Drop");
		}
		subGroup_StatusChange.Subgroup_statuschange_afterapprove_drop(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Staus Change Drop Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Staus Change Drop Audit Trails");
		}
		subGroup_StatusChange.Subgroup_statuschange_afterapprove_drop_auditrails(testData);
		Logout.signOutPage();
	}
}
