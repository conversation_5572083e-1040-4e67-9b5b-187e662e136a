package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseInvitationStrings;
import com.Automation.Strings.RespondFeedBackRequestStrings;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_RespondFeedback extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[8]//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respondMenu;

	@FindBy(id = "TMS_Course Manager_Respond_MEN51")
	WebElement feedBackRequest;

	@FindBy(xpath = "//i[@class='ft-search']")
	WebElement commonSearch;

	@FindBy(xpath = "//input[@class='form-control caliber-textbox']")
	WebElement commonSearchTextEnter;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//input[@id='FeedBackQuestionDetView_feedbackQuestionLists_0__UserAnswer']")
	WebElement trainerResponse;

	@FindBy(id = "btnSubmit")
	WebElement submitBtn;

	public void respondFeedbackRequest(HashMap<String, String> testData) {

		waitForElementVisibile(menu);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(respondMenu, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());

		click2(feedBackRequest, RespondFeedBackRequestStrings.FeedbackRequestMenu_DC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.FeedbackRequestMenu_AC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.FeedbackRequestMenu_AR.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.FeedbackRequestMenu_SS.getRespondFeedBackRequestStrings());

		switchToBodyFrame(driver);

		click2(commonSearch, CourseInvitationStrings.SearchBy_DC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy_SS.getCourseInvitationStrings());

		sendKeys2(commonSearchTextEnter, RespondFeedBackRequestStrings.SearchBy2_DC.getRespondFeedBackRequestStrings(),
				CM_Course.getCourse(), CourseInvitationStrings.SearchBy2_AC.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_AR.getCourseInvitationStrings(),
				CourseInvitationStrings.SearchBy2_SS.getCourseInvitationStrings());

		click2(displayedRecord, RespondFeedBackRequestStrings.SearchBy1_DC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.SearchBy1_AC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.SearchBy1_AR.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.SearchBy1_SS.getRespondFeedBackRequestStrings());
		TimeUtil.shortWait();
		sendKeys2(trainerResponse, RespondFeedBackRequestStrings.remarks_DC.getRespondFeedBackRequestStrings(),
				testData.get("TraineeResponse"),
				RespondFeedBackRequestStrings.sendKeys_AC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.sendKeys_AR.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.remarks_SS.getRespondFeedBackRequestStrings());
		click2(submitBtn, RespondFeedBackRequestStrings.Submit_DC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.Submit_AC.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.Submit_AR.getRespondFeedBackRequestStrings(),
				RespondFeedBackRequestStrings.Submit_SS.getRespondFeedBackRequestStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
}
