package com.Automation.Strings;

public enum TopicStrings {

	// TopicConfiguration

	Topic_Config_DC("Click on 'Topic' submenu."),
	Topic_Config_AC("'Topic Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	Topic_Config_AR("'Topic Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	Topic_Config_SS("'Configuration'"),

	Click_DoneatTopicConfig_AC("'Topic Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatTopicConfig_AR("'Topic Configuration Registration' screen is getting displayed.</div>"),

	// SubGroup configuration Audit trails

	Topic_ConfigAudit_DC("Click on 'Topic' submenu."),
	Topic_ConfigAudit_AC("'Topic Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Topic_ConfigAudit_AR("'Topic Configuration Registration' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Topic_ConfigAudit_SS("'Topic' submenu"),

	// Topic configuration Audit trails
	Click_Topicfor_ConfigAuditTrails_DC("Click on the above registered 'Unique Code'."),
	Click_Topicfor_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Topicfor_ConfigAuditTrails_AR("'Transactions' screen is getting.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Topicfor_ConfigAuditTrails_SS("'Topic Configuration' - Audit Trails."),

	Close_TopicConfigAuditTrails_Group_AC("'Topic Configuration Audit Trails' screen should be displayed.</div>"),
	Close_TopicConfigAuditTrails_Group_AR("'Topic Configuration Audit Trails' screen is getting displayed.</div>"),

	// subgroup configuration proceed
	Click_TopicConfig_Proceed_AC(
			"'Topic Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Topic Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_TopicConfig_Proceed_AR(
			"'Topic Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Subgroup Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_TopicConfig_Proceed_SS("'Topic Configuration - Audit Trails'."),
	// Topic Registration Screen

	TopicRegistrationScreen_DC("Click on 'Topic' menu."),
	TopicRegistrationScreen_AC("'Topic Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Topic Name, Topic Unique Code, Description, Category Tag(s) & Subject Tag(s)' should be available.</div>"
			+ "<div><b>*</b> Current login plant code should be displayed by default for 'Topic Unique Code' field..</div>"
			+ "<div><b>*</b> The option to search with 'Document Category Name &  Document Name' should be available with 'Fetch Records' hyperlink under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b>The screen should contain 'Recent 10 Documents and Reset' with hyperlinks under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b> All the registered document categories at the time of document registration in both Master plant & Plant1 (if any) should be listed in 'Document Categories' list under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b> Note: Light Shade Blue Highlighted Color Indicates docs-iq Documents should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b>Under 'View Existing', registered active records of 'Topics' should be displayed if any.</div>"),

	TopicRegistrationScreen_AR("'Topic Registration Initiation' screen is getting  displayed.</div>"
			+ "<div><b>*</b> The screen is getting displayed with 'Topic Name, Topic Unique Code, Description, Category Tag(s) & Subject Tag(s)' should be available.</div>"
			+ "<div><b>*</b> Current login plant code is getting displayed by default for 'Topic Unique Code' field.</div>"
			+ "<div><b>*</b> The option to search with 'Document Category Name &  Document Name' should be available with 'Fetch Records' hyperlink under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b>The screen  contains 'Recent 10 Documents and Reset' with hyperlinks under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b> All the registered document categories at the time of document registration in both Master plant & Plant1 (if any) is  listed in 'Document Categories' list under 'Link Documents to Topic - Existing Document Categories' section.</div>"
			+ "<div><b>*</b> Note: Light Shade Blue Highlighted Color Indicates docs-iq Documents should be displayed.</div>"
			+ "<div><b>*</b> The screen contains 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b>Under 'View Existing', registered active records of 'Topics' is getting  displayed (if any).</div>"),
	TopicRegistrationScreen_SS("'Topic Registration'"),

	// Topic approve screen
	TopicApproveScreen_AC("'Topic Approval Tasks' screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b>By default, the 'Registration' tab details should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain 'Search this Page', 'Advanced Search' & 'Total Records Count' icons..<div>"
			+ "<div><b>*</b>The screen should contain 'Topic Name, Unique Code, Subject Name, Topic Unique Code, Initiated By, Initiated On and Revision No.' columns.<div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic Unique Code' should be available.<div>"
			+ "<div><b>*</b>By default, 'Records Per Page' should be displayed as '20'.<div>"
			+ "<div><b>*</b>List of Topic(s) whose registration request is to be approved by the current login user should be listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b>Topics that are registered in Master plant/ other plant should not be displayed (if any).<div>"),

	TopicApproveScreen_AR("'Topic Approval Tasks' screen is getting  displayed.<div>"
			+ "<div><b>*</b> Screen  contains 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b>By default, the 'Registration' tab details is getting displayed.<div>"
			+ "<div><b>*</b>The screen contains 'Search this Page', 'Advanced Search' & 'Total Records Count' icons..<div>"
			+ "<div><b>*</b>The screen contains 'Topic Name, Unique Code, Subject Name, Topic Unique Code, Initiated By, Initiated On and Revision No.' columns.<div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic Unique Code' are available.<div>"
			+ "<div><b>*</b>By default, 'Records Per Page' is getting  displayed as '20'.<div>"
			+ "<div><b>*</b>List of Topic(s) whose registration request is  approved by the current login user is listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b>Topics that are registered in Master plant/ other plant are not displayed (if any).<div>"),

	TopicApproveScreen_SS("'Topic Approval Tasks'"),
//Enter topic name
	TopicName_DC("Enter the value less than or equals to 250 characters in 'Topic Name' field."),
	TopicName_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	TopicName_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"), TopicName_SS("'Topic Name'"),
//Enter topic unique code
	TopicUniqueCode_DC("Enter the value less than or equals to 25 characters in 'Topic Unique Code' field."),
	TopicUniqueCode_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	TopicUniqueCode_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	TopicUniqueCode_SS("'Unique Code'"),
//Enter topic description
	TopicDescription_DC("Enter the value less than or equals to 500 characters in  'Description' field."),
	TopicDescription_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	TopicDescription_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	TopicDescription_SS("'Description'"),
//Click document name
	ClickDocumentName_DC("Select 'Document Name'."), ClickDocumentName_AC("Selection should be accepted.</div>"),
	ClickDocumentName_AR("Selection is getting accepted.</div>"), ClickDocumentName_SS("'Document Name'"),
//document search text box
	TopicdocumentSearchTextBox_DC("Enter the required 'Document Name'"),
	TopicdocumentSearchTextBox_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	TopicdocumentSearchTextBox_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	TopicdocumentSearchTextBox_SS("'Document Name'"),
//fetch records
	Topicfetchrecords_DC("Click on 'Fetch Records'."),
	Topicfetchrecords_AC(
			"Records should be displayed based on the search criteria in 'Available Documents' column.</div>"),
	Topicfetchrecords_AR(
			"Records are getting displayed based on the search criteria in 'Available Documents' column.</div>"),
	Topicfetchrecords_SS("'Available'"),

	greenTickMark_DC("Select the required document in 'Available Documents' section."),
	greenTickMark_AC("Selected document should be moved to 'Selected Documents' section.</div>"),
	greenTickMark_AR("Selected document is getting moved to 'Selected Documents' section.</div>"),
	greenTickMark_SS("'Selected Document'"),
//submit
	Submit_DC("Click on 'Submit' button."),
	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Topic: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Topic: Registration Initiation'.</div>"),
	Submit_SS("'E-Sign window'"),
//search by 
	SearchBy_TP_DC("Click on 'Search By' dropdown."),
	SearchBy_TP_AC(
			"Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic UniqueCode' should be diplayed.<div>"),
	SearchBy_TP_AR(
			"Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic Unique Code' is getting diplayed.<div>"),
	SearchBy_TP_SS("'Search By'."),
//search by topic name
	SearchBy_TopicName_DC("Select 'Topic Name'."), SearchBy_TopicName_AC("Selection should be accepted.</div>"),
	SearchBy_TopicName_AR(" Selection is getting accepted.</div>"), SearchBy_TopicName_SS("'Search By''Topic Name'."),
	SearchBy_UniqueCode_SS("'Search By''Unique Code'."),

	SearchBy_UniqueCode_DC("Select 'Unique Code'."),
//lile topic name
	Like_TopicName_DC("Enter the above registered 'Topic Name' in 'Like' text box."),

	Like_UniqueCode_DC("Enter the above registered 'Unique Code' in 'Like' text box."),
	Like_TopicName_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	Like_TopicName_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	Like_TopicName_SS("Topic Name"),

	Like_UniqueCode_SS("'Unique Code'"),

//click on above registered topic
	TP_for_AuditTrails_DC("Click on the above registered 'Topic Name'."),
//click topic audit trails
	TP_for_AuditTrails_SS("'Topic Audit Trails'"),
	TP_for_AuditTrails_AC("'Topic - Audit Trails Revision No.: 0 -Registration' screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain the details entered/ selected during registration.</div>"
			+ "<div><b>*</b>'Final Status' should be displayed as 'Initiated'.</div>"
			+ "<div><b>*</b> The 'Events' section should contain registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons' details.</div>"
			+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be read as '1' and the 'No. of Approvals Completed' should be read as '0'.</div>"
			+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),
	TP_for_AuditTrails_AR("'Topic - Audit Trails Revision No.: 0 -Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b>The screen contains the details entered/ selected during registration.</div>"
			+ "<div><b>*</b>'Final Status' is getting displayed as 'Initiated'.</div>"
			+ "<div><b>*</b> The 'Events' section  contains registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons' details.</div>"
			+ "<div><b>*</b> Also, the 'No. of Approvals Required' is  read as '1' and the 'No. of Approvals Completed' is read as '0'.</div>"
			+ "<div><b>*</b>All the particulars is getting displayed in read only format.</div>"),

	// topic audit trails in approval screen

	TP_for_AuditTrails1_AC("'Topic Registration Approval' screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain the details of the Topic entered/ selected during registration.<div>"
			+ "<div><b>*</b>'Final Status' should be displayed as 'Initiated'.<div>"
			+ "<div><b>*</b>The 'Events' section should contain the 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons' details only.<div>"
			+ "<div><b>*</b>Also, the 'No. of Approvals Required' should be read as 1 and the 'No. of Approvals Completed' should be read as 0.<div>"
			+ "<div><b>*</b>'Approve, Return & Drop' options should be available for 'Decision' field.<div>"
			+ "<div><b>*</b>The option to enter/ select the 'Remark(s)/ Reason(s)' should be available.<div>"),

	TP_for_AuditTrails1_AR("'Topic Registration Approval' screen is getting displayed.<div>"
			+ "<div><b>*</b>The screen contains the details of the Topic entered/ selected during registration.<div>"
			+ "<div><b>*</b>'Final Status' is getting displayed as 'Initiated'.<div>"
			+ "<div><b>*</b>The 'Events' section contains the 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons' details only.<div>"
			+ "<div><b>*</b>Also, the 'No. of Approvals Required' is read as 1 and the 'No. of Approvals Completed' is read as 0.<div>"
			+ "<div><b>*</b>'Approve, Return & Drop' options is available for 'Decision' field.<div>"
			+ "<div><b>*</b>The option to enter/ select the 'Remark(s)/ Reason(s)' are available.<div>"),

	// submit at Topic approval
	SubmitTopicapproval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Topic: Registration Approval: Approve'.</div>"),
	SubmitTopicapproval_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Topic: Registration Approval: Approve'.</div>"),
	SubmitSubgroupapproval_SS("'E-Sign window'"),

	// esign proceed at topic approval
	Esign_TopicProceedapproval_AC(
			"'Topic Registration Approved' Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_TopicProceedapproval_AR(
			"'Topic Registration Approved' Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),
//esign proceed at topic registration
	Esign_TopicProceed_AC(
			"'Topic Registration Initiated Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_TopicProceed_AR(
			"'Topic Registration Initiated Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	// Topic Audit trails

	click_TopicAuditTrails_DC("Click on 'Topic' submenu."),

	click_TopicAuditTrails_SS("'Topic Audit Trials'"),

	click_TopicAuditTrails_AC("'Topic Audit Trails' screen should be displayed..</div>"
			+ "<div><b>*</b>The screen should contain 'Search this Page', Advanced Search' & 'Total Records Count' icons..</div>"
			+ "<div><b>*</b>The screen should contain 'Topic Name', 'Unique Code', Subject Name, 'Topic Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name, Topic Unique Code' should be available.</div>"
			+ "<div><b>*</b> The screen should contain a list of all the registered Topics..</div>"
			+ "<div><b>*</b> '--' should be displayed for 'Subject Name' if no subject tag has been selected for any Topic during Registration (if any).</div>"),

	click_TopicAuditTrails_AR("'Topic Audit Trails' screen is getting displayed..</div>"
			+ "<div><b>*</b>The screen contains 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b>The screen is getting displayed with 'Topic Name', 'Unique Code', Subject Name, 'Topic Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name, Topic Unique Code' are available.</div>"
			+ "<div><b>*</b> The screen should contain a list of all the registered Topics..</div>"
			+ "<div><b>*</b> '--' is displayed for 'Subject Name' if no subject tag has been selected for any Topic during Registration (if any).</div>"),

	TopicApproveMenu1_AC("'Topic Audit Trails' screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b>The screen should contain Topic Name, Unique Code, Subject Name, Topic Unique Code, Initiated By, Initiated On and Revision No.' columns'.<div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code and Initiated Between' should be available.<div>"
			+ "<div><b>*</b>By default, 'Records Per Page' should be displayed as '20'.<div>"),
	TopicApproveMenu1_AR("'Topic Audit Trails' screen is getting displayed.."
			+ "<div><b>*</b>The screen contains 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b>The screen is getting displayed with Topic Name, Unique Code, Subject Name, Topic Unique Code, Initiated By, Initiated On and Revision No.'columns.<div>"
			+ "<div><b>*</b>The option to search with 'Top 250 Records, Topic Name, Unique Code and Initiated Between' are available.<div>"
			+ "<div><b>*</b>By default, 'Records Per Page' is getting displayed as '20'.<div>"),

	click_TopicAuditTrails1_AC("'Topic - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain the details of the Topic entered/ selected during registration.</div>"
			+ "<div><b>*</b>'Final Status' should be displayed as 'Approved'.</div>"
			+ "<div><b>*</b>The 'Events' section should contain 'Initiated and Approved' transaction's with 'Username, Date & Time and Remarks / Reasons' details.</div>"
			+ "<div><b>*</b> Also, the 'No. of Approvals Required' and 'No. of Approvals completed' both should be read as 1.</div>"
			+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	click_TopicAuditTrails1_AR(
			"'Topic - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the details of the Topic entered/ selected during registration.</div>"
					+ "<div><b>*</b>'Final Status' is getting  displayed as 'Approved'.</div>"
					+ "<div><b>*</b>The 'Events' section  contains 'Initiated and Approved' transaction's with 'Username, Date & Time and Remarks / Reasons' details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' and 'No. of Approvals completed' both are  read as 1.</div>"
					+ "<div><b>*</b>All the particulars is displayed in read only format.</div>"),

	Close_AuditTrailsTopic_AC("'Topic Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsTopic_AR("'Topic Audit trails' screen is getting displayed.</div>"),

	CategoryAdd_DC("'Click, +Add button present against 'Category Tag(s)'.</div>"),
	CategoryAdd_AC(
			"'A text box, highlighted in blue color should be displayed with an option to create a 'Category Tag(s)'.</div>"),
	CategoryAdd_AR(
			"'A text box, highlighted in blue color is getting displayed with an option to create a 'Category Tag(s).</div>"),
	CategoryAdd_SS("'+Add button .</div>"),

	CategorySearchbox_DC(
			"Enter a value less than or equal to 25 characters for 'Category Tag(s)' field & click cursor on the screen.</div>"),

	CategorySearchbox_AC("Entered value should be displayed for the field.</div>"
			+ "<div><b>*</b>Just created Category Tag should be displayed beside Category Tag(s) field.</div>"
			+ "<div><b>*</b>Note: While entering the value for Category Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available will be listed in the drop-down (if any).<div>"),

	CategorySearchbox_AR("Entered value is getting displayed for the field.</div>"
			+ "<div><b>*</b>Just created Category Tag is displayed beside Category Tag(s) field.</div>"
			+ "<div><b>*</b>Note: While entering the value for Category Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available are listed in the drop-down (if any).<div>"),

	CategorySearchbox_SS("Category Tag(s)."),

	SubjectAdd_DC("'Click, +Add button present against 'Subject Tag(s)'.</div>"),
	SubjectAdd_AC(
			"'A text box, highlighted in blue color should be displayed with an option to create a 'Subject Tag(s)'.</div>"),
	SubjectAdd_AR(
			"'A text box, highlighted in blue color is getting displayed with an option to create a 'Subject Tag(s)'.</div>"),
	SubjectAdd_SS("'+Add button .</div>"),

	SubjectSearchbox_DC(
			"Enter a value less than or equal to 25 characters for 'Subject Tag(s)' field & click cursor on the screen.</div>"),

	SubjectSearchbox_AC("Entered value should be displayed for the field.</div>"
			+ "<div><b>*</b>Just created Subject Tag should be displayed beside Subject Tag(s) field.</div>"
			+ "<div><b>*</b>Note: While entering the value for Subject Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available will be listed in the drop-down (if any).<div>"),

	SubjectSearchbox_AR("Entered value is getting displayed for the field.</div>"
			+ "<div><b>*</b>Just created Subject Tag is displayed beside Subject Tag(s) field.</div>"
			+ "<div><b>*</b>Note: While entering the value for Subject Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available are listed in the drop-down (if any).<div>"),

	SubjectSearchbox_SS("Subject Tag(s).");

	private final String topicStrings;

	TopicStrings(String topicStrings) {

		this.topicStrings = topicStrings;

	}

	public String getTopicStrings() {
		return topicStrings;
	}

}
