package com.Automation.learniqObjects;

import java.awt.AWTException;
import java.awt.Robot;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.awt.event.KeyEvent;
import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.SYS_Subgroup;
import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.DocumentRegistrationStrings;
import com.Automation.Strings.JobResponsibilityStrings;
import com.Automation.Strings.SubGroupStrings;
import com.Automation.Strings.SubgroupAssignmentStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TextUtils;
import com.Automation.Utils.TimeUtil;

public class SYS_JobResponsibility extends OQActionEngine {
	public static String QueryDocName = "";
	public static String RegReportingToValue = "";
	public static String RegAuthorizedDeputyValue = "";
	public static String RegQualificationValue = "";
	public static String RegPreviousExperienceVal = "";
	public static String RegExternalCertificates = "";
	public static String RegsubGroupNameValue = "";
	public static String RegJobResponsibilityValue = "";
	public static String RegDesignationValue = "";
	public static String RegEmployeeName = "";
	public static String RegEmployeeID = "";
	public static String RegDepartName = "";
	public static String JobResNew = "";
	public static String IntiatorNameInitiatorEmpID = "";
	public static String InitiatorEmployeeID = "";
	public static String InitiatorName = "";

	public static String ApproverNameInitiatorEmpID = "";
	public static String ApproverEmployeeID = "";
	public static String ApproverName = "";

	public static String Re_Int_NameInitiatorEmpID = "";
	public static String Re_Int_EmployeeID = "";
	public static String Re_Int_Name = "";

	public static String Mod_Initiator_Name_EmpID = "";
	public static String Mod_Initiator_EmployeeID = "";
	public static String Mod_Initiator_Name = "";
	private int submitClickCount = 0;
	public static String QualificationValue = "";
	public static String PreviousExperienceval = "";
//	String RegQualificationValue = "";

	String RegEmpName = "";
	String RegAuthorizedDeputy = "";
	Properties prop;

	public String getRegAuthorizedDeputy() {
		return RegAuthorizedDeputy;
	}

	public void setRegAuthorizedDeputy(String regAuthorizedDeputy) {
		RegAuthorizedDeputy = regAuthorizedDeputy;
	}

	public String getRegEmpName() {
		return RegEmpName;
	}

	public void setRegEmpName(String regEmpName) {
		RegEmpName = regEmpName;
	}

	public static String getQueryDocumentName() {
		return QueryDocName;
	}

	public static void setQueryDocumentName(String querydocumentName) {
		QueryDocName = querydocumentName;
	}

	public static String getQualificationValue() {
		return QualificationValue;
	}

	public static void setQualificationValue(String qualificationValue) {
		QualificationValue = qualificationValue;
	}

	public static String getPreviousExperienceVal() {
		return PreviousExperienceval;
	}

	public static void setPreviousExperienceVal(String previousExperienceVal) {
		PreviousExperienceval = previousExperienceVal;
	}

	public static String getJobRes() {
		return JobResNew;
	}

	public static void setJobRes(String jobRes) {
		JobResNew = jobRes;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/preceding-sibling::a")
	WebElement systemManager;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Initiate')]")
	WebElement initiateMenu;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Modify')]")
	WebElement modifyMenu;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/preceding-sibling::a")
	WebElement userGroups;

	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Modify')]/following-sibling::ul/li/a/span[text()='Job Responsibility']")
	WebElement modifyJR;
	@FindBy(id = "TMS_System Manager_User Groups_MEN66_SUBMEN10")
	WebElement jobResponsbilityMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN07_SUBMEN10")
	WebElement jobResponsibiltyAprvMenu;
	@FindBy(id = "JobResponsibility_DOJ_btn")
	WebElement dateofJoining;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[1]")
	WebElement mElement;
	@FindBy(xpath = "//body[1]/div[2]/div[2]/div[1]/table[1]/thead[1]/tr[1]/th[2]/select[2]")
	WebElement yElement;
	@FindBy(xpath = "//button[@id='JobResponsibility_Employee_selectBtn']")
	WebElement addNew;
	@FindBy(xpath = "//span[@id='select2-JobResponsibility_DeptCode-container']")
	WebElement departmentdd;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement departmentSearchfield;
	@FindBy(xpath = "//textarea[@id='JobResponsibility_JobDescription']")
	WebElement jobResponsibilty;
	@FindBy(xpath = "//*[@id='JobResponsibility_DmsDocCodeDiv']//a")
	WebElement externalCertfAtReg;
	@FindBy(id = "select2-JobResponsibility_AppSubGrpsApp_Select-container")
	WebElement LineApproverstReg;

	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_AdptPopUpVc']")
	WebElement authorizedDeputy;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_FindTxt']")
	WebElement authorizedDeputyFindNew;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_DisplayBtn']")
	WebElement authorizedDeputyApplyNew;
	@FindBy(xpath = "//table[@id='ListTab']//tbody/tr[1]/td[1]/input[1]")
	WebElement authorizedDeputyEmployeeRB;
	@FindBy(xpath = "//button[@id='JobResponsibility_AdptPopUpVc_selectBtn']")
	WebElement authorizedDeputyAddNew;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_DmsDoc']")
	WebElement externalcerificates;
	@FindBy(xpath = "//tbody/tr[1]/td[3]/button[1]")
	WebElement documentCodeAddNew;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_selectBtn']")
	WebElement documentUploaderNew;
	@FindBy(xpath = "//span[@id='select2-JobResponsibility_AppSubGrpsApp_Select-container']")
	WebElement approverNew;
	@FindBy(xpath = "//body/span[1]/span[1]/span[1]/input[1]")
	WebElement approverSearchNew;
	@FindBy(xpath = "/html[1]/body[1]/span[1]/span[1]/span[2]/ul[1]/li[1]")
	WebElement approverNewoption;
	@FindBy(id = "JobResponsibility_Qualification")
	WebElement qualification;
	@FindBy(id = "JobResponsibility_PreviousExperience")
	WebElement previousExperience;
	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;
	@FindBy(xpath = "//span[contains(@class,'conficonfirmationMsgon_text')]")
	WebElement confirmationMsg;
	@FindBy(xpath = "//ul[@class='sub-menu']//li[contains(@class,'nav-item open')]//a[contains(@class,'nav-link nav-toggle')][contains(text(),'Audit Trails')]")
	WebElement auditTrails;
	@FindBy(id = "displayBtn")
	WebElement displayBtn;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement searchFilter;
	@FindBy(xpath = "//span[@id='select2-SearchType-container']")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Employee Name')]")
	WebElement searchByNewDropdown;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement auditEmployeeNameField;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Employee Name']")
	WebElement searchByEmployeeNameOption;
	@FindBy(xpath = "//input[@id='EmployeeName']")
	WebElement employeeName;
	@FindBy(xpath = "//button[@id='displayBtn']")
	WebElement applyNew;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement empFullName;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement empID;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement Dept;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[4]")
	WebElement InitiatedBy;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[6]")
	WebElement RevisionNo;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[5]")
	WebElement InitiatedDate;
	@FindBy(xpath = "//label[text()='Employee Name']/following-sibling::input")
	WebElement EmpNameValue;
	@FindBy(xpath = "//label[text()='Employee ID']/following-sibling::input")
	WebElement EmpIDValue;
	@FindBy(xpath = "//label[text()='Designation']/following-sibling::input")
	WebElement DesValue;
	@FindBy(xpath = "//label[text()='Department']/following-sibling::input")
	WebElement Dept_Value;

	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(xpath = "//input[@id='AppClsJson']/preceding-sibling::button")
	WebElement submitBtn1;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Audit Trails')]")
	WebElement audittrails;
	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN10")
	WebElement auditJobResponsibility;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[1]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditEmpName;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[7]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditJobResponsibility1;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[9]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditAuthorizedDeputy;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[10]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditQualification;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[11]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditPreviousExperience;
	@FindBy(xpath = "//body//div[@id='CompareTRN']//div//div//div[14]//div[1]//div[1]//div[1]//span[1]")
	WebElement auditLineOfApprovers;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[2]")
	WebElement initiatedBY;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[2]")
	WebElement returnActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[2]")
	WebElement reInitiateActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[2]")
	WebElement reInitiateActionApprove1;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[5]//td[2]")
	WebElement reInitiateActionApprove2;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[6]//td[2]")
	WebElement reInitiateActionApprove3;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[2]")
	WebElement subGrpApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[2]")
	WebElement userApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[2]")
	WebElement adApprovalActionBy;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[3]")
	WebElement auditDateTimeValue1;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[2]//td[3]")
	WebElement auditDateTimeValue2;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[3]//td[3]")
	WebElement auditDateTimeValue3;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[4]//td[3]")
	WebElement auditDateTimeValue4;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[5]//td[3]")
	WebElement auditDateTimeValue5;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr[6]//td[3]")
	WebElement auditDateTimeValue6;
	@FindBy(xpath = "//div[@class=\"approve-status\"]//span[1]//span[1]")
	WebElement auditApprovalRequiredValue;
	@FindBy(xpath = "//div[@class=\"approve-status\"]//span[2]//span[1]")
	WebElement auditApprovalCompletedValue;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Modification\")]")
	WebElement modificationAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Registration\")]")
	WebElement registrationAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Re-initiation Task Transfer (Reg.)\")]")
	WebElement regReinitTransferAuditBtn;
	@FindBy(xpath = "//div[@class=\"textdiv qsTitle\"]//div[contains(text(),\"Re-initiation Task Transfer (Mod.)\")]")
	WebElement modREInitTransferAuditBtn;
	@FindBy(xpath = "//button[@class=\"caliber-button-primary btn_center\"]")
	WebElement auditProceedBtn;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Approve')]")
	WebElement approve;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager_User Groups\"]/child::li/a[contains(text(),'accept')]")
	WebElement accept;
	@FindBy(id = "TMS_System Manager_User Groups_MEN02_SUBMEN10")
	WebElement acceptJR;
	@FindBy(id = "TMS_System Manager_User Groups_MEN02_SUBMEN12")
	WebElement acceptJRADAcceptance;
	@FindBy(id = "SelectedDecision_2")
	WebElement approveRadioBtn;
	@FindBy(id = "SelectedDecision_3")
	WebElement returnRadioBtn;
	@FindBy(id = "Remarks")
	WebElement approveRemarks;
	@FindBy(xpath = "//ul[@class=\"nav nav-pills nav-justified\"]//li[2]//button")
	WebElement approveModification;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager_User Groups\"]/child::li/a[contains(text(),'Modify')]")
	WebElement modify;
	@FindBy(id = "TMS_System Manager_User Groups_MEN74_SUBMEN10")
	WebElement modifyJobResMenu;
	@FindBy(id = "JobResponsibility_Remarks")
	WebElement modifyJobResRemarks;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']//li[2]")
	WebElement defaultAdminSecurity;
	@FindBy(xpath = "//ul[@id=\"TMS_System Manager\"]//li[2]//li[1]")
	WebElement defaultAdminSecurityInitiate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']//li[2]//li[1]//ul//li[3]")
	WebElement setCentralCnfiguration;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobRspAccRequired\"]")
	WebElement jobResponsibilityAcceptanceYes;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizeDeputyAcceptanceYes;
	@FindBy(id = "CentralConfiguration_Remarks")
	WebElement centralConfigurationRemarks;
	@FindBy(id = "Remarks")
	WebElement remarksVal;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Re-Initiation')]")
	WebElement reInitiateMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN109_SUBMEN10")
	WebElement reInitiateJobResMenu;
	@FindBy(id = "JobResponsibility_Remarks")
	WebElement reInitRemarks;
	@FindBy(xpath = "//ul[@class=\"nav nav-pills nav-justified\"]//li[2]//button")
	WebElement modificationReIntiate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'RI Transfer')]")
	WebElement RITransferMenu;
	// ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'RI
	// Transfer')]//parent::li/ul/li/a[text()='Job Responsibility']
	@FindBy(id = "TMS_System Manager_User Groups_MODMEN21_SUBMEN10")
	WebElement riTransferjobresponsibility;
	@FindBy(id = "TransferUserPopUpBtn")
	WebElement addItemBtn;
	@FindBy(xpath = "//input[contains(@placeholder,'Search this Page')]")
	WebElement user;
	@FindBy(xpath = "//tbody/tr[1]/td[1]/input[1]")
	WebElement displayedUserRadioBtn;
	@FindBy(id = "Transfer_selectBtn")
	WebElement addBtn;
	@FindBy(id = "Remarks")
	WebElement riTransferRemarks;
	@FindBy(xpath = "//div[@class=\"event-div\"]//span[2]")
	WebElement transferedFrom;
	@FindBy(xpath = "//div[@class=\"event-div\"]//span[4]")
	WebElement transferedTo;
	@FindBy(xpath = "//table[@class=\"audit-preview-container my-3 w-100\"]//tr//td[2]")
	WebElement trasferedBy;
	@FindBy(id = "Description")
	WebElement sendTxtLike;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceYes;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobRspAccRequired\"]")
	WebElement jobResponsibilityAcceptanceNO;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceNO;
	@FindBy(xpath = "//span[text()='Add Users']")
	WebElement authorizedDeputyAddUsers;
	@FindBy(xpath = "//i[@class='ft-search']")
	WebElement authorizedDeputyGlobalSearch;
	@FindBy(xpath = "//div[@id='ListTab_filter']//label//input")
	WebElement authorizedDeputyGlobalSearchTextEnter;
	@FindBy(xpath = "//table[@id='ListTab']//td//input")
	WebElement authorizedDeputySelectEmployee;
	@FindBy(id = "JobResponsibility_AdptPopUpVc_selectBtn")
	WebElement authorizedDeputyAdd;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/li[7]/a[1]")
	WebElement configMenu;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtAppCheckBox;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/li[7]/a[1]/following-sibling::ul/li[4]")
	WebElement jobResponsbilityMenu1;
	@FindBy(id = "select2-JobResponsibility_DeptCode-container")
	WebElement department;
	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement searchFieldtxt;
	@FindBy(xpath = "//ul[@id='select2-JobResponsibility_DeptCode-results']/li[1]")
	WebElement departmentFieldoption;
	@FindBy(xpath = "//span[@class='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(xpath = "//div[@class='form-group']//input[@id='JobResponsibility_EmployeeName']")
	WebElement EmployeeNameJobRes;
	@FindBy(xpath = "//input[@id='JobResponsibility_EmployeeId']")
	WebElement EmployeeIDJobRes;
	@FindBy(xpath = "//input[@id='JobResponsibility_DesignationName']")
	WebElement DesignationJobRes;
	@FindBy(xpath = "//label[text()='Approve']")
	WebElement approvetext;
	@FindBy(xpath = "//input[@id='JobResponsibility_DesignationName']")
	WebElement DesignationJobName;
	@FindBy(xpath = "//textarea[@id='Remarks']")
	WebElement approveremarks;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_AdptPopUpVc']")
	WebElement AddItemAuthorizedDeputy;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']/child::li/a[contains(text(),'Accept')]")
	WebElement usergroupsAccept;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN02_SUBMEN10']")
	WebElement acceptJobresponsiblity;
	@FindBy(xpath = "//a[@id='TMS_System Manager_User Groups_MEN02_SUBMEN12']")
	WebElement acceptADacceptance;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_FindTxt']")
	WebElement AddItemAuthorizedDeputytext;
	@FindBy(xpath = "//input[@id='JobResponsibility_AdptPopUpVc_DisplayBtn']")
	WebElement AddItemAuthorizedDeputyapply;
	@FindBy(xpath = "//input[@name='recordSelection']")
	WebElement AddItemAuthorizedDeputyadd;
	@FindBy(xpath = "//button[@id='JobResponsibility_AdptPopUpVc_selectBtn']")
	WebElement AddItemAuthorizedDeputyadd1;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_User Groups']//li//a[text()='Audit Trails']")
	WebElement auditTrailsMenu;
	@FindBy(id = "TMS_System Manager_User Groups_MEN12_SUBMEN10")
	WebElement jobResAuditTrails;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[5]")
	WebElement auditTrailPageColumn5;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[6]")
	WebElement auditTrailPageColumn6;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Employee Name']/following-sibling::span")
	WebElement auditCompareTRNEmployeeName;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Employee ID']/following-sibling::span")
	WebElement auditCompareTRNEmployeeID;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Designation']/following-sibling::span")
	WebElement auditCompareTRNDesignation;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Department')]/following-sibling::span")
	WebElement auditCompareTRNDepartment;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Date of Joining']/following-sibling::span")
	WebElement auditCompareTRNDateOfJoining;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Revision No.']/following-sibling::span")
	WebElement auditCompareTRNRevisionNo;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Job Responsibility']/following-sibling::span")
	WebElement auditCompareTRNJobResp;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Reporting To']/following-sibling::span")
	WebElement auditCompareTRNReportingTo;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Authorized Deputy']/following-sibling::div | //div[@id='CompareTRN']//label[text()='Authorized Deputy']/following-sibling::div//span")
	WebElement auditCompareTRNAuthorizedDeputy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Qualification']/following-sibling::span")
	WebElement auditCompareTRNQualification;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[text()='Previous Experience']/following-sibling::span")
	WebElement auditCompareTRNPreviousExpe;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'External Certificates')]/following-sibling::span//a")
	WebElement auditCompareTRNExternalCertificates;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Line of Approvers')]/following-sibling::span")
	WebElement auditCompareTRNLineOfApprovers;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareApprovedActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']")
	WebElement auditCompareReturnedActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditApprovedByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement auditApprovedDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditApprovedRemarksVal;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditReturnedByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditReturnedTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditReturnedRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']")
	WebElement auditCompare_RE_Init_ActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement audit_RE_Init_ByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement audit_RE_Init_DateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement audit_RE_Init_RemarksVal;

	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;

	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//button[@id='btnModal_JobResponsibility_DmsDoc']//span[@class='add-item'][normalize-space()='Add Item']")
	WebElement addExternalCertificates;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDocUploadFileTabBtn']")
	WebElement uploadFile;
	@FindBy(xpath = "//input[@id='DocumentDesc']")
	WebElement DocumentDescription;
	@FindBy(xpath = "//input[@id='KeyWords']")
	WebElement Keyword;
	@FindBy(xpath = "//label[@for='DocFile']")
	WebElement chooseFile;

	@FindBy(xpath = "//input[@id='DocFile']")
	WebElement externalDocumentUpload;

	@FindBy(xpath = "//p[@id='status-message-1-1']")
	WebElement auditCompareTRNApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[1]")
	WebElement auditCompareTRNApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[2]")
	WebElement auditCompareTRNApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditCompareTRNApproveRemarksValue;
	@FindBy(xpath = "//p[@id='status-message-1-2']")
	WebElement auditCompareTRNUserAccepApproveActionValue;
	@FindBy(xpath = "//p[@id='approve-name-1-2']")
	WebElement auditCompareTRNUserAccepApproveActionByValue;
	@FindBy(xpath = "//p[@id='approve-date-1-2']")
	WebElement auditCompareTRNUserAccepApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='progress-reports-1-2']")
	WebElement auditCompareTRNUserAccepApproveRemarksValue;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_submitBtn']")
	WebElement chooseFileUpload;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDocSearchFileTabBtn']")
	WebElement searchfile;
	@FindBy(xpath = "(//span[@class='select2-selection__arrow'])[2]")
	WebElement searchbydrop;
	@FindBy(xpath = "//li[text()='Document Description']")
	WebElement selectdocumentdescription;
	@FindBy(xpath = "//li[text()='Document Code']")
	WebElement selectdocumentcode;
	@FindBy(xpath = "//input[@id='JobResponsibility_DmsDoc_FindTxt']")
	WebElement documentfindtext;
	@FindBy(xpath = "//input[@id='JobResponsibility_DmsDoc_DisplayBtn']")
	WebElement applydoc;
	@FindBy(xpath = "//button[@id='OkBtn']")
	WebElement okdoc;
	@FindBy(xpath = "//tr[@role='row']//button[@type='button'][normalize-space()='Add']")
	WebElement adddoc;
	@FindBy(xpath = "//button[@id='JobResponsibility_DmsDoc_selectBtn']")
	WebElement adddocum;
	@FindBy(xpath = "//input[@id='Description']")
	WebElement auditEmployeeNameLike;
	@FindBy(xpath = "/html[1]/body[1]/div[5]/h3[1]")
	WebElement docconfirmationText;
	@FindBy(xpath = "//input[@id='JobResponsibility_RevisionNo']")
	WebElement revisionnumber;
	@FindBy(xpath = "//input[@id='JobResponsibility_ReportingToName']")
	WebElement jobReportingTo;

	@FindBy(xpath = "//p[@id='status-message-1-3']")
	WebElement auditCompareTRNADApproveActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[1]")
	WebElement auditCompareTRNADApproveActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[2]")
	WebElement auditCompareTRNADApproveDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-3']//following-sibling::p[3]")
	WebElement auditCompareTRNADApproveRemarksValue;
	@FindBy(xpath = "//span[text()='Add Item']")
	WebElement AddItem;
	@FindBy(name = "UserRbtn")
	WebElement UserRbtn;
	@FindBy(xpath = "//*[@id='AuditEventModal']//*[text()='Registration']")
	WebElement RegAuditTrailTab;

	@FindBy(xpath = "//*[@id='AuditEventModal']//*[text()='Modification']")
	WebElement ModAuditTrailTab;

	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement proCeedButton;

	@FindBy(xpath = "//span[@class='popup-value-item-close JobResponsibility_DmsDocremove-multipopup']")
	WebElement deselelctDoc;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Employee Name']/following-sibling::span")
	WebElement auditMainTRNEmployeeName;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Employee ID']/following-sibling::span")
	WebElement auditMainTRNEmployeeID;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Designation']/following-sibling::span")
	WebElement auditMainTRNDesignation;

	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Department')]/following-sibling::span")
	WebElement auditMainTRNDepartment;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Date of Joining']/following-sibling::span")
	WebElement auditMainTRNDateOfJoining;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Revision No.']/following-sibling::span")
	WebElement auditMainTRNRevisionNo;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Job Responsibility']/following-sibling::span")
	WebElement auditMainTRNJobResp;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Reporting To']/following-sibling::span")
	WebElement auditMainTRNReportingTo;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Authorized Deputy']/following-sibling::div | //div[@id='MainTRN']//label[text()='Authorized Deputy']/following-sibling::div//span")
	WebElement auditMainTRNAuthorizedDeputy;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Qualification']/following-sibling::span")
	WebElement auditMainTRNQualification;

	@FindBy(xpath = "//div[@id='MainTRN']//label[text()='Previous Experience']/following-sibling::span")
	WebElement auditMainTRNPreviousExpe;

	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'External Certificates')]/following-sibling::span//a")
	WebElement auditMainTRNExternalCertificates;

	@FindBy(xpath = "//div[@id='MainTRN']//label[contains(text(),'Line of Approvers')]/following-sibling::span")
	WebElement auditMainTRNLineOfApprovers;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']")
	WebElement auditMainTRNActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']")
	WebElement auditMainTRNApprovedActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']")
	WebElement auditMainTRNReturnedActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditMainTRNActionByValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditMainTRNDateTimeValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditMainTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='MainTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditMainTRNApprovalComVal;

	@FindBy(xpath = "//div[@id='MainTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditMainTRNFinalStatus;

	@FindBy(xpath = "(//label[text()='Employee Name']/following-sibling::span)[2]")
	WebElement auditModRITransferEmployeeName;

	@FindBy(xpath = "(//label[text()='Employee ID']/following-sibling::span)[2]")
	WebElement auditModRITransferEmployeeID;

	@FindBy(xpath = "(//label[text()='Designation']/following-sibling::span)[2]")
	WebElement auditModRITransferDesignation;

	@FindBy(xpath = "(//label[contains(text(),'Department')]/following-sibling::span)[2]")
	WebElement auditModRITransferDepartment;

	@FindBy(xpath = "(//label[text()='Date of Joining']/following-sibling::span)[2]")
	WebElement auditModRITransferDateOfJoining;

	@FindBy(xpath = "(//label[text()='Revision No.']/following-sibling::span)[2]")
	WebElement auditModRITransferRevisionNo;

	@FindBy(xpath = "(//label[text()='Job Responsibility']/following-sibling::span)[2]")
	WebElement auditModRITransferJobResp;

	@FindBy(xpath = "(//label[text()='Reporting To']/following-sibling::span)[2]")
	WebElement auditModRITransferReportingTo;

	@FindBy(xpath = "//label[text()='Authorized Deputy']/following-sibling::div | (//label[text()='Authorized Deputy']/following-sibling::div//span)[2]")
	WebElement auditModRITransferAuthorizedDeputy;

	@FindBy(xpath = "(//label[text()='Qualification']/following-sibling::span)[2]")
	WebElement auditModRITransferQualification;

	@FindBy(xpath = "(//label[text()='Previous Experience']/following-sibling::span)[2]")
	WebElement auditModRITransferPreviousExpe;

	@FindBy(xpath = "(//label[contains(text(),'External Certificates')]/following-sibling::span//a)[2]")
	WebElement auditModRITransferExternalCertificates;

	@FindBy(xpath = "(//label[contains(text(),'Line of Approvers')]/following-sibling::span)[2]")
	WebElement auditModRITransferLineOfApprovers;

	@FindBy(xpath = "(//p[text()='Initiated'])[2]")
	WebElement auditModRITransferActionValue;

	@FindBy(xpath = "(//p[text()='Approved'])[2]")
	WebElement auditModRITransferApprovedActionValue;

	@FindBy(xpath = "(//p[text()='Returned'])[2]")
	WebElement auditModRITransferReturnedActionValue;

	@FindBy(xpath = "(//p[text()='Initiated']//following-sibling::p[1])[2]")
	WebElement auditModRITransferActionByValue;

	@FindBy(xpath = "(//p[text()='Initiated']//following-sibling::p[2])[2]")
	WebElement auditModRITransferDateTimeValue;

	@FindBy(xpath = "(//p[text()='Initiated']//following-sibling::p[3])[2]")
	WebElement auditModRITransferRemarksVal1;

	@FindBy(xpath = "(//span[text()='No. of Approvals Required:']//child::span)[2]")
	WebElement auditModRITransferApprovalReqVal;
	@FindBy(xpath = "(//span[text()='No. of Approvals Completed:']//child::span)[2]")
	WebElement auditModRITransferApprovalComVal;

	@FindBy(xpath = "(//div[@class='event-div']//h6[@class='status_heading'])[2]")
	WebElement auditModRITransferFinalStatus;

	@FindBy(xpath = "//button[text()='Modification']")
	WebElement ModApprovalTab;
	
	
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditMainTRNApprovedByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement auditMainTRNApprovedDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditMainTRNApprovedRemarksVal;
	
	
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']")
	WebElement audit_MainTRN_RE_Init_ActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement audit_MainTRN_RE_Init_ByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement audit_MainTRN_RE_Init_DateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement audit_MainTRN_RE_Init_RemarksVal;
	
	
	
	
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditMainTRNReturnedByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditMainTRNReturnedTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditMainTRNReturnedRemarksVal1;
	
	
	
	
	
	
	@FindBy(xpath = "//span[@id='appAction']")
	WebElement confirmationText2;
	
	
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement auditCompareApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditCompareApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditCompareApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditCompareApproveTRNRemarksVal1;
	
	
	@FindBy(xpath = "(//div[@id='CompareTRN']//p[text()='Approved'])[2]")
	WebElement auditUACompareApproveTRNActionValue;

	@FindBy(xpath = "(//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1])[2]")
	WebElement auditUACompareApproveTRNActionByValue;

	@FindBy(xpath = "(//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2])[2]")

	WebElement auditUACompareApproveTRNDateTimeValue;
	@FindBy(xpath = "(//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3])[2]")
	WebElement auditUACompareApproveTRNRemarksVal1;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']")
	WebElement auditCompareReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditCompareReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditCompareReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditCompareReturnTRNRemarksVal1;
	
	@FindBy(xpath = "//div[text()='Re-initiation Task Transfer (Reg.)']")
	WebElement reiniatetasktransferregistration;

	@FindBy(xpath = "//div[text()='Registration']")
	WebElement reiniatetaskregistration;
	
	
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[1]")
	WebElement ritransferinitiateddate;
	@FindBy(xpath = "//p[@id='approve-name-0']//following::p[2]")
	WebElement ritransferremarks;

	@FindBy(xpath = "//h6[@id='status_heading']")
	WebElement ritransferusernamestatus;
	
	
	@FindBy(xpath = "//button[@id='AuditEventModal_View']")
	WebElement ritransferproceed;
	
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']")
	WebElement auditCompareReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditCompareReinitateTRNActionByValue;
	@FindBy(xpath = "c")
	WebElement auditCompareReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditCompareReinitateTRNRemarksVal1;
	
	@FindBy(xpath = "//p[@id='status-message-1-4']")
	WebElement auditUACompareReturnTRNActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-4']//following-sibling::p[1]")
	WebElement auditUACompareReturnTRNActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-4']//following-sibling::p[2]")
	WebElement auditUACompareReturnTRNDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-4']//following-sibling::p[3]")
	WebElement auditUACompareReturnTRNRemarksVal1;
	
	@FindBy(xpath = "//p[@id='status-message-1-5']")
	WebElement auditUACompareReinitTRNActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-5']//following-sibling::p[1]")
	WebElement auditUACompareReinitTRNActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-5']//following-sibling::p[2]")
	WebElement auditUACompareReinitTRNDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-5']//following-sibling::p[3]")
	WebElement auditUACompareReinitTRNRemarksVal1;
	
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']")
	WebElement auditMainReturnTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[1]")
	WebElement auditMainReturnTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[2]")
	WebElement auditMainReturnTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Returned']//following-sibling::p[3]")
	WebElement auditMainReturnTRNRemarksVal1;
	
	@FindBy(xpath = "//p[@id='status-message-1-6']")
	WebElement auditUACompareappTRNActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-6']//following-sibling::p[1]")
	WebElement auditUACompareappTRNActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-6']//following-sibling::p[2]")
	WebElement auditUACompareappTRNDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-6']//following-sibling::p[3]")
	WebElement auditUACompareappTRNRemarksVal1;
	
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']")
	WebElement auditMainReinitateTRNActionValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[1]")
	WebElement auditMainReinitateTRNActionByValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[2]")
	WebElement auditMainReinitateTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Re-Initiated']//following-sibling::p[3]")
	WebElement auditMainReinitateTRNRemarksVal1;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']")
	WebElement auditMainApproveTRNActionValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement auditMainApproveTRNActionByValue;

	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[2]")

	WebElement auditMainApproveTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='MainTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement auditMainApproveTRNRemarksVal1;
	
	@FindBy(xpath = "//p[@id='status-message-1-1']//following-sibling::p[3]")
	WebElement auditMainTRNRemarksVal2;
	@FindBy(xpath = "//p[@id='status-message-1-7']")
	WebElement auditUACompareuserappTRNActionValue;
	@FindBy(xpath = "//p[@id='status-message-1-7']//following-sibling::p[1]")
	WebElement auditUACompareuserappTRNActionByValue;
	@FindBy(xpath = "//p[@id='status-message-1-7']//following-sibling::p[2]")
	WebElement auditUACompareuserappTRNDateTimeValue;
	@FindBy(xpath = "//p[@id='status-message-1-7']//following-sibling::p[3]")
	WebElement auditUACompareuserappTRNRemarksVal1;



	public SYS_JobResponsibility() {
		PageFactory.initElements(driver, this);
	}

	public void jobresponsbility_Configuration(HashMap<String, String> testData) {
		TimeUtil.shortWait();
		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, configMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(configMenu, jobResponsbilityMenu1,
				CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu1,
				JobResponsibilityStrings.JobResponsibility_Config_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibility_Config_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		TimeUtil.shortWait();
		SelectRadioBtnAndCheckbox(driver, eSignAtAppCheckBox, "Approval");
		TimeUtil.shortWait();
		scrollToViewElement(remarks);
		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void jobResponsibiltyRegWith1Approval(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegAuthorizedDeputyValue = testData.get("AuthorizedDeputy");
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegsubGroupNameValue = testData.get("SubGroupNameValue");
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));
		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		// RegEmployeeName = "userJCUZ.userJCUZ";
		// RegEmployeeID = "userJCUZ";
		RegEmployeeID = SSO_UserRegistration.getEmployeeID();
		// RegDepartName = MDM_Department_Registration.getDepartmentName();

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, initiateMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(initiateMenu, jobResponsbilityMenu,
				CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		waitForElementVisibile(dateofJoining);
		scrollToViewElement(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(AddItemAuthorizedDeputy, JobResponsibilityStrings.Authorized_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(AddItemAuthorizedDeputytext,
				JobResponsibilityStrings.AuthorizedDeputy_DC.getJobResponsibilityStrings(),
				testData.get("AuthorizedDeputy") + testData.get("percentageSign"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.AuthorizedDeputy_SS.getJobResponsibilityStrings());
		click2(AddItemAuthorizedDeputyapply, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		clickAndWaitforNextElement(AddItemAuthorizedDeputyadd, AddItemAuthorizedDeputyadd1,
				JobResponsibilityStrings.Authorized_Deputyadd_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Authorized_Deputyadd_SS.getJobResponsibilityStrings());
		click2(AddItemAuthorizedDeputyadd1, JobResponsibilityStrings.DeputyAuthorized_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.AuthorizedDeputyadd_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.AuthorizedDeputyadd_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
//		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
//				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(uploadFile, DocumentDescription,
//				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
//		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
//				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
//				testData.get("Keyworddescription"),
//				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
////		clickChooseFile(chooseFile, testData.get("DocumentUpload"),
////				DocumentRegistrationStrings.Choosefiledocument_DC.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_AC.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_AR.getDocumentRegistrationStrings(),
////				DocumentRegistrationStrings.Choosefiledocument_SS.getDocumentRegistrationStrings());
//		scrollToViewElement(chooseFile);
//		TimeUtil.mediumWait();
//		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
////		externalDocumentUpload
//		TimeUtil.mediumWait();
//		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		saveExternalCode(driver, docconfirmationText);
//		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(searchfile, searchbydrop,
//				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
//				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
//				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
//		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
//				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
//		TimeUtil.shortWait();
//		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//		clickAndWaitforNextElement(adddoc, adddocum,
//				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(adddocum, approverNew,
//				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
//				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		// TimeUtil.longwait();
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		// String ConfirmationTextAtEsign =
		// Constants.JOBRESPONSIBILITY_REGISTRATION_CONFIRMATION_TEXT_ESIGN;
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
//		String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
//		String ConfirmationText = Constants.REGISTRATION_CONFIRMATION_TEXT;
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void jobResponsibilityLineOfApproversApproveWithAudiTrails(HashMap<String, String> testData,
			String ApprovalStatus, String ValidateApprovalScreen) {

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(approve, jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameField,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
				// + testData.get("percentageSign"),
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		clickAndWaitforNextElement(displayBtn, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		if (ValidateApprovalScreen.equals("ReInitApproval")) {

			verifyExactCaption(InitiatedBy, Re_Int_Name, "Re_Initiated By (Employee Name)");

		}

		else {

			verifyExactCaption(InitiatedBy, InitiatorName, "Initiated By (Employee Name)");
		}

		validateMultipleDateFormats(InitiatedDate);

		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());

		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));

		if (ValidateApprovalScreen.equals("IntiateApprove")) {

			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, RegJobResponsibilityValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, RegQualificationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, RegPreviousExperienceVal,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			scrollToViewElement(returnRadioBtn);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.GROUPREMARKS, "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		}

		if (ValidateApprovalScreen.equals("ReturnedReInitApproval")) {

			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, RegJobResponsibilityValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, RegQualificationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, RegPreviousExperienceVal,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			scrollToViewElement(returnRadioBtn);

			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditReturnedByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditReturnedTimeValue);
			verifyExactCaption(auditReturnedRemarksVal1, Constants.GROUPREMARKS, "Remarks");

			verifyExactCaption(auditCompareReturnedActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			verifyExactCaption(auditReturnedByValue, ApproverNameInitiatorEmpID, "Initiated By");
			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, "Returned", "Remarks");

			verifyExactCaption(auditCompare_RE_Init_ActionValue, Constants.REINITIATE_ACTIONVAL, "Re Initiated");

			verifyExactCaption(audit_RE_Init_ByValue, Re_Int_NameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(audit_RE_Init_DateTimeValue);
			verifyExactCaption(audit_RE_Init_RemarksVal, "ReInitiating", "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		}

		scrollToViewElement(approvetext);
		if (ApprovalStatus.equals("Approved")) {
			click2(approveRadioBtn, CommonStrings.Select_Approve_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());
		}

		else if (ApprovalStatus.equals("Returned")) {

			click2(returnRadioBtn, "Select decision as 'Return'", CommonStrings.Selction_AC.getCommonStrings(),
					CommonStrings.Selction_AR.getCommonStrings(), CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ReturnRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());

		}
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		getUserNameFromEsgin();
		ApproverNameInitiatorEmpID = EmpNameEmpID;
		ApproverEmployeeID = EmpID;
		ApproverName = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void jobResponsibilityUserAcceptanceApprove(HashMap<String, String> testData) {

//	RegDesignationValue = SSO_UserRegistration.getDesignationValue();
//	RegEmployeeName = SSO_UserRegistration.getEmployeeName();
//	RegEmployeeID = SSO_UserRegistration.getEmployeeID();
//	RegDepartName = MDM_Deparment.getDepartmentName();
//		RegReportingToValue = "--";
//		RegDesignationValue = "Junior SQA Engineer-L105";
//		RegEmployeeName = "sessionnew15.sessionnew15";
//		RegEmployeeID = "sessionnew15";
//		RegDepartName = "DepartmentEYO";

//	RegAuthorizedDeputyValue = "laxmi4";
//	RegQualificationValue = "Tester";
//	RegPreviousExperienceVal = "3";
//	RegExternalCertificates = "querydocument";
//	RegsubGroupNameValue = "Approvers Subgroup";
//	RegJobResponsibilityValue = "DocumentReading";

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, usergroupsAccept, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(usergroupsAccept);
		clickAndWaitforNextElement(usergroupsAccept, acceptJobresponsiblity,
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		click2(acceptJobresponsiblity,
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveclickemployyee_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveclickemployyee_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("SSONewUserRegPassword"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, displayedRecord, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityacceptapproval_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityacceptapproval_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void jobResponsibility_AuthorizedDeputyAcceptance_ApproveWithAudiTrails(HashMap<String, String> testData) {

		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		RegEmployeeID = SSO_UserRegistration.getEmployeeID();
		// RegDepartName = MDM_Deparment.getDepartmentName();

//		RegReportingToValue = "--";
//		RegDesignationValue = "Junior SQA Engineer-L105";
//		RegEmployeeName = "sessionnew15.sessionnew15";
//		RegEmployeeID = "sessionnew15";
//		RegDepartName = "DepartmentEYO";

//	RegAuthorizedDeputyValue = "laxmi4";
//	RegQualificationValue = "Tester";
//	RegPreviousExperienceVal = "3";
//	RegExternalCertificates = "querydocument";
//	RegsubGroupNameValue = "Approvers Subgroup";
//	RegJobResponsibilityValue = "DocumentReading";
//	ExternalCode = "LEIQ24000150";

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, usergroupsAccept, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(usergroupsAccept);
		clickAndWaitforNextElement(usergroupsAccept, acceptADacceptance,
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
		click2(acceptADacceptance,
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresacceptadApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityacceptApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, employeeName,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		clickAndWaitforNextElement(displayBtn, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(displayedRecord, highlightAuditScreenWindowTitle,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresADApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresADApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), testData.get("ADPassword"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(userGroups, auditTrailsMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		clickAndWaitforNextElement(auditTrailsMenu, jobResAuditTrails,
				CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		clickAndWaitforNextElement(searchByNew, searchByEmployeeNameOption,
				CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameLike,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(applyNew, displayedRecord, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityADDapproval_AC
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityADDapproval_AR
						.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void jobResponsibiltyRegWith_UA_NO_AD_NO(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}
		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegsubGroupNameValue = testData.get("SubGroupNameValue");
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));
		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		RegEmployeeID = SSO_UserRegistration.getEmployeeID();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				// "VijayFTPIZ.VijayLTPIZ",
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		waitForElementVisibile(dateofJoining);
		verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
		verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
		verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
		verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");
		scrollToViewElement(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();

		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());

		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(uploadFile, DocumentDescription,
				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
				testData.get("Keyworddescription"),
				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
		scrollToViewElement(chooseFile);
		TimeUtil.mediumWait();
		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
		TimeUtil.mediumWait();
		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		saveExternalCode(driver, docconfirmationText);
		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchfile, searchbydrop,
				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		clickAndWaitforNextElement(adddoc, adddocum,
				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(adddocum, approverNew,
				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

		TimeUtil.shortWait();
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());
		getUserNameFromEsgin();
		IntiatorNameInitiatorEmpID = EmpNameEmpID;
		InitiatorEmployeeID = EmpID;
		InitiatorName = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void jobResponsibiltyRegWith_UA_NO_AD_NO_AuditTrails(HashMap<String, String> testData,
			String ApprovalStatus) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName,
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(InitiatedBy, InitiatorName, "Initiated By (Employee Name)");
		validateMultipleDateFormats(InitiatedDate);
		verifyExactCaption(RevisionNo, Constants.REVISION_NO_AS_0, "Revision Number");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		if (ApprovalStatus.equals("Re-Initiation Under Approval")) {
			click2(RegAuditTrailTab, "Click on Reigstrartion Tab", "", "", "");
			click2(proCeedButton, "Click on Proceed button", "", "", "");
		}
		if (ApprovalStatus.equals("Under Approval") || ApprovalStatus.equals("Re-Initiation Under Approval")) {
			TimeUtil.shortWait();

			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
		}
		if (ApprovalStatus.equals("Under Approval")) {
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.GROUPREMARKS, "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		}

		else if (ApprovalStatus.equals("Direct Approved")) {
			verifyExactCaption(auditCompareApprovedActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditApprovedByValue, ApproverNameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(auditApprovedDateTimeValue);
			verifyExactCaption(auditApprovedRemarksVal, "approved", "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_1);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		}

		else if (ApprovalStatus.equals("Re-Initiation Under Approval")) {

			verifyExactCaption(auditCompare_RE_Init_ActionValue, Constants.REINITIATE_ACTIONVAL, "Re Initiated");

			verifyExactCaption(audit_RE_Init_ByValue, Re_Int_NameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(audit_RE_Init_DateTimeValue);
			verifyExactCaption(audit_RE_Init_RemarksVal, "ReInitiating", "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		}

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();

		switchToDefaultContent(driver);
	}

	public void jobResponsibilityLineOfApproversApprove(HashMap<String, String> testData) {
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(approve, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				RegEmployeeName + testData.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		scrollToViewElement(approvetext);
		clickAndWaitforNextElement(approveRadioBtn, approveremarks, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		approveremarks.clear();
		sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void reinitiateJobResponsibility(HashMap<String, String> testData0, HashMap<String, String> testData) {
		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(reInitiateMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(reInitiateJobResMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
				// "VijayFCWYV.VijayLCWYV",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());

		verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
		verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
		verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
		verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");

		verifyExactCaption(jobResponsibilty, testData0.get("JobRes"),
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(qualification, testData0.get("Qualificationvalue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(previousExperience, testData0.get("PreviousExperienceval"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(externalCertfAtReg, QueryDocName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(LineApproverstReg, testData0.get("SubGroupNameValue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));

		waitForElementVisibile(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
		click2(deselelctDoc, "Deselect Selected Document", "", "", "");
		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(uploadFile, DocumentDescription,
				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
				testData.get("Keyworddescription"),
				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
		scrollToViewElement(chooseFile);
		TimeUtil.mediumWait();
		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
		TimeUtil.mediumWait();
		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		saveExternalCode(driver, docconfirmationText);
		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchfile, searchbydrop,
				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		clickAndWaitforNextElement(adddoc, adddocum,
				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(adddocum, approverNew,
				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

		TimeUtil.shortWait();
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		sendKeys2(modifyJobResRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RegReInitRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());

		getUserNameFromEsgin();
		Re_Int_NameInitiatorEmpID = EmpNameEmpID;
		Re_Int_EmployeeID = EmpID;
		Re_Int_Name = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), testData.get("TransaferToPSW"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void JR_RI_Transfer(HashMap<String, String> testData, HashMap<String, String> testData2, String Stage) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(RITransferMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(riTransferjobresponsibility,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);

		if (Stage.equals("ModificationRITransfer")) {
			click2(ModApprovalTab, "Click on Modification Tab", "", "", "");

		}
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				// "VijayGOTU.VijayGOTU",
				RegEmployeeName,
				// + testData.get("percentageSign"),
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();

		if (Stage.equals("ModificationRITransfer")) {
			verifyExactCaption(auditModRITransferEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditModRITransferDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferRevisionNo, Constants.REVISION_NO_AS_1,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditModRITransferLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		} else {
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		}

		scrollToViewElement(AddItem);
		click2(AddItem, "Click on Add Item", "", "", "");
		sendKeys2(user, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				testData2.get("TransferToEmployee"),
				// RegEmployeeName,
				// + testData.get("percentageSign"),
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(UserRbtn, "Select required employee", "", "", "");
		click2(addBtn, "Click on Add Button", "", "", "");
		sendKeys2(approveRemarks, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				testData2.get("TransferredRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());

		scrollToViewElement(submit);
		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void jobResponsibilty_Mod_With_UA_NO_AD_NO(HashMap<String, String> RegtestData,
			HashMap<String, String> ModtestData) {

		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(modifyMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(modifyJR, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				// "VijayFTPIZ.VijayLTPIZ",
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(InitiatedBy, InitiatorName, "Initiated By (Employee Name)");
		validateMultipleDateFormats(InitiatedDate);
		verifyExactCaption(RevisionNo, Constants.REVISION_NO_AS_0, "Revision Number");

		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		waitForElementVisibile(dateofJoining);
		verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
		verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
		verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
		verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");
		scrollToViewElement(dateofJoining);

		scrollToViewElement(dateofJoining);
		verifyExactCaption(jobResponsibilty, RegtestData.get("JobRes"),
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(qualification, RegtestData.get("Qualificationvalue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(previousExperience, RegtestData.get("PreviousExperienceval"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(externalCertfAtReg, QueryDocName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");

		setQueryDocumentName(QueryDocName = ModtestData.get("QueryDocumentName") + s);
		RegQualificationValue = ModtestData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = ModtestData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegJobResponsibilityValue = ModtestData.get("JobRes");
		setJobRes(JobResNew = ModtestData.get("JobRes"));

		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				ModtestData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();

		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				ModtestData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				ModtestData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
		click2(deselelctDoc, "Deselect Selected Document", "", "", "");
		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(uploadFile, DocumentDescription,
				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
				ModtestData.get("Keyworddescription"),
				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
		scrollToViewElement(chooseFile);
		TimeUtil.mediumWait();
		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
		TimeUtil.mediumWait();
		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		saveExternalCode(driver, docconfirmationText);
		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchfile, searchbydrop,
				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		clickAndWaitforNextElement(adddoc, adddocum,
				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(adddocum, approverNew,
				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

		TimeUtil.shortWait();
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				ModtestData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
//		clickAndWaitforNextElement(approverNewoption, modifyJobResRemarks,
//				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		sendKeys2(modifyJobResRemarks, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
				ModtestData.get("ModifiedRemarks"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());
		getUserNameFromEsgin();
		Mod_Initiator_Name_EmpID = EmpNameEmpID;
		Mod_Initiator_EmployeeID = EmpID;
		Mod_Initiator_Name = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void jobResponsibilty_Reg_Mod_With_UA_NO_AD_NO_AuditTrails(HashMap<String, String> RegtestData,
			HashMap<String, String> ModtestData, String ApprovalStatus) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName,
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(InitiatedBy, InitiatorName, "Initiated By (Employee Name)");
		validateMultipleDateFormats(InitiatedDate);
		verifyExactCaption(RevisionNo, Constants.REVISION_NO_AS_1, "Revision Number");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);

		click2(RegAuditTrailTab, "Click on Reigstrartion Tab", "", "", "");
		click2(ModAuditTrailTab, "Click on Modification Tab", "", "", "");
		click2(proCeedButton, "Click on Proceed button", "", "", "");
		TimeUtil.shortWait();
		if(ApprovalStatus.equals("Under Approval")) {
			verifyExactCaption(auditMainTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNRevisionNo, Constants.REVISION_NO_AS_1,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNJobResp, ModtestData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNQualification, ModtestData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNPreviousExpe, ModtestData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(auditMainTRNLineOfApprovers);
			verifyExactCaption(auditMainTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");
			verifyExactCaption(auditMainTRNActionByValue, Mod_Initiator_Name_EmpID, "Initiated By");

			validateMultipleDateFormats(auditMainTRNDateTimeValue);
			verifyExactCaption(auditMainTRNRemarksVal1, "Modified", "Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal, auditMainTRNApprovalComVal,
					Constants.NOOFAPPROVALS_REQUIRED_AS_1, Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
			
		}
		else if (ApprovalStatus.equals("Direct Approved")) {
			verifyExactCaption(auditMainTRNApprovedActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditMainTRNApprovedByValue, ApproverNameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(auditMainTRNApprovedDateTimeValue);
			verifyExactCaption(auditMainTRNApprovedRemarksVal, "approved", "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal,
					auditMainTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_1);
			verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		}
		
		else if (ApprovalStatus.equals("ModificationRe-Initiation Under Approval")) {

			verifyExactCaption(audit_MainTRN_RE_Init_ActionValue, Constants.REINITIATE_ACTIONVAL, "Re Initiated");

			verifyExactCaption(audit_MainTRN_RE_Init_ByValue, Re_Int_NameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(audit_MainTRN_RE_Init_DateTimeValue);
			verifyExactCaption(audit_MainTRN_RE_Init_RemarksVal, "MOD_ReInitiating", "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal,
					auditMainTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		}
		

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();

		switchToDefaultContent(driver);
	}

	// Modification Approval

	public void jobResponsibility_Modification_Approval_UA_NO_AD_NO(HashMap<String, String> testData,
			String ApprovalStatus, String ValidateApprovalScreen) {

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		clickAndWaitforNextElement(approve, jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
		click2(jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(ModApprovalTab, "Click on Moidification Tab", "", "", "");
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameField,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
				// + testData.get("percentageSign"),
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		clickAndWaitforNextElement(displayBtn, auditTrailPageColumn1, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		if (ValidateApprovalScreen.equals("ModReInitApproval")) {

			verifyExactCaption(InitiatedBy, Re_Int_Name, "Re_Initiated By (Employee Name)");

		}

		else {

			verifyExactCaption(InitiatedBy, Mod_Initiator_Name, "Initiated By (Employee Name)");
		}

		validateMultipleDateFormats(InitiatedDate);

		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());

		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));
		TimeUtil.mediumWait();
		if (ValidateApprovalScreen.equals("ModificationIntiateApprove")) {

			verifyExactCaption(auditMainTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNRevisionNo, Constants.REVISION_NO_AS_1,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNJobResp, RegJobResponsibilityValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNQualification, RegQualificationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNPreviousExpe, RegPreviousExperienceVal,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			scrollToViewElement(auditMainTRNActionValue);
			verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditMainTRNActionByValue, Mod_Initiator_Name_EmpID, "Initiated By");

			validateMultipleDateFormats(auditMainTRNDateTimeValue);
			verifyExactCaption(auditMainTRNRemarksVal1, testData.get("ModifiedRemarks"), "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal,
					auditMainTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		}

		if (ValidateApprovalScreen.equals("ModReInitApproval")) {

			verifyExactCaption(auditMainTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditMainTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNRevisionNo, Constants.REVISION_NO_AS_1,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNJobResp, RegJobResponsibilityValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNReportingTo, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNAuthorizedDeputy, Constants.VALID_TO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNQualification, RegQualificationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNPreviousExpe, RegPreviousExperienceVal,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditMainTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			scrollToViewElement(returnRadioBtn);

			
			
			
			
			
			
			
			verifyExactCaption(auditMainTRNReturnedActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			verifyExactCaption(auditMainTRNReturnedByValue, ApproverNameInitiatorEmpID, "ReturnedBy");
			validateMultipleDateFormats(auditMainTRNReturnedTimeValue);
			verifyExactCaption(auditMainTRNReturnedRemarksVal1, "Returned", "Remarks");

			
			
			
			
			
			
			
			
			verifyExactCaption(audit_MainTRN_RE_Init_ActionValue, Constants.REINITIATE_ACTIONVAL, "Re Initiated");

			verifyExactCaption(audit_MainTRN_RE_Init_ByValue, Re_Int_NameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(audit_MainTRN_RE_Init_DateTimeValue);
			verifyExactCaption(audit_MainTRN_RE_Init_RemarksVal, "MOD_ReInitiating", "Remarks");
			
			
			
			
			
			
			
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditMainTRNApprovalReqVal,
					auditMainTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
					Constants.NOOFAPPROVALS_REQUIRED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		}

		scrollToViewElement(approvetext);
		if (ApprovalStatus.equals("Approved")) {
			click2(approveRadioBtn, CommonStrings.Select_Approve_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());
		}

		else if (ApprovalStatus.equals("Returned")) {

			click2(returnRadioBtn, "Select decision as 'Return'", CommonStrings.Selction_AC.getCommonStrings(),
					CommonStrings.Selction_AR.getCommonStrings(), CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ReturnRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());

		}
		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
		getUserNameFromEsgin();
		ApproverNameInitiatorEmpID = EmpNameEmpID;
		ApproverEmployeeID = EmpID;
		ApproverName = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);

	}

	public void ModreinitiateJobResponsibility(HashMap<String, String> testData0, HashMap<String, String> testData) {
		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(reInitiateMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		click2(reInitiateJobResMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		switchToBodyFrame(driver);

		click2(ModApprovalTab, "", "", "", "");
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
				// "VijayFCWYV.VijayLCWYV",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());

		verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
		verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
		verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
		verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");

		verifyExactCaption(jobResponsibilty, testData0.get("JobRes"),
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(qualification, testData0.get("Qualificationvalue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(previousExperience, testData0.get("PreviousExperienceval"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(externalCertfAtReg, QueryDocName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(LineApproverstReg, testData0.get("SubGroupNameValue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));

		waitForElementVisibile(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
		click2(deselelctDoc, "Deselect Selected Document", "", "", "");
		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(uploadFile, DocumentDescription,
				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
				testData.get("Keyworddescription"),
				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
		scrollToViewElement(chooseFile);
		TimeUtil.mediumWait();
		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
		TimeUtil.mediumWait();
		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		saveExternalCode(driver, docconfirmationText);
		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchfile, searchbydrop,
				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		clickAndWaitforNextElement(adddoc, adddocum,
				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(adddocum, approverNew,
				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

		TimeUtil.shortWait();
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		sendKeys2(modifyJobResRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RegReInitRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());

		getUserNameFromEsgin();
		Re_Int_NameInitiatorEmpID = EmpNameEmpID;
		Re_Int_EmployeeID = EmpID;
		Re_Int_Name = Empname;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), testData.get("TransaferToPSW"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}
	
	
	public void jobResponsibiltyRegWith_UA_YES_AD_NO(HashMap<String, String> testData) {

		TimeUtil.shortWait();
		String s = "";
		String s1 = "";
		prop = ConfigsReader.readProperties("./configs/configuration.properties");
		if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
			s = TextUtils.randomvalue(4);
			s1 = TextUtils.randomAlphaNumeric(4);
			System.out.println("Generated S Value is: " + s);

		}
		setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
		RegQualificationValue = testData.get("Qualificationvalue");
		RegPreviousExperienceVal = PreviousExperienceval;
		setPreviousExperienceVal(PreviousExperienceval = testData.get("PreviousExperienceval"));
		RegExternalCertificates = getQueryDocumentName();
		RegsubGroupNameValue = testData.get("SubGroupNameValue");
		RegJobResponsibilityValue = testData.get("JobRes");
		setJobRes(JobResNew = testData.get("JobRes"));
		RegDesignationValue = SSO_UserRegistration.getDesignationValue();
		RegEmployeeName = SSO_UserRegistration.getEmployeeName();
		RegEmployeeID = SSO_UserRegistration.getEmployeeID();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		click2(initiateMenu, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AC.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		click2(jobResponsbilityMenu, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilitySS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				// "VijayFTPIZ.VijayLTPIZ",
				RegEmployeeName, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_SS.getJobResponsibilityStrings());
		waitForElementVisibile(dateofJoining);
		verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
		verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
		verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
		verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");
		scrollToViewElement(dateofJoining);
		click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
		selectCurrentdate();
		sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
				testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();

		sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
				testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
		TimeUtil.mediumWait();
		sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
				testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());

		clickAndWaitforNextElement(addExternalCertificates, uploadFile,
				JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(uploadFile, DocumentDescription,
				JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
				QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
				testData.get("Keyworddescription"),
				JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
		scrollToViewElement(chooseFile);
		TimeUtil.mediumWait();
		FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
		TimeUtil.mediumWait();
		click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		saveExternalCode(driver, docconfirmationText);
		clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchfile, searchbydrop,
				JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
				JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
				JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
		sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
				ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		clickAndWaitforNextElement(adddoc, adddocum,
				JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(adddocum, approverNew,
				JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

		TimeUtil.shortWait();
		scrollToViewElement(approverNew);
		clickAndWaitforNextElement(approverNew, approverSearchNew,
				JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
				testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
		clickAndWaitforNextElement(approverNewoption, submitButton,
				JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());
		click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());
		getUserNameFromEsgin();
		IntiatorNameInitiatorEmpID = EmpNameEmpID;
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Esign_JobProceed_AR.getJobResponsibilityStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
	
	
	public void jobResponsibiltyRegWith_UA_YES_AD_NO_AuditTrails(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName,
				// "VijayFTPIZ.VijayLTPIZ",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNReportingTo, Constants.VALID_TO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.VALID_TO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");
		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_1,
				Constants.NOOFAPPROVALS_REQUIRED_AS_0);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}


	
	
	
	
	
	
	
	
	
	public void jobResponsibilityLineOfApproversApprove(HashMap<String, String> testData, String Status,
			String ApprovalStatus, String ConfirmationTextESIGN) {

		clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		clickAndWaitforNextElement(userGroups, approve, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

		clickAndWaitforNextElement(approve, jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());

		click2(jobResponsibiltyAprvMenu,
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		clickAndWaitforNextElement(searchByEmployeeNameOption, auditEmployeeNameField,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());

		sendKeys2(auditEmployeeNameField,
				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());

//		sendKeys2(auditEmployeeNameField,
//				JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), "sweFWFRR.sweLWFRR",
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());

		click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation against SSO Registered User name and Employee name displayed at Job Responsibility Registration list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation against SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registration list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation against SSO Registered Department Name");
//		String[] parts = InitiatorEmployeeID.split("\\(");
//		String result = parts[0];
//		System.out.println(result);
		// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
		validateMultipleDateFormats(auditTrailPageColumn5);
		TimeUtil.shortWait();

		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
				"Validation against SSO Registered User name and Employee name");
		verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID, "Validation against SSO Registered Employee ID");
		verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
				"Validation against SSO Registered Designation");
		verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation against SSO Registered Department");
		verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
				"Validation against SSO Registered Revision No");
		verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
				"Validation against SSO Registered Job Responsibility");
		verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
				"Validation against SSO Registered Reporting To");
		verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
				"Validation against SSO Registered Authorized Deputy");
		verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
				"Validation against SSO Registered Qualification");
		verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
				"Validation against SSO Registered Previous Experience");
		verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
				"Validation against SSO Registered External Certificates");
		verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
				"Validation against Line Of Approvers Subgroup");

		// ✅ Status-based method execution using switch-case
		switch (Status.trim()) {
		case "InitiatedDetails":
			jobResponsibilty_UA_YES_AD_NO_afterinitiatedEvents(testData);
			break;
		case "CompleteApprovedDetails":
			jobResponsibilyWith_UA_YES_AD_NO_ReiniatedApprovedEventDetails(testData);
			break;

		case "UserAcceptanceCompleteApprovedDetails":
			jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserApprovedEvents(testData);
			break;

		default:
			System.out.println("Unrecognized status: " + Status);
			break;
		}

		scrollToViewElement(approvetext);

		if (ApprovalStatus.equals("Approved")) {
			click2(approveRadioBtn, CommonStrings.Select_Approve_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());

		} else if (ApprovalStatus.equals("Returned")) {
			click2(returnRadioBtn, "Select decision as 'Return'", CommonStrings.Selction_AC.getCommonStrings(),
					CommonStrings.Selction_AR.getCommonStrings(), CommonStrings.Select_Approve_SS.getCommonStrings());
			approveremarks.clear();
			sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ReturnRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());
		}

		scrollToViewElement(submitBtn1);
		click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
				JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());

		getUserNameFromEsgin();
		ApproverNameInitiatorEmpID = EmpNameEmpID;
		ApproverEmployeeID = EmpID;
		ApproverName = Empname;

		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
				ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
				CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());

		TimeUtil.shortWait();

		if (ConfirmationTextESIGN.equals("ConfirmationApproved")) {

			String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
			verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

			String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
			verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());

			TimeUtil.shortWait();
			saveUniqueCode(driver, confirmationText);

			String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
			String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;

			getCurrentDate();
			verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

			switchToDefaultContent(driver);

		} else if (ConfirmationTextESIGN.equals("ConfirmationReturned")) {

			String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
			verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

			String ConfirmationTextEsign = Constants.RETURN_ACTION_VALUE;
			verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());

			TimeUtil.shortWait();
			saveUniqueCode(driver, confirmationText);

			String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
			String ConfirmationText = Constants.JOB_REGISTRATION_RETURN_CONFIRMATION_TEXT;

			getCurrentDate();
			verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

			switchToDefaultContent(driver);

		} else if (ConfirmationTextESIGN.equals("Confirmationafterreinitiatedapprove")) {

			String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
			verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

			String ConfirmationTextEsign = Constants.SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN;
			verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

			click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
					CommonStrings.Esign_Proceed_SS.getCommonStrings());

			TimeUtil.shortWait();
			saveUniqueCode(driver, confirmationText);

			String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
			String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;

			getCurrentDate();
			verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

			switchToDefaultContent(driver);
		}

	}
		
	
		
		
		
		// jobResponsibilty Registationandapprove With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltWith_UA_YES_AD_NO_afterapprovedAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation against SSO Registered User name and Employee name displayed at Job Responsibility Registration list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation against SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registration list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation against SSO Registered Department Name");
			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);
			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, " Approve Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS1);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}
		
		
		
		
		// jobResponsibilty RI transger reiniate approveal With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltylineofapprovalsUserAcceptanceWith_UA_YES_AD_NO(HashMap<String, String> testData,
				String UAStatus, String ApprovalStatus, String ConfirmationTextESIGN) {
			{

				clickAndWaitforNextElement(menu, systemManager, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
						CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
						CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
				clickAndWaitforNextElement(systemManager, userGroups, CommonStrings.SYS_Menus_DC.getCommonStrings(),
						CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
						CommonStrings.CM_Menus_SS.getCommonStrings());
				clickAndWaitforNextElement(userGroups, usergroupsAccept,
						CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
				scrollToViewElement(usergroupsAccept);
				clickAndWaitforNextElement(usergroupsAccept, acceptJobresponsiblity,
						JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_DC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsAcceptMenu_SS.getJobResponsibilityStrings());
				click2(acceptJobresponsiblity,
						JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_DC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsjobresacceptApproveMenu_SS.getJobResponsibilityStrings());
				switchToBodyFrame(driver);
				click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
						JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
						CommonStrings.SearchBy_SS.getCommonStrings());
				click2(searchByEmployeeNameOption,
						SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
						CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
						SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
				sendKeys2(employeeName, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
						RegEmployeeName,
						// RegEmployeeName,
						// + testData.get("percentageSign"),
						// "sweFWFRR.sweLWFRR",
						CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
						SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
				TimeUtil.mediumWait();

				click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
						CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
						CommonStrings.ApplyButton_SS.getCommonStrings());

				TimeUtil.mediumWait();

				verifyExactCaption(displayedRecord, RegEmployeeName,
						"Validation against SSO Registered User name and Employee name displayed at Job Responsibility Registration list screen");
				verifyExactCaption(empID, RegEmployeeID,
						"Validation against SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registration list screen");

				String[] parts = InitiatorEmployeeID.split("\\(");
				String result = parts[0];
				System.out.println(result);
				// verifyExactCaption(auditTrailPageColumn3, result, "Initiated By");
				validateMultipleDateFormats(auditTrailPageColumn4);
				click2(displayedRecord,
						JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC
								.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS
								.getJobResponsibilityStrings());

				highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
				verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
				verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
				verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
						"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

				verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

				// switchToBodyFrame(driver);
				// ✅ Status-based method execution using switch-case
				switch (UAStatus.trim()) {

				case "ApprovedDetails":
					jobResponsibilty_UA_YES_AD_NO_afterinitiatedApprovedEvents(testData);
					break;

				case "UserAcceptanceReturnDetails":
					jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserReturnedEvents(testData);
					break;

				case "UserAcceptanceSSOUserApprovedDetails":
					jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserafterApprovedEvents(testData);
					break;

				default:
					System.out.println("Unrecognized status: " + UAStatus);
					break;
				}

				scrollToViewElement(approvetext);
				if (ApprovalStatus.equals("Approved")) {
					click2(approveRadioBtn, CommonStrings.Select_Approve_DC.getCommonStrings(),
							CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
							CommonStrings.Select_Approve_SS.getCommonStrings());
					approveremarks.clear();
					sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ApproveRemarks"),
							CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
							CommonStrings.Remarks_SS.getCommonStrings());
				}

				else if (ApprovalStatus.equals("Returned")) {

					click2(returnRadioBtn, "Select decision as 'Return'", CommonStrings.Selction_AC.getCommonStrings(),
							CommonStrings.Selction_AR.getCommonStrings(),
							CommonStrings.Select_Approve_SS.getCommonStrings());
					approveremarks.clear();
					sendKeys2(approveremarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ReturnRemarks"),
							CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
							CommonStrings.Remarks_SS.getCommonStrings());

				}
				scrollToViewElement(submitBtn1);
				click2(submitBtn1, CommonStrings.Submit_Button_DC.getCommonStrings(),
						JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
				getUserNameFromEsgin();
				UserAcceptanceApproverNameInitiatorEmpID = EmpNameEmpID;
				UserAcceptanceApproverEmployeeID = EmpID;
				UserAcceptanceApproverName = Empname;

				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQApprovalPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();

				if (ConfirmationTextESIGN.equals("UserConfirmationApproved")) {

					String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
					verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

					String ConfirmationTextEsign = Constants.APPROVAL_ACTION_VALUE;
					verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

					click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
							JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
							JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
							CommonStrings.Esign_Proceed_SS.getCommonStrings());

					TimeUtil.shortWait();
					saveUniqueCode(driver, confirmationText);

					String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
					String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;

					getCurrentDate();
					verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

					switchToDefaultContent(driver);

				} else if (ConfirmationTextESIGN.equals("UserConfirmationReturned")) {

					String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
					verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

					String ConfirmationTextEsign = Constants.RETURN_ACTION_VALUE;
					verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

					click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
							JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
							JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
							CommonStrings.Esign_Proceed_SS.getCommonStrings());

					TimeUtil.shortWait();
					saveUniqueCode(driver, confirmationText);

					String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
					String ConfirmationText = Constants.RETURN_CONFIRMATION_TEXT;

					getCurrentDate();
					verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

					switchToDefaultContent(driver);
				}

//				String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
//				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");
//				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//						JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
//						JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
//						CommonStrings.Esign_Proceed_SS.getCommonStrings());
//				TimeUtil.shortWait();
//				saveUniqueCode(driver, confirmationText);
//				String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
//				String ConfirmationText = Constants.REGISTRATION_APPROVE_CONFIRMATION_TEXT;
//				getCurrentDate();
//				verifyConfirmationText(confirmationText, MenuName, ConfirmationText);
//				switchToDefaultContent(driver);

			}

		}	
	
		
		
		
		
		
		//jobResponsibilty Registationandapprove With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltWith_UA_YES_AD_NO_afterapprovedUserAccepatanceAuditTrails(
				HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();

			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);
			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.REVISION_NO_AS_0;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, " Approve Remarks");

			verifyExactCaption(auditUACompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditUACompareApproveTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Approved By");

			validateMultipleDateFormats(auditUACompareApproveTRNDateTimeValue);
			verifyExactCaption(auditUACompareApproveTRNRemarksVal1, Constants.approveremarks, " Approve Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS2);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}
	
		
		
		
		
		
		
		
		
		// jobResponsibilty Return With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltyRegandAppWith_UA_YES_AD_NO_afterreturnedAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}
		
		
		
		
		
		
		
		public void JR_RI_Transfer(HashMap<String, String> testData, String RIStatus, String ConfirmationTextESIGN) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			click2(RITransferMenu, JobResponsibilityStrings.SYS_UserGroupsApproveMenu_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsApproveMenu_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsApproveMenu_SS.getJobResponsibilityStrings());
			click2(riTransferjobresponsibility,
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveMenu_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobres_ApproveMenu_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameField,
					JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
					// + testData.get("percentageSign"),
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.RETURN_ACTIONVAL;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			TimeUtil.shortWait();
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());
			TimeUtil.mediumWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			// ✅ Status-based method execution using switch-case
			switch (RIStatus.trim()) {
			case "ReturnDetails":
				jobResponsibilyWith_UA_YES_AD_NO_RITransferEventDetails(testData);
				break;

			default:
				System.out.println("Unrecognized status: " + RIStatus);
				break;
			}

			scrollToViewElement(AddItem);
			click2(AddItem, "Click on Add Item", "", "", "");
			sendKeys2(user, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
					testData.get("TransferToEmployee"),
					// RegEmployeeName,
					// + testData.get("percentageSign"),
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			click2(UserRbtn, "Select required employee", "", "", "");
			click2(addBtn, "Click on Add Button", "", "", "");
			sendKeys2(approveRemarks, JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(),
					testData.get("TransferredRemarks"),
					// RegEmployeeName,
					// + testData.get("percentageSign"),
					// "VijayFTPIZ.VijayLTPIZ",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());

			scrollToViewElement(submit);
			click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
					JobResponsibilityStrings.Submitjobapp_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Submitjobapp_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Submit_Button_SS.getJobResponsibilityStrings());
			sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), ConfigsReader.getPropValue("EpicUserPWD"),
					CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
					CommonStrings.Password_SS.getCommonStrings());
			TimeUtil.shortWait();
//			clickAndWaitforNextElement(proceed, confirmationText, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
//					JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
//					JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
//					CommonStrings.Esign_Proceed_SS.getCommonStrings());
//			switchToDefaultContent(driver);

			if (ConfirmationTextESIGN.equals("RItranasfeered")) {

				String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_RI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());

				TimeUtil.shortWait();
				saveUniqueCode(driver, confirmationText);

				String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
				String ConfirmationText = Constants.RITRANSFER_SUBGROUP_CONFIRMATION_TEXT;

				getCurrentDate();
				verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

				switchToDefaultContent(driver);

			} else if (ConfirmationTextESIGN.equals("UserConfirmationtranasfeeredRI")) {

				String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				String ConfirmationTextEsign = Constants.RETURN_ACTIONVAL;
				verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());

				TimeUtil.shortWait();
				saveUniqueCode(driver, confirmationText);

				String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
				String ConfirmationText = Constants.JOB_REGISTRATION_RETURN_CONFIRMATION_TEXT;

				getCurrentDate();
				verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

				switchToDefaultContent(driver);
			}
		}

		
		
	//	jobResponsibilty reiniate RI With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltyRegandAppWith_UA_YES_AD_NO_afterRITransferAuditTrails(
				HashMap<String, String> testData) {
			{

				click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
						CommonStrings.LearnIQ_ICON_AC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AR.getCommonStrings(),
						CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
						CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
						CommonStrings.CM_Menus_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
						CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
				scrollToViewElement(auditTrailsMenu);
				click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
						CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
						CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
						CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
				click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
				switchToBodyFrame(driver);
				TimeUtil.shortWait();
				click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
						JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
						CommonStrings.SearchBy_SS.getCommonStrings());
				click2(searchByEmployeeNameOption,
						SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
						CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
						SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
				sendKeys2(auditEmployeeNameLike,
						JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(), RegEmployeeName,
						// "sweFLBVP.sweLLBVP",
						CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
						JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
				click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
						CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
						CommonStrings.ApplyButton_SS.getCommonStrings());
				TimeUtil.shortWait();
				verifyExactCaption(displayedRecord, RegEmployeeName,
						"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(empID, RegEmployeeID,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

				String[] parts = InitiatorEmployeeID.split("\\(");
				String result = parts[0];
				System.out.println(result);

				// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
				validateMultipleDateFormats(auditTrailPageColumn5);

				String revisionNO = Constants.REVISION_NO_AS_0;
				verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
				TimeUtil.shortWait();

				click2(displayedRecord,
						JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
						JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());

				TimeUtil.shortWait();
				driver.switchTo().frame(0);
				
				
				
				click2(reiniatetasktransferregistration,
						SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());

				click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
						SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
				
				
				
				
				
				
				// driver.switchTo().frame(0);
				TimeUtil.shortWait();
				highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
				verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
				verifyExactCaption(revisionNoValueCompareTRN, Constants.TRANSWERREVISION_NUM_0REG, "0 - Registration");
				verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
						"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

				verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
				verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
						"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

				verifyExactCaption(ritransferusernamestatus, Constants.FINALSTATUS_RI_TRANSFRRED, "Remarks and Reasons");
				verifyExactCaption(ritransferremarks, Constants.RITRANSFERED_ACTIONVAL, "Remarks and Reasons");
				validateMultipleDateFormats(ritransferinitiateddate);

				switchToDefaultContent(driver);
			}

		}
	
	
		
		
		
		
		
		
		
		public void reinitiateJobResponsibility(HashMap<String, String> testData0, HashMap<String, String> testData,
				String RenitatedStatus, String ConfirmationTextESIGN) {
			TimeUtil.shortWait();
			String s = "";
			String s1 = "";
			prop = ConfigsReader.readProperties("./configs/configuration.properties");
			if (prop.getProperty("EnableAppendRandomValue").equalsIgnoreCase("YES")) {
				s = TextUtils.randomvalue(4);
				s1 = TextUtils.randomAlphaNumeric(4);
				System.out.println("Generated S Value is: " + s);

			}

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

			click2(reInitiateMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());

			click2(reInitiateJobResMenu, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			switchToBodyFrame(driver);
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameField,
					JobResponsibilityStrings.Like_EmployeeNameapp_DC.getJobResponsibilityStrings(), RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			click2(displayBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();

//			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
//					JobResponsibilityStrings.SearchBy_JobResponsibility_AC.getJobResponsibilityStrings(),
//					JobResponsibilityStrings.SearchBy_JobResponsibility_AR.getJobResponsibilityStrings(),
//					CommonStrings.SearchBy_SS.getCommonStrings());
	//
//			click2(searchByEmployeeIDOption,
//					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
//					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
//					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
	//
//			sendKeys2(employeeID, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
//					"sweEWFRR",
//					// RegEmployeeID,
//					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
//			TimeUtil.shortWait();
////			TimeUtil.longwait();
////			TimeUtil.longwait();
	//
//			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
//					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
			// CommonStrings.ApplyButton_SS.getCommonStrings());

	//
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);


			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.RETURN_ACTIONVAL;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			TimeUtil.shortWait();

			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval1_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SYS_UserGroupsjobresApproveclickemployyee_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibilityApproval_SS.getJobResponsibilityStrings());

			verifyExactValueInFeild(EmpNameValue, RegEmployeeName, "");
			verifyExactValueInFeild(EmpIDValue, RegEmployeeID, "");
			verifyExactValueInFeild(DesValue, SSO_UserRegistration.designationValue, "");
			verifyExactValueInFeild(Dept_Value, SSO_UserProductModuleAssignment.getDepartmentName(), "");

			verifyExactCaption(jobResponsibilty, testData0.get("JobRes"),
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(qualification, testData0.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(previousExperience, testData0.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(externalCertfAtReg, QueryDocName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(LineApproverstReg, testData0.get("SubGroupNameValue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			setQueryDocumentName(QueryDocName = testData.get("QueryDocumentName") + s);
			waitForElementVisibile(dateofJoining);
			click2(dateofJoining, JobResponsibilityStrings.Click_DateofJoining_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Click_DateofJoining_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Click_DateofJoining_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Click_DateofJoining_SS.getJobResponsibilityStrings());
			selectCurrentdate();
			sendKeys2(jobResponsibilty, JobResponsibilityStrings.Job_Responsibility_DC.getJobResponsibilityStrings(),
					testData.get("JobRes"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.Job_Responsibility_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			sendKeys2(qualification, JobResponsibilityStrings.Qualification_DC.getJobResponsibilityStrings(),
					testData.get("Qualificationvalue"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.Qualification_SS.getJobResponsibilityStrings());
			TimeUtil.mediumWait();
			sendKeys2(previousExperience, JobResponsibilityStrings.PreviousExperience_DC.getJobResponsibilityStrings(),
					testData.get("PreviousExperienceval"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.PreviousExperience_SS.getJobResponsibilityStrings());
			click2(deselelctDoc, "Deselect Selected Document", "", "", "");
			clickAndWaitforNextElement(addExternalCertificates, uploadFile,
					JobResponsibilityStrings.ClickExternaladditem_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaladditem_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaladditem_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaladditem_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(uploadFile, DocumentDescription,
					JobResponsibilityStrings.ClickExternaluploadfile_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaluploadfile_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaluploadfile_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
			sendKeys2(DocumentDescription, JobResponsibilityStrings.ExternalDescription_DC.getJobResponsibilityStrings(),
					QueryDocName, JobResponsibilityStrings.ExternalDescription_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ExternalDescription_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ExternalDescription_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			sendKeys2(Keyword, JobResponsibilityStrings.KeywordDescription_DC.getJobResponsibilityStrings(),
					testData.get("Keyworddescription"),
					JobResponsibilityStrings.KeywordDescription_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.KeywordDescription_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.KeywordDescription_SS.getJobResponsibilityStrings());
			scrollToViewElement(chooseFile);
			TimeUtil.mediumWait();
			FileUploadWithSendKeys(externalDocumentUpload, ConfigsReader.getPropValue("PDFUpload10Pages"));
			TimeUtil.mediumWait();
			click2(chooseFileUpload, JobResponsibilityStrings.uploadocument_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.uploadocument_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.uploadocument_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.uploadocument_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			saveExternalCode(driver, docconfirmationText);
			clickAndWaitforNextElement(okdoc, searchfile, JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.clickok_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(searchfile, searchbydrop,
					JobResponsibilityStrings.clickok_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.clickok_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.clickok_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickExternaluploadfile_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(searchbydrop, selectdocumentcode,
					JobResponsibilityStrings.ExternalCertificatesdropdown_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ExternalCertificatesdropdown_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ExternalCertificatesdropdown_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ExternalCertificatesdropdown_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(selectdocumentcode, documentfindtext,
					JobResponsibilityStrings.SelectDocumentCode_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SelectDocumentCode_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SelectDocumentCode_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SelectDocumentCode_SS.getJobResponsibilityStrings());
			sendKeys2(documentfindtext, JobResponsibilityStrings.Enter_DocumentCode_DC.getJobResponsibilityStrings(),
					ExternalCode, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.Enter_DocumentCode_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			click2(applydoc, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			clickAndWaitforNextElement(adddoc, adddocum,
					JobResponsibilityStrings.addDocumentCode_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocumentCode_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocumentCode_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocumentCode_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(adddocum, approverNew,
					JobResponsibilityStrings.addDocument_Code_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocument_Code_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocument_Code_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.addDocument_Code_SS.getJobResponsibilityStrings());

			TimeUtil.shortWait();
			scrollToViewElement(approverNew);
			clickAndWaitforNextElement(approverNew, approverSearchNew,
					JobResponsibilityStrings.ApproverGroupSelect_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ApproverGroupSelect_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ApproverGroupSelect_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ApproverGroupSelect_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			sendKeys2(approverSearchNew, JobResponsibilityStrings.Enter_ApproverGroup_DC.getJobResponsibilityStrings(),
					testData.get("SubGroupNameValue"), CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.Enter_ApproverGroup_SS.getJobResponsibilityStrings());
			clickAndWaitforNextElement(approverNewoption, modifyJobResRemarks,
					JobResponsibilityStrings.Click_ApproverGroup_DC.getJobResponsibilityStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					JobResponsibilityStrings.Click_ApproverGroup_SS.getJobResponsibilityStrings());

			// ✅ Status-based method execution using switch-case
			switch (RenitatedStatus.trim()) {

			case "RITransferDetails":
				jobResponsibilyWith_UA_YES_AD_NO_ReiniatedEventDetails(testData);
				break;

			case "UserAceptanceRITransferDetails":
				jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserRITransferEvents(testData);
				break;

			default:
				System.out.println("Unrecognized status: " + RenitatedStatus);
				break;
			}
			sendKeys2(modifyJobResRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("RegReInitRemarks"),
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					CommonStrings.Remarks_SS.getCommonStrings());
			scrollToViewElement(submitButton);
			// --- Submit Button Logic ---
			if (submitClickCount == 0) {
				try {
					WebElement submitBtn1 = driver.findElement(By.id("btnSubmit")); // Change locator as per your page
					if (submitBtn1.isDisplayed()) {
						submitBtn1.click();
						System.out.println("Clicked first submit button.");
					}
				} catch (NoSuchElementException e) {
					System.out.println("First submit button not found.");
				}
				submitClickCount++;
			} else if (submitClickCount == 1) {
				try {
					WebElement submitBtn2 = driver.findElement(By.id("btnSubmit")); // Change locator as per your page
					submitBtn2.click();
					System.out.println("Clicked second submit button.");
				} catch (NoSuchElementException e) {
					System.out.println("Second submit button not found.");
				}
				submitClickCount++;
			}
			click2(submitButton, JobResponsibilityStrings.Submit_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Submit_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Submit_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Submit_SS.getJobResponsibilityStrings());

			getUserNameFromEsgin();
			Re_Int_NameInitiatorEmpID = EmpNameEmpID;
			Re_Int_EmployeeID = EmpID;
			Re_Int_Name = Empname;

			sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), testData.get("TransaferToPSW"),
					CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
					CommonStrings.Password_SS.getCommonStrings());
			TimeUtil.shortWait();

			if (ConfirmationTextESIGN.equals("UserConfirmationReinitiated")) {

				String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_REINITI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

//				String ConfirmationTextEsign = Constants.APPROVE_ACTIONVAL;
//				verifyExactCaption(confirmationText2, ConfirmationTextEsign, "ConfirmationTextAtEsign");

				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());

				TimeUtil.shortWait();
				saveUniqueCode(driver, confirmationText);

				String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
				String ConfirmationText = Constants.REINITIATION_CONFIRMATION_TEXT;

				getCurrentDate();
				verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

				switchToDefaultContent(driver);

			} else if (ConfirmationTextESIGN.equals("UserUserConfirmationReinitiated")) {

				String ConfirmationTextAtEsign = Constants.JOBRESPONSIBILITY_REINITI_CONFIRMATION_TEXT_ESIGN;
				verifyExactCaption(confirmationText1, ConfirmationTextAtEsign, "ConfirmationTextAtEsign");

				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AC.getJobResponsibilityStrings(),
						JobResponsibilityStrings.Esign_JobProceedapp_AR.getJobResponsibilityStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());

				TimeUtil.shortWait();
				saveUniqueCode(driver, confirmationText);

				String MenuName = Constants.MENUNAME_AS_JOBRESPONSIBILITY;
				String ConfirmationText = Constants.REINITIATION_CONFIRMATION_TEXT;

				getCurrentDate();
				verifyConfirmationText(confirmationText, MenuName, ConfirmationText);

				switchToDefaultContent(driver);
			}

		}


	
		
		
	//	jobResponsibilty Return reiniate With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltyRegandAppWith_UA_YES_AD_NO_afterreiniatedAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			TimeUtil.shortWait();
			driver.switchTo().frame(0);
			click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			TimeUtil.longwait();
			click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			// driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}
	
		
		
		
		
		
	//	jobResponsibilty Return reiniate approve With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibiltyRegandAppWith_UA_YES_AD_NO_afterapprovedcompleteAuditTrails(
				HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.REVISION_NO_AS_0;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			TimeUtil.longwait();
			click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			// driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS1);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}

		
		
		
		
		
		
		// jobResponsibilty user acceptance return audit trails

		public void jobResponsibilyWith_UA_YES_AD_NO_returnedUserAcceptanceAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.REVISION_NO_AS_0;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			TimeUtil.longwait();
			click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			// driver.switchTo().frame(0);
			TimeUtil.shortWait();

			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReturnTRNActionByValue);
			verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Returned By");

			validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
			verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}
	
		
		
		
		// jobResponsibilty user acceptance return audit trails

		public void jobResponsibilyWith_UA_YES_AD_NO_reiniatedUserAcceptanceAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.REVISION_NO_AS_0;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			TimeUtil.longwait();
			click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			// driver.switchTo().frame(0);
			TimeUtil.shortWait();

			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReturnTRNActionByValue);
			verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Returned By");

			validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
			verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditUACompareReinitTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReinitTRNActionByValue);
			verifyExactCaption(auditUACompareReinitTRNActionByValue, Re_Int_NameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareReinitTRNDateTimeValue);
			verifyExactCaption(auditUACompareReinitTRNRemarksVal1, Constants.Reiniateremarks, "Returned Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();
			switchToDefaultContent(driver);
		}

		
		
		
		
		
		
		// jobResponsibilty user acceptance approve audit trails

		public void jobResponsibilyWith_UA_YES_AD_NO_ApprovedUserAcceptanceAuditTrails(HashMap<String, String> testData) {

			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			TimeUtil.shortWait();
			click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
					CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
			scrollToViewElement(auditTrailsMenu);
			click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
					CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
			click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			switchToBodyFrame(driver);
			TimeUtil.shortWait();
			click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
					CommonStrings.SearchBy_SS.getCommonStrings());
			click2(searchByEmployeeNameOption,
					SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
					CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
					SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
			sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
					RegEmployeeName,
					// "sweFWFRR.sweLWFRR",
					CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
					JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
			click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());
			TimeUtil.shortWait();
			verifyExactCaption(displayedRecord, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(empID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			String[] parts = InitiatorEmployeeID.split("\\(");
			String result = parts[0];
			System.out.println(result);

			// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
			validateMultipleDateFormats(auditTrailPageColumn5);

			String revisionNO = Constants.REVISION_NO_AS_0;
			verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
			click2(displayedRecord,
					JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
					JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
			driver.switchTo().frame(0);
			click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			TimeUtil.longwait();
			click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
					SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
			// driver.switchTo().frame(0);
			TimeUtil.shortWait();
			highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
			verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
			verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
			verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
					"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

			verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
					"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
			scrollToViewElement(revisionNoTitleCompareTRN);
			scrollToViewElement(auditCompareTRNLineOfApprovers);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReturnTRNActionByValue);
			verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Returned By");

			validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
			verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditUACompareReinitTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReinitTRNActionByValue);
			verifyExactCaption(auditUACompareReinitTRNActionByValue, Re_Int_NameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareReinitTRNDateTimeValue);
			verifyExactCaption(auditUACompareReinitTRNRemarksVal1, Constants.Reiniateremarks, "Returned Remarks");

			verifyExactCaption(auditUACompareappTRNActionValue, Constants.APPROVE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareappTRNActionByValue);
			verifyExactCaption(auditUACompareappTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareappTRNDateTimeValue);
			verifyExactCaption(auditUACompareappTRNRemarksVal1, Constants.approveremarks, "Returned Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS1);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
			click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
					JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
					CommonStrings.Close_Icon_SS.getCommonStrings());
			TimeUtil.shortWait();

			switchToDefaultContent(driver);
		}

		public void jobResponsibilty_UA_YES_AD_NO_afterinitiatedEvents(HashMap<String, String> testData) {
			// TimeUtil.longwait();
			scrollToViewElement(auditCompareTRNActionValue);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Remarks");
			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,
					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_INITIATED);

		}
		
		
		public void jobResponsibilyWith_UA_YES_AD_NO_ReiniatedApprovedEventDetails(HashMap<String, String> testData) {

			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS_0);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

		}
		
		public void jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserApprovedEvents(HashMap<String, String> testData)

		{
			scrollToViewElement(auditCompareTRNActionValue);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReturnTRNActionByValue);
			verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Returned By");

			validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
			verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditUACompareReinitTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReinitTRNActionByValue);
			verifyExactCaption(auditUACompareReinitTRNActionByValue, Re_Int_NameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareReinitTRNDateTimeValue);
			verifyExactCaption(auditUACompareReinitTRNRemarksVal1, Constants.Reiniateremarks, "Returned Remarks");

			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_REINITIATED);

			TimeUtil.shortWait();

		}
		
		
		
		
		
		// jobResponsibilty Registationandapprove With_UA_YES_AD_NO_AuditTrails

		public void jobResponsibilty_UA_YES_AD_NO_afterinitiatedApprovedEvents(HashMap<String, String> testData) {

			scrollToViewElement(auditCompareTRNActionValue);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");
			scrollToViewElement(auditCompareApproveTRNActionValue);
			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approved");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, " Approve Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS1);
			scrollToViewElement(auditCompareTRNFinalStatus);

			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		}
		
		
		
		
		
		
		public void jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserReturnedEvents(HashMap<String, String> testData) {

			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

					auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
					Constants.NOOFAPPROVALS_COMPLETED_AS1);
			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

		}

		
		
		
		
		
		
		
		
		public void jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserafterApprovedEvents(
				HashMap<String, String> testData)

		{
			scrollToViewElement(auditCompareTRNActionValue);
			verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

			verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");



			validateMultipleDateFormats(auditCompareTRNDateTimeValue);
			verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

			verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditCompareReturnTRNActionByValue);
			verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
			verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
			scrollToViewElement(auditCompareReinitateTRNActionValue);
			verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

			validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
			verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

			verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

			verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

			scrollToViewElement(auditCompareApproveTRNDateTimeValue);
			validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
			verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

			verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReturnTRNActionByValue);
			verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
					"Returned By");

			validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
			verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

			verifyExactCaption(auditUACompareReinitTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareReinitTRNActionByValue);
			verifyExactCaption(auditUACompareReinitTRNActionByValue, Re_Int_NameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareReinitTRNDateTimeValue);
			verifyExactCaption(auditUACompareReinitTRNRemarksVal1, Constants.Reiniateremarks, "Returned Remarks");

			verifyExactCaption(auditUACompareappTRNActionValue, Constants.APPROVE_ACTIONVAL, "Returned");
			scrollToViewElement(auditUACompareappTRNActionByValue);
			verifyExactCaption(auditUACompareappTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

			validateMultipleDateFormats(auditUACompareappTRNDateTimeValue);
			verifyExactCaption(auditUACompareappTRNRemarksVal1, Constants.approveremarks, "Returned Remarks");

			verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);

			TimeUtil.shortWait();

		}

	

		
		
		
		
		
public void jobResponsibilyWith_UA_YES_AD_NO_RITransferEventDetails(HashMap<String, String> testData) {
	scrollToViewElement(auditCompareTRNActionValue);
	verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

	verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

	validateMultipleDateFormats(auditCompareTRNDateTimeValue);
	verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

	verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
	scrollToViewElement(auditCompareReturnTRNActionByValue);
	verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

	validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
	verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");
	verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

			auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
			Constants.NOOFAPPROVALS_COMPLETED_AS_0);
	verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

}
	
		
		
		
public void jobResponsibilyWith_UA_YES_AD_NO_ReiniatedEventDetails(HashMap<String, String> testData) {

	{

		verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

		validateMultipleDateFormats(auditMainTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal1, Constants.VALID_TO, "Remarks");

		verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Initiated");

		verifyExactCaption(auditMainReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Initiated By");
		scrollToViewElement(auditMainReturnTRNDateTimeValue);

		validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
		verifyExactCaption(auditMainTRNRemarksVal2, Constants.RETURN_ACTIONVAL, "Remarks");

		verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

	}

}
		
public void jobResponsibilyWith_UA_YES_AD_NO_UseracceptanceSSOuserRITransferEvents(HashMap<String, String> testData)

{
	scrollToViewElement(auditMainTRNActionValue);
	verifyExactCaption(auditMainTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

	verifyExactCaption(auditMainTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

	validateMultipleDateFormats(auditMainTRNDateTimeValue);
	verifyExactCaption(auditMainTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

	verifyExactCaption(auditMainReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
	scrollToViewElement(auditMainReturnTRNActionByValue);
	verifyExactCaption(auditMainReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

	validateMultipleDateFormats(auditMainReturnTRNDateTimeValue);
	verifyExactCaption(auditMainReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

	verifyExactCaption(auditMainReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
	scrollToViewElement(auditMainReinitateTRNActionValue);
	verifyExactCaption(auditMainReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

	validateMultipleDateFormats(auditMainReinitateTRNDateTimeValue);
	verifyExactCaption(auditMainReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

	verifyExactCaption(auditMainApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

	verifyExactCaption(auditMainApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

	scrollToViewElement(auditMainApproveTRNDateTimeValue);
	validateMultipleDateFormats(auditMainApproveTRNDateTimeValue);
	verifyExactCaption(auditMainApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

	verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
	scrollToViewElement(auditUACompareReturnTRNActionByValue);
	verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
			"Returned By");

	validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
	verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

	verifyAuditTrailsCompareTRNFinalStatus(auditMainTRNFinalStatus, Constants.FINALSTATUS_RETURNED);

	TimeUtil.shortWait();

}		
		
//jobResponsibilty user acceptance SSO user approve audit trails

	public void jobResponsibilyWith_UA_YES_AD_NO_SSoUserAcceptanceApproveAuditTrails(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(systemManager, CommonStrings.SYS_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(userGroups, CommonStrings.SYS_UserGroupsMenu_DC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AC.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_AR.getCommonStrings(),
				CommonStrings.SYS_UserGroupsMenu_SS.getCommonStrings());
		scrollToViewElement(auditTrailsMenu);
		click2(auditTrailsMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		click2(jobResAuditTrails, JobResponsibilityStrings.JobResponsibilityDC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.SearchBy_JobResponsibilityApproval_AR.getJobResponsibilityStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByEmployeeNameOption,
				SubgroupAssignmentStrings.Select_EmployeeName_DC.getSubgroupAssignmentStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				SubgroupAssignmentStrings.EmployeeName_SS.getSubgroupAssignmentStrings());
		sendKeys2(auditEmployeeNameLike, JobResponsibilityStrings.Like_EmployeeName_DC.getJobResponsibilityStrings(),
				RegEmployeeName,
				// "sweFWFRR.sweLWFRR",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				JobResponsibilityStrings.EmployeeName_SS.getJobResponsibilityStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		verifyExactCaption(displayedRecord, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(empID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(Dept, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		String[] parts = InitiatorEmployeeID.split("\\(");
		String result = parts[0];
		System.out.println(result);

		// verifyExactCaption(auditTrailPageColumn4, result, "Initiated By");
		validateMultipleDateFormats(auditTrailPageColumn5);

		String revisionNO = Constants.REVISION_NO_AS_0;
		verifyExactCaption(auditTrailPageColumn6, revisionNO, "Revision No");
		click2(displayedRecord,
				JobResponsibilityStrings.Click_EmpName_For_JobResponsibility_DC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.ClickJobResponsibilityAudittrails_AR.getJobResponsibilityStrings(),
				JobResponsibilityStrings.JobResponsibilityAudittrails_SS.getJobResponsibilityStrings());
		driver.switchTo().frame(0);
		click2(reiniatetaskregistration, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		TimeUtil.longwait();
		click2(ritransferproceed, SubGroupStrings.Click_Subgroup_for_AuditTrails_DC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AC.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails1_AR.getSubGroupStrings(),
				SubGroupStrings.Click_Subgroup_for_AuditTrails_SS.getSubGroupStrings());
		// driver.switchTo().frame(0);
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		verifyExactCaption(revisionNoTitleCompareTRN, Constants.REVISIONNUM_TITLE, "Revision No.:");
		verifyExactCaption(revisionNoValueCompareTRN, Constants.REVISION_NUM_0REG, "0 - Registration");
		verifyExactCaption(auditCompareTRNEmployeeName, RegEmployeeName,
				"Validation agaist SSO Rgister User name and Employee name displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNEmployeeID, RegEmployeeID,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNDesignation, SSO_UserRegistration.designationValue,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");

		verifyExactCaption(auditCompareTRNDepartment, SSO_UserProductModuleAssignment.getDepartmentName(),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNRevisionNo, Constants.REVISION_NO_ZERO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNJobResp, testData.get("JobRes"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNReportingTo, Constants.CATEGORYTAG_NO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNAuthorizedDeputy, Constants.CATEGORYTAG_NO,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNQualification, testData.get("Qualificationvalue"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNPreviousExpe, testData.get("PreviousExperienceval"),
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNExternalCertificates, QueryDocName,
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		verifyExactCaption(auditCompareTRNLineOfApprovers, "Approvers Subgroup",
				"Validation agaist SSO Registered Employee ID and Employee ID displayed at Job Responsibility Registratioln list screen");
		scrollToViewElement(revisionNoTitleCompareTRN);
		scrollToViewElement(auditCompareTRNLineOfApprovers);
		verifyExactCaption(auditCompareTRNActionValue, Constants.INITIATE_ACTIONVAL, "Initiated");

		verifyExactCaption(auditCompareTRNActionByValue, IntiatorNameInitiatorEmpID, "Initiated By");

		validateMultipleDateFormats(auditCompareTRNDateTimeValue);
		verifyExactCaption(auditCompareTRNRemarksVal1, Constants.VALID_TO, "Initiated Remarks");

		verifyExactCaption(auditCompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
		scrollToViewElement(auditCompareReturnTRNActionByValue);
		verifyExactCaption(auditCompareReturnTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

		validateMultipleDateFormats(auditCompareReturnTRNDateTimeValue);
		verifyExactCaption(auditCompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

		verifyExactCaption(auditCompareReinitateTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Re-Initiated");
		scrollToViewElement(auditCompareReinitateTRNActionValue);
		verifyExactCaption(auditCompareReinitateTRNActionByValue, Re_Int_NameInitiatorEmpID, "Re-initiated By");

		validateMultipleDateFormats(auditCompareReinitateTRNDateTimeValue);
		verifyExactCaption(auditCompareReinitateTRNRemarksVal1, Constants.Reiniateremarks, "Reinitiated Remarks");

		verifyExactCaption(auditCompareApproveTRNActionValue, Constants.APPROVE_ACTIONVAL, "Approve");

		verifyExactCaption(auditCompareApproveTRNActionByValue, ApproverNameInitiatorEmpID, "Approved By");

		scrollToViewElement(auditCompareApproveTRNDateTimeValue);
		validateMultipleDateFormats(auditCompareApproveTRNDateTimeValue);
		verifyExactCaption(auditCompareApproveTRNRemarksVal1, Constants.approveremarks, "Approve Remarks");

		verifyExactCaption(auditUACompareReturnTRNActionValue, Constants.RETURN_ACTIONVAL, "Returned");
		scrollToViewElement(auditUACompareReturnTRNActionByValue);
		verifyExactCaption(auditUACompareReturnTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
				"Returned By");

		validateMultipleDateFormats(auditUACompareReturnTRNDateTimeValue);
		verifyExactCaption(auditUACompareReturnTRNRemarksVal1, Constants.RETURN_ACTIONVAL, "Returned Remarks");

		verifyExactCaption(auditUACompareReinitTRNActionValue, Constants.REINITIATE_ACTIONVAL, "Returned");
		scrollToViewElement(auditUACompareReinitTRNActionByValue);
		verifyExactCaption(auditUACompareReinitTRNActionByValue, Re_Int_NameInitiatorEmpID, "Returned By");

		validateMultipleDateFormats(auditUACompareReinitTRNDateTimeValue);
		verifyExactCaption(auditUACompareReinitTRNRemarksVal1, Constants.Reiniateremarks, "Returned Remarks");

		verifyExactCaption(auditUACompareappTRNActionValue, Constants.APPROVE_ACTIONVAL, "Returned");
		scrollToViewElement(auditUACompareappTRNActionByValue);
		verifyExactCaption(auditUACompareappTRNActionByValue, ApproverNameInitiatorEmpID, "Returned By");

		validateMultipleDateFormats(auditUACompareappTRNDateTimeValue);
		verifyExactCaption(auditUACompareappTRNRemarksVal1, Constants.approveremarks, "Returned Remarks");

		verifyExactCaption(auditUACompareuserappTRNActionValue, Constants.APPROVE_ACTIONVAL, "Returned");
		scrollToViewElement(auditUACompareuserappTRNActionByValue);
		verifyExactCaption(auditUACompareuserappTRNActionByValue, UserAcceptanceApproverNameInitiatorEmpID,
				"Returned By");

		validateMultipleDateFormats(auditUACompareuserappTRNDateTimeValue);
		verifyExactCaption(auditUACompareuserappTRNRemarksVal1, Constants.approveremarks, "Returned Remarks");

		verifyAuditTrailsCompareTRNApprovalsRequiredCompleted(auditCompareTRNApprovalReqVal,

				auditCompareTRNApprovalComVal, Constants.NOOFAPPROVALS_REQUIRED_AS_2,
				Constants.NOOFAPPROVALS_COMPLETED_AS2);
		verifyAuditTrailsCompareTRNFinalStatus(auditCompareTRNFinalStatus, Constants.FINALSTATUS_APPROVED);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AC.getJobResponsibilityStrings(),
				JobResponsibilityStrings.Close_AuditTrails_JR_AR.getJobResponsibilityStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}
		
		

	}