package com.Automation.Utils;

public class Constants {

	public static final String EXTENT_REPORT_PATH = System.getProperty("user.dir") + "/Extent-Test-Results.html";

	public static final String FILEPATH = System.getProperty("user.dir") + "\\Documents\\1.pdf";

	public static final String PRINTFILEPATH = "//calqa/NICHELON SELENIUM WORKS//PrintAttendaceReport/";

	public static final String htmlFILEPATH = System.getProperty("user.dir")
			+ "/test-output/custom-emailable-report.html";

	// NoofApprovals values

	public static final String NOOFAPPROVALS_REQUIRED_AS_0 = "0";

	public static final String NOOFAPPROVALS_COMPLETED_AS_0 = "0";
	

	public static final String NOOFAPPROVALS_REQUIRED_AS_1 = "1";
	public static final String NOOFAPPROVALS_REQUIRED_AS_3 = "3";
	public static final String NOOFAPPROVALS_COMPLETED_AS2 = "2";
	public static final String NOOFAPPROVALS_COMPLETED_AS1 = "1";

	public static final String NOOFAPPROVALS_REQUIRED_AS_2 = "2";
	public static final String NOOFAPPROVALS_COMPLETED_AS3 = "3";

	// Audit trail values

	public static final String INITIATE_ACTIONVAL = "Initiated";

	public static final String APPROVE_ACTIONVAL = "Approved";
	
	public static final String approveremarks = "approved";
	
	public static final String reinationapproveremarks = "ReInitiationApproved";
	
	

	public static final String RETURN_ACTIONVAL = "Returned";

	public static final String REINITIATE_ACTIONVAL = "Re-Initiated";
	
	
	public static final String Reiniateremarks = "ReInitiating";
	
	
	
	public static final String REINITIATETRS_ACTIONVAL = "Transferred Particulars";

	public static final String RITRANSFER_ACTIONVAL = "RI-Trasfered";
	
	
	public static final String RITRANSFERED_ACTIONVAL = "Transferrring";
	

	public static final String DROP_ACTIONVAL = "Dropped";

	public static final String STATUSCHANGE_ACTIVE = "Active";

	public static final String STATUSCHANGE_INACTIVE = "Inactive";

	public static final String FINALSTATUS_INITIATED = "Final Status : Initiated";
	
	public static final String TRANSWER_INITIATED = "Final Status : Transferred";

	public static final String FINALSTATUS_APPROVED = "Final Status : Approved";

	public static final String FINALSTATUS_RETURNED = "Final Status : Returned";

	public static final String FINALSTATUS_REINITIATED = "Final Status : Re-Initiated";

	public static final String FINALSTATUS_DROPPED = "Final Status : Dropped";

	public static final String FINALSTATUS_RI_TRANSFRRED = "Final Status : Transferred";

	// REVISION NUMBER VALUES

	public static final String REVISIONNUM_TITLE = "Revision No.:";

	public static final String REVISION_NUM_0REG = "0 - Registration";
	
	public static final String TRANSWERREVISION_NUM_0REG = "0 - Re-initiation Task Transfer (Reg.)";

	public static final String REVISION_NUM_0STC = "0 - Status Change";

	public static final String REVISION_NUM_1MOD = "1 - Modification";
	
	public static final String REVISION_NUM_1MODIFICATION = "1 - Modification";
  
	public static final String REVISION_NUM_1STC = "1 - StatusChange";

	public static final String REVISION_NUM_0REINITIATION_TASKTRANSFER = "0 - Re-initiation Task Transfer (Reg.)";
	public static final String MOD_REVISION_NUM_0REINITIATION_TASKTRANSFER = "1 - Re-initiation Task Transfer (Mod.)";
	

	public static final String REVISION_NUM_1_MOD_REINITIATION_TASKTRANSFER = "1 - Re-initiation Task Transfer (Mod.)";

	// CONFIRMATION MESSAGES

	public static final String CONFIGURE_CONFIRMATION_TEXT = "Configuration Registered";

	public static final String REGISTRATION_CONFIRMATION_TEXT = "Registration Initiated";
	
	
	public static final String MODIFICATION_CONFIRMATION_TEXT = "Change Request Initiated";
	

	public static final String REINITIATION_CONFIRMATION_TEXT = "Registration Re-initiated";
	
	
	
	
	
	
	public static final String MODIFICAION_REINITIATION_CONFIRMATION_TEXT = "Change Request Re-initiated";
	
	

	public static final String RITRANSFER_SUBGROUP_CONFIRMATION_TEXT = "Re-initiation Task Transferred";

	public static final String STATUSCHANGE_CONFIRMATION_TEXT = "Status Change Initiated";

	public static final String MODIFY_CONFIRMATION_TEXT = "Change Request Initiated";
	
	public static final String MODIFYFICATION_CONFIRMATION_TEXT = "Change Request Re-initiated";

	public static final String RITRANSFER_CONFIRMATION_TEXT = "Subgroup Re-initiation Task Transferred";
	
	public static final String GBRITRANSFER_CONFIRMATION_TEXT = "Re-initiation Task Transferred";

	public static final String RETURN_CONFIRMATION_TEXT = "Registration Request Returned For Re-initiation";
	
	
	
	public static final String RETURN_MODIFICATION_CONFIRMATION_TEXT = "Change Request Returned For Re-initiation";
	
	
	

	public static final String SUBGROUP_DROP_CONFIRMATION_TEXT = "Registration Dropped";
	
	

	public static final String MODIFICATION_RETURN_CONFIRMATION_TEXT = "Change Request Returned For Re-initiation";

	public static final String MODIFICATION_REINITIATION_CONFIRMATION_TEXT = "Change Request Re-initiated";

	public static final String MODIFICATION_APPROVE_CONFIRMATION_TEXT = "Change Request Approved";

	public static final String REGISTRATION_APPROVE_CONFIRMATION_TEXT = "Registration Approved";
	
	public static final String GROUP_STATUSCHANGEDROP_CONFIRMATION_TEXT = "Status Change Dropped";

	
	
	
	
	
	public static final String JOB_REGISTRATION_RETURN_CONFIRMATION_TEXT = "Registration Request Returned For Re-initiation";
	
	
	
	
	
	
	
	
	
	public static final String DROPSTATUSCHANGE_APPROVE_CONFIRMATION_TEXT = "Status Change Dropped";
	
	
	public static final String STATUSCHANGE_APPROVE_CONFIRMATION_TEXT = "Status Change Approved";

	public static final String ALREADY_INITIATED_CONFIRMATION_TEXT = "Registration Already Initiated";

	public static final String DROP_CONFIRMATION_TEXT = "Registration Dropped";

	public static final String MODIFICATION_DROP_CONFIRMATION_TEXT = "Change Request Dropped";

	public static final String DOCUMENTREADING_CONFIRMATION_TEXT = "Document Reading Status Updated";

	public static final String OJT_CONFIRMATION_TEXT = "On Job Training Completion Confirmed";

	public static final String RECORD_ATTENDANCE_CONFIRM_TEXT = "Batch Attendance Updated";

	public static final String RECORD_MARKS_CONFIRM_TEXT = "Record Marks Updated";

	// ERROR MESSAGES

	public static final String ENTERVALUE_ERROR_MSG = "Enter Value";

	public static final String SELECTVALUE_ERROR_MSG = "Select Value";

	public static final String SPECIALCHARACTER_ERROR_MSG = "Enter Alpha-Numeric Input Only";

	public static final String NODATAAVAILABLE_TEXT = "No data available in table";

	public static final String SPECIALCHARACTER_TEXT = "@#$%";

	public static final String PERCENTAGE_SIGN = "%";

	public static final String RECORDS_PER_PAGE = "999";

	public static final String UNIQUECODE_CONSTANT = "NPLT";

	public static final String DOCUMENTUNIQUECODE_CONSTANT = "GLBADMP";

	public static final String UNIQUECODE_CONSTANT320 = "ADPT";

	public static final String RETAKEASSESSMENT_REQUIRED_AS_NA = "N/A";

	// MenuName

	public static final String MENUNAME_AS_COURSE = "Course";

	public static final String MENUNAME_AS_TRAINER = "Trainer";

	public static final String MENUNAME_AS_TOPIC = "Topic";

	public static final String MENUNAME_AS_DOCUMENTREGISTRATION = "Document Registration";

	public static final String MENUNAME_AS_TRAININGVENUE = "Training Venue";

	public static final String MENUNAME_AS_COURSESESSION = "Course Sessions";

	public static final String MENUNAME_AS_SELFNOMINATION = "Self-Nomination";

	public static final String Unscheduled_Session_Required = "Unscheduled Session Required";

	public static final String MENUNAME_AS_BATCHFORMATION = "Batch Formation";

	public static final String MENUNAME_AS_QUESTIONBANK = "Question Bank";

	public static final String MENUNAME_AS_SUBGROUP = "Subgroup";

	public static final String MENUNAME_AS_GROUP = "Group";

	public static final String MENUNAME_AS_SUBGROUPASSIGNMENT = "Subgroup Assignment";

	public static final String MENUNAME_AS_TRAININGSCHEDULE = "Training Schedule";

	public static final String MENUNAME_AS_COURSEINVITATION = "Course Invitation Updated";

	public static final String MENUNAME_AS_INTERIM_GTP = "Group Training Plan";

	public static final String MENUNAME_AS_QUESTIONPAPER = "Question Paper";

	public static final String MENUNAME_AS_EVALUATEANSWERKEY = "Answer Paper Evaluated";

	public static final String MENUNAME_AS_ROLE = "Role";

	public static final String MENUNAME_AS_USERREGISTRATION = "User Registration";

	public static final String MENUNAME_AS_DOCUMENTREADINGSTATUSUPDATED = "Respond Document Reading";

	public static final String MENUNAME_AS_USER_PRODUCT_MODULE = "User Product / Module Assignment";

	public static final String MENUNAME_AS_GLOBALPROFILE = "Global Profile";

	public static final String MENUNAME_AS_JOBRESPONSIBILITY = "Job Responsibility";

	public static final String MENUNAME_AS_RECORDMARKS = "Record Marks";

	// AuditTrails

	public static final String QUE_TYPE_AS_MULTIPLECHOICE = "Multiple Choice";

	public static final String QUE_TYPE_AS_FILLINTHEBLANK = "Fill In The Blanks";

	public static final String QUE_TYPE_AS_TRUEORFALSE = "True or False";

	public static final String QUE_TYPE_AS_ESSAY = "Essay";

	public static final String CATEGORYTAG_NO = "--";

	public static final String SUBJECTTAG_NO = "--";

	public static final String EVALUATOR_NO = "--";

	// Report Status

	public static final String EMPLOYEESTATUS_AS_QUALIFIED = "Qualified";

	public static final String EMPLOYEESTATUS_AS_TO_BE_RETRAINED = "To Be Re-trained";

	public static final String EMPLOYEESTATUS_AS_Not_100_QUALIFIED = "Not100%Qualified";

	public static final String EMPLOYEESTATUS_AS_TO_BE_RETRAINED_VERBAL = "To Be Re-trained (Verbal)";

	public static final String QUALIFIED_EVALUATION_NA = "ITQualifiedEvaluationNA";

	public static final String EMPLOYEESTATUS_AS_SKIPPED = "Skipped";

	public static final String EMPLOYEESTATUS_AS_ABSENT = "Absent for The Training Session";

	public static final String EMPLOYEESTATUS_AS_PRESENT = "Present";

	public static final String EMPLOYEESTATUS_AS_CIREJECT = "Course Invitation Rejected";

	public static final String EMPLOYEESTATUS_AS_CIACCEPT = "Course Invitation Accepted";

	public static final String EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL = "Course Session Under Approval";
	public static final String Session_PROPOSED_FOR_RE = "Session Proposed for Document Reading";
	public static final String DR_COMPLETED = "Document Reading Completed";

	public static final String EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED = "Course Session Proposed";

	public static final String EMPLOYEESTATUS_AS_SELFNOMINATED = "Self-Nominated";

	public static final String EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED = "Batch Proposed-Trainee Is Not Selected for The Batch";

	public static final String EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED = "Batch Proposed-Trainee Is Selected for The Batch";

	public static final String EMPLOYEESTATUS_AS_ATTENDANCE_RECORDED = "Attendance Recorded";

	public static final String EMPLOYEESTATUS_AS_RESPONSE_SUBMITTED = "Response Submitted";

	public static final String EMPLOYEESTATUS_AS_RESPONSE_NOT_YET_SUBMITTED = "Response Not Yet Submitted";

	public static final String EMPLOYEESTATUS_AS_PENDING_LIST = "Pending List";

	public static final String EMPLOYEESTATUS_AS_COMPLETED_LIST = "Completed List";

	// MDM

	public static final String ESIGN_TITLE_FORM_WORKFLOW_MAPPING_CODE_MOD = "Form Workflow Mapping - Modification Initiation";

	public static final String MENUNAME_AS_FORM_WORKFLOW = "Form Workflow Mapping";

	public static final String MENUNAME_AS_DEPARTMENT = "Department";

	public static final String OBJECT_AS_PLANTMAPPING = "Plant Mapping";

	public static final String MODIFICATION_TRANSACTION = "Modification Initiation";

	public static final String REGISTRATION_INTIATION = "Registration Initiation";

	public static final String REGISTRATION_APPROVAL = "Registration Approval";

	public static final String FORM_WRKFLOW_MAPCODE = "Form Workflow Mapping Code";

	public static final String DEPARTMENT_CODE_KEY = "Department Code";

	public static final String PLANT_MAPPING_CODE_KEY = "Plant Mapping Code";

	public static final String ESIGN_TITLE_DEPARTMENT_REGISTRATION = "Department - Registration Initiation";

	public static final String ESIGN_TITLE_COMMOMMASTERSMODIFY = "Plant Mapping - Modification Initiation";

	public static final String ESIGN_TITLE_DEPARTMENT_APPROVAL = "Department - Registration Approval";

	public static final String DEPT_REVISIONNUM_TITLE = "Revision No.";

	public static final String REVISION_NO_ZERO = "0";

	public static final String REG_TRANSACTION_TYPE = "Registration";

	public static final String STAGE_APPROLVAL = "Approval";

	// //Esign confirmation messages

	public static final String SUBGROUP_RI_CONFIRMATION_TEXT_ESIGN = "Subgroup: Registration Re-initiation Transfer";
	
	public static final String SUBGROUP_MOD_RI_CONFIRMATION_TEXT_ESIGN = "Subgroup: Modification Re-initiation Transfer";
	
	
	
	public static final String _MOD_Reinitiaion_RI_CONFIRMATION_TEXT_ESIGN = "Subgroup: Modification Re-initiation";
	
	
	
	
	

	public static final String DROP_SUBGROUP_CONFIRMATION_TEXT_ESIGN = "Subgroup: Registration Approval:";

	public static final String USER_REGISTRATION_RESET_PASSWORD_CONFIRMATION = "Changed Password Successfully";

	public static final String ROLE_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Role: Registration Initiation";

	public static final String TOPIC_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Topic: Registration Initiation";

	public static final String BATCHFORMATION_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Batch Formation: Registration Initiation";

	public static final String DOCUMENT_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Document Registration: Registration Initiation";

	public static final String SUBGROUP_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Subgroup: Registration Initiation";
	
	public static final String SUBGROUP_MODIFICATION_CONFIRMATION_TEXT_ESIGN = "Subgroup: Modification Initiation";
	

	public static final String SUBGROUP_REINITIATE_CONFIRMATION_TEXT_ESIGN = "Subgroup: Registration Re-initiation";
	
	
	public static final String SUBGROUP_MODIFICATION_REINITIATE_CONFIRMATION_TEXT_ESIGN = "Subgroup: Modification Re-initiation";
	
	

	public static final String SUBGROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Subgroup: Registration Approval:";
	
	
	public static final String SUBGROUP_Mod_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Subgroup: Modification Approval:";
	
	public static final String TOPIC_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Topic: Registration Approval:";

	public static final String JOBRESPONSIBILITY_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Job Responsibility: Registration Initiation";
	
	public static final String JOBRESPONSIBILITY_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Job Responsibility: Registration Approval:";
	
	
	
	
	public static final String JOBRESPONSIBILITY_RI_CONFIRMATION_TEXT_ESIGN = "Job Responsibility: Registration Re-initiation Transfer";
	
	public static final String JOBRESPONSIBILITY_REINITI_CONFIRMATION_TEXT_ESIGN = "Job Responsibility: Registration Re-initiation";
	


	

	
	public static final String DOCUMENTREGISTRATION_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Document Registration: Registration Approval:";

	public static final String SUBGROUP_APPROVAL1_CONFIRMATION_TEXT_ESIGN = "Approve";

	public static final String COURSE_REG_ESIGN_WINDOWTEXT = "Course: Registration Initiation";

	public static final String USER_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "User Registration: Registration Initiation";

	public static final String USER_PRODUCT_MODULE_CONFIRMATION_TEXT_ESIGN = "User Product / Module Assignment: Registration Initiation";

	public static final String GLOBAL_PROFILE_TEXT_ESIGN = "Global Profile: Registration Initiation";

	public static final String GROUP_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Groups: Registration Initiation";

	public static final String GROUP_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Groups: Registration Approval:";
	
	public static final String GROUP_APPROVAL_MODCONFIRMATION_TEXT_ESIGN = "Groups: Modification Approval:";
	
	
	public static final String GROUP_APPROVAL_STATUSCHANGE_TEXT_ESIGN = "Groups: Status Change Approval:";
	
	public static final String GROUP_TSRITRANSWER_CONFIRMATION_TEXT_ESIGN = "Groups: Registration Re-initiation Transfer";
	
	public static final String GROUP_TSRITRANSWERMOD_CONFIRMATION_TEXT_ESIGN = "Groups: Modification Re-initiation Transfer";

	public static final String APPROVAL_ACTION_VALUE = "Approve";
	
	public static final String RETUENL_ACTION_VALUE = "Return";
	
	public static final String TRANSWER_ACTION_VALUE = "Transfer";
	
	public static final String DROP_ACTION_VALUE = "Drop";

	public static final String COURSE_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Course: Registration Approval:";

	public static final String TRAINER_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Trainer: Registration Initiation";

	public static final String TRAINER_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Trainer: Registration Approval:";

	public static final String COURSESESSION_REG_ESIGN_WINDOWTEXT = "Course Sessions: Registration Initiation";

	public static final String COURSESESSION_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Course Sessions: Registration Approval:";

	public static final String RECORDMARKS_CONFIRMATION_TEXT_ESIGN = "Record Marks: Registration Initiation";

	public static final String REMARKSANDREASONS = "Remarks / Reasons";

	// Revision Numbers
	public static final String REVISION_NO_AS_1 = "1";
	
	public static final String REVISION_NO_AS_0 = "0";

	public static final String VALID_TO = "--";

	// Esign Title for Subgroup Assignment

	public static final String ESIGN_TITLE_SGP_ASSIGNMENT_REGISTRATION = "Subgroup Assignment: Registration Initiation";

	public static final String ESIGN_TITLE_SGP_ASSIGNMENT_REG_APPROVE = "Subgroup Assignment: Registration Approval:Approve";

	// E Sign Title for TSCH Registratiomn
	public static final String ESIGN_TITLE_TSCHE_REGISTRATION = "Training Schedule: Registration Initiation";

	// Configuration AuditTrails
	public static final String CONFIGURATION_VALUE_AS_CONFIGURED = "Configured";

	// Esignn Title for Training Schedule Approval
	public static final String ESIGN_TITLE_TSCH_REG_APPROVE = "Training Schedule: Registration Approval:";

	// E Sign Title for Quesiton Bank Registration

	public static final String ESIGN_TITLE_QB_REGISTRATION = "Question Bank: Registration Initiation";

	// Esignn Title for QB Approval
	public static final String ESIGN_TITLE_QB_REG_APPROVE = "Question Bank: Registration Approval:";

	public static final String RESPONDQUESTIONPAPER_REGISTRATION_CONFIRMATION_TEXT_ESIGN = "Respond To Question Paper: Registration Initiation";

	public static final String MENUNAME_AS_ASSESMENT = "Assessment";

	public static final String RECORD_ATTENDANCE_ESIGN = "Record Attendance Esign:";

	public static final String EVALUATORSTATUS = "Submitted";

	public static final String EVALUATION_RETRAINED = "To Be Re-Trained";

	public static final String RESULT_QUALIFIED = "Qualified";

	public static final String sessionInfoOffline = "Off-Line";
	public static final String sessionInfoOnline = "Online";
	public static final String REType = "Document Reading";
	public static final String ITType = "Internal Classroom";
	
	
//	Remarks and Reasons
	
	public static final String GROUPAPPROVEREMAKS = "Approve";
	
	public static final String GROUPDROPREMAKS = "Drop";
	
	public static final String GROUPREMARKS = "---";
	
	public static final String GROUPRetrunRemarks = "Return";
	
	public static final String GROUPReInitiateRemarks = "Re-Initiate";
	
	public static final String GROUPMODInitiateRemarks = "Modification";
	
	public static final String GROUPRTRSInitiateRemarks = "RITranswer";

	public static final String nodataavilable = "No data available in table";
	
	public static final String ModifyRemarks = "Modified";
	
	public static final String statusRemarks = "statusChange";
	
	
    public static final String EventInitiatedDetails = "InitiatedDetails";
	
	public static final String EventApprovedDetails = "ApprovedDetails";
	
	
	
	public static final String CompleteEventApprovedDetails = "CompleteApprovedDetails";

	public static final String EventReinitiationDetails= "ReinitiationDetails";
	
	public static final String EVENTSRITRANSFERDDDETAILS = "RITransferDetails";
	
	public static final String EVENTRETURNDETAILS = "ReturnDetails";
	
	
	 public static final String UserAccepatnceapprovedDetails = "UAapprovedDetails";
	 
	 public static final String USERACCEPTRETURNDETAILS ="UserAcceptanceReturnDetails";
	 
	 public static final String USERACCEPTRITRANSFERDETAILS  ="UserAceptanceRITransferDetails";
	 public static final String USERACCEPTAPPROVEDETAILS =" UserAcceptanceCompleteApprovedDetails";
	 public static final String USERACCEPTSSOAPPROVEDETAILS =" UserAcceptanceSSOUserApprovedDetails"; 


	
	
	 public static final String TEXTCONFIRMATIONAPPROVED ="ConfirmationApproved";
	 
	 public static final String TEXTCONFIRMATIONRETURNED ="ConfirmationReturned";
	 
	 public static final String TEXTCONFIRMATIONAFTERREINITAPPROVED ="Confirmationafterreinitiatedapprove";
	 
	 
	 public static final String RITRANSFERCONFIRMTXT="RItranasfeered";
	 
	 
 public static final String TEXTCONFIRMATIONREINIATED ="UserConfirmationReinitiated";
	 
	 
	 public static final String USERTEXTCONFIRMATIONREINIATED ="UserUserConfirmationReinitiated";
	 
	
	
	 
	 
 public static final String USERACEEPTTEXTCONFIRMATIONAPPROVED ="UserConfirmationApproved";
	 
	 public static final String USERACCEPTTEXTCONFIRMATIONRETURNED ="UserConfirmationReturned";
	 
	 public static final String USERACCEPTTEXTCONFIRMATIONAFTERREINITAPPROVED ="UserConfirmationafterreinitiatedapprove";
	 
	 
	 
	 public static final String USERACCEPTTEXTCONFIRMATIONRITRANSFERRED ="UserConfirmationtranasfeeredRI";

	 
 public static final String TEXTCONFIRMATIONAFTERREINITATION ="ConfirmationReinitiation";
	 
	

	 
	
//	Swatha
	
	public static final String RETURN_ACTION_VALUE = "Return";
	public static final String STATUSCHANGE_SUBGROUP_CONFIRMATION_TEXT_ESIGN = "Subgroup: Status Change Initiation";

	public static final String INITIATESTATUSCHANGE = "status changed";
	public static final String SUBGROUP_STATUSCHANGE_APPROVAL_CONFIRMATION_TEXT_ESIGN = "Subgroup: Status Change Approval:";
	
	
}

