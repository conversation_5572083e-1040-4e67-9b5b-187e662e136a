package com.Automation.learniqObjects;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.EvaluateStrings;
import com.Automation.Strings.RecordMarksStrings;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_EvaluateAnswerPaper extends OQActionEngine {
	public static boolean isElemetPresent;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//a[contains(@class,'sub-menu')][contains(text(),'Evaluate')]")
	WebElement evaluate;
//	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Evaluate']//a[contains(text(),'Answer Paper')]")
//	WebElement answerPaper;
	
	
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Evaluate_MEN06']")
	WebElement answerPaper;
	
	
	
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//li[contains(text(),'Batch Name')]")
	WebElement searchByBatchName;

	@FindBy(xpath = "//table[@id='ListTab']//tbody//tr//td//a[@class='tneListClick']")
	WebElement toggle;

	@FindBy(xpath = "//table[@class='table']/tbody[1]/tr[1]/td[1]/a")

	WebElement toggle1;
	@FindBy(xpath = "//button[@id='btnNext']")
	WebElement next;
	@FindBy(xpath = "//span[text()='Qualified']")
	WebElement qualified;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//button[@id='btnSubmit' and @class='caliber-button-primary']")
	WebElement submit;
	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(id = "BatchName")
	WebElement batchNameLike;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "Result")
	WebElement Result;
	@FindBy(xpath = "//label[text()='Employee Name']//following-sibling::input")
	WebElement EmployeeName;
	@FindBy(xpath = "//label[text()='Course Name']//following-sibling::input")
	WebElement coursename;
	@FindBy(xpath = "//label[text()='Training Type']//following-sibling::input")
	WebElement trainingtype;
	@FindBy(xpath = "//label[text()='Course Session Name']//following-sibling::input")
	WebElement coursesessionname;
	@FindBy(xpath = "//label[text()='Batch Name']//following-sibling::input")
	WebElement batchname;
	@FindBy(xpath = "//label[text()='Course Session Date']//following-sibling::input")
	WebElement coursessiondate;
	@FindBy(xpath = "//label[text()='Maximum Marks']//following-sibling::input")
	WebElement maximummarks;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement VerifyCourseName;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement VerifyTrainingType;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement VerifyBatchName;
	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[4]")
	WebElement InitiatedOn;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[3]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/span[1]/table[1]/tbody[1]/tr[1]/td[1]")
	WebElement VerifyEmpName;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[3]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/span[1]/table[1]/tbody[1]/tr[1]/td[2]")
	WebElement Verifydepartment;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[3]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/span[1]/table[1]/tbody[1]/tr[1]/td[3]")
	WebElement VerifyStatus;
	@FindBy(xpath = "//label[text()='Qualifying Mark']//following-sibling::input")
	WebElement qualifyingmark;
	@FindBy(xpath = "//label[text()='Qualifying Percentage']//following-sibling::input")
	WebElement qualifyingpercentage;
	@FindBy(xpath = "//label[text()='Acquired Marks']//following-sibling::input")
	WebElement acciquiredmarks;
	@FindBy(xpath = "//label[text()='Acquired Percentage']//following-sibling::input")
	WebElement acciquiredpercentage;
	@FindBy(id = "PageNo")
	WebElement pageNo;
	@FindBy(xpath = "//span[@id='Result']")
	WebElement result;

	public boolean isNullOrEmpty(String value) {
		return value == null || value.trim().isEmpty();
	}

	public static void checkIsElemetPresent(WebElement element) {

		try {
			isElemetPresent = element.isDisplayed();
		} catch (NoSuchElementException e) {
			isElemetPresent = false;
		}
	}

	public void manualEvaluation() {

		// String BatchName = "eva ans paper";
		String BatchName = CM_Course.getCourse();

		// Click through the necessary menus and search for the batch
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(evaluate, EvaluateStrings.Click_Evaluate_DC.getEvaluateStrings(),
				EvaluateStrings.Click_Evaluate_AC.getEvaluateStrings(),
				EvaluateStrings.Click_Evaluate_AR.getEvaluateStrings(),
				EvaluateStrings.Click_Evaluate_SS.getEvaluateStrings());
		click2(answerPaper, EvaluateStrings.Click_AnswerPaper_DC.getEvaluateStrings(),
				EvaluateStrings.Click_AnswerPaper_AC.getEvaluateStrings(),
				EvaluateStrings.Click_AnswerPaper_AR.getEvaluateStrings(),
				EvaluateStrings.Click_AnswerPaper_SS.getEvaluateStrings());
		switchToBodyFrame(driver);
		TimeUtil.mediumWait();

		// Search for the batch
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				EvaluateStrings.Search_By_AC.getEvaluateStrings(), EvaluateStrings.Search_By_AR.getEvaluateStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByBatchName, EvaluateStrings.SearchBy_BatchName_DC.getEvaluateStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				EvaluateStrings.SearchBy_BatchName_SS.getEvaluateStrings());
		sendKeys2(batchNameLike, EvaluateStrings.Enter_BatchName_DC.getEvaluateStrings(), BatchName + "%",
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordMarksStrings.Like_batchname_DC.getRecordMarksStrings());
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.longwait();
		TimeUtil.longwait();

		click2(toggle, EvaluateStrings.Click_Toggle_DC.getEvaluateStrings(),
				EvaluateStrings.Click_Toggle_AC.getEvaluateStrings(),
				EvaluateStrings.Click_Toggle_AR.getEvaluateStrings(),
				EvaluateStrings.Click_Toggle_SS.getEvaluateStrings());
		TimeUtil.mediumWait();

		while (true) {
			List<WebElement> EmployeeList = driver.findElements(By.xpath("//span//table//tbody//tr"));

			boolean foundSubmitted = false;

			for (WebElement employeeRow : EmployeeList) {
				try {
					// Fetch the status for the current employee
					WebElement status = employeeRow.findElement(By.xpath(".//td[3]"));
					String statusText = status.getText().trim();

					// Log the status for debugging
					System.out.println("Employee status: '" + statusText + "'");

					// Only proceed if the status is "Submitted"
					if ("Submitted".equalsIgnoreCase(statusText)
							|| "Question Paper Submitted for Attempt No: 1".equalsIgnoreCase(statusText)) {
						foundSubmitted = true;

						// Locate and click on the employee name
						WebElement employeeName = employeeRow.findElement(By.xpath(".//td[1]//a"));

						// Click on the employee name
						click2(employeeName, EvaluateStrings.Click_Employee_DC.getEvaluateStrings(),
								EvaluateStrings.Click_Employee_AC.getEvaluateStrings(),
								EvaluateStrings.Click_Employee_AR.getEvaluateStrings(),
								EvaluateStrings.Click_Employee_SS.getEvaluateStrings());
						TimeUtil.shortWait();

						// Proceed with further actions
						click2(next, EvaluateStrings.Click_Next_DC.getEvaluateStrings(),
								EvaluateStrings.Click_Next_AC.getEvaluateStrings(),
								EvaluateStrings.Click_Next_AR.getEvaluateStrings(),
								EvaluateStrings.Click_Next_SS.getEvaluateStrings());
						TimeUtil.mediumWait();
						scrollToViewElement(submit);
						highLightElement(driver, result, "Configuration screen", test);
						waitForElementVisibile(submit);
						click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
								EvaluateStrings.Click_Submit_AC.getEvaluateStrings(),
								EvaluateStrings.Click_Submit_AR.getEvaluateStrings(),
								EvaluateStrings.Submit_SS.getEvaluateStrings());
						waitForElementVisibile(confirmationDone);
						click2(confirmationDone, "Click on done button",
								"<div><b>* </b> Done button should be clicked."
										+ "<div><b>* </b>Evaluate Answer Paper screen should be displayed.",
								"Done button." + "<div><b>* </b>Evaluate Answer Paper screen is displayed.",
								"Done button");
						TimeUtil.shortWait();

						// Reset fields for the next employee
						sendKeys2(pageNo, EvaluateStrings.Enter_BatchName_DC.getEvaluateStrings(), "1",
								CommonStrings.sendKeys_AC.getCommonStrings(),
								CommonStrings.sendKeys_AR.getCommonStrings(),
								RecordMarksStrings.Like_batchname_DC.getRecordMarksStrings());
						sendKeys2(batchNameLike, EvaluateStrings.Enter_BatchName_DC.getEvaluateStrings(),
								BatchName + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
								CommonStrings.sendKeys_AR.getCommonStrings(),
								RecordMarksStrings.Like_batchname_DC.getRecordMarksStrings());
						click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
								CommonStrings.ApplyButton_AC.getCommonStrings(),
								CommonStrings.ApplyButton_AR.getCommonStrings(),
								CommonStrings.ApplyButton_SS.getCommonStrings());
						waitForElementVisibile(toggle);
						click2(toggle, EvaluateStrings.Click_Toggle_DC.getEvaluateStrings(),
								EvaluateStrings.Click_Toggle_AC.getEvaluateStrings(),
								EvaluateStrings.Click_Toggle_AR.getEvaluateStrings(),
								EvaluateStrings.Click_Toggle_SS.getEvaluateStrings());
						TimeUtil.mediumWait();

						break; // Break the for loop and re-check the employee list
					}
				} catch (NoSuchElementException e) {
					System.out.println("Element not found: " + e.getMessage());
				}
			}

			// If no submitted employee was found, break the while loop
			if (!foundSubmitted) {
				break;
			}
		}

		switchToDefaultContent(driver);
	}

}
