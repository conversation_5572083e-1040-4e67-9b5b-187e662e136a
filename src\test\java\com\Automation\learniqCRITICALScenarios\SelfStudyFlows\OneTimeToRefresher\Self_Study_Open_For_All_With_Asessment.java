package com.Automation.learniqCRITICALScenarios.SelfStudyFlows.OneTimeToRefresher;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_SelfStudyCourse;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Self_Study_Open_For_All_With_Asessment extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Self_Study_Flows/Self_Study_Open_For_All_With_Asessment.xlsx";

	public Self_Study_Open_For_All_With_Asessment() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);
	}

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		selfStudyCourse.Self_Study_Course_Registration_OpenForAll_Assessment_Yes(testData);
	}

	ExcelUtilUpdated SSQBData = new ExcelUtilUpdated(ExcelPath, "SSQB");

	@DataProvider(name = "SSQBData")
	public Object[][] getSSQBData() throws Exception {
		Object[][] obj = new Object[SSQBData.getRowCount()][1];
		for (int i = 1; i <= SSQBData.getRowCount(); i++) {
			HashMap<String, String> testData = SSQBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "SSQBData", enabled = true)
	public void SSQB(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		SSQB.selfStudyQuestioBank(testData);
		// SSQB.selfStudyQuestioBank_through_menus(testData);
		Logout.signOutPage();
	}

	@Test(priority = 4, enabled = true)
	public void Initiate_Self_Study_Course() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.QualifieduserID)

					.assignCategory("Initiate Self Study Course");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.QualifieduserID,
				CM_SelfStudyCourse.QualifiedPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}

	@Test(priority = 5, enabled = true)
	public void respondDocumentReading_Completed() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading and complete")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading and complete");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed", CM_SelfStudyCourse.QualifiedPSW);
	}

	@Test(priority = 6, enabled = true)
	public void respond_QP_Qualified() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond QP and Qualified");
		}

		RespondQP.respondSelfStudy_QP("Qualified", CM_SelfStudyCourse.QualifiedPSW);
		Logout.signOutPage();
	}

	@Test(priority = 7, enabled = true)
	public void Initiate_Self_Study_Course_By_TBRuser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)

					.assignCategory("Initiate Self Study Course");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.ToBeRetrainedUserID,
				CM_SelfStudyCourse.ToBeRetrainedPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}

	@Test(priority = 8, enabled = true)
	public void respondDocumentReading_TBR() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond Document Reading");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed", CM_SelfStudyCourse.ToBeRetrainedPSW);
	}

	@Test(priority = 9, enabled = true)
	public void respond_QP_ToBeRetrained() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QP and To Be Retrained")
					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)
					.assignCategory("Respond QP and To Be Retrained");
		}

		RespondQP.respondSelfStudy_QP("To Be Retrained", CM_SelfStudyCourse.ToBeRetrainedPSW);
		Logout.signOutPage();
	}

	@Test(priority = 10, enabled = true)
	public void Initiate_Self_Study_Course_By_RespondQPUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course for Respond QP Pending")

					.assignAuthor(CM_SelfStudyCourse.RQPPendingEmpID)

					.assignCategory("Initiate Self Study Course for Respond QP Pending");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.RQPPendingUserID,
				CM_SelfStudyCourse.RQPPendingPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}

	@Test(priority = 11, enabled = true)
	public void respondDocumentReading_RespondQPPendingUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading for Respond QP pending user")
					.assignAuthor(CM_SelfStudyCourse.RQPPendingEmpID)
					.assignCategory("Respond Document Reading for Respond QP pending user");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed", CM_SelfStudyCourse.RQPPendingPSW);
		Logout.signOutPage();
	}

	@Test(priority = 12, enabled = true)
	public void Initiate_Self_Study_Course_By_RespondDRPendingUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course for Respond DR Pending")

					.assignAuthor(CM_SelfStudyCourse.DRInProgressEmpID)

					.assignCategory("Initiate Self Study Course for Respond DR Pending");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
				CM_SelfStudyCourse.DRInProgressPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}

	@Test(priority = 13, enabled = true)
	public void IERReportForQualifieduser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Qualified Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Qualified Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.REType);
	}

	@Test(priority = 14, enabled = true)
	public void IERReportFor_TBR_Employee() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for To Be Retrained Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for To Be Retrained Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.ToBeRetrainedEmpID,
				Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType);

	}

	@Test(priority = 15, enabled = true)
	public void IERReportFor_Respond_QP_PendingUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond QP pending Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Respond QP pending Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.RQPPendingEmpID, Constants.DR_COMPLETED,
				Constants.REType);

	}

	@Test(priority = 16, enabled = true)
	public void IERReportFor_Respond_DR_PendingUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Respond DR pending Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Respond DR pending Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.DRInProgressEmpID, Constants.Session_PROPOSED_FOR_RE,
				Constants.REType);

		Logout.signOutPage();
	}

	@Test(priority = 17, enabled = true)
	public void modifyCourse_To_Refresher() {
		if (isReportedRequired == true) {
			test = extent.createTest("Modify Course by selecting course retraining")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Modify Course by selecting course retraining");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		selfStudyCourse.modifyCourse_To_Refrehser();
	}

	@Test(priority = 18, enabled = true)
	public void Edit_SSQB() {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		SSQB.Edit_selfStudyQuestioBank();
	}

	@Test(priority = 19, enabled = true)
	public void IERReportForQualifieduser_After_ModifyCourse() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Qualified Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Qualified Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.REType);
	}

	@Test(priority = 20, enabled = true)
	public void IERReportFor_TBR_Employee_After_ModifyCourse() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for To Be Retrained Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for To Be Retrained Employee");
		}

		IERReport.individualEmployeeReport(CM_SelfStudyCourse.ToBeRetrainedEmpID,
				Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType);
		Logout.signOutPage();
	}

	@Test(priority = 21, enabled = true)
	public void Initiate_Self_Study_Course_After_CourseName_Modification() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)

					.assignCategory("Initiate Self Study Course");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.ToBeRetrainedUserID,
				CM_SelfStudyCourse.ToBeRetrainedPSW);
		epiclogin.plant1();
		selfStudyCourse.validate_Initiate_Self_Study_Course_AftrCoursename_Modification();
		Logout.signOutPage();
	}

	@Test(priority = 22, enabled = true)
	public void check_CourseName_At_respond_QP_ToBeRetrained() {
		if (isReportedRequired == true) {
			test = extent.createTest("Check modified course name at Respond QP pending")
					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)
					.assignCategory("Check modified course name at Respond QP pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.RQPPendingUserID,
				CM_SelfStudyCourse.RQPPendingPSW);
		epiclogin.plant1();
		RespondQP.Check_CourseName_respondSelfStudy_QP();
		Logout.signOutPage();
	}

	@Test(priority = 23, enabled = true)
	public void check_CourseName_At_respond_DR_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Check modified course name at Respond DR pending")
					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)
					.assignCategory("Check modified course name at Respond DR pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
				CM_SelfStudyCourse.DRInProgressPSW);
		epiclogin.plant1();
		RespondDR.check_CourseName_At_respondDocReading();

	}

}
