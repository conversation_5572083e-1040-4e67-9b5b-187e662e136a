package com.Automation.Strings;

public enum DM_DocumentRegistration {
	
	//Document Manager
	
	Click_DM_Menu_DC("Click on 'Document Manager' menu."),
	Click_DM_Menu_AC("Assinged menus under 'Document Manager' should be displayed.'"),
	Click_DM_Menu_AR("Assinged menus under 'Document Manager' are getting displayed.'"),
	Click_DM_Menu_SS("'Document Manager'");
	
	//Initiate Menu
	
	
	private final String DocRegStrings;

	DM_DocumentRegistration(String DocRegStrings) {

		this.DocRegStrings = DocRegStrings;

	}

	public String getDocRegStrings() {
		return DocRegStrings;
	}

}
