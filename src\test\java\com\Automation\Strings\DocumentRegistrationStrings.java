package com.Automation.Strings;

public enum DocumentRegistrationStrings {
	
	// Document Registration Configuration Screen
	DocumentRegistration_Config_DC("Click on 'Subgroup' submenu."),
	DocumentRegistration_Config_AC("'Document Registration Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
				+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	DocumentRegistration_Config_AR("'Document Registration Configuration Registration' screen is getting displayed</div>"
				+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),
	DocumentRegistrationMenu_SS("'Document Registration' submenu"),
		
//Document Registration Sub Menu
	DM_ConfigAudit_DC("Click on 'Document Registration' submenu."),
	DM_ConfigAudit_AC("'Document Configuration Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	DM_ConfigAudit_AR("'Document  Configuration Registration' screen is getting displayed</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	DM_ConfigAudit_SS("'Document Registration' submenu"),

	// DocumentRegistration configuration Audit trails
	Click_DocumentRegistrationfor_ConfigAuditTrails_DC("Click on the above registered 'Document Name'."),
	Click_DocumentRegistrationfor_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>"
			+ "<div><b>*</b>" + " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_DocumentRegistrationfor_ConfigAuditTrails_AR("'Transactions' screen is getting.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_DocumentRegistrationfor_ConfigAuditTrails_SS("'Document Configuration' - Audit Trails."),

	// DocumentRegistration configuration proceed
	Click_DocumentRegistrationConfig_Proceed_AC(
			"'Document Registration Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Document Registration Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_DocumentRegistrationConfig_Proceed_AR(
			"'Document Registration Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Document Registration Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_DocumentRegistrationConfig_Proceed_SS("'Document Registration Configuration - Audit Trails'."),

	Close_DocumentRegistrationConfigAuditTrails_Group_AC(
			"'Document Registration Configuration Audit Trails' screen should be displayed.</div>"),
	Close_DocumentRegistrationConfigAuditTrails_Group_AR(
			"'Document Registration Configuration Audit Trails' screen is getting displayed.</div>"),
//click document registration
	DocumentRegistrationScreen_DC("Click on 'Document Registration' submenu."),
	DocumentRegistrationScreen_AC("‘Document Registration - Registration Initiation’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Document Name, Unique Code, Version No, Effective From, Next Review, Description and Document Location’ fields.</div>"
			+ "<div><b>*</b> The screen should contain ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Document’ should be displayed (if any).</div>"),
	DocumentRegistrationScreen_AR("‘Document Registration - Registration Initiation’ screen is getting displayed.</div>"
			+ "<div><b>*</b>The screen is getting displayed with‘Document Name, Unique Code, Version No, Effective From, Next Review, Description and Document Location’ fields.</div>"
			+ "<div><b>*</b> The screen contains ‘View Existing’, and ‘Submit’ buttons.</div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Document’ is getting  displayed (if any).</div>"),
	DocumentRegistrationScreen_SS("'Document Registration'"),

	// Enter Document name
	DocumentName_DC("Enter the value less than or equals to 200 characters in 'Document Name' field."),
	DocumentName_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	DocumentName_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	DocumentName_SS("'Document Name'"),
	// Enter Document unique code
	DocumentNameUniqueCode_DC("Enter the value less than or equals to 25 characters in 'Unique Code' field."),
	DocumentNameUniqueCode_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	DocumentNameUniqueCode_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	DocumentNameCode_SS("'Unique Code'"),

	// Enter VersionNo
	VersionNo_DC("Enter a value less than or equal to 5 characters for ‘Version No.’ field."),
	VersionNo_SS("'Version No.'"),
//click effective from
	Click_EffectiveFrom_DC("Click on  ‘Effective From’ field icon."),

	Click_EffectiveFrom_AC("Calender should be displayed</div>"
			+ "<div><b>*</b> By default current date should be highlighted in blue colour.</div>"),
	Click_EffectiveFrom_AR("Calender is getting displayed</div>"
			+ "<div><b>*</b> By default current date is gettingd highlighted in blue colour.</div>"),
	Click_EffectiveFrom_SS("'Effective From'"),
//select Effective From
	Select_EffectiveFrom_DC("Select required date for ‘Effective From’ field."),
	Select_EffectiveFrom_AC("Selected date should be displayed for the 'Effective From' field."),
	Select_EffectiveFrom_AR("Selected date is getting displayed for the 'Effective From'field."),
	Select_EffectiveFrom_SS("'Effective From'"), Click_NextReviewDate_DC("Click on  ‘Next Review Date’ field icon."),
//Click NextReviewDate
	Click_NextReviewDate_AC("Calender should be displayed</div>"
			+ "<div><b>*</b> By default Future date should be highlighted in blue colour.</div>"),
	Click_NextReviewDate_AR("Calender is getting displayed</div>"
			+ "<div><b>*</b> By default Future date is gettingd highlighted in blue colour.</div>"),
	Click_NextReviewDate_SS("'Next Review'"),
//Select NextReviewDate
	Select_NextReviewDate_DC("Select the ‘Next Review’ date greater than the ‘Effective From’ Date."),
	Select_NextReviewDate_AC("Selected date should be displayed for the 'Next Review Date'."),
	Select_NextReviewDate_AR("Selected date is getting displayed for the 'Next Review Date'."),
	Select_NextReviewDate_SS("'Next Review'"),

	// Enter Description
	DocumentDescription_DC("Enter the value less than or equals to 250 characters in  'Description' field."),
	DocumentDescription_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	DocumentDescription_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	DocumentDescription_SS("'Description'"),

	ADDDocumentCategorytags_DC("Click ‘Add’ button present against ‘Document Category Tag(s)’ field."),
	ADDDocumentCategorytags_AC(
			"A text box, highlighted in blue colour should be displayed with an option to create a ‘Document Category Tag(s)’.</div>"),

	ADDDocumentCategorytags_AR(
			"A text box, highlighted in blue colour is getting displayed with an option to create a ‘Document Category Tag(s)’.</div>"),

	ADDDocumentCategorytags_SS("'Add'"),

	// Enter Description
	DocumentCategorytags_DC("Enter a value less than or equal to 25 characters for ‘Document Category Tag(s)’ field."),
	DocumentCategorytags_AC("Entered data should be accepted.</div>"
			+ "<div><b>*</b>Just created Document Category Tag should be displayed for ‘Document Category Tag(s)’ field.</div>"
			+ "<div><b>*</b> Note: While entering the value for Document Category Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available will be listed in the drop-down (if any).</div>"),
	DocumentCategorytags_AR("Entered data is getting  accepted.</div>"
			+ "<div><b>*</b>Created Document Category Tag is displayed for ‘Document Category Tag(s)’ field.</div>"
			+ "<div><b>*</b> Note: While entered  value for Document Category Tag(s) field, similar categories (Plant1/ Master plant related) that are already registered/ available are listed in the drop-down (if any).</div>"),
	DocumentCategorytags_SS("'Document Category Tag(s)'"),
	// submit
	Submit_DC("Click on 'Submit' button."),
	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Document Registration: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Document Registration: Registration Initiation'.</div>"),
	Submit_SS("'E-Sign window'"),

	// esign proceed at Document registration
	Esign_DocumentRegistrationProceed_AC(
			"'Document Registration Registration Initiated Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_DocumentRegistrationProceed_AR(
			"'Document Registration Registration Initiated Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),

	click_DocumentRegistrationAuditTrails_DC("Click on 'Document Registration' submenu."),
	click_DocumentRegistrationAuditTrails_SS("'Document Registration Audit Trials'"),

	click_DocumentRegistrationAuditTrails_AC("‘Document Registration Audit Trails’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen should contain ‘Document Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On and Revision No.’ columns.</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ should be available.</div>"
			+ "<div><b>*</b>The screen should contain a list of all the registered Documents at Master Plant</div>"
			+ "<div><b>*</b> Documents that are registered in  Plant1 should not be displayed (if any).</div>"),

	click_DocumentRegistrationAuditTrails_AR("‘Document Registration Audit Trails’ screen is getting  displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen is getting displayed with ‘Document Name’, ‘Unique Code’, ‘Initiated By’, ‘Initiated On and Revision No.’ columns.</div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ are available.</div>"
			+ "<div><b>*</b>The screen contains  list of all the registered Documents at Master Plant.</div>"
			+ "<div><b>*</b> Documents that are registered in Plant1 are displayed (if any).</div>"),

	// search by document name
	SearchBy_DN_DC("Click on 'Search By' dropdown."),
	SearchBy_DN_AC(
			"Option to search with 'Top 250 Records, Document Name, Unique Code, Initiated Between' should be diplayed.<div>"),
	SearchBy_DN_AR(
			"Option to search with 'Top 250 Records, Document Name, Unique Code, Initiated Between' is getting diplayed.<div>"),
	SearchBy_DN_SS("'Search By'."), SearchBy_UniqueCode_DC("Select 'Unique Code'."),

	SearchBy_UniqueCode_SS("'Unique Code'."),
	Like_UniqueCode_DC("Enter the above registered 'Unique Code' in 'Like' text box."),
	Like_UniqueCode_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	Like_UniqueCode_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	Like_UniqueCode_SS("Unique Code"),

	// click on above registered DocumentName
	DocumentName_for_AuditTrails_DC("Click on the above registered 'Document Name'."),
	// click Document audit trails
	DocumentName_for_AuditTrails_SS("'Document Registration Audit Trails'"),
	DocumentName_for_AuditTrails_AC(
			"‘Document Registration- Audit Trails - Revision No.: 0 -Registration’ screen should be displayed.<div>"
					+ "<div><b>*</b>The screen should contain the details entered/ selected during registration.</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain only the ‘Initiated’ transaction’s with ‘Username, Date & Time and Remark(s) / Reason(s)’ details.</div>"
					+ "<div><b>*</b> Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘0’.</div>"),

	DocumentName_for_AuditTrails_AR(
			"‘Document Registration- Audit Trails - Revision No.: 0 -Registration’ screen is getting displayed.<div>"
					+ "<div><b>*</b>The screen contains the details entered/ selected during registration.</div>"
					+ "<div><b>*</b>‘Final Status’ is getting  displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b> The ‘Events’ section contains only the ‘Initiated’ transaction’s with ‘Username, Date & Time and Remark(s) / Reason(s)’ details.</div>"
					+ "<div><b>*</b> Also, the ‘No. of Approvals Required’ are read as ‘1’ and the ‘No. of Approvals Completed’ are read as ‘0’.</div>"),
	Close_AuditTrailsDM_AC("'Document Registration Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsDM_AR("'Document Registration Audit trails' screen is getting displayed.</div>"),
	Close_AuditTrailsDM_SS("'Close.'"),
//document approve screen
	DocumentNameApproveScreen_DC("Click on 'Document Registration' submenu."),
	DocumentNameApproveScreen_AC("‘Document Registration Registration Approval Tasks’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Registration, Modification and Status Change’ tabs.</div>"
			+ "<div><b>*</b> By default, the ‘Registration’ tab details should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen should contain ‘Document Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ should be available.</div>"
			+ "<div><b>*</b> All the Documents whose registration request is to be approved by the current login user should be listed and available for approval..</div>"
			+ "<div><b>*</b> Documents that are registered in plant1 should not be displayed (if any).</div>"),

	DocumentNameApproveScreen_AR("‘Document Registration Registration Approval Tasks’ screen is getting displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Registration, Modification and Status Change’ tabs.</div>"
			+ "<div><b>*</b> By default, the ‘Registration’ tab details is displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>*</b>The screen contains ‘Document Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns.</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ are available.</div>"
			+ "<div><b>*</b> All the Documents whose registration request is to be approved by the current login user are listed and available for approval..</div>"
			+ "<div><b>*</b> Documents that are registered in plant1 are not getting displayed (if any).</div>"),

	DocumentNameApproveScreen_SS("'Document Registration Approval Tasks'"),

	ClickDocumentName_for_AuditTrails_AC("‘Document Registration Registration Approval’ screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain the details of the Document entered during registration.<div>"
			+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Initiated’.<div>"
			+ "<div><b>*</b>The ‘Events’ section should contain the ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details only.<div>"
			+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as 1 and the ‘No. of Approvals Completed’ should be read as 0.<div>"
			+ "<div><b>*</b>‘Approve, Return & Drop’ options should be available for ‘Decision’ field.<div>"
			+ "<div><b>*</b>The option to enter/ select the ‘Remark(s)/ Reason(s)’ should be available.<div>"),

	ClickDocumentName_for_AuditTrails_AR("‘Document Registration Registration Approval’ screen is getting displayed.<div>"
			+ "<div><b>*</b>The screen contains the details of the Document entered during registration.<div>"
			+ "<div><b>*</b>‘Final Status’ is getting displayed as ‘Initiated’.<div>"
			+ "<div><b>*</b>The ‘Events’ section contains the ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details only.<div>"
			+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ are read as 1 and the ‘No. of Approvals Completed’ are read as 0.<div>"
			+ "<div><b>*</b>‘Approve, Return & Drop’ options is available for ‘Decision’ field.<div>"
			+ "<div><b>*</b>The option to enter/ select the ‘Remark(s)/ Reason(s)’ are available.<div>"),

	// submit at Document approval
	SubmitDocumentapproval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Document Registration: Registration Approval: Approve'.</div>"),
	SubmitDocumentapproval_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Document Registration: Registration Approval: Approve'.</div>"),

	// esign proceed at Document approval
	Esign_DocumentProceedapproval_AC(
			"'Document Registration Registration Approved' Unique Code: (Unique Code) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_DocumentProceedapproval_AR(
			"'Document Registration Registration Approved' Unique Code: (Unique Code) confirmation message is getting displayed with 'Done' button.</div>"),
	DocumentAudittrailsMenu1_DC("Click on 'Document Registration' submenu."),
	DocumentAudittrailsMenu1_SS("'Document Registration Audit Trials'"),

	DocumentAudittrailsMenu1_AC("‘Document Registration Audit Trails’ screen should be displayed.<div>"
			+ "<div><b>*</b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen should contain Document Name, Unique Code, Initiated By, Initiated On and Revision No.’ columns’.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ should be available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’ should be displayed as ‘20’.<div>"),
	DocumentAudittrailsMenu1_AR("‘Document Registration Audit Trails’ screen is getting displayed.."
			+ "<div><b>*</b>The screen contains ‘Total Records Count’, ‘Advanced Search’ and ‘Search this page’ icons.<div>"
			+ "<div><b>*</b>The screen is getting displayed with Document Name, Unique Code, Initiated By, Initiated On and Revision No.’columns.<div>"
			+ "<div><b>*</b>The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ are available.<div>"
			+ "<div><b>*</b>By default, ‘Records Per Page’ is getting displayed as ‘20’.<div>"),

	// click on above registered Document Registration
	DocumentRegistration_for_AuditTrails_DC("Click on the above registered 'Document Registration'."),
	// click Document Registration audit trails
	DocumentRegistration_for_AuditTrails_SS("'Document Registration Audit Trails'"),
	DocumentRegistrationMenu_DC("Click on 'Document Registration' submenu."),
	DocumentRegistration_for_AuditTrails_AC(
			"‘Document Registration - Audit Trails Revision No.: 0 -Registration’ screen should be displayed.<div>"
					+ "<div><b>*</b>The screen should contain the details entered during registration.</div>"
					+ "<div><b>*</b>‘Final Status’ should be displayed as ‘Approved’.</div>"
					+ "<div><b>*</b> The ‘Events’ section should contain Initiated and Approved transaction’s with ‘Username, Date & Time and Remarks / Reasons’ details..</div>"
					+ "<div><b>*</b> Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both should be read as 1.</div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),
	DocumentRegistration_for_AuditTrails_AR(
			"‘Document Registration - Audit Trails Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen contains the details entered during registration.</div>"
					+ "<div><b>*</b>‘Final Status’ is getting displayed as ‘Initiated’.</div>"
					+ "<div><b>*</b> The ‘Events’ section  contains registration ‘Initiated’ transaction with ‘Username, Date & Time and Remarks / Reasons’ details.</div>"
					+ "<div><b>*</b>  Also, the ‘No. of Approvals Required’ and ‘No. of Approvals completed’ both are read as 1.</div>"
					+ "<div><b>*</b>All the particulars is getting displayed in read only format.</div>"),
	Close_AuditTrailsDocumentRegistration_AC("'Document Registration Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsDocumentRegistration_AR("'Document Registration Audit trails' screen is getting displayed.</div>"),
	
	
	DocumentNameApproveScreen1_AC("‘Document Registration Registration Approval Tasks’ screen should be displayed.</div>"
			+ "<div><b>*</b>The screen should contain ‘Registration, Modification and Status Change’ tabs.</div>"
			+ "<div><b>*</b> By default, the ‘Registration’ tab details should be displayed..</div>"
			+ "<div><b>*</b>The screen should contain‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen should contain ‘Document Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns. .</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ should be available..</div>"
			+ "<div><b>*</b> All the Documents whose registration request is to be approved by the current login user should be listed and available for approval.</div>"
			+ "<div><b>*</b> Documents that are registered in Master should not be displayed (if any).</div>"),

	DocumentNameApproveScreen1_AR("‘Document Registration Registration Approval Tasks’ screen is getting displayed.</div>"
			+ "<div><b>*</b>The screen contains ‘Registration, Modification and Status Change’ tabs.</div>"
			+ "<div><b>*</b> By default, the ‘Registration’ tab details is displayed..</div>"
			+ "<div><b>*</b>The screen contains ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons..</div>"
			+ "<div><b>*</b>The screen contains ‘Document Name’, ‘Unique Code’, ‘Initiated By’ and ‘Initiated On.’ columns. .</div>"
			+ "<div><b>*</b> The option to search with ‘Top 250 Records, Document Name, Unique Code and Initiated Between’ are available..</div>"
			+ "<div><b>*</b> All the Documents whose registration request is to be approved by the current login user are listed and available for approval.</div>"
			+ "<div><b>*</b> Documents that are registered in Master are not getting displayed (if any).</div>"),	

	Choosefiledocument_DC("Select <PDF/ .MP4 document which is less than or equal to 100 MB> in Document Location"),
	Choosefiledocument_AC("Selected Document should be displayed for the 'Document Location.</div>"),

	Choosefiledocument_AR("Selected Document is getting displayed for the 'Document Location.</div>"),

	Choosefiledocument_SS("'Document Location'"),
	
	;
	
	private final String DocumentRegistrationStrings;

	DocumentRegistrationStrings(String DocumentRegistrationStrings) {

		this.DocumentRegistrationStrings = DocumentRegistrationStrings;

	}

	public String getDocumentRegistrationStrings() {
		return DocumentRegistrationStrings;
	}

}
