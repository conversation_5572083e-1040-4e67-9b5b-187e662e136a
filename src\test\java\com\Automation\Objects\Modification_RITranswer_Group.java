package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;

public class Modification_RITranswer_Group extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	public Modification_RITranswer_Group() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration")
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Registration With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Group Registration With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "course")
	public Object[][] getGroupReturnData() throws Exception {
		Object[][] obj = new Object[topicData2.getRowCount()][1];
		for (int i = 1; i <= topicData2.getRowCount(); i++) {
			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "course")
	public void groupModification(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify Group Modification");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.groupModification(testdata);
		group_Modification.groupModificationWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData5 = new ExcelUtilUpdated(ExcelPath, "GroupReturn");

	@DataProvider(name = "groupReturn")
	public Object[][] getGroupReturnnData() throws Exception {
		Object[][] obj = new Object[topicData5.getRowCount()][1];
		for (int i = 1; i <= topicData5.getRowCount(); i++) {
			HashMap<String, String> testData = topicData5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 4, dataProvider = "groupReturn")
	public void groupRegistrationReturn(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Return With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Return With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_Returns(testData);
		group_Modification.modificationgroup_ReturnWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData6 = new ExcelUtilUpdated(ExcelPath, "Sheet8");

	@DataProvider(name = "riTranswer")
	public Object[][] getGroupRItranswerData() throws Exception {
		Object[][] obj = new Object[topicData6.getRowCount()][1];
		for (int i = 1; i <= topicData6.getRowCount(); i++) {
			HashMap<String, String> testData = topicData6.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Topic Configuration & Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 5, dataProvider = "riTranswer")
	public void groupRITranswer(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Return With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Return With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		group_Modification.modificationgroupRITranswaer(testData);
		group_Modification.modificationgroupRITranswaerWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "Course");

	@DataProvider(name = "groupReinitiate")
	public Object[][] getGroupReInitiateData() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Re-Initiate
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 6, dataProvider = "groupReinitiate")
	public void groupRegistrationReInitiate(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Re-Initiate With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Group Re-Initiate With AuditTrails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupTRSID"),
				ConfigsReader.getPropValue("GroupTRSPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroupRITRInitiate(testData);
		group_Modification.modificationgroupRIReInitiateWithAuditTrails_Yes(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData4 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration1Approval");

	@DataProvider(name = "groupRegistration1Approval")
	public Object[][] getGroupApproveData() throws Exception {
		Object[][] obj = new Object[topicData4.getRowCount()][1];
		for (int i = 1; i <= topicData4.getRowCount(); i++) {
			HashMap<String, String> testData = topicData4.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 8, dataProvider = "groupRegistration1Approval")
	public void groupRegistrationApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Group Approvals With AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("Group Approvals With AuditTrails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		group_Modification.modificationgroup_TranswerApproval(testData);
		group_Modification.modificationgroup_TranswerApprovalWithAuditTrials_Yes(testData);
		Logout.signOutPage();
	}
}
