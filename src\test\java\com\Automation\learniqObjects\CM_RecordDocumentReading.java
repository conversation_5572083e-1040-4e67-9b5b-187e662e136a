package com.Automation.learniqObjects;

import java.util.ArrayList;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.learniqBase.OQActionEngine;
import com.Automation.Strings.*;
import com.Automation.Utils.*;

public class CM_RecordDocumentReading extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[7]")
	WebElement recordMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Record']/li[3]")
	WebElement recordDoc;
	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Session Name')]")
	WebElement searchByCourseSessionNameDropdown;
	@FindBy(id = "CrsSesName")
	WebElement courseSessionName;
	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(id = "SelectAllEmp")
	WebElement selectAllEmp;
	@FindBy(xpath = "//input[@id='SelectAllEmp']")
	WebElement selectAllEmp1;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone1;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Course Session Name')]")
	WebElement searchByCourseSessionnameDropdown;
	@FindBy(id = "CourseSessionName")
	WebElement courseSessionNameLike;
	@FindBy(xpath = "//input[@id='CrsSesName']")
	WebElement courseSessionNameLike1;
	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;
	@FindBy(xpath = "//tbody/tr/td[text()='No data available in table']")
	WebElement noDataAvailable;
	
	public static int ActualQualifiedCount;

	/**
	 * This method is for Record Document Reading
	 * 
	 * 
	 */

	public void RecordDocumentReading() {

		String CourseNameValue = CM_Course.getCourse();
		click2(menu, PageTitles.LearnIQ_ICON_DC.getTitle(), PageTitles.LearnIQ_ICON_AC.getTitle(),
				PageTitles.LearnIQ_ICON_AR.getTitle(), PageTitles.LearnIQ_ICON_SS.getTitle());
		TimeUtil.shortWait();
		click2(courseManagermenu, PageTitles.CM_Menus_DC.getTitle(), PageTitles.CM_Menus_AC.getTitle(),
				PageTitles.CM_Menus_AR.getTitle(), PageTitles.CM_Menus_SS.getTitle());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.Record_menu_DC.getCommonStrings(),
				CommonStrings.Record_menu_AC.getCommonStrings(), CommonStrings.Record_menu_AR.getCommonStrings(),
				CommonStrings.Record_menu_SS.getCommonStrings());
		click2(recordDoc, RecordDRStrings.Record_DR_Menu_DC.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_AC.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_AR.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_SS.getRecordDRStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordDRStrings.SearchBy_atRecordDR_AC.getRecordDRStrings(),
				RecordDRStrings.SearchBy_atRecordDR_AR.getRecordDRStrings(),
				CommonStrings.SearchBy_DC.getCommonStrings());
		click2(searchByCourseSessionnameDropdown, RecordDRStrings.Select_CoursSessionName_DC.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_AC.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_AR.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_SS.getRecordDRStrings());
		sendKeys2(courseSessionNameLike1, RecordDRStrings.Like_CSName_DC.getRecordDRStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), RecordDRStrings.Like_CSName_SS.getRecordDRStrings());
		TimeUtil.shortWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordDRStrings.Click_CS_for_RecordDR_DC.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_AC.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_AR.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_SS.getRecordDRStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		List<WebElement> employees = driver.findElements(By.xpath("//*[@id='EmpLst']/tbody/tr/td[1]"));
		for (int i = 1; i <= employees.size(); i++) {

			TimeUtil.mediumWait();
			String employeeQualify = driver.findElement(By.xpath("//*[@id='EmpLst']/tbody/tr[" + i + "]/td[1]"))
					.getText().trim();

			if (employeeQualify.equals(CM_CourseSession.getQualifiedEmployee())) {
				TimeUtil.mediumWait();
				WebElement qualify = driver.findElement(By.xpath("//*[@id='EmpLst']/tbody/tr[" + i + "]/td[5]/input"));
				click2(qualify, RecordDRStrings.Slect_Employees_DC.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_AC.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_AR.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_SS.getRecordDRStrings());
				break;

			}

			TimeUtil.mediumWait();
		}

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				RecordDRStrings.RecordDR_Confirmation_SS.getRecordDRStrings());
		switchToDefaultContent(driver);
	}

	public void NewRecordDocumentReading() {

		String CourseNameValue = CM_Course.getCourse();
		click2(menu, PageTitles.LearnIQ_ICON_DC.getTitle(), PageTitles.LearnIQ_ICON_AC.getTitle(),
				PageTitles.LearnIQ_ICON_AR.getTitle(), PageTitles.LearnIQ_ICON_SS.getTitle());
		TimeUtil.shortWait();
		click2(courseManagermenu, PageTitles.CM_Menus_DC.getTitle(), PageTitles.CM_Menus_AC.getTitle(),
				PageTitles.CM_Menus_AR.getTitle(), PageTitles.CM_Menus_SS.getTitle());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.Record_menu_DC.getCommonStrings(),
				CommonStrings.Record_menu_AC.getCommonStrings(), CommonStrings.Record_menu_AR.getCommonStrings(),
				CommonStrings.Record_menu_SS.getCommonStrings());
		click2(recordDoc, RecordDRStrings.Record_DR_Menu_DC.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_AC.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_AR.getRecordDRStrings(),
				RecordDRStrings.Record_DR_Menu_SS.getRecordDRStrings());
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordDRStrings.SearchBy_atRecordDR_AC.getRecordDRStrings(),
				RecordDRStrings.SearchBy_atRecordDR_AR.getRecordDRStrings(),
				CommonStrings.SearchBy_DC.getCommonStrings());
		click2(searchByCourseSessionnameDropdown, RecordDRStrings.Select_CoursSessionName_DC.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_AC.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_AR.getRecordDRStrings(),
				RecordDRStrings.Select_CoursSessionName_SS.getRecordDRStrings());
		sendKeys2(courseSessionNameLike1, RecordDRStrings.Like_CSName_DC.getRecordDRStrings(),
				CourseNameValue + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), RecordDRStrings.Like_CSName_SS.getRecordDRStrings());
		TimeUtil.shortWait();
		TimeUtil.mediumWait();
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, RecordDRStrings.Click_CS_for_RecordDR_DC.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_AC.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_AR.getRecordDRStrings(),
				RecordDRStrings.Click_CS_for_RecordDR_SS.getRecordDRStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
		List<WebElement> employees = driver.findElements(By.xpath("//*[@id='EmpLst']/tbody/tr/td[1]"));
		for (int i = 1; i <= employees.size(); i++) {

			TimeUtil.mediumWait();
			String employeeQualify = driver.findElement(By.xpath("//*[@id='EmpLst']/tbody/tr[" + i + "]/td[1]"))
					.getText().trim();
			
			

			if (employeeQualify.equals(CM_VerifyCourseSessionScreen.getQualifiedEmployee())) {
				TimeUtil.mediumWait();
				List<String> list = new ArrayList<>();
				list.add(employeeQualify);
			    ActualQualifiedCount = list.size();
				WebElement qualify = driver.findElement(By.xpath("//*[@id='EmpLst']/tbody/tr[" + i + "]/td[5]/input"));
				click2(qualify, RecordDRStrings.Slect_Employees_DC.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_AC.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_AR.getRecordDRStrings(),
						RecordDRStrings.Slect_Employees_SS.getRecordDRStrings());
				break;

			}
			
			
			
			TimeUtil.mediumWait();
		}
		

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				RecordDRStrings.RecordDR_Confirmation_SS.getRecordDRStrings());
		switchToDefaultContent(driver);
	}

}
