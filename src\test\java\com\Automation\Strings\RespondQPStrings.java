package com.Automation.Strings;

public enum RespondQPStrings {

	Respond_QP_menu_DC("Click on the 'Question Paper' submenu."),
	Respond_QP_menu_AC("'Respond To Question Paper' screen should be displayed.</div>"),
	Respond_QP_menu_AR("'Respond To Question Paper' screen is getting displayed.</div>"),
	Respond_QP_menu_SS("'Respond To Question Paper'"),

//search by	
	SearchBy_AC(
			"Option to search with 'Top 250 Records, Batch Name, Course Name and Initiated Between' should be available.</div>"),
	SearchBy_AR(
			"Option to search with 'Top 250 Records, Batch Name, Course Name and Initiated Between'  available.</div>"),

	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR(" Selection is getting accepted.</div>"),
	SearchBy_CourseName_SS("'Search By''Course Name'."),

	// Document Reading Completed Course Name

	Like_CourseNameRE_DC("Enter the course name for which the document reading is completed."),
	Like_CourseNameForQp_DC("Enter the course name for which the record attendance is updated for the above batch."),
	Like_CourseName_SS("'Course Name'"),

	Click_Course_DC("Click on the required course name to respond question paper."),
	Click_Course_RE_DC(
			"Click on the required course name for which the document reading is completed to respond question paper."),
	Click_Course_AC("Respond To Question Paper' screen should be displayed.<div>"
			+ "</div><b>*</b> Option to select/enter answers for the available questions should be displayed.</div>"),
	Click_Course_AR("Respond To Question Paper' screen is getting displayed.<div>"
			+ "</div><b>*</b> Option to select/enter answers for the available questions are getting displayed.</div>"),
	Click_Course_SS("'Respond To Question Paper'"),

	Select_ValidAns_DC(
			"Select the required answer for the available question and make sure that should be qualified with the selected answer."),
	Select_ValidAns_AC("Selection should be accepted."), Select_ValidAns_AR("Selection should be accepted."),
	Select_ValidAns_SS("Answer."),

	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Respond To Question Paper: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Respond To Question Paper: Registration Initiation'.</div>"),

	Esign_RespondQPProceed_AC(
			"'Congratulations! You Are Qualified and You Scored a Perfect 100' message should be displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details should be displayed accurately along with other details.</div>"),
	Esign_RespondQPProceed_AR(
			"'Congratulations! You Are Qualified and You Scored a Perfect 100' message is getting displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details are getting displayed accurately along with other details.</div>"),

	Esign_RespondQPProceed_ManualEval_AC(
			"'Assessment Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_RespondQPProceed_ManualEval_AR(
			"'Assessment Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Select_InValidAns_DC(
			"Select the wrong answer for the available question and make sure that should not be qualified with the selected answer."),
	Select_InValidAns_AC("Selection should be accepted."), Select_InValidAns_AR("Selection should be accepted."),
	Select_InValidAns_SS("Answer."),
	Esign_RespondWrongQPProceed_AC(
			"'Sorry!You Have Not Cleared The Assessment.Please Retake After Retraining' message should be displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details should be displayed accurately along with other details.</div>"),
	Esign_RespondWrongQPProceed_AR(
			"'Sorry!You Have Not Cleared The Assessment.Please Retake After Retraining' message is getting displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details are getting displayed accurately along with other details.</div>"),
	Click_Done_DC("Click 'Done' button."), Click_Done_SS("'Done'"),

//Just Qualified	

	Esign_RespondQP_JustQualified_AC(
			"'Congratulations! You Are Qualified.' message should be displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details should be displayed accurately along with other details.</div>"),
	Esign_RespondQP_JustQualified_AR(
			"'Congratulations! You Are Qualified.' message is getting displayed in 'Result' field.</div>"
					+ "<div><b>*</b> 'Acquired Marks and Acquire Percentage' details are getting displayed accurately along with other details.</div>"),

	Respond_MQA_menu_DC("Click on the 'Missed Question Analysis' submenu."),
	Respond_MQA_menu_AC("'Respond To Missed Question Analysis' screen should be displayed.</div>"),
	Respond_MQA_menu_AR("'Respond To Missed Question Analysis' screen is getting displayed.</div>"),
	Respond_MQA_menu_SS("'Respond To Missed Question Analysis'"),

	;

	private final String respondQPStrings;

	RespondQPStrings(String RespondQPStrings) {

		this.respondQPStrings = RespondQPStrings;

	}

	public String getRespondQPStrings() {
		return respondQPStrings;
	}

}
