package com.Automation.learniqBase;

import java.io.IOException;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;

import org.openqa.selenium.HasAuthentication;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.UsernameAndPassword;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.ie.InternetExplorerDriver;
import org.openqa.selenium.support.PageFactory;

import java.util.concurrent.TimeUnit;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.ITestResult;
import org.testng.SkipException;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.AfterTest;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.MyScreenRecorder;
import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import com.aventstack.extentreports.reporter.ExtentSparkReporter;
import com.aventstack.extentreports.reporter.configuration.Theme;

public class TestEngine {
	private static final Duration TimeOut = null;
	public static WebDriver driver;
	public static ExtentReports extent;
	public static ExtentTest test;
	public static ExtentSparkReporter htmlReporter;
	private boolean isTestFailed;
	public static boolean isReportedRequired;
	String URL = "";
	protected String currentSysUsername = System.getProperty("user.name");

	@BeforeTest(alwaysRun = true, enabled = true)

	public void setUp() throws IOException {

		isReportedRequired = Boolean.valueOf(ConfigsReader.getPropValue("isReportedRequired"));

		if (isReportedRequired == true) {

			String path;
			String basePath;
			ConfigsReader.readProperties(System.getProperty("user.dir") + "\\configs\\configuration.properties");
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMMM-yyyy HH.mm.ss");
			basePath = ConfigsReader.getPropValue("Reports");
			path = basePath + "learn-iq" + "_" + this.getClass().getSimpleName() + "_"
					+ ConfigsReader.getPropValue("ScenarioType") + "_Test_Automation" + "_"
					+ ConfigsReader.getPropValue("browser") + "_" + dateFormat.format(new Date()) + ".html";

			extent = new ExtentReports();
			ExtentSparkReporter spark = new ExtentSparkReporter(path);
			spark.loadXMLConfig(System.getProperty("user.dir") + "\\configs\\extent-config.xml");
			extent.attachReporter(spark);
			spark.config().setTheme(Theme.STANDARD);
			spark.config().setReportName("End To End Execution - " + this.getClass().getSimpleName());
			spark.config().setTimeStampFormat("dd-MM-yyyy hh:mm:ss");
			spark.config().setDocumentTitle(ConfigsReader.getPropValue("DocumentTitle"));
			InetAddress address = InetAddress.getLocalHost();
			extent.setSystemInfo("Product Name", ConfigsReader.getPropValue("ProductName"));
			extent.setSystemInfo("Version", ConfigsReader.getPropValue("Version"));
			extent.setSystemInfo("Product Code", ConfigsReader.getPropValue("ProductCode"));
			extent.setSystemInfo("URL", ConfigsReader.getPropValue("URL"));
			extent.setSystemInfo("Name", ConfigsReader.getPropValue("Name"));
			extent.setSystemInfo("System Name", ConfigsReader.getPropValue("SystemName"));
			extent.setSystemInfo("OS", ConfigsReader.getPropValue("OS"));
			extent.setSystemInfo("Host Name", address.getHostName());
			extent.setSystemInfo("IP Address", address.getHostAddress());
			// test = extent.createTest(getClass().getSimpleName(), "Executed Test Steps");

			switch (ConfigsReader.getPropValue("browser").toLowerCase()) {
			case "chrome":
				extent.setSystemInfo("Browser", "Chrome");
				ChromeOptions options = new ChromeOptions();

				// Bypass protocol handler prompt
				options.addArguments("--protocol-handler-outcome=allow");
				options.addArguments("--enable-features=ProtocolHandler");
				driver = new ChromeDriver(options);

				if (ConfigsReader.getPropValue("ScreenRecording").equalsIgnoreCase("YES")) {

					MyScreenRecorder.startRecording(this.getClass().getSimpleName());
				}

				break;
			case "firefox":
				extent.setSystemInfo("Browser", "FireFox");
				FirefoxOptions fo = new FirefoxOptions();

				driver = new FirefoxDriver(fo);
				break;
			case "edge":
				extent.setSystemInfo("Browser", "Edge");
				EdgeOptions eo = new EdgeOptions();
				driver = new EdgeDriver(eo);
				break;
			case "internetExplorer":
				extent.setSystemInfo("Browser", "InternetExplorer");
				driver = new InternetExplorerDriver();
				break;
			default:
				throw new RuntimeException("Browser is not supported");
			}
			driver.manage().window().maximize();
			// ((HasAuthentication) driver).register(UsernameAndPassword.of("swetha.ap",
			// "Sweety@38"));
			driver.get(URL);

			System.out.println("TestEngine" + driver);
			PageInitializer.initializePageObjects();
			isTestFailed = false;
		}

		else {

			switch (ConfigsReader.getPropValue("browser").toLowerCase()) {
			case "chrome":
				driver = new ChromeDriver();
				if (ConfigsReader.getPropValue("ScreenRecording").equalsIgnoreCase("YES")) {

					MyScreenRecorder.startRecording(this.getClass().getSimpleName());
				}
				break;
			case "firefox":
				FirefoxOptions fo = new FirefoxOptions();

				driver = new FirefoxDriver(fo);
				break;
			case "edge":
				EdgeOptions eo = new EdgeOptions();
				driver = new EdgeDriver(eo);
				break;
			case "internetExplorer":
				driver = new InternetExplorerDriver();
				break;
			default:
				throw new RuntimeException("Browser is not supported");
			}
			driver.manage().window().maximize();
			// ((HasAuthentication) driver).register(UsernameAndPassword.of("swetha.ap",
			// "Sweety@38"));
			driver.get(URL);

			System.out.println("TestEngine" + driver);
			PageInitializer.initializePageObjects();
			isTestFailed = false;
		}

	}

	public TestEngine(String url) {
		URL = url;
		PageFactory.initElements(driver, this);
	}

	public TestEngine() {

		PageFactory.initElements(driver, this);
	}

	@AfterMethod(alwaysRun = true)
	public void getResult(ITestResult result) {

		if (isReportedRequired == true) {
			if (result.getStatus() == ITestResult.FAILURE) {
				test.log(Status.FAIL, MarkupHelper.createLabel(result.getName() + " FAILED ", ExtentColor.RED));
				test.fail(result.getThrowable());
				TakesScreenshot ts = (TakesScreenshot) driver;
				String scrBase64 = ts.getScreenshotAs(OutputType.BASE64);
				test.addScreenCaptureFromBase64String(scrBase64, result.getName());
				isTestFailed = true;
				driver.quit();
				if (isTestFailed) {
					throw new SkipException("Skipping remaining tests due to test failure");
				}
			} else if (result.getStatus() == ITestResult.SUCCESS) {
				test.log(Status.PASS, MarkupHelper.createLabel(result.getName() + " PASSED ", ExtentColor.GREEN));
			} else {
				test.log(Status.SKIP, MarkupHelper.createLabel(result.getName() + " SKIPPED ", ExtentColor.ORANGE));
				test.skip(result.getThrowable());
			}

		}

		else {

			if (result.getStatus() == ITestResult.FAILURE) {
				isTestFailed = true;
				driver.quit();
				if (isTestFailed) {
					throw new SkipException("Skipping remaining tests due to test failure");
				}
			}

			else if (result.getStatus() == ITestResult.SUCCESS) {
			} else {
			}
		}

	}

	@AfterTest()
	public void afterTest() {

		if (isReportedRequired == true) {
			if (driver != null) {
				driver.quit();
			}
			extent.flush();
			MyScreenRecorder.stopRecording();
			driver.quit();

		} else {
			if (driver != null) {
				driver.quit();
			}
			MyScreenRecorder.stopRecording();
			driver.quit();

		}
	}

}