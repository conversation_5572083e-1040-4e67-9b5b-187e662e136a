package com.Automation.learniqObjects;

import java.awt.AWTException;
import java.awt.Robot;
import java.awt.event.KeyEvent;

import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.learniqObjects.SSO_UserRegistration;
import com.Automation.Strings.CommonStrings;
//import com.automation.login.pages.PlantselectionPage;
//import com.automation.login.pages.PlantselectionPage;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class EPICloginpage extends OQActionEngine {

	public static String firstName = "";
	public static String lastName = "";

	@FindBy(id = "company")
	WebElement companyTxtBx;

	@FindBy(xpath = "//input[@id='username']")
	WebElement userNameTxtBx;

	@FindBy(xpath = "//input[@id='txtUserIdVis']")
	WebElement userNameTxtBx_SSO;

	@FindBy(id = "password")
	WebElement passwordTxtBx;

	@FindBy(id = "txtUserIdVis")
	WebElement ssoUserNameTxtBox;

	@FindBy(id = "txtPasswordVis")
	WebElement ssoPasswordTxtBx;

	@FindBy(id = "LoginBtn")
	WebElement ssoLoginBtn;

	@FindBy(id = "btnSubmit")
	WebElement loginBtn;

	@FindBy(id = "selectPlant_38")
	WebElement plant1;

	@FindBy(id = "selectPlant_39")
	WebElement masterPlant;

	@FindBy(xpath = "//a[@id='logoutBtn']")
	WebElement signOut;

	@FindBy(xpath = "//li[@class='nav-item dropdown']/a")
	WebElement plantSelectionDropDown;

	@FindBy(xpath = "//a[@id='DdlPlant_39']")
	WebElement masterPlantDropDown;

	@FindBy(xpath = "//a[@id='DdlPlant_149']")
	WebElement masterPlantDropDown320;

//	@FindBy(xpath = "//a[@id='DdlPlant_26']")
//	WebElement learnIQPlantDropDown;

	@FindBy(xpath = "//a[@id='DdlPlant_38']")
	WebElement learnIQPlantDropDown;

	@FindBy(id = "selectedPlant")
	WebElement plantDropdown;

	@FindBy(id = "selectPlant_148")
	WebElement plant1_320;

	@FindBy(id = "selectPlant_149")
	WebElement masterPlant320;

	@FindBy(xpath = "//a[@id='DdlPlant_148']")
	WebElement learnIQPlantDropDown320;

	public EPICloginpage() {
		PageFactory.initElements(driver, this);

	}

	/**
	 * This method is for loginToApplication
	 */

//	public void loginToApplication(String company, String userName, String pwd) {
//
//		waitForElementVisibile(companyTxtBx);
//
//		sendKeys2(companyTxtBx, CommonStrings.Company_DC_310.getCommonStrings(), company,
//				CommonStrings.Company_AC.getCommonStrings(), CommonStrings.Company_AR.getCommonStrings(),
//				CommonStrings.Company_SS.getCommonStrings());
//
//		sendKeys2(userNameTxtBx, CommonStrings.UserName_DC.getCommonStrings(), userName,
//				CommonStrings.UserName_AC.getCommonStrings(), CommonStrings.UserName_AR.getCommonStrings(),
//				CommonStrings.UserName_SS.getCommonStrings());
//
//		// highLightEle1(driver, passwordTxtBx, test);
//
//		sendKeys2(passwordTxtBx, CommonStrings.Password_DC.getCommonStrings(), pwd,
//				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
//				CommonStrings.Password_SS.getCommonStrings());
//
//		// highLightEle1(driver, loginBtn, test);
//
////		clickLoginButton(loginBtn, CommonStrings.Loginbutton_DC.getCommonStrings(),
////				CommonStrings.Loginbutton_AC.getCommonStrings(), CommonStrings.Loginbutton_AR.getCommonStrings(),
////				CommonStrings.Loginbutton_SS.getCommonStrings());
//		loginBtn.click();
//		TimeUtil.mediumWait();
//		String multisessionURL = driver.getCurrentUrl();
//
//		if (!multisessionURL.equals(ConfigsReader.getPropValue("applicationUrl") + "Pages/PlantSelection?ref=login")) {
//
//			driver.findElement(By.id("btnYes")).click();
//		}
//	}

	/**
	 * This method is to select plant1
	 * @throws Throwable 
	 */

	public void plant1() {

//		WebElement plantSelection = driver.findElement(By.xpath("//span[@class='text-ellipsis plant-text' and text()='"
//				+ ConfigsReader.getPropValue("plantSelection").trim() + "']"));
		TimeUtil.mediumWait();
		waitForElementVisibile(driver.findElement(By.xpath("//span[@class='text-ellipsis plant-text' and text()='"
				+ ConfigsReader.getPropValue("plantSelection").trim() + "']")));
		TimeUtil.mediumWait();
		WebElement plantSelection = driver.findElement(By.xpath("//span[@class='text-ellipsis plant-text' and text()='"
				+ ConfigsReader.getPropValue("plantSelection").trim() + "']"));

		click2(plantSelection, CommonStrings.Plant1_DC.getCommonStrings(), CommonStrings.Plant1_AC.getCommonStrings(),
				CommonStrings.Plant1_AR.getCommonStrings(), CommonStrings.Plant1_SS.getCommonStrings());
		TimeUtil.mediumWait();
	      
	}

	/**
	 * This method is to select masterPlant
	 */

	public void masterPlant() {

		WebElement MasterplantSelection = driver
				.findElement(By.xpath("//span[@class='text-ellipsis plant-text' and text()='"
						+ ConfigsReader.getPropValue("masterPlantSelection").trim() + "']"));

		waitForElementVisibile(MasterplantSelection);

		click2(MasterplantSelection, CommonStrings.MasterPlant_DC.getCommonStrings(),
				CommonStrings.MasterPlant_AC.getCommonStrings(), CommonStrings.MasterPlant_AR.getCommonStrings(),
				CommonStrings.MasterPlant_SS.getCommonStrings());

	}

	/**
	 * 
	 * This method is for within the application navigate to Master Plant
	 * 
	 */

	public void navigateToMasterPlant() {
		waitForElementVisibile(plantSelectionDropDown);
		click2(plantSelectionDropDown, CommonStrings.PlantSelectionDropDown_DC.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_AC.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_AR.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_SS.getCommonStrings());

		click2(masterPlantDropDown, CommonStrings.MasterPlantDropDown_DC.getCommonStrings(),
				CommonStrings.MasterPlantDropDown_AC.getCommonStrings(),
				CommonStrings.MasterPlantDropDown_AR.getCommonStrings(),
				CommonStrings.MasterPlant_SS.getCommonStrings());
		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
	}

	public void navigateTolearnIQPlant() {

		// highLightEle1(driver, plantSelectionDropDown, test);
		click2(plantSelectionDropDown, CommonStrings.PlantSelectionDropDown_DC.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_AC.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_AR.getCommonStrings(),
				CommonStrings.PlantSelectionDropDown_SS.getCommonStrings());

//		if (driver.getCurrentUrl().contains(ConfigsReader.getPropValue("applicationUrl310"))) {
//			highLightEle1(driver, learnIQPlantDropDown, test);
		click2(learnIQPlantDropDown, CommonStrings.learnIQPlantDropDown_DC.getCommonStrings(),
				CommonStrings.learnIQPlantDropDown_AC.getCommonStrings(),
				CommonStrings.learnIQPlantDropDown_AR.getCommonStrings(),
				CommonStrings.learnIQPlantDropDown_SS.getCommonStrings());

		TimeUtil.mediumWait();
		TimeUtil.mediumWait();
	}

//		else if (driver.getCurrentUrl().contains(ConfigsReader.getPropValue("applicationUrl320"))) {
//			highLightEle1(driver, learnIQPlantDropDown320, test);
//			click2(learnIQPlantDropDown320, CommonStrings.learnIQPlantDropDown_DC.getCommonStrings(),
//					CommonStrings.learnIQPlantDropDown_AC.getCommonStrings(),
//					CommonStrings.learnIQPlantDropDown_AR.getCommonStrings(),
//					CommonStrings.learnIQPlantDropDown_SS.getCommonStrings());
//		}

	public void loginToSSOApplication(String company, String userNme, String pwd) {

		waitForElementVisibile(companyTxtBx);

		sendKeys2(companyTxtBx, CommonStrings.SSO_200_Company_DC.getCommonStrings(), company,
				CommonStrings.Company_AC.getCommonStrings(), CommonStrings.Company_AR.getCommonStrings(),
				CommonStrings.Company_SS.getCommonStrings());

		waitForElementVisibile(userNameTxtBx_SSO);
		sendKeys2(userNameTxtBx_SSO, CommonStrings.UserName_DC.getCommonStrings(), userNme,
				CommonStrings.UserName_AC.getCommonStrings(), CommonStrings.UserName_AR.getCommonStrings(),
				CommonStrings.UserName_SS.getCommonStrings());

		ssoPasswordTxtBx.click();

		waitForElementVisibile(ssoPasswordTxtBx);

		sendKeys2(ssoPasswordTxtBx, CommonStrings.Password_DC.getCommonStrings(), pwd,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());

		click2(ssoLoginBtn, CommonStrings.Loginbutton_DC.getCommonStrings(), CommonStrings.Plant1_AC.getCommonStrings(),
				CommonStrings.Plant1_AR.getCommonStrings(), CommonStrings.Loginbutton_SS.getCommonStrings());

		TimeUtil.mediumWait();
		String currentURL = driver.getCurrentUrl();

		if (!currentURL.equals(ConfigsReader.getPropValue("MultisessionSSOURL"))) {

			driver.findElement(By.xpath("//button[@class='caliber-button-primary']")).click();
		}
	}

	/**
	 * This method is for loginToApplication into SINGLE BUILD
	 */

	public void loginToApplication(String company, String userName, String pwd) {

		waitForElementVisibile(companyTxtBx);

		sendKeys2(companyTxtBx, CommonStrings.Company_DC_310.getCommonStrings(), company,
				CommonStrings.Company_AC.getCommonStrings(), CommonStrings.Company_AR.getCommonStrings(),
				CommonStrings.Company_SS.getCommonStrings());

		sendKeys2(userNameTxtBx, CommonStrings.UserName_DC.getCommonStrings(), userName,
				CommonStrings.UserName_AC.getCommonStrings(), CommonStrings.UserName_AR.getCommonStrings(),
				CommonStrings.UserName_SS.getCommonStrings());

		// highLightEle1(driver, passwordTxtBx, test);

		sendKeys2(passwordTxtBx, CommonStrings.Password_DC.getCommonStrings(), pwd,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());

		// highLightEle1(driver, loginBtn, test);

//		clickLoginButton(loginBtn, CommonStrings.Loginbutton_DC.getCommonStrings(),
//				CommonStrings.Loginbutton_AC.getCommonStrings(), CommonStrings.Loginbutton_AR.getCommonStrings(),
//				CommonStrings.Loginbutton_SS.getCommonStrings());
		loginBtn.click();
		TimeUtil.mediumWait();

		String multisessionURL = driver.getCurrentUrl();

		if (!multisessionURL.equals(ConfigsReader.getPropValue("applicationUrl") + "Pages/PlantSelection?ref=login")) {
			
			driver.findElement(By.id("btnYes")).click();
		}

	}

	public void loginToEPIQforResetPassword(String company) {

		waitForElementVisibile(companyTxtBx);

		String currenturl1 = driver.getCurrentUrl();
		if (currenturl1.contains(ConfigsReader.getPropValue("applicationUrl"))) {
			sendKeys2(companyTxtBx, CommonStrings.Company_DC_310.getCommonStrings(), company,
					CommonStrings.Company_AC.getCommonStrings(), CommonStrings.Company_AR.getCommonStrings(),
					CommonStrings.Company_SS.getCommonStrings());
			sendKeys2(userNameTxtBx, CommonStrings.NewUser_Login_DC.getCommonStrings(),
					SSO_UserRegistration.getUserID(), CommonStrings.UserName_AC.getCommonStrings(),
					CommonStrings.UserName_AR.getCommonStrings(), CommonStrings.UserName_SS.getCommonStrings());

			String tempPassword = OQActionEngine.tempPwd;
			sendKeys2(passwordTxtBx, CommonStrings.TemporaryPassword_DC.getCommonStrings(), tempPassword,
					CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
					CommonStrings.Password_SS.getCommonStrings());

			click2(loginBtn, CommonStrings.Loginbutton_DC.getCommonStrings(),
					CommonStrings.NewUserLoginbuttonEPIQ_AC.getCommonStrings(),
					CommonStrings.NewUserLoginbuttonEPIQ_AR.getCommonStrings(),
					CommonStrings.Loginbutton_SS.getCommonStrings());

		}

	}
}
