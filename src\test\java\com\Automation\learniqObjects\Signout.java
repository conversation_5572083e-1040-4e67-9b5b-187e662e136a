package com.Automation.learniqObjects;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;


import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.*;

public class Signout extends OQActionEngine {

	@FindBy(xpath = "//a[@href='#']//i[@class='ft-power font-red font-size-20']")
	WebElement signoutBtn;
	@FindBy(id = "logoutBtn")
	WebElement signout;

	public Signout() {
		PageFactory.initElements(driver, this);
	}

	/**
	 * This method is for SignOut Page
	 *
	 */

	public void signOutPage() {

		switchToDefaultContent(driver);
		waitForElementVisibile(signoutBtn);
		click2(signoutBtn, CommonStrings.SignOut_icon_DC.getCommonStrings(),
				CommonStrings.SignOut_icon_AC.getCommonStrings(), CommonStrings.SignOut_icon_AR.getCommonStrings(),
				CommonStrings.SignOut_icon_SS.getCommonStrings());

	}

	public void signOutPageForResetPassword() {

		switchToDefaultContent(driver);
		click2(signout, CommonStrings.SignOut_icon_DC.getCommonStrings(),
				CommonStrings.SignOut_icon_AC.getCommonStrings(), CommonStrings.SignOut_icon_AR.getCommonStrings(),
				CommonStrings.SignOut_icon_SS.getCommonStrings());

	}

	/**
	 * This method is for SSO signOut Page
	 *
	 */

	public void SSOsignOutPage() {

		TimeUtil.shortWait();
		waitForElementVisibile(signout);

		click2(signout, CommonStrings.SignOut_icon_DC.getCommonStrings(),
				CommonStrings.SignOut_icon_AC.getCommonStrings(), CommonStrings.SignOut_icon_AR.getCommonStrings(),
				CommonStrings.SignOut_icon_SS.getCommonStrings());

		TimeUtil.shortWait();

	}

}
