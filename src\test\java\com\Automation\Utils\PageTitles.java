package com.Automation.Utils;

public enum PageTitles {

	EnterValue_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	dropdown_AR("Selection is getting accepted.</div>"),
	dropdown_AC("Selection should be accepted.</div>"),

	SignOut_icon_DC("Click on 'Signout' icon."),
	SignOut_icon_AC("'Login' screen should be displayed.</div>"),
	SignOut_icon_AR("'Login' screen is getting displayed.</div>"),
	SignOut_icon_SS("'Signout'"),

	sendKeys_AC("Entered value should be displayed for the field."),
	sendKeys_AR("Entered value is getting displayed for the field."),

	/*
	 * 
	 * Company
	 * 
	 */

	Company_DC("Open the epiq 3.1.0 (Service Pack 2) application URL and enter the valid 'Company' in 'Company' field."),
	Company_New_DC("Enter the valid 'Company' in 'Company' field."),

	Company_AC("Entered value should be  displayed  in 'Company' field."),
	Company_AR("Entered value is getting displayed in 'Company' field."),
	Company_SS("'Company'"),

	// -------------------------------------------------

	/*
	 * 
	 * User Name
	 * 
	 */

	UserName_DC("Enter valid 'User ID' in 'User ID' field."),
	UserName_Reports("Enter the valid 'User ID' of the user who has right to view reports in 'User ID' field."),

	UserName_AC("Entered value should displayed in 'User ID' field in hidden mode.</div>"),

	UserName_AR("Entered value is getting displayed in 'User ID' field in hidden mode."),
	UserName_SS("'User ID"),

	/*
	 * 
	 * User Name
	 * 
	 */

	TraineeResDoc_DC("Enter valid 'User ID' of the user who is selected as a trainee in the above proposed session 'User ID' field."),

	// TraineeResDoc_AC("Entered value should" + "</br>" + " be displayed" + "</br>"
	// + "accurately in 'User " + "</br>" + "ID' feild."),

	// -------------------------------------------------

	/*
	 * 
	 * Password
	 * 
	 */

	Password_DC("Enter valid Password in the 'Password' field."),

	Password_AC("Entered value should be displayed in 'Password' field in hidden mode."),

	Password_AR("Entered value is getting displayed in 'Password' field in hidden mode."),
	Password_SS("'Password'"),

	// ----------------------------------------------------

	/*
	 * 
	 * Login Button
	 * 
	 */

	Loginbutton_DC("Click on 'Login' button."),

	Loginbutton_AC("Option to select plants should be available."),

	Loginbutton_AR("Option to select plants are available."),

	Loginbutton_SS("'Plants'"),

	/*
	 * 
	 * Signout Button
	 * 
	 */

	Signoutbutton_DC("Click on 'Signout'" + "</br>" + "button."),

	Signoutbutton_AC("<div><b>*</b> 'Signout' button" + "</br>" + "should be clicked" + "</br>" + "successfully and"
			+ "</br>" + "'Login' screen should" + "</br>" + "be displayed</div>"),

	Signoutbutton_AR("'Signout' button" + "</br>" + "is clicked" + "</br>" + "successfully and" + "</br>"
			+ "'Login' screen has" + "</br>" + "been displayed"),

	Signoutbutton_SS("'Signout'"),

	// ------------------------------------------------------
	/*
	 * 
	 * Plant 1 Selection
	 * 
	 */

	Plant1_DC("Click on 'Plant1."),

	Plant1_AC("Home screen of the login user should be displayed.</div>"),

	Plant1_AR("Home Screen of the login user is getting displayed"),

	Plant1_SS("'Plant1'"),
	// -------------------------------------------------------------

	/*
	 * 
	 * Master Plant Selection
	 * 
	 */

	MasterPlant_DC("Click on 'Master Plant'."),

	MasterPlant_AC("Home screen of the login user should be displayed.</div>"),

	MasterPlant_AR("Home Screen of the login user is getting displayed.</div>"),

	MasterPlant_SS("'Master Plant'."),

	PlantSelectionDropDown_DC("Click on plant selection dropdown."),
	PlantSelectionDropDown_AC("Option to select the plants should be displayed."),
	PlantSelectionDropDown_AR(
			"Option to select the plants is getting displayed."),
	PlantSelectionDropDown_SS("'Plants'"),

	MasterPlantDropDown_DC("Select the 'Master Plant'."),
	MasterPlantDropDown_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Home screen of the login user is getting displayed.</div>"),
	MasterPlantDropDown_AC("Selection should be accepted."+ "<div><b>*</b> Home screen of the login user "
			+ "should be displayed.<>/div"),
	MasterPlantDropDown_SS("''Master Plant"),
	

	learnIQPlantDropDown_DC("Select the 'Plant1'"),
	learnIQPlantDropDown_AR("Selection is getting accepted.</div>"
			+ "<div><b>*</b> Home screen of the login user is getting displayed.</div>"),
	learnIQPlantDropDown_AC("Selection should be accepted."+ "<div><b>*</b> Home screen of the login user "
			+ "should be displayed.<>/div"),

	learnIQPlantDropDown_SS("'Plant1'"),

	// -------------------------------------------------------------

	/*
	 * 
	 * Learn-iq icon
	 * 
	 */

	LearnIQ_ICON_DC("Click on 'learn-iq' icon."),
	LearnIQ_ICON_AC("Option to select 'Course Manager, Document Manager and System Manager' should be available.</div>"),
	LearnIQ_ICON_AR("Option to select 'Course Manager, Document Manager and System Manager' are available.</div>"),
	LearnIQ_ICON_SS("'learn-iq' icon"),

	// -----------------------------------------------------------

	/*
	 * Call E-Sign at Registration Initiation
	 * 
	 */

	Call_ESign_RegInitiation_DC(
			"Select 'Registration' check box under 'Call E-Sign: At: Initiation'"),
	Call_ESign_RegInitiation_AC("Selection should be accepted.</div>"),
	Call_ESign_RegInitiation_AR("Selection is getting accepted."),
	Call_ESign_RegInitiation_SS("'Registration'"),

	Topic_Config_DC("Click on 'Topic' menu"),
	Topic_Config_AC("'Topic Configuration Registration' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),

	Topic_Config_AR("'Topic Configuration Registration' screen is getting displayed</div>" 
	+ "<div><b>*</b> Option to select  E-Sign and 'No of Approvals Required' are available.</div>"),

	Topic_Config_SS("'Configuration'"),

	// Course Configuration
	CourseConfig_DC("Click on 'Course' menu."),
	CourseConfig_AC("'Course Configuration Registration' screen should be displayed.</div>" 
	+ "<div><b>*</b> Option to select the E-Sign and "
			+ "'No of Approvals Required' should be available.</div>"),
	CourseConfig_AR("'Course Configuration Registration' screen is getting displayed.</div>" +
			"<div><b>*</b> Option to select the E-Sign and "
			+ "'No of Approvals Required' are available.</div>"),
	CourseConfig_SS("'Configuration'"),

	CourseRegistrationScreen_DC("Click 'Course' menu."),
	CourseRegistrationScreen_AC("'Course Registration Initiation' screen should be displayed.</div>" 
	+ "<div><b>*</b> Screen should contain 'Course Name, Unique Code, Description' fields.</div>"
			+ "<div><b>*</b> Option to select 'External Training, Self-Study, Training Method and Topics' should be available.</div>"),
	CourseRegistrationScreen_AR("'Course Registration Initiation' screen is getting displayed.</div>" 
	+ "<div><b>*</b> Screen contains 'Course Name, Unique Code, Description' fields.</div>"
			+ "<div><b>*</b> Option to select 'External Training, Self-Study, Training Method and Topics' are available.</div>"),
	CourseRegistrationScreen_SS("'Course Registration'"),

	CourseName_DC("Enter the value less than or equals to 250 characters in 'CourseName' field."),
	CourseName_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	CourseName_AR("'CourseName'.</div>"), CourseName_SS("'Course Name'"),

	CourseDescription_DC("Enter the value less than or equals to 250 characters in 'Description' field."),
	CourseDescription_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	CourseDescription_AR("'Description'.</div>"), CourseDescription_SS("'Description'"),

	TrainingTypeDropDown_DC("Click 'Training Type'"),
	TrainingTypeDropDown_AC("Option to select the 'cGMP Training, Safety Training, "
			+ "Technical Training, Induction Training and On Job Training' should be available.</div>"),
	TrainingTypeDropDown_AR("Option to select the 'cGMP Training, Safety Training, "
			+ "Technical Training, Induction Training and On Job Training' are available.</div>"),
	TrainingTypeDropDown_SS("'Training Type'"),

	TrainingTypeLike_DC("Enter the 'Technical Training'."),
	TrainingTypeLike_AC("Option to select the 'Technical Training' should be displayed."),
	TrainingTypeLike_AR("'Training Type'.</div>"), TrainingTypeLike_SS("'Training Type'"),

	TechnicalTraining_Select_DC("Select the 'Technical Training' for 'Training Type' field."),
	TechnicalTraining_Select_AC("Selected value should be displayed for 'Training Type' field</div>"),
	TechnicalTraining_Select_AR("Selected value is getting displayed for 'Training Type' field</div>"),
	TechnicalTraining_Select_SS("'Technical Training'"),

	SelfStudyNo_DC("Select  the 'Self-study' as 'No'"),
	SelfStudyNo_AC("Selected value should be accepted or 'Self-Study' field.</div>"),
	SelfStudyNo_AR("Selected value is getting accepted for 'Self-Study' field.</div>"),
	SelfStudyNo_SS("'Self-Study'"),

	SearchByTopicName_DC("Select 'Topic Name'."),
	SearchByTopicName_AC("Selected value should be accepted</div>"),
	SearchByTopicName_AR("Selected value is getting accepted."),
	SearchByTopicName_SS("'Topic Name'"),

	TopicNameLike_DC("Enter the above registered topic name."),
	TopicNameLike_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	TopicNameLike_AR("'Topic Name'</div>"), TopicNameLike_SS("'TopicName'"),

	FetchRecords_DC("Click on 'Fetch Records' hyperlink."),
	FetchRecords_AC("Records should be displayed based on the search criteria in 'Available Topics'.</div>"),
	FetchRecords_AR("Records are getting displayed based on the search criteria in 'Available Topics' column.</div>"),
	FetchRecords_SS("'Fetch Records'"),

	AvailableTopic_DC("Select the above registered topic in 'Available Topics' column."),
	AvailableTopic_AC("Selected topic should be moved to 'Selected  Topics' column.</div>"),
	AvailableTopic_AR("Selected topic is getting moved to 'Selected  Topics' column.</div>"),
	AvailableTopic_SS("'Selected Topics'"),

	NextButtonCR_DC("Click on 'Next' button."),
	NextButtonCR_AC("Option to select the documents should be available.</div>"),
	NextButtonCR_AR("Option to select documents are available.</div>"),
	NextButtonCR_SS("'Next'"),

	SelectAllDoc_DC("Select 'Select All'check box for documents"),
	SelectAllDoc_AC("Selected document(s) should be accepted."),
	SelectAllDoc_AR("Selected document(s) is getting accepted."),
	SelectAllDoc_SS("Documents"),

	
	NextButton1_AC("Option to select subgroups should be available.</div>"),
	NextButton1_AR("Option to select subgroups are available.</div>"),
	NextButton1_SS("'Next'"),

	SearchBySubgroupName_DC("Select 'Subgroup Name'."),
	SearchBySubgroupName_AC("Selected value should be accepted.</div>"),
	SearchBySubgroupName_AR("Selected value is gettting accepted.</div>"),
	SearchBySubgroupName_SS("'Subgroup Name'"),

	SubgroupNameLike_DC("Enter the required 'Subgroup Name'."),
	SubgroupNameLike_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	SubgroupNameLike_AR("'Subgroup Name'.</div>"),
	SubgroupNameLike_SS("'Subgroup Name'"),

	SubgrpName_FetchRcrds_DC("Click on 'FetchRecords' hyperlink"),
	SubgrpName_FetchRcrds_AC("Records should be displayed based on the search "
			+ "criteria in 'Available Subgroups' column.</div>"),
	SubgrpName_FetchRcrds_AR("Records are getting displayed based on the search "
			+ "criteria in 'Available Subgroups' column.</div>"),
	SubgrpName_FetchRcrds_SS("'Fetch Records'"),

	AvailableSubgrp_DC("Select the required Subgroup in 'Available Subgroups' column."),
	AvailableSubgrp_AC("Selected Subgroup should be moved to 'Selected Subgroups' column.</div>"),
	AvailableSubgrp_AR("Selected Subgroup are getting moved to 'Selected Subgroups' column.</div>"),
	AvailableSubgrp_SS("'Available Subgroups'"),

	ApprovalforCandidature_DC("Select 'Approval for Candidature as 'Not Required'"),
	ApprovalforCandidature_AC("Selected value should be accepted</div>"),
	ApprovalforCandidature_AR("Selected value is getting accepted.</div>"),
	ApprovalforCandidature_SS("'Not Required'"),

	Preview_DC("Click on 'Preview' button."),
	Preview_AC("All selected/entered details should be displayed in read only format."),
	Preview_AR("All selected/entered details are getting displayed in read only format."),
	Preview_SS("'Preview'"),

	SearchByCourse_Dropdown_DC("Click on 'Search By' dropdown."),
	SearchByCourse_Dropdown_AC("Option to search with 'Top 250 Records, Course Name, Unique' Code', Initiated Between should be displayed.</div>"),
	SearchByCourse_Dropdown_AR("Option to search with 'Top 250 Records, Course Name, Unique' Code', Initiated Between is getting displayed.</div>"),
	SearchByCourse_Dropdown_SS("'Search By'"),

	Select_CoursName_DC("Select 'Course Name'."),
	Select_CoursName_AC("Selection should be accepted."),
	Select_CoursName_AR("Selection is getting accepted."),
	Select_CoursName_SS("'Course Name'."),

//Topic Registration

	InitiateTopic_DC("Click on 'Initiate' menu"),
	InitiateTopic_AC("Assigned sub menus for the user under 'Initiate' menu should be displayed.</div>"),
	InitiateTopic_AR("Assigned sub menus for the user under 'Initiate' menu are getting displayed.</div>"),
	InitiateTopic_SS("'Initiate'"),

	TopicRegistrationScreen_DC("Click on 'Topic' menu."),
	TopicRegistrationScreen_AC("'Topic Registration Initiation' screen should be displayed.</div>"
	+ "<div><b>*</b> Screen should contain 'Topic Name, Topic Unique Code, Description' fields.</div>"),

	TopicRegistrationScreen_AR("'Topic Registration Initiation' screen is getting displayed.</div>" 
	+ "<div><b>*</b> Screen contains 'Topic Name, Topic Unique Code,Description' fields.</div>"),

	TopicRegistrationScreen_SS("'Topic Registration'"),

	TopicName_DC("Enter the value less" + "</br>" + "than or equals to" + "</br>" + "250 characters in" + "</br>"
			+ "'Topic Name' field."),
	TopicName_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed for the" + "</br>" + "field.</div>"),
	TopicName_AR("<div><b>*</b> Entered value is" + "</br>" + "getting displayed for the" + "</br>" + "field.</div>"),
	TopicName_SS("'Topic Name'"),

	TopicUniqueCode_DC("Enter the value less" + "</br>" + "than or equals to 25" + "</br>" + "characters in 'Topic"
			+ "</br>" + "Unique Code' field."),
	TopicUniqueCode_AC(
			"<div><b>*</b> Entered value should" + "</br>" + "be displayed for the" + "</br>" + "field.</div>"),
	TopicUniqueCode_AR(
			"<div><b>*</b> Entered value is" + "</br>" + "getting displayed for" + "</br>" + "the field.</div>"),
	TopicUniqueCode_SS("'Unique Code'"),

	TopicDescription_DC("Enter the value less" + "</br>" + "than or equals to" + "</br>" + "500 characters in" + "</br>"
			+ "'Description' field."),
	TopicDescription_AC(
			"<div><b>*</b> Entered value should" + "</br>" + "be displayed for the" + "</br>" + "field.</div>"),
	TopicDescription_AR(
			"<div><b>*</b> Entered value is" + "</br>" + "getting displayed for" + "</br>" + "the field.</div>"),
	TopicDescription_SS("'Description'"),

	ClickDocumentName_DC("Select 'Document Name'."),
	ClickDocumentName_AC("Selection should be accepted.</div>"),
	ClickDocumentName_AR("Selection is getting accepted.</div>"),
	ClickDocumentName_SS("'Document Name'"),

	TopicdocumentSearchTextBox_DC("Enter the required" + "</br>" + "'Document Name'"),
	TopicdocumentSearchTextBox_AC(
			"<div><b>*</b> Entered value should" + "</br>" + "be displayed for the" + "</br>" + "field.</div>"),
	TopicdocumentSearchTextBox_AR(
			"<div><b>*</b> Entered value is" + "</br>" + "getting displayed for" + "</br>" + "the field.</div>"),
	TopicdocumentSearchTextBox_SS("'Document Name'"),

	Topicfetchrecords_DC("Click on 'Fetch Records'."),

	Topicfetchrecords_AC("Records should be displayed based on the search criteria in 'Available Documents' column.</div>"),

	Topicfetchrecords_AR("Records are getting displayed based on the search criteria in 'Available Documents' column.</div>"),
	Topicfetchrecords_SS("'Available'"),

	greenTickMark_DC("Select the required" + "</br>" + "document in 'Available" + "</br>" + "Documents' section."),
	greenTickMark_AC("Selected document should be moved to 'Selected Documents' section.</div>"),
	greenTickMark_AR("Selected document is getting moved to 'Selected Documents' section.</div>"),
	greenTickMark_SS("'Selected Document'"),

	Submit_DC("Click on 'Submit' button."),
	Submit_AC("'Meaning of This Electronic Signature' window should be displayed as 'Topic: Registration Initiation'.</div>"),
	Submit_AR("'Meaning of This Electronic Signature' window is getting displayed as 'Topic: Registration Initiation'.</div>"),

	Submit_SS("'E-Sign window'"),

	click_TopicAuditTrails_DC("Click on 'Topic' menu."),
	click_TopicAuditTrails_AC("'Topic Audit Trails' screen should be displayed and list of topics should be displayed.<div>"),
	click_TopicAuditTrails_AR(
			"'Topic Audit Trails' screen is getting displayed.</div>"),
	click_TopicAuditTrails_SS("'Topic Audit Trials'"),

	CourseManagerAudiTrails_DC("Click on 'Audit Trails' menu"),
	CourseManagerAudiTrails_AC("Assigned sub menus for the user under 'Audit Trails' menu should be displayed.<div>"),
	CourseManagerAudiTrails_AR("Assigned sub menus for the user under 'Audit Trails' menu are getting displayed.<div>"),
	CourseManagerAudiTrails_SS("'Menus'"),

	CourseAudittrails_DC("Click on 'Course' menu."),
	CourseAudittrails_AC("'Course Audit Trails' screen should be displayed.</div>"),
	CourseAudittrails_AR("'Course Audit trails' screen is getting displayed.</div>"),
	CourseAudittrails_SS("'Course Audit Trails' screen."),

	SearchBy_TP_DC("Click on 'Search By'" + "</br>" + "dropdown."),
	SearchBy_TP_AC("Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic UniqueCode' should be diplayed.<div>"),
	SearchBy_TP_AR("Option to search with 'Top 250 Records, Topic Name, Unique Code, Initiated Between, Subject Name and Topic Unique Code' is getting diplayed.<div>"),
	SearchBy_TP_SS("'Search By'."),

	SearchBy_TopicName_DC("Select 'Topic Name'."),
	SearchBy_TopicName_AC("<div><b>*</b> Selection should be" + "</br>" + "accepted.</div>"),
	SearchBy_TopicName_AR("<div><b>*</b> 'Selection is getting" + "</br>" + "accepted.</div>"),
	SearchBy_TopicName_SS("'Search By''Topic" + "</br>" + "Name'."),

	Like_TopicName_DC(
			"Enter the above" + "</br>" + "registered 'Topic" + "</br>" + "Name' in 'Like' text" + "</br>" + "box."),
	Like_TopicName_AC(
			"<div><b>*</b> Entered value should" + "</br>" + "be displayed for the" + "</br>" + "field.</div>"),
	Like_TopicName_AR(
			"<div><b>*</b> Entered value is getting" + "</br>" + "displayed for the" + "</br>" + "field.</div>"),
	Like_TopicName_SS("Topic Name"),

	TP_for_AuditTrails_DC("Click on the above" + "</br>" + "registered 'Topic" + "</br>" + "Name'."),

	TP_for_AuditTrails_AC("'Topic-Audit Trails: Revision No.:Title 0-Registration' screen should be displayed.</div>" +
	"<div><b>*</b> 'Topic Name, Unique Code, Topic Unique Code, Description, Document Name & Document Code should be displayed with accurate details.</div>"),

	TP_for_AuditTrails_AR("'Topic-Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>" +
			"<div><b>*</b> 'Topic Name, Unique Code, Topic Unique Code, Description, Document Name & Document Code are getting displayed with accurate details.</div>"),

	TP_for_AuditTrails_SS("'Topic Audit Trails'"),

	// -------------------------------------------
	/*
	 * Course Session Configuration
	 * 
	 * 
	 */
	CM_Menus_DC("Click on 'Course Manager'"),
	CM_Menus_AC("Assigned menus for user  should  be displayed.</div>"),
	CM_Menus_AR("Assigned menus for user are getting displayed.</div>"),
	CM_Menus_SS("Menus"),

	ConfigureCS_DC("Click on 'Configure' menu"),
	ConfigureCS_AC("Assigned sub menus for the user under 'Configure' menu should be displayed.</div>"),
	ConfigureCS_AR("Assigned sub menus for user under 'Configure' menu are getting displayed."),
	ConfigureCS_SS("'Menus'"),

	CourseSessionConfig_DC("Click on 'Course Session' menu."),

	CourseSessionConfig_AC("'Course Session' Configuration Registration' screen should be displayed.</div>" +
	"<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),

//	CourseSessionConfig_AC("<div><b>*</b> 'Course Session'" + "</br>" + "Configuration" + "</br>" + "Registration' screen" + "</br>"
//			+ "should be displayed.</div>" +"<div><b>*</b> Option to select" + " " + "</br>" + "the E-Sign and" + "</br>"
//			+ "'No of Approvals" + "</br>" + "Required' should be" + "</br>" + "available.</div>"),

	CourseSessionConfig_AR("'Course Session Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' are avialable.</div>"),

	CourseSessionConfig_SS("'Configuration'"),

	// ---------------------------------
	/*
	 * Number of Approvals available.
	 * 
	 */

	NoOfApprovals_DC("Click on 'Registration' dropdown under 'No. of Approvals Required:'."),

	NoOfApprovals_AC("Option to select from '0' to '6' should be available."),
	NoOfApprovals_AR("Option to select from '0' to '6' are available.</div>"),

	NoOfApprovals_SS("'No of Approvals" + "</br>" + "Required'"),

	// ---------------------------------
	/*
	 * No Approvals
	 * 
	 */

	Zero_Approvals_DC("Select '0' approvals."),
	Zero_Approvals_AC("Selection  should be accepted."),
	Zero_Approvals_AR("Selection is getting accepted"),
	Zero_Approvals_SS("'0' Approvals."),

	// ---------------------------------
	/*
	 * Remarks
	 * 
	 */

	Remarks_DC("Enter the value less than or equals to 250 characters in 'Remark(s)/Reason(s)' field"),
	Remarks_AC("Entered value should be displayed for the field.</div>"),
	Remarks_AR("Entered value is getting displayed for the field."),
	Remarks_SS("'Remarks'"),

	// ---------------------------------
	/*
	 * Submit Button
	 * 
	 */

	Submit_Button_DC("Click on 'Submit' Button."),
	Submit_Button_AC("'Configuration Registered' confirmation message should be displayed with 'Done' button.</div>"),
	Submit_Button_AR("'Configuration Registered' confirmation message is getting displayed with 'Done' button.</div>"),
	Submit_Button_SS("'Configuration'"),

	// ---------------------------------
	/*
	 * Propose Menu
	 * 
	 */

	CM_ProposeMenus_AC("<div><b>*</b> 'Propose' menu" + "</br>" + "should be displayed" + "</br>" + "and other menus"
			+ "</br>" + "should be" + "</br>" + "displayed(if any).</div>"),
	CM_ProposeMenus_AR("'Course Manager'menu" + "</br>" + "and 'Configure' menu" + "</br>" + "has been displayed"),
	CM_ProposeMenus_SS("Menus"),

	Propse_Menu_DC("Click 'Propose' menu'."),
	Propse_Menu_AC("Assigned sub menus for the user under 'Propose' menu should be displayed.</div>"),
	Propse_Menu_AR("Assigned sub menus for the user under 'Propose' menu is getting displayed.</div>"),
	Propse_Menu_SS("'Menus'"),

	Course_Session_AC("'Course Session Registration Initiation' screen should be displayed.</div>" 
	+ "<div><b>*</b> Screen should contain 'Scheduled Courses', Unscheduled Courses and Interim Courses' tabs.</div>"),
	Course_Session_AR("'Course Session Registration Initiation' screen is getting displayed.</div>" 
			+ "<div><b>*</b> Screen contains 'Scheduled Courses', Unscheduled Courses and Interim Courses' tabs.</div>"),
	Cours_Session_SS("'Course Session Registration Initiation' screen"),

	UnscheduledCourse_DC("Click on 'Unscheduled'" + "</br>" + "Courses' tab."),
	UnscheduledCourse_AC("List of the unscheduled courses should be displayed.</div>"),
	UnscheduledCourse_AR("List of the unscheduled courses are getting displayed.</div>"),
	UnscheduledCourse_SS("'Unscheduled Courses'"),

	SearchBy_CS_DC("Click on 'Search By' dropdown."),
	SearchBy_CS_AC("Option to search with 'Top 250 Records, Course Name and Course Code' should be displayed.</div>"),
	SearchBy_CS_AR("Option to search with 'Top 250 Records, Course Name and Course Code' should be displayed.</div>"),
	SearchBy_CS_SS("'Search By' dropdown."),

	SearchBy_CourseName_DC("Select 'Course Name'."),
	SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR("Selection is getting accepted.</div>"),
	SearchBy_CourseName_SS("'Course Name'"),

	Like_CourseName_DC("Enter the above registered 'Course Name' in 'Like' field."),
	Like_CourseName_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	Like_CourseName_AR("'Course Name'.<div>"), 
	Like_CourseName_SS("Course Name"),

	Click_Course_for_AuditTrails_DC("Click on the above registered 'Course' Name'."),
	Click_Course_for_AuditTrails_AC("'Course - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
	+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),
	Click_Course_for_AuditTrails_AR("'Course - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),
	Click_Course_for_AuditTrails_SS("'Course' - " + "</br>" + "Audit Trails."),

	// -----------------------------

	/*
	 * 
	 * Apply button
	 * 
	 */

	ApplyButton_DC("Click on 'Apply' button."),
	ApplyButton_AC("Records should be displayed based on the search criteria."),

	ApplyButton_AR("Records are getting displayed based on the search criteria.</div>"),

	ApplyButton_SS("'Apply'"),

	CS_CourseName_DC("Click on above registred course."),
	CS_CourseName_AC("Option to select 'Subgroups' should be available.</div>"),
	CS_CourseName_AR("Option to select 'Subgroups' are available.</div>"),
	CS_CourseName_SS("'Subgroups'"),

	AddItem_Subgroup_DC("Click on 'Add Item' for 'Selected Subgroup List' field"),
	AddItem_Subgroup_AC("'Subgroup List' window should be displayed.</div>"),
	AddItem_Subgroup_AR("'Subgroup List' window is getting displayed.</div>"),
	AddItem_Subgroup_SS("'Subroup List' window.<div>"),

	Find_Subgroup_DC("Enter the required 'Subgroup Name' in 'Find' textbox"),
	Find_Subgroup_AC("<div><b>*</b> Entered 'Subgroup" + "</br>" + "Name' should be" + "</br>" + "displayed.</div>"),
	Find_Subgroup_AR("'Subgroup Name'"),
	Find_Subgroup_SS("'Subgroup Name'"),

	Click_Add_DC("Click on 'Add' against the required subgroup."),
	Click_Trainee_Add_DC("Click on 'Add' against the required trainee."),
	Click_Add_AC("Selected record should be moved to 'Selected Items' column.</div>"),
	Click_Add_AR("Selected record is getting moved to 'Selected Items' cloumn.</div>"),
	click_Add_SS("'Selected Items'"),

	Click_AddButton_DC("Click on 'Add' button."),
	Click_AddButton_AC("Selected subgroup name(s)should be displayed for 'Selected Subgroup List' field.</div>"),
	Click_AddButton_AR("Selected subgroup name(s) is getting displayed for 'Selected Subgroup List' field.</div>"),
	Click_AddButton_SS("'Add' button"),

	Click_NextButton_DC("Click on 'Next' button"),
	Click_NextButton_AC("Options to select 'Session Information Management' and 'Trainees' should be available.</div>"),
	Click_NextButton_AR("Options to select 'Session Information Management' and 'Trainees' are available.</div>"),
	Click_NextButton_SS("'Next' button."),

	sessionInfoManagement_DC("Click on 'Session Information Management'"),
	sessionInfoManagement_AC("Option to select 'Online and Offline' should be available.</div>"),
	sessionInfoManagement_AR("Option to select 'Online and Offline' are available.</div>"),
	sessionInfoManagement_SS("'Session Information Management'"),

//	Enter_Online_DC("Enter 'On-Line'"),
	// Enter_Online_AC("'On-Line' Option" +"</br>" + "should be displayed."),
	// Enter_Online_AR("Session Informatio")

	Select_Online_DC("Select 'Session Information Management' as 'On-Line'"),
	Select_Online_AC("Selection should be accepted.</div>"),
	Select_Online_AR("Selection is getting accepted.</div>"), 
	Select_Online_SS("'On-Line'"),

	AddItem_Trainee_DC("Click on 'Add Item' for Select Trainees List' field and click 'OK' at alert."),
	AddItem_Trainee_AC("'Trainee List' window should be displayed.</div>"),
	AddItem_Trainee_AR("'Trainee List' window is getting displayed.</div>"),
	AddItem_Trainee_SS("'Trainee List' window"),

	Click_SearchThisPage_DC("Click on 'Search This Page' icon."),
	Click_SearchThisPage_AC("Search This Page' text box should be displayed.</div>"),
	Click_SearchThisPage_AR("Search This Page' text box is getting displayed.</div>"),
	Click_SearchThisPage_SS("'Trainee List' window"),

	Enter_TraineeName_DC("Enter the valid 'Employee Name' in 'Search This Page' textbox"),
	SendTraineeName_AR("'Employee Name'"), 
	SendTraineeName_SS("'Employee Name'"),

	Click_AddButtonTrainee_DC("Click on 'Add' button."),

	Click_AddButtonTrainee_AC("Selected Employee(s) count should be displayed for 'Total Selected Trainees' field.</div>" 
	+ "<div><b>*</b> Option to select the 'Training Method' should be displayed.</div>"),

	Click_AddButtonTrainee_AR("Selected Employee(s) count are getting displayed for 'Total Selected Trainees' field.</div>" 
			+ "<div><b>*</b> Option to select the 'Training Method' is getting displayed.</div>"),

	Click_AddButtonTrainee_SS("'Add' button"),

	Select_DR_DC("Select 'Document Reading' as 'Training Method'"),

	Select_DR_AC("The option to select the 'Assessment Required' with 'Yes and No/Verbal' options should be available.</div>"),

	Select_DR_AR("The option to select the 'Assessment Required' with 'Yes and No/Verbal' options are available.</div>"),

	Select_DR_SS("'Documemnt Reading'"),

	Assessment_No_DC("Select 'Assessment" + "</br>" + "Required' as 'No/Verbal'."),
	Assessment_No_AC("Selected option should be accepted for 'Assessment Required' field.</div>"),
	Assessment_No_AR("Selected option is getting accepted for 'Assessment Required' field.</div>"),
	Assessment_No_SS("'Assessment Required'"),

	SubmitwithEsign_AC("'Meaning of This Electronic Signature' window should be displayed as 'Course: Registration Initiation'.</div>"),
	SubmitCSwithEsign_AC("'Meaning of This Electronic Signature' window should be displayed as 'Course Session: Registration Initiation'.</div>"),
	SubmitCSwithEsign_AR("'Meaning of This Electronic Signature' window is getting displayed as 'Course Session: Registration Initiation'.</div>"),
	

	SubmitwithEsign_AR("'Meaning of This Electronic Signature' window is getting displayed as 'Course: Registration Initiation'.</div>"),
	SubmitwithEsign_SS("'E-Sign'"),
	//Respond To Document Reading:
	
	SubmitwithEsignRSDR_AC("'Meaning of This Electronic Signature' window should be displayed as 'Respond To Document Reading:'.</div>"),
	
	SubmitwithEsignRSDR_AR("'Meaning of This Electronic Signature' window is getting displayed as 'Respond To Document Reading:'.</div>"),
	

	Next2_DC("Click on 'Next' button."),
	Next2_AC("All the selected/entered particulars should be displayed.</div>"+
	"<div><b>*</b> The option to select documents should be available.</div>"),
	Next2_AR("All the selected/entered particulars are getting displayed.</div>"+
			"<div><b>*</b> The option to select documents are available.</div>"),
	Next2_SS("'Next' button"),

	Select_DC("Select 'Select All' checkbox"),
	Select_AC("Selected document(s) should be accepted."),
	Select_AR("Selected document(s) is getting accepted."),
	Select_SS("Select Documents."),

	Esign_Proceed_DC("Click on 'Proceed' button."),
	Esign_TopicProceed_AC("'Topic Registration Initiated Unique Code: Unique Code confirmation message should be displayed with 'Done' button.</div>"),
	Esign_TopicProceed_AR("'Topic Registration Initiated Unique Code: Unique Code confirmation message is getting displayed with 'Done' button.</div>"),

	Esign_Proceed_AC("'Course Sessions Registration Initiated Unique Code:Unique Code' confirmation message should be displayed with 'Done' button.</div>"),

	Esign_Proceed_AR("Course Sessions Registration Initiated Unique Code: Unique Code' confirmation message is getting displayed with 'Done' button.</div>"),
	Esign_Proceed_SS("'Proceed'"),
	
	Esign_ProceedCourse_AC("'Course  Registration Initiated Unique Code:Unique Code' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedCourse_AR("'Course  Registration Initiated Unique Code:Unique Code' confirmation message is getting displayed with 'Done' button.</div>"),
	

	
	Esign_ProceedRSPDOC_AC("'Document Reading Status Updated' confirmation message should be displayed with 'Done' button."),
	Esign_ProceedRSPDOC_AR("'Document Reading Status Updated' confirmation message is getting  displayed with 'Done' button."),
	
	Click_DoneatConfig_DC("Click on 'Done' button."),
	Click_DoneatTopicConfig_AC("'Topic Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatCSConfig_AC("'Course Session Configuration Registration' screen should be displayed.</div>"),
	
	Click_DoneatCSConfig_AR("'Course Session Configuration Registration' screen is getting displayed.</div>"),

	Click_DoneatTopicConfig_AR("'Topic Configuration Registration' screen is getting displayed.</div>"),

	Click_DoneatCourseConfig_AC(
			"'Course Configuration" + "</br>" + "Registration' screen" + "</br>" + "should be displayed."),
	Click_DoneatCourseConfig_AR("'Course Configuration Registration' screen is getting displayed.</div>"),

	Click_DoneatConfig_SS("'Done'"),

	Click_Done_DC("Click 'Done' button."),
	Click_Done_AC("Home screen of the login user should be displayed.</div>"),

	Click_Done_AR("Home screen of the login user is getting displayed.</div>"),
	Click_Done_SS("'Done'"),

	CM_AuditTrailsMenus_DC("Click 'Course Manager'."),
	CM_AuditTrailsMenus_AC("'Audit trails' menu" + "</br>" + "should be displayed" + "</br>" + "and other menus"
			+ "</br>" + "should be" + "</br>" + "displayed(if any)."),
	CM_AuditTrailsMenus_AR(
			"'Course Manager'menu" + "</br>" + "and 'Audit Trails' menu" + "</br>" + "is getting displayed"),
	CM_AuditTrailsMenus_SS("Menus"),

	CM_CSAuditTrails_DC("Click 'Audit Trails' menu"),
	CM_CSAuditTrails_AC("'Course Session' menu" + "</br>" + "should be displayed" + "</br>" + "and other menus"
			+ "</br>" + "should be" + "</br>" + "displayed(if any)."),
	CM_CSAuditTrails_AR(
			"'Audit trails'menu" + "</br>" + "and 'Course Session' menu" + "</br>" + "is getting displayed"),
	CM_CSAuditTrails_SS("Menus"),

	Click_CSAudittrails_DC("Click 'Course Session' menu."),
	Click_CSAudittrails_AC("'Course Session Audit Trails' screen should be displayed.<div>"),
	Click_CSAudittrails_AR("'Course Session Audit trails' screen is getting displayed.</div>"),
	Click_CSAudittrails_SS("'Course Session Audit Trails'"),

	SearchBy_AT_AC("Option to search with 'Top 250 Records, Course Session Name, Unique Code and Initiated Between' should be available."),
	SearchBy_AT_AR("Option to search with 'Top 250 Records, Course Session Name, Unique Code and Initiated Between' are available.</div>"),
	SearchBy_AT_SS("'Search By'"),

	Select_CoursSessionName_DC("Select 'Course Session Name'."),
	Select_CoursSessionName_AC("Selection should be accepted.<div>"),
	Select_CoursSessionName_AR("Selection is getting accepted.<div>"),
	Select_CoursSessionName_SS("'Course Session Name'."),

	Like_CSName_DC("Enter the above registered 'Course Session Name' in 'Like' field."),
	Like_CSName_AC("<div><b>*</b> Entered 'value should" + "</br>" + "be displayed.</div>"),
	Like_CSName_AR("'CourseSession Name'"), 
	Like_CSName_SS("'Course Session Name'"),

	AT_Click_RegisteredCS_DC("Click on the above registered 'Course Session Name'."),
	AT_Click_RegisteredCS_AC("'Course Sessions-Audit Trails: Revision No.: 0 - Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> All the entered/selected at registration details should be displayed in read only mode.<div>"),
	AT_Click_RegisteredCS_AR("'Course Sessions-Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> All the entered/selected at registration details are getting displayed in read only mode.<div>"),

	AT_Click_RegisteredCS_SS("'Course Sessions-" + "</br>" + "Audit Trails."),

	Close_AuditTrails_DC("Click on 'Close' icon."),
	Close_AuditTrailsTopic_AC("'Topic Audit' screen should be displayed.</div>"),
	Close_AuditTrailsTopic_AR("'Topic Audit trails' screen is getting displayed."),
	
	Close_AuditTrails_Course_AC("'Course Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_Course_AR("'Course Audit trails' screen  is getting displayed.</div>"),
	
	Close_AuditTrailsCS_AC("'Course Session Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrailsCS_AR("'Course Session Audit trails' screen  is getting displayed.</div>"),
	Close_AuditTrails_SS("'Audit Trails'"),

	// Respond Document Reading
	Respond_menu_DC("Click on 'Respond' menu."),
	Respond_menu_AC("Assigned sub menus under 'Respond' menu for the user should  be displayed.</div>"),
	Respond_menu_AR("Assigned sub menus under 'Respond' menu for the user are getting  displayed.</div>"),
	Respond_menu_SS("'Menus'"),

	Click_DRMenu_DC("Click on 'Document Reading'."),

	Click_DRMenu_AC("'Document Reading Course List' screen should be diplayed.</div>" 
	+ "<div><b>*</b> Screen should contain 'Regular Course(s), Mandatory Course(s) and Open Self-Study Course(s) tabs.</div>"
			+ "<div><b>*</b> By default 'Regular Course(s)' tab should be" + "</br>" + "selected."),
	Click_DRMenu_AR("'Document Reading Course List' screen is getting diplayed.</div>" 
	+ "<div><b>*</b> Screen contains 'Regular Course(s), Mandatory Course(s) and Open Self-Study Course(s) tabs.</div>"
			+ "<div><b>*</b> By default 'Regular Course(s)' tab is getting" + "</br>" + "selected."),
	Click_DRMenu_SS("'Document Reading'"),

	Enter_CSName_RSPDR_DC("Enter above registered Course Session Name in 'Search This Page' text box."),
	Enter_CSName_RSPDR_AR("'CourseSession Name'"),
	Enter_CSName_RSPDR_SS("'Course Session Name'"),

	Click_CSNameRSDR_DC("Click on the above registred Course Session Name."),
	Click_CSNameRSDR_AC("'Document Reading Course List' screen should be diplayed.</div>"
	+ "<div><b>*</b> Screen should contain 'Course Name, Course Code, Training Type, Course Session Name, Topic Name and Document List' details accurately.</div>" 
			+ "<div><b>*</b> Option to read the document should be available.</div>"),
	Click_CSNameRSDR_AR("'Document Reading Course List' screen is getting diplayed.</div>"
	+ "<div><b>*</b> Screen contains 'Course Name, Course Code, Training Type, Course Session Name, Topic Name and Document List' details accurately.</div>" 
			+ "<div><b>*</b> Option to read the document is available.</div>"),
	
	Click_CSNameRSDR_SS("Document Reading"),
	
	click_To_Read_DC("Click on 'Click To Read' against the required document"),
	click_To_Read_AC("Preview of the document should be displayed.</div>"),
	click_To_Read_AR("Preview of the document is getting displayed.</div>"),
	click_To_Read_SS("Preview"),

	Preview_Close_DC("Click on Close icon."),
	Preview_Close_AC("'Document Reading Course List' screen should be diplayed.</div>" 
	+ "<div><b>*</b> Option to select the 'Mark Reading Status As' should be enabled.</div>"),
	Preview_Close_AR("'Document Reading Course List' is getting diplayed.</div>" 
			+ "<div><b>*</b> Option to select the 'Mark Reading Status As' is getting enabled.</div>"),
	Preview_Close_SS("Mark Reading Status As."),
	
	
	Select_Completed_DC("Select Mark Reading Status As' as 'Completed'."),
	Select_Completed_AC("Selection should be acccepted.</div>"),
	Select_Completed_AR("Selection is getting acccepted."),
	Select_Completed_SS("Completed"),
	
	SubmitwithEsignRSPDR_AC("<div><b>*</b> 'Meaning of This" + "</br>" + "Electronic Signature'</div>"
			+ "<div><b>*</b> Window should be" + "</br>" + "displayed as 'Respond" + "</br>"
			+ "To Document Reading:'</div>"),
//	Esign_ProceedRSPDR_AC("<div><b>*</b> Document Reading" + "</br>" + "Status Update'" + "</br>"
//			+ "confirmation message" + "</br>" + "should be displayed.</div>"),

	CourseManagerMenu_Course_AC("<div><b>*</b> 'CourseManager' menu" + "</br>" + " should be displayed," + "</br>"
			+ "other menus should" + "</br>" + "be displayed(if any).</div>"),
	CourseManagerMenu_Course_AR("'CourseManager' menu" + "</br>" + "and other menus" + "</br>" + "are displayed"),
	CourseManagerMenu_Course_SS("'CourseManager'"),

	// Reports

	ClickReports_DC("Click on 'Reports' menu."),
	ClickReports_AC("Assigned submenus for the user under 'Reports' menu should be displayed</div>"),
	ClickReports_AR("Assigned submenus for the user under 'Reports' menu is getting displayed</div>"),
	ClickReports_SS("'Reports'"),

	ClickDWCS_DC("Click 'Date Wise Course Session Report'"),
	ClickDWCS_AC("'Date Wise Course Session Report' screen should be displayed.</div>" 
	+ "<div><b>*</b> Screen should contain 'Course Session Name, Course Unique Code, Start Date and End Date' columns.</div>"),
	ClickDWCS_AR("'Date Wise Course Session Report' screen is getting displayed.</div>" 
			+ "<div><b>*</b> Screen contains 'Course Session Name, Course Unique Code, Start Date and End Date' columns.</div>"),
	ClickDWCS_SS("'Date Wise Course Session Report'"),

	Click_ToggleBtn_DC("Click on 'Toggle dropDown'"),
	Click_ToggleBtn_AC("Option to search with 'Course Session Name, Course Unique Code, Training Method, Start Date, and End Date' should be available.</div>"),
	Click_ToggleBtn_AR("Option to search with 'Course Session Name, Course Unique Code, Training Method, Start Date, and End Date' are available.</div>"),
	Click_ToggleBtn_SS("'Toggle dropdown'"),

	Unselect_CS_Name_DC("Unselect the 'Course" + "</br>" + "Session Name'"),
	Unselect_CS_Name_AC("'Course Session Name' should be unselected.</div>"),
	Unselect_CS_Name_AR("'Course Session Name' is getting unselected.</div>"),
	Unselect_CS_Name_SS("Unselect 'Course Session Name'"),

	CS_Name_Like_DC("Enter the above document reading completed 'Course Session Name'."),
	CS_Name_Like_AC("<div><b>*</b> Entered value should" + "</br>" + "be displayed.</div>"),
	CS_Name_Like_AR("<div><b>*</b> Entered value is getting" + "</br>" + "displayed.</div>"),
	CS_Name_Like_SS("'Course Session Name'"),

	TrainingMethodDrpDwn_DC("Click 'Training Method'"),
	TrainingMethodDrpDwn_AC("Option to select 'Classroom Type and Document Reading' should be available.</div>"),
	TrainingMethodDrpDwn_AR("Option to select 'Classroom Type and Document Reading' are available.</div>"),
	TrainingMethodDrpDwn_SS("'Training Method'"),

	SelectDocumentReading_DC("Select 'Document Reading'."),
	SelectDocumentReading_AC("Selection should be accepted.</div>"),
	SelectDocumentReading_AR("Selection is getting accepted."),
	SelectDocumentReading_SS("'Document Reading'"),

	ClickViewReport_DC("Click 'View Report'."),
	ClickViewReport_AC("Records should be displayed based on the search criteria.</div>"),
	ClickViewReport_AR("Records are getting displayed based on the search criteria.</div>"),
	ClickViewReport_SS("'View Report'"),

	ClickCSNameReport_DC("Click on the above document reading completed Course Session Name."),
	ClickCSNameReport_AC("'Training Attendance Record' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should contain all the details of Topic Covered, Course and Employee Name.</div>"),
	ClickCSNameReport_AR("'Training Attendance Record' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen contains all the details of Topic Covered, Course and Employee Name.</div>"),
	ClickCSNameReport_SS("'Training Attendance'"),

	ClickEmpNameReport_DC("Click on the employee name who is responded to document reading."),
	ClickEmpNameReport_AC("'Trainee Report' screen should be displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status should be updated accurately in the screen.'</div>"
			+"<div><b>*</b> 'Employee Status' should be displayed as 'Qualified'.</div>"),
	ClickEmpNameReport_AR("'Trainee Report' screen is getting displayed.</div>"
			+ "<div><b>*</b> 'Start Date, End Date and Employee Status are getting updated accurately in the screen.'</div>"
			+"<div><b>*</b> 'Employee Status' should is getting displayed as 'Qualified'.</div>"),
	ClickEmpNameReport_SS("'Trainee Report'"),

	ClickDocRead_TC_DC("Click on 'Training Certificate' hyperlink."),
	ClickDocRead_TC_AC("'Training Certificate' screen should be displayed and all the details should be displayed accurately."
			+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickDocRead_TC_AR("'Training Certificate' screen should be displayed and all the details should be displayed accurately."
			+ "<div><b>*</b> Result should be read as 'Qualified'.</div>"),
	ClickDocRead_TC_SS("'TrainingCertificate'");

	private final String title;

	PageTitles(String title) {

		this.title = title;

	}

	public String getTitle() {
		return title;
	}

}
