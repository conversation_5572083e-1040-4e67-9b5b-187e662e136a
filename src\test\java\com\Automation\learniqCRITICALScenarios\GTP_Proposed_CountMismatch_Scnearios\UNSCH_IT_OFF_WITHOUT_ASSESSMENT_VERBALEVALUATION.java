package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class UNSCH_IT_OFF_WITHOUT_ASSESSMENT_VERBALEVALUATION extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/UNSCH_IT_OFF_WITHOUT_ASSESSMENT_VERBALEVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/NewXYZ.xlsx";
	public UNSCH_IT_OFF_WITHOUT_ASSESSMENT_VERBALEVALUATION() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Configuration, Registration, Approve with Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Configuration, Registration, Approve with Audit Trails");
		}
		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Configuration, Registration, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Configuration, Registration, Approve with Audit Trails");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for Trainer Configuration, Modification, Approve with
	// Audit Trails

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Configuration_Modification_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Configuration, Modification, Approve with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Configuration, Modification, Approve with Audit Trails");
		}

		trainer.trainer_Modification_AuditTrails(testData);

	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Configuration_Registration_Approve(HashMap<String, String> testData)
			throws InterruptedException {

		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Configuration, Registration, Approve with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Configuration, Registration, Approve with AuditTrails");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();

		verifyCSScreen.courseSession_Offline_WithOutExam_VerbalEvaluation(testData);

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();
	}

	// Test Method for BatchFormation Configuration, Registration with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Configuration_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Configuration, Registration with AuditTrails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Configuration, Registration with AuditTrails");
		}

		BatchFormation.batchFormation_Offline_Users_CountMisMatch(testData);

	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}

		RecordAttendance.recordAttendance_Offline_VerbalEvaluation_CountMismatch(testData);

	}

	@Test(priority = 7, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Course Session screen after diff states")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session screen after diff states");
		}
		verifyCSScreen
				.checkCourseSession_IT_Without_Assessment_Evaluation_Verbal_After_Keeping_Employees_In_Different_States();
	}

	@Test(priority = 8, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Course Session Report after diff states")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session Report after diff states");
		}
		CSRReport.TBPCSRReport_IT_WithOut_Assesment_Evaulation_Verbal_After_keping_Emplpoyees_In_Diff_States();
	}

	@Test(priority = 9, enabled = true)
	public void courseRetraining() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Course Retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Retraining");
		}
		CourseRetraining.UnSchduled_course_Retraining_IT_Offline_WithOut_Assessment_VerbalEvaluation();
	}

	@Test(priority = 10, enabled = true)
	public void verifyCourseSessionScreenAfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Course Session screen (Unsch) after Course Retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session screen (Unsch) after Course Retraining");
		}
		verifyCSScreen.IT_With_Assessment_VerifyCourseSession_Screen_After_CourseRetraining();
	}

	@Test(priority = 11, enabled = true)
	public void verifyCSR_AfterCourseRetraining() {
		if (isReportedRequired == true) {
			test = extent
					.createTest(
							"Course Session Report after Course Retraining")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID()).assignCategory(
							"Course Session Report after Course Retraining");
		}
		CSRReport.TBPCSRReport_Unsch_IT_OffflineWithoutAssesment_Verbal_After_CourseRetraining();
	}

}