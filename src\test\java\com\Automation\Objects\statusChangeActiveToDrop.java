package com.Automation.Objects;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;

public class statusChangeActiveToDrop extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SGTestData/Group.xlsx";
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "GroupConfig");

	public statusChangeActiveToDrop() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	@DataProvider(name = "Configuration")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Configuration
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 1, dataProvider = "Configuration", enabled = false)
	public void configurationApp(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("Configuration Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configuration Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.group_Configuration(testData);

		Logout.signOutPage();

	}

	ExcelUtilUpdated topicData1 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration1")
	public Object[][] getGroupDataST() throws Exception {
		Object[][] obj = new Object[topicData1.getRowCount()][1];
		for (int i = 1; i <= topicData1.getRowCount(); i++) {
			HashMap<String, String> testData = topicData1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 2, dataProvider = "groupRegistration1")
	public void groupRegistration1(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData11 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprove1")
	public Object[][] getGroupStAPPData() throws Exception {
		Object[][] obj = new Object[topicData11.getRowCount()][1];
		for (int i = 1; i <= topicData11.getRowCount(); i++) {
			HashMap<String, String> testData = topicData11.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 3, dataProvider = "groupApprove1")
	public void groupRegistrationApproveSGTAP(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

//	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Course");
//
//	@DataProvider(name = "course")
//	public Object[][] getGroupReturnData() throws Exception {
//		Object[][] obj = new Object[topicData2.getRowCount()][1];
//		for (int i = 1; i <= topicData2.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 3, dataProvider = "course",enabled = false)
//	public void groupModification(HashMap<String, String> testdata) {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Verify Group Modification");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		group_Modification.groupModification(testdata);
//		group_Modification.groupModificationWithAuditTrails(testdata);
//		Logout.signOutPage();
//	}

//	Active To InActive 
	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "statuschange");

	@DataProvider(name = "Statuschange1")
	public Object[][] getGroupStatusChange1() throws Exception {
		Object[][] obj = new Object[topicData2.getRowCount()][1];
		for (int i = 1; i <= topicData2.getRowCount(); i++) {
			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "Statuschange1")
	public void groupStatusChang1e(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("check the Group Status Change (Active to Inactive) Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("check the Group Status Change (Active to Inactive) Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChange(testdata);
		sYS_Group_StatusChange.groupStatusChangeWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData12 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification1")
	public Object[][] getGroupVeriftModData() throws Exception {
		Object[][] obj = new Object[topicData12.getRowCount()][1];
		for (int i = 1; i <= topicData12.getRowCount(); i++) {
			HashMap<String, String> testData = topicData12.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "verifyModification1")
	public void VerifyModificationAgainSt(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the inactive Group is not available for modification at Group Modification screen.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the inactive Group is not available for modification at Group Modification screen.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.verifiModificationGroup(testData);
		Logout.signOutPage();
	}

//	Drop
	ExcelUtilUpdated topicData4 = new ExcelUtilUpdated(ExcelPath, "StatusChangeDrop");

	@DataProvider(name = "StatuschangeApprove1")
	public Object[][] getGroupStatusChangeAp1p() throws Exception {
		Object[][] obj = new Object[topicData4.getRowCount()][1];
		for (int i = 1; i <= topicData4.getRowCount(); i++) {
			HashMap<String, String> testData = topicData4.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "StatuschangeApprove1")
	public void groupStatusChangeApproveST(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Status Change (Active to Inactive) Drop Flow and ensures its updates in the Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Status Change (Active to Inactive) Drop Flow and ensures its updates in the Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChangeDrop(testdata);
		sYS_Group_StatusChange.groupStatusChangeDropWithAuditTrails(testdata);
		Logout.signOutPage();
	}

//	Again Group Registration 
	ExcelUtilUpdated topicData5 = new ExcelUtilUpdated(ExcelPath, "GroupRegistration");

	@DataProvider(name = "groupRegistration2")
	public Object[][] getGroupData() throws Exception {
		Object[][] obj = new Object[topicData5.getRowCount()][1];
		for (int i = 1; i <= topicData5.getRowCount(); i++) {
			HashMap<String, String> testData = topicData5.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Registration Flow
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 7, dataProvider = "groupRegistration2")
	public void groupRegistration(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Flow With Audit trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("The following steps check the Group Registration Flow With Audit trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Group.groupRegistration(testData);
		Group.groupRegistrationWithAuditTrails(testData);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData3 = new ExcelUtilUpdated(ExcelPath, "GroupApprove");

	@DataProvider(name = "groupApprove2")
	public Object[][] getGroupVerifyData() throws Exception {
		Object[][] obj = new Object[topicData3.getRowCount()][1];
		for (int i = 1; i <= topicData3.getRowCount(); i++) {
			HashMap<String, String> testData = topicData3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	/**
	 * Method is for Group Approvals
	 * 
	 * @throws Throwable
	 * 
	 */
	// ---------------------------------------------------------------------------------------------------------------------------
	@Test(priority = 8, dataProvider = "groupApprove2")
	public void groupRegistrationApprove(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("The following steps check the Group Registration Approval Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the Group Registration Approval Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		Group.groupRegistrationApproval(testData);
		Group.groupRegistrationApproveWithAuditTrails_Yes(testData);

		Logout.signOutPage();
	}

//	ExcelUtilUpdated topicData2 = new ExcelUtilUpdated(ExcelPath, "Course");
//
//	@DataProvider(name = "course")
//	public Object[][] getGroupReturnData() throws Exception {
//		Object[][] obj = new Object[topicData2.getRowCount()][1];
//		for (int i = 1; i <= topicData2.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData2.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 3, dataProvider = "course",enabled = false)
//	public void groupModification(HashMap<String, String> testdata) {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Verify Group Modification").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Verify Group Modification");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		group_Modification.groupModification(testdata);
//		group_Modification.groupModificationWithAuditTrails(testdata);
//		Logout.signOutPage();
//	}

	ExcelUtilUpdated topicData6 = new ExcelUtilUpdated(ExcelPath, "Statuschange");

	@DataProvider(name = "statuschange5")
	public Object[][] getGroupStatusChange() throws Exception {
		Object[][] obj = new Object[topicData6.getRowCount()][1];
		for (int i = 1; i <= topicData6.getRowCount(); i++) {
			HashMap<String, String> testData = topicData6.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "statuschange5")
	public void groupStatusChange(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("check the Group Status Change (Active to Inactive) Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("check the Group Status Change (Active to Inactive) Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChange(testdata);
		sYS_Group_StatusChange.groupStatusChangeWithAuditTrails(testdata);
		Logout.signOutPage();
	}

//	ExcelUtilUpdated topicData9 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");
//
//	@DataProvider(name = "verifyModification")
//	public Object[][] getGroupApproveDataA() throws Exception {
//		Object[][] obj = new Object[topicData9.getRowCount()][1];
//		for (int i = 1; i <= topicData9.getRowCount(); i++) {
//			HashMap<String, String> testData = topicData9.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
//
//	@Test(priority = 4, dataProvider = "verifyModification")
//	public void VerifyModificationAgain1(HashMap<String, String> testData) {
//
//		if (isReportedRequired == true) {
//			test = extent.createTest("Group Registration With AuditTrails")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Group Registration With AuditTrails");
//		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//		epiclogin.plant1();
//
//		sYS_Group_StatusChange.verifiModificationGroup(testData);
//		Logout.signOutPage();
//	}

	ExcelUtilUpdated topicData7 = new ExcelUtilUpdated(ExcelPath, "StatusChangeApprove");

	@DataProvider(name = "statuschangeApprove2")
	public Object[][] getGroupStatusChangeApp() throws Exception {
		Object[][] obj = new Object[topicData7.getRowCount()][1];
		for (int i = 1; i <= topicData7.getRowCount(); i++) {
			HashMap<String, String> testData = topicData7.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 10, dataProvider = "statuschangeApprove2")
	public void groupStatusChangeApprove(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Status Change (Inactive to Active) Approval and ensures its updates in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Status Change (Inactive to Active) Approval and ensures its updates in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChangeApprove(testdata);
		sYS_Group_StatusChange.groupStatusChangeApproveWithAuditTrails(testdata);
		Logout.signOutPage();
	}

//	InActive 
	ExcelUtilUpdated topicData8 = new ExcelUtilUpdated(ExcelPath, "statuschange");

	@DataProvider(name = "Statuschange3")
	public Object[][] getGroupStatusChangeInActToAc() throws Exception {
		Object[][] obj = new Object[topicData8.getRowCount()][1];
		for (int i = 1; i <= topicData8.getRowCount(); i++) {
			HashMap<String, String> testData = topicData8.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 11, dataProvider = "Statuschange3")
	public void groupStatusChangeInAcToActive(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("check the Group Status Change (Active to Inactive) Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("check the Group Status Change (Active to Inactive) Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChangeInActive(testdata);
		sYS_Group_StatusChange.groupStatusChangeInActiveWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData14 = new ExcelUtilUpdated(ExcelPath, "StatusChangeApprove");

	@DataProvider(name = "statuschangeApprove3")
	public Object[][] getGroupStatusChangeAppSt() throws Exception {
		Object[][] obj = new Object[topicData14.getRowCount()][1];
		for (int i = 1; i <= topicData14.getRowCount(); i++) {
			HashMap<String, String> testData = topicData14.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 12, dataProvider = "statuschangeApprove3")
	public void groupStatusChangeApproveST4(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the Group Status Change (Inactive to Active) Approval and ensures its updates in the Audit Trails.")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID")).assignCategory(
							"The following steps check the Group Status Change (Inactive to Active) Approval and ensures its updates in the Audit Trails.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChangeApprove(testdata);
		sYS_Group_StatusChange.groupStatusChangeApproveWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	ExcelUtilUpdated topicData9 = new ExcelUtilUpdated(ExcelPath, "VerifyModification");

	@DataProvider(name = "verifyModification3")
	public Object[][] getGroupApproveData() throws Exception {
		Object[][] obj = new Object[topicData9.getRowCount()][1];
		for (int i = 1; i <= topicData9.getRowCount(); i++) {
			HashMap<String, String> testData = topicData9.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 13, dataProvider = "verifyModification3")
	public void VerifyModificationAgain(HashMap<String, String> testData) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest(
					"The following steps check the modified and approved Group is available for further modification at Group Modification screen.")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory(
							"The following steps check the modified and approved Group is available for further modification at Group Modification screen.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));

		epiclogin.plant1();

		sYS_Group_StatusChange.verifiModificationGroup(testData);
		Logout.signOutPage();
	}

//	
	ExcelUtilUpdated topicData13 = new ExcelUtilUpdated(ExcelPath, "Statuschange");

	@DataProvider(name = "statuschange2")
	public Object[][] getGroupStatusChangeAgaing() throws Exception {
		Object[][] obj = new Object[topicData13.getRowCount()][1];
		for (int i = 1; i <= topicData13.getRowCount(); i++) {
			HashMap<String, String> testData = topicData13.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 14, dataProvider = "statuschange2")
	public void groupStatusChangeA1(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent.createTest("check the Group Status Change (Active to Inactive) Flow With Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("check the Group Status Change (Active to Inactive) Flow With Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChange(testdata);
		sYS_Group_StatusChange.groupStatusChangeWithAuditTrailsADA(testdata);
		Logout.signOutPage();
	}

//Drop			

	ExcelUtilUpdated topicData10 = new ExcelUtilUpdated(ExcelPath, "StatusChangeDrop");

	@DataProvider(name = "StatuschangeDropDp")
	public Object[][] getGroupStatusChangeInDrop() throws Exception {
		Object[][] obj = new Object[topicData10.getRowCount()][1];
		for (int i = 1; i <= topicData10.getRowCount(); i++) {
			HashMap<String, String> testData = topicData10.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 15, dataProvider = "StatuschangeDropDp")
	public void groupStatusChangeInActiveDrop(HashMap<String, String> testdata) throws Throwable {

		if (isReportedRequired == true) {
			test = extent
					.createTest("The following steps check the activated Group is available for modification at Group ")
					.assignAuthor(ConfigsReader.getPropValue("GroupAppID"))
					.assignCategory("The following steps check the activated Group is available for screen.");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("GroupAppID"),
				ConfigsReader.getPropValue("GroupPwd"));
		epiclogin.plant1();

		sYS_Group_StatusChange.groupStatusChangeInActiveDrop(testdata);
		sYS_Group_StatusChange.groupStatusChangeInActiveDropWithAuditTrails(testdata);
		Logout.signOutPage();
	}

	@AfterTest

	public void afterTest() {

		extent.flush();
		MyScreenRecorder.stopRecording();

		driver.quit();
	}
}
