package com.Automation.learniqCRITICALScenarios.SelfStudyFlows.OneTimeToRefresher;

import java.util.HashMap;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_SelfStudyCourse;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Self_Study_Open_For_All_WithOut_Asessment extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Self_Study_Flows/Self_Study_Open_For_All_WithOut_Asessment.xlsx";
	
	public Self_Study_Open_For_All_WithOut_Asessment() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	
	
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		InitiateTopic.topic_Registration(testData);

	}
	
	
	
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Self-Study Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Self-Study Course Registration");
		}
		selfStudyCourse.Self_Study_Course_Registration_OpenForAll(testData);
		Logout.signOutPage();
	}
	
	
	
	
	
	@Test(priority = 3,enabled = true)
	public void Initiate_Self_Study_Course() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.QualifieduserID)

					.assignCategory("Initiate Self Study Course");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.QualifieduserID,
				CM_SelfStudyCourse.QualifiedPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}
	
	
	@Test(priority = 4,enabled = true)
	public void respondDocumentReading_Completed() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading and complete")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading and complete");
		}

		RespondDR.respondDocReading_Self_Study_Course("Completed",CM_SelfStudyCourse.QualifiedPSW );
		Logout.signOutPage();
	}
	
	
	@Test(priority = 5, enabled = true)
	public void Initiate_Self_Study_Course_By_InProgressUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.DRInProgressUserID)

					.assignCategory("Initiate Self Study Course");
		}
		
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
				CM_SelfStudyCourse.DRInProgressPSW);
		epiclogin.plant1();
		selfStudyCourse.initiate_Self_Study_Course();
	}
	@Test(priority = 6, enabled = true)
	public void respondDocumentReading_InProgress() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading and mark as In- Progress")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading and mark as In- Progress");
		}

		RespondDR.respondDocReading_Self_Study_Course("InProgress",CM_SelfStudyCourse.DRInProgressPSW );


	}
	
	
	@Test(priority = 7, enabled = true)
	public void IERReportForQualifieduser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Qualified Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Qualified Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID,
					Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);

			//Logout.signOutPage();
	}
	
	@Test(priority = 8, enabled = true)
	public void IERReportForInProgressUser() {
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for In-Progress Employee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for In-Progress Employee");
			}

			IERReport.individualEmployeeReport(CM_SelfStudyCourse.DRInProgressEmpID,
					Constants.Session_PROPOSED_FOR_RE, Constants.REType);

			Logout.signOutPage();
	}
	
	
	@Test(priority = 9, enabled = true)
	public void modifyCourse_To_Refresher() {
		if (isReportedRequired == true) {
			test = extent.createTest("Modify Course from One time to Refresher course")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Modify Course from One time to Refresher course");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		selfStudyCourse.modifyCourse_To_Refrehser();
		Logout.signOutPage();
	}
	
	@Test(priority = 10, enabled = true)
	public void Initiate_Self_Study_Course_After_CourseName_Modification() {
		if (isReportedRequired == true) {
			test = extent.createTest("Check Modified Course at Initiate Self Study Course")

					.assignAuthor(CM_SelfStudyCourse.checkCourseEmpID)

					.assignCategory("Chreck Modified Course at Initiate Self Study Course");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.checkCourseUserID,
				CM_SelfStudyCourse.checkCoursePSW);
		epiclogin.plant1();
		selfStudyCourse.validate_Initiate_Self_Study_Course_AftrCoursename_Modification();
		Logout.signOutPage();
	}
	
	
	@Test(priority = 11, enabled = true)
	public void check_CourseName_At_respond_DR_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Check modified course name at Respond DR pending")
					.assignAuthor(CM_SelfStudyCourse.ToBeRetrainedUserID)
					.assignCategory("Check modified course name at Respond DR pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
				CM_SelfStudyCourse.DRInProgressPSW);
		epiclogin.plant1();
		RespondDR.check_CourseName_At_respondDocReading();
		Logout.signOutPage();
	}
	
	
}
