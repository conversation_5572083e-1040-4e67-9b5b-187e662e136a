package com.Automation.learniqCRITICALScenarios.SelfStudyFlows.RefresherTypeCourses;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_SelfStudyCourse;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

	public class REF_Course_Self_Study_Open_For_All_WithOut_Asessment extends OQActionEngine {
		String ExcelPath = "./learnIQTestData/Self_Study_Flows/RefresherCourses/REF_Self_Study_Open_For_All_WithOut_Asessment.xlsx";
		
		public REF_Course_Self_Study_Open_For_All_WithOut_Asessment() {
	
			super(ConfigsReader.getPropValue("applicationUrl"));
		}
		
		
		ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");
	
		@DataProvider(name = "TopicReg")
		public Object[][] getTopicData() throws Exception {
			Object[][] obj = new Object[topicData.getRowCount()][1];
			for (int i = 1; i <= topicData.getRowCount(); i++) {
				HashMap<String, String> testData = topicData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	
		@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
		public void Topic_Configuration_Registration_Approve(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("Topic Registration")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Topic Registration");
			}
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			InitiateTopic.topic_Registration(testData);
	
		}
		
		
		
		ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");
	
		@DataProvider(name = "CourseRegistrationData")
		public Object[][] getCourseData() throws Exception {
			Object[][] obj = new Object[CourseData.getRowCount()][1];
			for (int i = 1; i <= CourseData.getRowCount(); i++) {
				HashMap<String, String> testData = CourseData.getTestDataInMap(i);
				obj[i - 1][0] = testData;
			}
			return obj;
		}
	
		@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
		public void Course_Configuration_Registration_Approve(HashMap<String, String> testData) {
			if (isReportedRequired == true) {
				test = extent.createTest("Self-Study Refresher Course Registration")
	
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	
						.assignCategory("Self-Study Refresher Course Registration");
			}
			selfStudyCourse.Self_Study_Course_Registration_OpenForAll(testData);
			Logout.signOutPage();
		}
		
		
		@Test(priority = 3,enabled = true)
		public void Initiate_Self_Study_Course() {
			if (isReportedRequired == true) {
				test = extent.createTest("Initiate Self Study Course")
	
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
	
						.assignCategory("Initiate Self Study Course");
			}
			
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.QualifieduserID,
					CM_SelfStudyCourse.QualifiedPSW);
			epiclogin.plant1();
			selfStudyCourse.initiate_Self_Study_Course();
		}
		
		
		@Test(priority = 4,enabled = true)
		public void respondDocumentReading_Completed() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Document Reading and complete")
						.assignAuthor(CM_VerifyCourseSessionScreen.getQualifiedTraineeID())
						.assignCategory("Respond DocumentReading and complete");
			}
	
			RespondDR.respondDocReading_Self_Study_Course("Completed",CM_SelfStudyCourse.QualifiedPSW );
			Logout.signOutPage();
		}
		
		
		@Test(priority = 5, enabled = true)
		public void Initiate_Self_Study_Course_By_InProgressUser() {
			if (isReportedRequired == true) {
				test = extent.createTest("Initiate Self Study Course")
	
						.assignAuthor(CM_SelfStudyCourse.DRInProgressUserID)
	
						.assignCategory("Initiate Self Study Course");
			}
			
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
					CM_SelfStudyCourse.DRInProgressPSW);
			epiclogin.plant1();
			selfStudyCourse.initiate_Self_Study_Course();
		}
		@Test(priority = 6, enabled = true)
		public void respondDocumentReading_InProgress() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Document Reading and mark as In- Progress")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Respond DocumentReading and mark as In- Progress");
			}
	
			RespondDR.respondDocReading_Self_Study_Course("InProgress",CM_SelfStudyCourse.DRInProgressPSW );
	
	
		}
		
		
		@Test(priority = 7, enabled = true)
		public void IERReportForQualifieduser() {
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report for Qualified Employee")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Individual Employee Report for Qualified Employee");
				}
	
				IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID,
						Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);
	
		}
		
		
		@Test(priority = 8, enabled = true)
		public void IERReportFor_TBRuser() {
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report for To BE Retrained Employee")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Individual Employee Report for To BE Retrained Employee");
				}
	
				IERReport.individualEmployeeReport(CM_SelfStudyCourse.DRInProgressEmpID,
						Constants.Session_PROPOSED_FOR_RE, Constants.REType);
	
				Logout.signOutPage();
		}
		
		@Test(priority = 9, enabled = true)
		public void modifyCourse_CourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Modify Course by selecting course retraining")
						.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
						.assignCategory("Modify Course by selecting course retraining");
				}
	
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			selfStudyCourse.modifyCourse_CourseRetraining();
				Logout.signOutPage();
		}
		
		
		
		
		@Test(priority = 10,enabled = true)
		public void Initiate_Self_Study_Course_For_QualifiedUser_After_ModifyCourse_CourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Check Course is available at Initiaite Self Study for already qualified user after modifying course by selecting course retraining")
	
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
	
						.assignCategory("Check Course is available at Initiaite Self Study for already qualified user after modifying course by selecting course retraining");
			}
			
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.QualifieduserID,
					CM_SelfStudyCourse.QualifiedPSW);
			epiclogin.plant1();
			selfStudyCourse.initiate_Self_Study_Course();
		}
		
		
		@Test(priority = 11,enabled = true)
		public void respondDocumentReading_Completed_For_QualifiedUser_After_ModifyCourse_CourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Document Reading and complete")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Respond DocumentReading and complete");
			}
	
			RespondDR.respondDocReading_Self_Study_Course("Completed",CM_SelfStudyCourse.QualifiedPSW );
			Logout.signOutPage();
		}
		
		@Test(priority = 12, enabled = true)
		public void Initiate_Self_Study_Course_By_InProgressUser_AfterCourseRetraning() {
			if (isReportedRequired == true) {
				test = extent.createTest("Check Course is available at Initiaite Self Study for user who are In-Progress at repsond DR after modifying course by selecting course retraining")
	
						.assignAuthor(CM_SelfStudyCourse.DRInProgressUserID)
	
						.assignCategory("Check Course is available at Initiaite Self Study for user who are In-Progress at repsond DR after modifying course by selecting course retraining");
			}
			
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_SelfStudyCourse.DRInProgressUserID,
					CM_SelfStudyCourse.DRInProgressPSW);
			epiclogin.plant1();
			selfStudyCourse.initiate_Self_Study_Course();
		}
		
		@Test(priority = 13, enabled = true)
		public void respondDocumentReading_InProgress_after_CourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Respond Document Reading and mark as In- Progress")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Respond DocumentReading and mark as In- Progress");
			}
	
			RespondDR.respondDocReading_Self_Study_Course("InProgress",CM_SelfStudyCourse.DRInProgressPSW );
	
	
		}
		
		@Test(priority = 14, enabled = true)
		public void IERReportFor_Qualifieduser_afterCourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report for Qualified Employee")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Individual Employee Report for Qualified Employee");
				}
	
				IERReport.individualEmployeeReport(CM_SelfStudyCourse.QualifiedEmpID,
						Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType);
	
		}
		
		@Test(priority = 15, enabled = true)
		public void IERReportFor_TBRuser_afterCourseRetraining() {
			if (isReportedRequired == true) {
				test = extent.createTest("Individual Employee Report for To BE Retrained Employee")
						.assignAuthor(CM_SelfStudyCourse.QualifieduserID)
						.assignCategory("Individual Employee Report for To BE Retrained Employee");
				}
	
				IERReport.individualEmployeeReport(CM_SelfStudyCourse.DRInProgressEmpID,
						Constants.Session_PROPOSED_FOR_RE, Constants.REType);
	
				Logout.signOutPage();
		}
		
		
		
		
}
