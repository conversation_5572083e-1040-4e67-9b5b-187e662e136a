<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Suite">
    <test name="test1">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_OFF_WITHOUT_ASSESSMENT_VERBALEVALUATION" />
        </classes>
    </test>

    <test name="test2">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION" />
        </classes>
    </test>

    <test name="test3">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITH_ASSESSMENT_MANUAL_EVALUATION" />
        </classes>
    </test>

    <test name="test4">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_OFF_WITHOUT_ASSESSMENT" />
        </classes>
    </test>

    <test name="test5">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_ON_WITHOUT_ASSESSMENT" />
        </classes>
    </test>

    <test name="test6">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION" />
        </classes>
    </test>

    <test name="test7">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_IT_OFF_WITHOUT_ASSESSMENT" />
        </classes>
    </test>

    <test name="test8">
        <classes>
            <class name="com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios.UNSCH_RE_ON_WITHOUT_ASSESSMENT" />
        </classes>
    </test>
</suite>
