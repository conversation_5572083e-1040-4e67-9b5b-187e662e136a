package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_ProposeInductionTraining extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement learnIQ_Icon;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[3]//a[contains(@class,'sub-menu')][contains(text(),'Propose')]")
	WebElement proposeSubMenu;

	@FindBy(id = "TMS_Course Manager_Propose_MEN63")
	WebElement inductionTrainingSubmenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']/preceding-sibling::a")
	WebElement AuditTrailsSubMenu;

	@FindBy(xpath = "//a[@id='TMS_Course Manager_Audit Trails_MEN63']")
	WebElement AuditTrailsInductionTrainingSubMenu;

	@FindBy(id = "btnModal_InductionTrng_EmpSelPopUpVc")
	WebElement addItem_EmployeeName;

	@FindBy(id = "InductionTrng_EmpSelPopUpVc_FindTxt")
	WebElement textbox_FindEmployeeName;

	@FindBy(id = "InductionTrng_EmpSelPopUpVc_DisplayBtn")
	WebElement apply_EmployeeList;

	@FindBy(xpath = "//button[@class='InductionTrng_EmpSelPopUpVcAddRemoveBtn btn btn-sm  notselected IndCheck']")
	WebElement AddButton_EmployeeList;

	@FindBy(id = "InductionTrng_EmpSelPopUpVc_selectBtn")
	WebElement AddButton2_EmployeeList;

	@FindBy(xpath = "//span[text()='Induction Course1']/ancestor::tr/td[1]/input[1]")
	WebElement CourseSelection_CheckBox;

	@FindBy(xpath = "//span[text()='Induction Course1']/ancestor::tr/td[3]/div")
	WebElement trainer_AddItemButton;

//	@FindBy(xpath = "//input[@id='InductionTrng_Trainer_195_FindTxt']")
	@FindBy(xpath = "//label[text()='Find']/following-sibling::input[contains(@id, '_FindTxt')]")
	WebElement textbox_FindEmployeeNameTrainer;

	@FindBy(id = "InductionTrng_Trainer_195_DisplayBtn")
	WebElement apply_EmployeeListTrainer;

	@FindBy(xpath = "//input[@name='recordSelection']")
	WebElement radio_Button_Trainer;

	@FindBy(id = "InductionTrng_Trainer_195_selectBtn")
	WebElement addButton_Trainer;

	@FindBy(id = "InductionTrng_TrainingDate_0_btn")
	WebElement dateField_TrainingDate;

	@FindBy(xpath = "//td[@class='today active start-date active end-date available']")
	WebElement currentDate_TrainingDate;
	
	@FindBy(xpath = "//td[@class='today weekend active start-date active end-date available']")
	WebElement weekendCurrentDate_TrainingDate;
	
	

	@FindBy(xpath = "//button[@id='btnPreview']")
	WebElement button_Preview;

	@FindBy(xpath = "//button[@class='caliber-button-primary' and @id='btnSubmit']")
	WebElement button_Submit;

	@FindBy(id = "cfnMsg_Next")
	WebElement button_Done;

	// Audit Trails

	@FindBy(xpath = "//span[@id='select2-SearchType-container']")
	WebElement dropdown_SearchBy;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/child::li[3]")
	WebElement dropdown_SearchByEmployeeName;

	@FindBy(id = "EmployeeId")
	WebElement textBox_EmployeeName;

	@FindBy(id = "displayBtn")
	WebElement apply_AuditTrails;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr")
	WebElement record_AudiTrails;

	@FindBy(id = "status_heading")
	WebElement finalStatus_Label;

	@FindBy(xpath = "//div[@id='approve-status']")
	WebElement ApproveStatus_Label;

	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement closeAuditTrails;

	@FindBy(xpath = "//span[@id='select2-InductionTrng_EmpSelPopUpVc_selectDdl-container']")
	WebElement employeeSearchByDropdown;

	@FindBy(xpath = "//ul[@id='select2-InductionTrng_EmpSelPopUpVc_selectDdl-results']/li[1]")
	WebElement employeeNameDropdownOption;

	@FindBy(xpath = "//table[@id='CrsListTab']/tbody/tr")
	List<WebElement> rows;

	public void proposeInductionTraining(HashMap<String, String> testData) {

		waitForElementVisibile(learnIQ_Icon);

		click2(learnIQ_Icon, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		waitForElementVisibile(courseManagerMenu);
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				"Course Manager menu");
		waitForElementVisibile(proposeSubMenu);
		click2(proposeSubMenu, CommonStrings.Propse_Menu_DC.getCommonStrings(),
				CommonStrings.Propse_Menu_AC.getCommonStrings(), CommonStrings.Propse_Menu_AR.getCommonStrings(),
				CommonStrings.Propse_Menu_SS.getCommonStrings());

		scrollToViewElement(inductionTrainingSubmenu);
		click2(inductionTrainingSubmenu, CommonStrings.ProposeInduction_DC.getCommonStrings(),
				CommonStrings.ProposeInduction_AC.getCommonStrings(),
				CommonStrings.ProposeInduction_AR.getCommonStrings(),
				CommonStrings.ProposeInduction_SS.getCommonStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(addItem_EmployeeName);
		click2(addItem_EmployeeName, CommonStrings.Click_SearchBy_DC.getCommonStrings(),
				CommonStrings.Click_AddItem_DC.getCommonStrings(), CommonStrings.Click_AddItem_AC.getCommonStrings(),
				CommonStrings.Click_AddItem_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(employeeSearchByDropdown);
		click2(employeeSearchByDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
				CommonStrings.Click_SearchBy_AC.getCommonStrings(), CommonStrings.Click_SearchBy_AR.getCommonStrings(),
				CommonStrings.Click_SearchBy_SS.getCommonStrings());
		click2(employeeNameDropdownOption, CommonStrings.Click_EmployeeName_DC.getCommonStrings(),
				CommonStrings.Click_EmployeeName_AC.getCommonStrings(),
				CommonStrings.Click_EmployeeName_AR.getCommonStrings(),
				CommonStrings.Click_EmployeeName_SS.getCommonStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(textbox_FindEmployeeName);
		sendKeys2(textbox_FindEmployeeName, CommonStrings.FindText_DC.getCommonStrings(),
				SSO_UserRegistration.getEmployeeName() + Constants.PERCENTAGE_SIGN, CommonStrings.FindText_AC.getCommonStrings(),
				CommonStrings.FindText_AR.getCommonStrings(), CommonStrings.FindText_SS.getCommonStrings());
		click2(apply_EmployeeList, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				"Apply button");
		TimeUtil.shortWait();
		waitForElementVisibile(AddButton_EmployeeList);
		click2(AddButton_EmployeeList, "Click on Employee 'Add' button", "Employee 'Add' button should be clicked",
				"Employee 'Add' button is clicked", "Add User button");
		TimeUtil.shortWait();
		click2(AddButton2_EmployeeList, "Click on 'Add' button", "Add button should be clicked",
				"Add button is clicked", "Add User button");
		for (WebElement ele : rows) {
			WebElement courseCheckBox = ele.findElement(By.xpath(".//td/input[@type='checkbox']"));
			WebElement trainerAddItem = ele.findElement(
					By.xpath(".//button[@type='button' and contains(@id, 'btnModal_InductionTrng_Trainer_')]"));
			WebElement trainingDate = ele.findElement(
					By.xpath(".//button[@type='button' and contains(@id, 'InductionTrng_TrainingDate_')]"));
			WebElement courseName = ele.findElement(By.xpath(".//td/span[text()]"));
			scrollToViewElement(courseName);
			if (courseName.getText().equals(testData.get("courseName"))) {
				TimeUtil.shortWait();
				click2(courseCheckBox, "Click on respective check box", "Course 'Check box' should be selected",
						"Course 'Check box' is selected", "Select Course check box");
				TimeUtil.shortWait();
				click2(trainerAddItem, "Click on 'Trainer Add Item Button'",
						"'Trainer Add Item Button' should be clicked", "'Trainer Add Item Button' is clicked",
						"Trainer add item button");
				TimeUtil.shortWait();
				WebElement employeeSearchDropdown = driver.findElement(
						By.xpath("//table[@id='CrsListTab']/tbody/tr//td/span[text()='" + testData.get("courseName")
								+ "']/parent::td/following-sibling::td//div[contains(@id, 'InductionTrng_Trainer_')]//div//span[contains(@id, '_selectDdl-container')]"));
				click2(employeeSearchDropdown, CommonStrings.SearchBy_DC.getCommonStrings(),
						CommonStrings.Click_SearchBy_AC.getCommonStrings(),
						CommonStrings.Click_SearchBy_AR.getCommonStrings(),
						CommonStrings.Click_SearchBy_SS.getCommonStrings());
				TimeUtil.shortWait();
				WebElement employeeName = driver.findElement(
						By.xpath("//ul[contains(@id, 'select2-InductionTrng_Trainer_')]/li[text()='Employee Name']"));
				click2(employeeName, CommonStrings.Click_EmployeeName_DC.getCommonStrings(),
						CommonStrings.Click_EmployeeName_AC.getCommonStrings(),
						CommonStrings.Click_EmployeeName_AR.getCommonStrings(),
						CommonStrings.Click_EmployeeName_SS.getCommonStrings());
				WebElement findTextBox = driver.findElement(
						By.xpath("//table[@id='CrsListTab']/tbody/tr//td/span[text()='" + testData.get("courseName")
								+ "']/parent::td/following-sibling::td//div[contains(@id, 'InductionTrng_Trainer_')]//div//input[contains(@id, '_FindTxt')]"));
				sendKeys2(findTextBox, CommonStrings.FindText_DC.getCommonStrings(), testData.get("trainerName") + "%",
						CommonStrings.FindText_AC.getCommonStrings(), CommonStrings.FindText_AR.getCommonStrings(),
						CommonStrings.FindText_SS.getCommonStrings());
				WebElement applyBtn = driver.findElement(By.xpath("//table[@id='CrsListTab']/tbody/tr//td/span[text()='"
						+ testData.get("courseName")
						+ "']/parent::td/following-sibling::td//div[contains(@id, 'InductionTrng_Trainer_')]//div//input[contains(@id, '_DisplayBtn')]"));
				click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
						CommonStrings.ApplyButton_AC.getCommonStrings(),
						CommonStrings.ApplyButton_AR.getCommonStrings(), "Apply button");
				TimeUtil.shortWait();
				WebElement radio_Button_Trainer = driver.findElement(
						By.xpath("(//table[@id='CrsListTab']/tbody/tr//td/span[text()='" + testData.get("courseName")
								+ "']/parent::td/following-sibling::td//div[@id='ListTab_wrapper']/table/tbody/tr/td//input[@type='radio'])[1]"));
				click2(radio_Button_Trainer, "Click on trainer radio button", "Trainer radio button should be clicked",
						"Trainer radio button is clicked", "Radio button");
				WebElement addBtn = driver.findElement(
						By.xpath("//table[@id='CrsListTab']/tbody/tr//td/span[text()='" + testData.get("courseName")
								+ "']/parent::td/following-sibling::td//footer//button[contains(@id, '_selectBtn')]"));
				click2(addBtn, CommonStrings.Click_Add_DC.getCommonStrings(),
						CommonStrings.Click_AddItem_AC.getCommonStrings(),
						CommonStrings.Click_AddItem_AR.getCommonStrings(), "Add button");
				click2(trainingDate, CommonStrings.Click_TrainingDate_DC.getCommonStrings(),
						CommonStrings.Click_TrainingDate_AC.getCommonStrings(),
						CommonStrings.Click_TrainingDate_AR.getCommonStrings(),
						CommonStrings.Click_TrainingDate_SS.getCommonStrings());
				click2(currentDate_TrainingDate, CommonStrings.Click_CurrentTrainingDate_DC.getCommonStrings(),
						CommonStrings.Click_CurrentTrainingDate_AC.getCommonStrings(),
						CommonStrings.Click_CurrentTrainingDate_AR.getCommonStrings(),
						CommonStrings.Click_CurrentTrainingDate_SS.getCommonStrings());
				waitForElementVisibile(button_Preview);
				click2(button_Preview, "Click 'Preview' button", "'Preview' button should be clicked",
						"'Preview' button is clicked", "Preview Button");
				waitForElementVisibile(button_Submit);
				TimeUtil.mediumWait();
				click2(button_Submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
						CommonStrings.Submit_Button_AC.getCommonStrings(),
						CommonStrings.Submit_Button_AR.getCommonStrings(),
						CommonStrings.Submit_Button_SS.getCommonStrings());
				waitForElementVisibile(button_Done);
				click2(button_Done, CommonStrings.Click_Done_DC.getCommonStrings(),
						CommonStrings.Click_Done_AC.getCommonStrings(), CommonStrings.Click_Done_AR.getCommonStrings(),
						CommonStrings.Click_Done_SS.getCommonStrings());
				TimeUtil.mediumWait();
				
				break;
				
			}
			
			
		}
		driver.navigate().refresh();
	}

	public void proposeInductionTrainingAuditTrails(HashMap<String, String> testData) {
		waitForElementVisibile(learnIQ_Icon);
		click2(learnIQ_Icon, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		waitForElementVisibile(courseManagerMenu);
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				"Course Manager menu");
		waitForElementVisibile(AuditTrailsSubMenu);
		click2(AuditTrailsSubMenu, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		scrollToViewElement(AuditTrailsInductionTrainingSubMenu);
		click2(AuditTrailsInductionTrainingSubMenu, CommonStrings.InductionAuditTrails_DC.getCommonStrings(),
				CommonStrings.InductionAuditTrails_AC.getCommonStrings(),
				CommonStrings.InductionAuditTrails_AR.getCommonStrings(),
				CommonStrings.InductionAuditTrails_SS.getCommonStrings());
		switchToBodyFrame(driver);
		waitForElementVisibile(dropdown_SearchBy);
		click2(dropdown_SearchBy, CommonStrings.SearchBy_DC.getCommonStrings(),
				CommonStrings.Click_SearchBy_AC.getCommonStrings(), CommonStrings.Click_SearchBy_AR.getCommonStrings(),
				CommonStrings.Click_SearchBy_SS.getCommonStrings());
		waitForElementVisibile(dropdown_SearchByEmployeeName);
		click2(dropdown_SearchByEmployeeName, CommonStrings.Click_EmployeeName_DC.getCommonStrings(),
				CommonStrings.Click_EmployeeName_AC.getCommonStrings(),
				CommonStrings.Click_EmployeeName_AR.getCommonStrings(),
				CommonStrings.Click_EmployeeName_SS.getCommonStrings());
		waitForElementVisibile(textBox_EmployeeName);
		sendKeys2(textBox_EmployeeName, CommonStrings.FindText_DC.getCommonStrings(),
				SSO_UserRegistration.getEmployeeID() , CommonStrings.FindText_AC.getCommonStrings(),
				CommonStrings.FindText_AR.getCommonStrings(), CommonStrings.FindText_SS.getCommonStrings());
		click2(apply_AuditTrails, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				"Apply button");
		waitForElementVisibile(record_AudiTrails);
		click2(record_AudiTrails, CommonStrings.InductionAuditTrails_DC.getCommonStrings(),
				CommonStrings.InductionAuditTrails_AC.getCommonStrings(),
				CommonStrings.InductionAuditTrails_AR.getCommonStrings(),
				CommonStrings.InductionAuditTrails_SS.getCommonStrings());
		driver.switchTo().frame(0);
		TimeUtil.shortWait();
		scrollToViewElement(ApproveStatus_Label);
		TimeUtil.shortWait();
		click2(closeAuditTrails, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CommonStrings.Close_AuditTrails_AC.getCommonStrings(),
				CommonStrings.Close_AuditTrails_AR.getCommonStrings(),
				CommonStrings.Close_AuditTrails_SS.getCommonStrings());
		TimeUtil.mediumWait();
		switchToDefaultContent(driver);

	}
}
