package com.Automation.Strings;

public enum TrainerStrings {

	// Trainer Configuration

	TrainerSubMenu_DC("Click 'Trainer' submenu."), TrainerSubMenu_SS("'Trainer' submenu"),

	TrainerConfig_DC("Click on 'Trainer' submenu."),
	TrainerConfig_AC("'Trainer Configuration Registration' screen should be displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' should be available.</div>"),
	TrainerConfig_AR("'Trainer Configuration Registration' screen is getting displayed.</div>"
			+ "<div><b>*</b> Option to select the E-Sign and 'No of Approvals Required' are available.</div>"),
	TrainerConfig_SS("'Trainer' submenu"),

	Click_DoneatTrainerConfig_AC("'Trainer Configuration Registration' screen should be displayed."),
	Click_DoneatTrainerConfig_AR("'Trainer Configuration Registration' screen is getting displayed.</div>"),

	// Trainer Registration

	Click_TrainerMenu_DC("Click on 'Trainer' submenu."),
	Click_TrainerMenu_AC("'Trainer' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Employee Name', 'Employee ID', 'Department' and 'Designation' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Top 250 Records, Employee Name', and 'Employee ID' should be available.</div>"
			+ "<div><b>*</b> List of Employee Name(s) who have been assigned to current plant and Job Responsibility completed should be displayed.</div>"),
	Click_TrainerMenu_AR("'Trainer' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', 'Advanced Search' and 'Total Record Count' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Employee Name', 'Employee ID', 'Department' and 'Designation' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Top 250 Records, Employee Name', and 'Employee ID' are available.</div>"
			+ "<div><b>*</b> List of Employee Name(s) who have been assigned to current plant and Job Responsibility completed are getting displayed.</div>"),
	Click_TrainerMenu_SS("'Trainer' submenu"),

	Click_Trainer_for_CourseReg_DC("Click on required 'Employee Name'."),
	Click_Trainer_for_CourseReg_AC("'Trainer Registration Initiation' Screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Trainer Name', 'Unique Code', 'Qualification', 'Experience', 'Cost Per Hour', 'Currency', 'Requalification Date', 'Any External Training Attended', 'Currently Responsible for', 'Area of Exposure', 'Courses' fields.</div>"
			+ "<div><b>*</b> 'Unique Code' should be system generated automatically based on the sequence order.</div>"
			+ "<div><b>*</b> 'Trainer Name' field details should be displayed in read only format.</div>"
			+ "<div><b>*</b> 'Qualification and Experience' fields details which is entered during Job Responsibility Registration should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b> Under 'View Existing', registered active records of 'Trainer' should be displayed if any.</div>"),
	Click_Trainer_for_CourseReg_AR("'Trainer Registration Initiation' Screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Trainer Name', 'Unique Code', 'Qualification', 'Experience', 'Cost Per Hour', 'Currency', 'Requalification Date', 'Any External Training Attended', 'Currently Responsible for', 'Area of Exposure', 'Courses' fields.</div>"
			+ "<div><b>*</b> 'Unique Code' is system generated automatically based on the sequence order.</div>"
			+ "<div><b>*</b> 'Trainer Name' field details are displayed in read only format.</div>"
			+ "<div><b>*</b> 'Qualification and Experience' fields details which is entered during Job Responsibility Registration are displayed.</div>"
			+ "<div><b>*</b> The screen contains 'View Existing', and 'Submit' buttons.</div>"
			+ "<div><b>*</b> Under 'View Existing', registered active records of 'Trainer' are displayed if any.</div>"),
	Click_Trainer_for_CourseReg_SS("'Trainer Registration Initiation' Screen"),

	Enter_CostPerHour_DC(
			"Enter a numerical value greater than 0 and less than or equal to 5 digits for 'Cost per Hour' field."),
	Enter_CostPerHour_SS("'Cost per Hour' field"),

	Click_CurrencyDropDown_DC(" Click on 'Currency' drop down list."),
	Click_CurrencyDropDown_AC(" The option to select 'USD' and 'INR' types should be available in the drop-down list."),
	Click_CurrencyDropDown_AR(" The option to select 'USD' and 'INR' types are available in the drop-down list."),
	Click_CurrencyDropDown_SS("'Currency' drop down list"),

	CurrencyTypeSearchField_DC("Enter 'USD' or 'INR' currency type from the drop-down list."),
	CurrencyTypeSearchField_SS("Enter 'Currency' type"),

	SelectCurrencyType_DC("Select required currency type from the drop-down list for 'Currency' field."),
	SelectCurrencyType_AC("Selection should be accepted."), SelectCurrencyType_AR("Selection is getting accepted."),
	SelectCurrencyType_SS("Select 'Currency'"),

	Click_RequalificationDate_DC("Click date calendar icon for 'Requalification Date' field."),
	Click_RequalificationDate_AC("'Date Calendar' window should be displayed to select the required dates.</div>"
			+ "<div><b>*</b> Current date should be highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select 'Today' date should be available.</div>"),
	Click_RequalificationDate_AR("'Date Calendar' window is getting displayed to select the required dates.</div>"
			+ "<div><b>*</b> Current date is highlighted with blue circle.</div>"
			+ "<div><b>*</b> The option to select 'Today' date is available.</div>"),
	Click_RequalificationDate_SS("'Requalification Date' field."),

	Select_RequalificationDate_DC("Select Requalification date greater than current date."),
	Select_RequalificationDate_AC("Selected date should be displayed for the 'Requalification Date' field."),
	Select_RequalificationDate_AR("Selected date is getting displayed for the 'Requalification Date' field."),
	Select_RequalificationDate_SS("'Requalification Date' field"),

	Enter_ExtTraining_DC(
			"Enter a value less than or equal to 500 characters at 'Any External Training Attended' field."),
	Enter_ExtTraining_SS("'Any External Training Attended' field"),

	Enter_CurrentlyRespFor_DC(
			"Enter a value less than or equal to 500 characters at 'Currently Responsible For' field."),
	Enter_CurrentlyRespFor_SS("'Currently Responsible For' field"),

	Enter_AreaofExposure_DC("Enter a value less than or equal to 500 characters at 'Area of Exposure' field."),
	Enter_AreaofExposure_SS("'Area of Exposure' field"),

	Click_CoursesAddItem_DC("Click on 'Add Item' for 'Courses' field"),
	Click_CoursesAddItem_AC("'Courses List' window should be displayed.</div>"
			+ "<div><b>*</b> List of active courses registered in both Master and current login plant with Course Code should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page' and 'Advanced Search' and 'Close' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Course Name', 'Course Code', 'Type of Training' and 'Add All' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Course Name', 'Course Code' and 'Training Type' should be available.</div>"
			+ "<div><b>*</b> By default, 'Records Limit' should be displayed as '50'.</div>"
			+ "<div><b>*</b> The option to add more than one course should be available.</div>"
			+ "<div><b>*</b> The screen should contain 'Cancel' and 'Add' buttons.</div>"
			+ "<div><b>*</b> 'Add' button should be displayed in disabled mode.</div>"),
	Click_CoursesAddItem_AR("'Courses List' window is getting displayed.</div>"
			+ "<div><b>*</b> List of active courses registered in both Master and current login plant with Course Code is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page' and 'Advanced Search' and 'Close' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Course Name', 'Course Code', 'Type of Training' and 'Add All' columns.</div>"
			+ "<div><b>*</b> The option to search with 'Course Name', 'Course Code' and 'Training Type' are available.</div>"
			+ "<div><b>*</b> By default, 'Records Limit' is displayed as '50'.</div>"
			+ "<div><b>*</b> The option to add more than one course is available.</div>"
			+ "<div><b>*</b> The screen contains 'Cancel' and 'Add' buttons.</div>"
			+ "<div><b>*</b> 'Add' button is displayed in disabled mode.</div>"),
	Click_CoursesAddItem_SS("'Courses List' window"),

	Enter_CourseInFind_DC("Enter the above registered 'Course Name' in 'Find' field."),
	Enter_CourseInFind_SS("'Find' field."),

	Click_AddButton_DC("Click on 'Add' button against required Course name."),
	Click_AddButton_AC(" Selected courses should be displayed under 'Selected Items' column with 'Course Unique Code'."
			+ "<div><b>*</b> 'Clear All' column should be displayed.</div>"),
	Click_AddButton_AR(
			" Selected courses are getting displayed under 'Selected Items' column with 'Course Unique Code'."
					+ "<div><b>*</b> 'Clear All' column is getting displayed.</div>"),
	Click_AddButton_SS("'Add' button"),

	Click_AddCoursesButton_DC("Click on 'Add' button."),
	Click_AddCoursesButton_AC(" The selected courses should be displayed under 'Courses' field."),
	Click_AddCoursesButton_AR(" The selected courses is getting displayed under 'Courses' field."),
	Click_AddCoursesButton_SS("'Add' button"),

	SubmitTrainerwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Trainer: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> should be displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' should be available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>*</b> 'Proceed' button should be displayed in disabled mode.</div>"),
	SubmitTrainerwithEsign_AR(
			"'Meaning of This Electronic Signature' is getting displayed as 'Trainer: Registration Initiation'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' is available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>*</b> 'Proceed' button is getting displayed in disabled mode.</div>"),

	Esign_ProceedTrainer_AC(
			"'Trainer  Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedTrainer_AR(
			"'Trainer  Registration Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	// Audit trails

	TrainerAudittrails_DC("Click on 'Trainer' menu."),
	TrainerAudittrails_AC("'Trainer Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b> The screen should contain 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen should contain 'Trainer Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.</div>"
			+ "<div><b>*</b> The screen should contain a list of all the registered Trainers.</div>"),
	TrainerAudittrails_AR("'Trainer Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>*</b> The screen contains 'Search this Page', Advanced Search' & 'Total Records Count' icons.</div>"
			+ "<div><b>*</b> The screen contains 'Trainer Name', 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is displayed as '20'.</div>"
			+ "<div><b>*</b> The screen contains a list of all the registered Trainers.</div>"),
	TrainerAudittrails_SS("'Trainer Audit Trails' screen."),

	SearchByTrainer_Dropdown_DC("Click on 'Search By' dropdown list."),
	SearchByTrainer_Dropdown_AC(
			"Option to search with 'Top 250 Records, Trainer Name, Unique Code, Initiated Between should be available.</div>"),
	SearchByTrainer_Dropdown_AR(
			"Option to search with 'Top 250 Records, Trainer Name, Unique Code, Initiated Between are available.</div>"),
	SearchByTrainer_Dropdown_SS("'Search By' dropdown list"),

	Select_UniqueCode_DC("Select 'Unique Code'."), Select_UniqueCode_SS("'Unique Code'"),

	Select_TrainerName_DC("Select 'Trainer Name'."), Select_TrainerName_SS("'Trainer Name'."),

	Click_Trainer_for_AuditTrails_DC("Click on the above registered 'Trainer Name'."),
	Click_Trainer_for_AuditTrails_AC(
			"'Trainer - Audit Trails Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should contain the details of the Trainer entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Initiated'.</div>"
					+ "<div><b>*</b> The 'Events' section should contain registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be read as '1' and the 'No. of Approvals Completed' should be read as '0'.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"

	),
	Click_Trainer_for_AuditTrails_AR(
			"'Trainer - Audit Trails Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen contains the details of the Trainer entered during registration.<div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed displayed as 'Initiated'.</div>"
					+ "<div><b>*</b> The 'Events' section contains registration 'Initiated' transaction with 'Username, Date & Time and Remarks / Reasons details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' reads as '1' and the 'No. of Approvals Completed'  reads as '0'.</div>"
					+ "<div><b>*</b> All the particulars are displayed in read only format.</div>"),
	Click_Trainer_for_AuditTrails_SS("'Trainer- Audit Trails'."),

	Close_AuditTrails_Trainer_AC("'Trainer Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_Trainer_AR("'Trainer Audit trails' screen  is getting displayed.</div>"),
	enterUniqueCodeDC("Enter the Unique Code of the above registered trainer"), enterUniqueCodeSS("'Unique Code'"),
	enterTrainerName_DC("Enter the above registered Trainer Name"), enterTrainerName_SS("'Trainer Name'"),

	// Modified Trainer Name

	Enter_ModTrainerName_DC("Enter the above modified 'Trainer Name'."),

	// Modified Trainer Name Audit Trails

	Click_ModifiedTrainer_for_AuditTrails_DC("Click on the above modified 'Trainer' Name'."),
	Click_ModifiedTrainer_for_AuditTrails_AC("'Transactions' screen should be displayed.</div>"
			+ "<div><b>*</b> All Registration and Modification tabs should be displayed with Revision No.:(No)<div>"),
	Click_ModifiedTrainer_for_AuditTrails_AR("'Transactions' screen is getting displayed.</div>"
			+ "<div><b>*</b> All Registration and Modification tabs are getting displayed with Revision No.:(No)<div>"),
	Click_ModifiedTrainer_for_AuditTrails_SS("'Transactions'"),

	// Click latest modified Modification tab

	Click_LastesModTab_DC("Click on the latest Modification Revision No.:(No) Tab."),
	Click_LastesModTab_AC("'Selected tab should be displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button should be enabled.<div>"),
	Click_LastesModTab_AR("'Selected tab is getting displayed with Blue Color border.</div>"
			+ "<div><b>*</b> 'Proceed' button is getting enabled.<div>"),

	// Click Proceed Button

	Click_Proceed_AC("'Trainer - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
			+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),

	Click_Proceed_AR("'Trainer - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
			+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),

	// Modification Remarks

	Enter_Remarks_DC("Enter the values less or equals to 250 characteres in 'Remark(s) / Reason(s)' field."),

	// Modicifiation Esign and Confirmation messages

	Mod_SubmitTrainerwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Trainer: Modification Initiation'."),
	Mod_SubmitTrainerwithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Trainer: Modification Initiation'."),

	Mod_Esign_ProceedTrainer_AC(
			"'Trainer Change Request Initiated Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Mod_Esign_ProceedTrainer_AR(
			"'Trainer Change Request Initiated Initiated Unique Code: (Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Trainer_ConfigAudit_AC("'Trainer Configuration Audit Trails' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " Screen should be displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),
	Trainer_ConfigAudit_AR("'Trainer Configuration Audit Trails' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " Screen is getting displayed with 'Unique Code', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"),

	Click_Trainer_for_ConfigAuditTrails_DC("Click on the 'Unique Code'."),
	Click_Trainer_for_ConfigAuditTrails_AC("'Transactions' screen should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button should be displayed in disabled mode.</div>"),
	Click_Trainer_for_ConfigAuditTrails_AR("'Transactions' screen is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Proceed' button.</div>" + "<div><b>*</b>"
			+ " Proceed button is getting displayed in disabled mode.</div>"),
	Click_Trainer_for_ConfigAuditTrails_SS("'Transactions'"),

	Click_Config_Proceed_AC(
			"'Trainer Configuration - Audit Trails: Revision No.: (No) -Modification' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during Registration of 'Trainer Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section should be displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),
	Click_Config_Proceed_AR(
			"'Trainer Configuration - Audit Trails: Revision No.: (No) -Modification' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during Registration of 'Trainer Configuration'.<div>"
					+ "<div><b>*</b> The 'Events' section is getting displayed with 'Initiated By, Initiated On and Remark(s) / Reason(s)' details.</div>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_Config_Proceed_SS("'Trainer Configuration - Audit Trails'."),

	Close_ConfigAuditTrails_Trainer_AC("'Trainer Configuration Audit Trails' screen should be displayed.</div>"),
	Close_ConfigAuditTrails_Trainer_AR("'Trainer Configuration Audit Trails' screen is getting displayed.</div>"),

	TrainerApproveMenu_AC("'Trainer Approval Tasks' screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details should be displayed.<div>"
			+ "<div><b>*</b> All the Trainers whose registration request is to be approved by the current user should be listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen should contain 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen should contain 'Trainer Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' should be displayed as '20'.<div>"),
	TrainerApproveMenu_AR("'Trainer Approval Tasks' screen is getting displayed."
			+ "<div><b>*</b> Screen contains 'Registration, Modification and Status Change' tabs.<div>"
			+ "<div><b>*</b> By default, the 'Registration' tab details are getting displayed.<div>"
			+ "<div><b>*</b> All the Trainers whose registration request is to be approved by the current user is listed and available for approval under the 'Registration' tab.<div>"
			+ "<div><b>*</b> The screen contains 'Total Records Count', 'Advanced Search' and 'Search this page' icons.<div>"
			+ "<div><b>*</b> The screen contains 'Trainer Name', 'Unique Code', 'Initiated By' and 'Initiated On.' columns.<div>"
			+ "<div><b>*</b> By default, 'Records Per Page' is getting displayed as '20'.<div>"),

	Like_TrainerUniqueCode_DC("Enter the Unique Code of the above registered Trainer in 'Like' field."),

	Submit_Trainer_Approval_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Trainer: Registration Approval: Approve'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> should be displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' should be available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button should be available.</div>"
					+ "<div><b>*</b> 'Proceed' button should be displayed in disabled mode.</div>"),
	Submit_Trainer_Approval_AR(
			"'Meaning of This Electronic Signature' is getting displayed as 'Trainer: Registration Approval: Approve'.</div>"
					+ "<div><b>*</b> Current login user with <First Name.Last Name (Employee ID)> is getting displayed for the 'Signed By' field.</div>"
					+ "<div><b>*</b> The option to enter 'Password' is available.</div>"
					+ "<div><b>*</b> 'Cancel' and 'Proceed' button are available.</div>"
					+ "<div><b>*</b> 'Proceed' button is getting displayed in disabled mode.</div>"),
	Submit_Trainer_Approval_SS("'E-Sign window'"),

	Esign_ProceedTrainer_Approval_AC(
			"'Trainer Registration Approved Unique Code:(Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_ProceedTrainer_Approval_AR(
			"'Trainer Registration Approved Unique Code:(Unique Code)' confirmation message is getting displayed with 'Done' button.</div>"),

	Click_Trainer_for_Approve_AuditTrails_AC(
			"'Trainer - Audit Trails: Revision No.: 0 -Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should display the details of the Trainer entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' should be displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section should be updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' should be as '1' and the 'No. of Approvals Completed' should be read as '1'.<div>"
					+ "<div><b>*</b> All the details should be displayed in read only format.<div>"),
	Click_Trainer_for_Approve_AuditTrails_AR(
			"'Trainer - Audit Trails: Revision No.: 0 -Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is displaying the details of the Trainer entered during registration.</div>"
					+ "<div><b>*</b> 'Final Status' is getting displayed as 'Approved'.<div>"
					+ "<div><b>*</b> The 'Events' section is updated with 'Initiated and Approved' transaction's with 'Username, Date & Time and Remark(s) / Reason(s)' details.<div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' is '1' and the 'No. of Approvals Completed' reads as '1'.<div>"
					+ "<div><b>*</b> All the details are getting displayed in read only format.<div>"),

	Click_Trainer_for_Approve_DC("Click on the above registered 'Trainer Name'."),
	Click_Trainer_for_Approve_AC(" 'Trainer Registration Approval' screen should be displayed</div>" + "<div><b>*</b>"
			+ " The screen should display the details of the Trainer entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' should be displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section should display the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' should be reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options should be available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' should be available.</div>"),
	Click_Trainer_for_Approve_AR(" 'Trainer Registration Approval' screen is getting displayed</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with details of the Trainer entered at the time of registration.</div>"
			+ "<div><b>*</b>" + " 'Final Status' is getting displayed as 'Initiated'.</div>" + "<div><b>*</b>"
			+ " The 'Events' section is displaying the 'Initiated' transaction with 'Username, Date & Time and Remark(s) / Reason(s) details.</div>"
			+ "<div><b>*</b>"
			+ " Also, the 'No. of Approvals Required' reads as 1 and the 'No. of Approvals Completed' reads as 0.</div>"
			+ "<div><b>*</b>" + " 'Approve, Return & Drop' options are available for 'Decision' field.</div>"
			+ "<div><b>*</b>" + " The option to enter/select 'Remark(s) / Reason(s)' are available.</div>"),
	Click_Trainer_for_Approve_SS("'Trainer' menu"),

	SearchByEmployeeName_AC(
			"Option to search with 'Top 250 Records, Employee Name, Employee ID should be available.</div>"),
	SearchByEmployeeName_AR("Option to search with 'Top 250 Records, Employee Name, Employee ID are available.</div>"),

	Select_EmployeeName_DC("Select 'Employee Name'."), Select_EmployeeName_SS("'Employee Name'"),

	Like_EmployeeName_DC("Enter the required Employee Name in 'Like' field."),

	;

	private final String trainerStrings;

	TrainerStrings(String trainerStrings) {

		this.trainerStrings = trainerStrings;

	}

	public String getTrainerStrings() {
		return trainerStrings;
	}

}
