package com.Automation.ObjectModelFlows_OQ.System_Manager.JDK;

import java.util.HashMap;
import org.openqa.selenium.JavascriptExecutor;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class Job_Responsibility_Modification_with_UA_as_No_AD_as_No extends OQActionEngine {

	public static String SSOTab = "";
	public static String EpiqTab = "";
	String ExcelPath = "./learnIQTestData/Object_Model_Flows/System_Manager/JobResponsibility/JR_With_UA_NO_AD_NO/JR_MOD_UA_NO_AD_NO.xlsx";
	String SSO_ExcelPath = "./learnIQTestData/SSO_UserRegistrationPrerequisites.xlsx";

	public Job_Responsibility_Modification_with_UA_as_No_AD_as_No() {

		super(ConfigsReader.getPropValue("SSOUrl"));
		//super(ConfigsReader.getPropValue("applicationUrl"));

	}

	ExcelUtilUpdated excel0 = new ExcelUtilUpdated(SSO_ExcelPath, "USREG");

	@DataProvider(name = "RegistrationUser")
	public Object[][] getRegistrationUser() throws Exception {
		Object[][] obj = new Object[excel0.getRowCount()][1];
		for (int i = 1; i <= excel0.getRowCount(); i++) {
			HashMap<String, String> testData = excel0.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 0, dataProvider = "RegistrationUser", enabled = true)
	public void userReg(HashMap<String, String> testData) {
		test = extent.createTest("Pre-Requisite: 1.0 User Registration Approval with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre-Requisite: 1.0 User Registration Approval with Audit Trails");
		SSOTab = driver.getWindowHandle();
		epiclogin.loginToSSOApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("SSOUserID"),
				ConfigsReader.getPropValue("SSOPassword"));
		TimeUtil.longwait();
		UserReg.sso_UserRegistration(testData);
		test = extent.createTest("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails");
		UserProductModuleAssignment.UserProductModuleAssignment_With_AuditTrails(testData);
		Logout.SSOsignOutPage();
		/*
		 * ((JavascriptExecutor)
		 * driver).executeScript("window.open(arguments[0], '_blank');",
		 * ConfigsReader.getPropValue("applicationUrl")); n = 0; screenshotCounter = 0;
		 */

	}

	@Test(priority = 1, enabled = true)
	public void ResetPasswordEPIQ() {

		test = extent.createTest("Pre Requisite: 6.0 Reset Login Passoword")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 6.0 Reset Login Passoword");
		((JavascriptExecutor) driver).executeScript("window.open(arguments[0], '_blank');",
				ConfigsReader.getPropValue("applicationUrl"));
		for (String handle : driver.getWindowHandles()) {
			if (!handle.equals(SSOTab)) {
				driver.switchTo().window(handle);
				EpiqTab = driver.getWindowHandle();
				break;
			}
		}
		epiclogin.loginToEPIQforResetPassword(ConfigsReader.getPropValue("company"));

		UserReg.userReg_resetPasswordEpiq(ConfigsReader.getPropValue("SSONewUserRegPassword"));

	}

	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "JR_Central_Configuration");

	@DataProvider(name = "JobResp_CentralConfig")
	public Object[][] getJR_Central_Config() throws Exception {
		Object[][] obj = new Object[excel1.getRowCount()][1];
		for (int i = 1; i <= excel1.getRowCount(); i++) {
			HashMap<String, String> testData = excel1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "JobResp_CentralConfig", enabled = true)
	public void JR_Central_Configuration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Set User Acceptance and Autorized Deputy as 'No' in central configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Set User Acceptance and Autorized Deputy as 'No' in central configuration");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EPIQDefaultID"), ConfigsReader.getPropValue("EPIQDefaultIPSW"));
			epiclogin.plant1();
			centralConfg.jobResponsibilityCentralConfigurations(testData);
			Logout.signOutPage();

		}

	}

	ExcelUtilUpdated excel2 = new ExcelUtilUpdated(ExcelPath, "E-SignConfiguration");

	@DataProvider(name = "JobResp_EsignConfig")
	public Object[][] getEsignConfigurationData() throws Exception {
		Object[][] obj = new Object[excel2.getRowCount()][1];
		for (int i = 1; i <= excel2.getRowCount(); i++) {
			HashMap<String, String> testData = excel2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "JobResp_EsignConfig", enabled = true)
	public void Job_Resp_Configuration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Configure E-Sign for Job Responsibility Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configure E-Sign for Job Responsibility Registration");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EpicUserID"), ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobresponsbility_Configuration(testData);

		}

	}

	ExcelUtilUpdated excel3 = new ExcelUtilUpdated(ExcelPath, "JR_REG");

	@DataProvider(name = "JR_Registration")
	public Object[][] getJRRegistrationData() throws Exception {
		Object[][] obj = new Object[excel3.getRowCount()][1];
		for (int i = 1; i <= excel3.getRowCount(); i++) {
			HashMap<String, String> testData = excel3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Registration Initiation");
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO(testData);
			Logout.signOutPage();

		}

	}

	@Test(priority = 5, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Approve Job Responsobility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Approve Job Responsobility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			epiclogin.plant1();
			String ApprovaScreen = "IntiateApprove";
			JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData,
					Constants.APPROVE_ACTIONVAL, ApprovaScreen);
			String ApprovalStatus = "Direct Approved";

			Logout.signOutPage();
		}

	}

	@DataProvider(name = "JR_Modification_Init_Data")
	public Object[][] getJR_ReinitiationData() throws Exception {
		ExcelUtilUpdated regExcel = new ExcelUtilUpdated(ExcelPath, "JR_REG");
		ExcelUtilUpdated ModInit = new ExcelUtilUpdated(ExcelPath, "JR_MOD_INIT");

		int rowCount = Math.min(regExcel.getRowCount(), ModInit.getRowCount()); // match rows

		Object[][] obj = new Object[rowCount][2];
		for (int i = 1; i <= rowCount; i++) {
			HashMap<String, String> regData = regExcel.getTestDataInMap(i);
			HashMap<String, String> ModinitData = ModInit.getTestDataInMap(i);

			obj[i - 1][0] = regData; // for validation
			obj[i - 1][1] = ModinitData;

		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "JR_Modification_Init_Data", enabled = true)
	public void Job_Resp_Modification(HashMap<String, String> regData, HashMap<String, String> ModinitData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Modification Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Modification Initiation");

			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EpicUserID"), ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobResponsibilty_Mod_With_UA_NO_AD_NO(regData, ModinitData);
			String ApprovalStatus ="Under Approval";
			JobResponsibility.jobResponsibilty_Reg_Mod_With_UA_NO_AD_NO_AuditTrails(regData, ModinitData, ApprovalStatus);
			Logout.signOutPage();
		}

	}

	@Test(priority = 7, dataProvider = "JR_Modification_Init_Data", enabled = true)
	public void JR_Reinitiation_Approval(HashMap<String, String> regData, HashMap<String, String> ModinitData) {

		test = extent.createTest("Approve Job Responsibility Modification Initiation")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Approve Job Responsibility Modification Initiation");

		// Login
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		String Approve = "ModificationIntiateApprove";
		JobResponsibility.jobResponsibility_Modification_Approval_UA_NO_AD_NO(ModinitData, Constants.APPROVE_ACTIONVAL,
				Approve);
		String ApprovalStatus ="Direct Approved";
		JobResponsibility.jobResponsibilty_Reg_Mod_With_UA_NO_AD_NO_AuditTrails(regData, ModinitData, ApprovalStatus);
	}

	
	
	@Test(priority = 8, enabled = true)
	public void print_JR_After_Registration_Approve() {

	    test = extent.createTest("Report")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Report");
	   print_JR.jobResponsibilityReport_MOD_UA_AD_NO("");
	   Logout.signOutPage();
	}
	
	
	@Test(priority = 9, dataProvider = "RegistrationUser", enabled = true)
	public void userReg2(HashMap<String, String> testData) {
		test = extent.createTest("Pre-Requisite: 1.0 User Registration Approval with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre-Requisite: 1.0 User Registration Approval with Audit Trails");
		driver.switchTo().window(SSOTab);
		epiclogin.loginToSSOApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("SSOUserID"),
				ConfigsReader.getPropValue("SSOPassword"));
		TimeUtil.longwait();
		UserReg.sso_UserRegistration(testData);
		test = extent.createTest("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails");
		UserProductModuleAssignment.UserProductModuleAssignment_With_AuditTrails(testData);
		Logout.SSOsignOutPage();
		/*
		 * ((JavascriptExecutor)
		 * driver).executeScript("window.open(arguments[0], '_blank');",
		 * ConfigsReader.getPropValue("applicationUrl")); n = 0; screenshotCounter = 0;
		 */

	}

	@Test(priority = 10, enabled = true)
	public void ResetPasswordEPIQ2() {

		test = extent.createTest("Pre Requisite: 6.0 Reset Login Passoword")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 6.0 Reset Login Passoword");
		driver.switchTo().window(EpiqTab);
		epiclogin.loginToEPIQforResetPassword(ConfigsReader.getPropValue("company"));

		UserReg.userReg_resetPasswordEpiq(ConfigsReader.getPropValue("SSONewUserRegPassword"));

	}

	@Test(priority = 11, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_2(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EpicUserID"), ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO(testData);
			Logout.signOutPage();

		}

	}

	@Test(priority = 12, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_Approve_2(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Approve Job Responsobility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Approve Job Responsobility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			epiclogin.plant1();
			String ApprovaScreen = "IntiateApprove";
			JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData,
					Constants.APPROVE_ACTIONVAL, ApprovaScreen);

			Logout.signOutPage();
		}

	}

	@Test(priority = 13, dataProvider = "JR_Modification_Init_Data", enabled = true)
	public void Job_Resp_Modification_2(HashMap<String, String> regData, HashMap<String, String> ModinitData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Modification Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Modification Initiation");

			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EpicUserID"), ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobResponsibilty_Mod_With_UA_NO_AD_NO(regData, ModinitData);
			Logout.signOutPage();
		}

	}

	@Test(priority = 14, dataProvider = "JR_Modification_Init_Data", enabled = true)
	public void JR_Reinitiation_Approval_Return(HashMap<String, String> regData, HashMap<String, String> ModinitData) {

		test = extent.createTest("Approve Job Responsibility Modification Initiation")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Approve Job Responsibility Modification Initiation");

		// Login
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		JobResponsibility.jobResponsibility_Modification_Approval_UA_NO_AD_NO(ModinitData, Constants.RETURN_ACTIONVAL,
				"");
		Logout.signOutPage();
	}

	@DataProvider(name = "JR_MOD_RI_TRANSFER")
	public Object[][] getJR_MOD_RI_TransferDataData() throws Exception {
		ExcelUtilUpdated ModExcel = new ExcelUtilUpdated(ExcelPath, "JR_MOD_INIT");
		ExcelUtilUpdated ModreinitExcel = new ExcelUtilUpdated(ExcelPath, "JR_MOD_REINIT");

		int rowCount = Math.min(ModExcel.getRowCount(), ModreinitExcel.getRowCount()); // match rows

		Object[][] obj = new Object[rowCount][2];
		for (int i = 1; i <= rowCount; i++) {
			HashMap<String, String> ModData = ModExcel.getTestDataInMap(i);
			HashMap<String, String> ModreinitData = ModreinitExcel.getTestDataInMap(i);

			obj[i - 1][0] = ModData; // for validation
			obj[i - 1][1] = ModreinitData;

		}
		return obj;
	}

	@Test(priority = 15, dataProvider = "JR_MOD_RI_TRANSFER", enabled = true)
	public void Job_Resp_Registration_RI_Transfer(HashMap<String, String> ModData,
			HashMap<String, String> ModreinitData) {

		if (isReportedRequired == true) {
			test = extent.createTest("RI Transfer of Re-Initiation Task")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RI Transfer of Re-Initiation Task");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EpicUserID"), ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			String Stage = "ModificationRITransfer";
			JobResponsibility.JR_RI_Transfer(ModData, ModreinitData, Stage);
			Logout.signOutPage();
		}
	}

	@DataProvider(name = "JR_Mod_ReinitiationData")
	public Object[][] getJR_Mod_ReinitiationData() throws Exception {
		ExcelUtilUpdated ModExcel = new ExcelUtilUpdated(ExcelPath, "JR_MOD_INIT");
		ExcelUtilUpdated ModreinitExcel = new ExcelUtilUpdated(ExcelPath, "JR_MOD_REINIT");

		int rowCount = Math.min(ModExcel.getRowCount(), ModreinitExcel.getRowCount()); // match rows

		Object[][] obj = new Object[rowCount][2];
		for (int i = 1; i <= rowCount; i++) {
			HashMap<String, String> ModData = ModExcel.getTestDataInMap(i);
			HashMap<String, String> ModreinitData = ModreinitExcel.getTestDataInMap(i);

			obj[i - 1][0] = ModData; // for validation
			obj[i - 1][1] = ModreinitData;

		}
		return obj;
	}

	@Test(priority = 16, dataProvider = "JR_Mod_ReinitiationData", enabled = true)
	public void JR_Reinitiation_Test(HashMap<String, String> ModData, HashMap<String, String> ModreinitData) {

		test = extent.createTest("Reinitiate Job Responsibility with Validation")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID")).assignCategory("Reinitiation Flow");

		// Login
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ModreinitData.get("TransaferToUserID"),
				ModreinitData.get("TransaferToPSW"));
		epiclogin.plant1();
		JobResponsibility.ModreinitiateJobResponsibility(ModData, ModreinitData);
		
		String ApprovalStatus ="ModificationRe-Initiation Under Approval";
		JobResponsibility.jobResponsibilty_Reg_Mod_With_UA_NO_AD_NO_AuditTrails(ModData, ModreinitData, ApprovalStatus);
//	    JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO_AuditTrails(ModreinitData,ApprovalStatus);
		Logout.signOutPage();

	}

	@Test(priority = 17, dataProvider = "JR_Mod_ReinitiationData", enabled = true)
	public void JR_Mod_Reinitiation_Approval(HashMap<String, String> ModData, HashMap<String, String> ModreinitData) {

		test = extent.createTest("Approve Job Responsibility Registraion Re-Initiation")
				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
				.assignCategory("Approve Job Responsibility Registraion Re-Initiation");

		// Login
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		String Approve = "ModReInitApproval";
		JobResponsibility.jobResponsibility_Modification_Approval_UA_NO_AD_NO(ModreinitData,
				Constants.APPROVE_ACTIONVAL, Approve);
	}
	
	
	@Test(priority = 18, enabled = true)
	public void print_JR_After_Registration_Re_Initiation_Approve() {

	    test = extent.createTest("Job Responsibility Repot after Rei")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Job Responsibility Repot after Re");
	    
	    String Stage = "Re_Initiate";
	   print_JR.jobResponsibilityReport_MOD_UA_AD_NO(Stage);
	   Logout.signOutPage();
	}

}
