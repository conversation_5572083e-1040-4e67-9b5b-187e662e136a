package com.Automation.Strings;

public enum UserProducAssignStrings {

	IM_UserProduct_DC("Click on 'User Product / Module Assignment' menu."),
	IM_UserProduct_AC("'User Product / Module Assignment Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>*</b>" + " 'User Name' Add item field should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'View Existing' and 'Submit' buttons.</div>"),
	IM_UserProduct_AR("'User Product / Module Assignment Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>*</b>" + " 'User Name' Add item field is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'View Existing' and 'Submit' buttons.</div>"),
	IM_UserProduct_SS("'User Product / Module Assignment'"),

	AddItem_UserName_DC("Click on 'Add Item' for 'User Name' field"),
	AddItem_UserName_AC("'Users List' window should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Search this Page', 'Advance Search' and 'Close' icons.</div>"
			+ "<div><b>*</b>" + " By default, 'User Name' should be displayed in 'Search By' field.</div>"
			+ "<div><b>*</b>" + " The screen should be displayed with 'Add' and 'Cancel' buttons.</div>"
			+ "<div><b>*</b>" + " Add button should be displayed in disabled mode.</div>"),
	AddItem_UserName_AR("'Users List' window is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Search this Page', 'Advance Search' and 'Close' icons.</div>"
			+ "<div><b>*</b>" + " By default, 'User Name' is getting displayed in 'Search By' field.</div>"
			+ "<div><b>*</b>" + " The screen is getting displayed with 'Add' and 'Cancel' buttons.</div>"
			+ "<div><b>*</b>" + " Add button is getting displayed in disabled mode.</div>"),
	AddItem_UserName_SS("'Users List' window.<div>"),

	Find_EmployeeID_DC("Enter the registered 'Employee ID' in 'Find' field"), Find_EmployeeID_SS("'Employee ID'"),

	UserNameRadioBtn_DC("Select the required 'User Name'"), UserNameRadioBtn_SS("'User Name'"),

	UserName_AddButton_DC("Click on 'Add' button."),
	UserName_AddButton_AC("Selected User Name should be displayed for 'User Name' field."),
	UserName_AddButton_AR("Selected User Name is getting displayed for 'User Name' field."),
	UserName_AddButton_SS("'Add' button"),

	SelectRadioBtn_DC("Click on 'Select' Radio Button"), SelectRadioBtn_SS("'Select' Radio Button"),

	Select_learniq_DC("Select on 'Select' checkbox against learn-iq (learn-iq) section"),
	Select_learniq_SS("'Select' checkbox"),

	Category_DC("Click on 'Category' dropdown."),
	Category_AC("The Option to select 'Login and Non-Login' Category should be available.</div>"),
	Category_AR("The Option to select 'Login and Non-Login' Category are available.</div>"),
	Category_SS("Option to select Login and Non-Login"),

	Category_Login_DC("Select 'Login'"), Category_Login_AC("Selected value should be accepted.</div>"),
	Category_Login_AR("Selected value is getting accepted."), Category_Login_SS("'Login'"),

	Role_DC("Click the 'Role' dropdown"), Role_AC("Option to select Roles should be available.</div>"),
	Role_AR("Option to select Roles are available.</div>"), Role_SS("'Role'"),

	Search_Role_DC("Enter the above registered Role"), Search_Role_SS("'Role'"),

	RoleSelect_DC("Select the above registered Role"), RoleSelect_AC("Selected value should be accepted.</div>"),
	RoleSelect_AR("Selected value is getting accepted."), RoleSelect_SS("'Select'"),

	AddItem_Plant_DC("Click on 'Add Item' for 'Plant' field"),
	AddItem_Plant_AC("'Plant List' window should be displayed.</div>" + "<div><b>*</b>"
			+ " The screen should be displayed with 'Search his Page', 'Advance Search', and 'Close' icons.</div>"
			+ "<div><b>*</b>" + " By default, 'Plant Code' should be displayed in 'Search By' field.</div>"
			+ "<div><b>*</b>" + " The screen should be displayed with 'Add' and 'Cancel' buttons.</div>"
			+ "<div><b>*</b>" + " Add button should be displayed in disabled mode."),
	AddItem_Plant_AR("'Plant List' window is getting displayed.</div>" + "<div><b>*</b>"
			+ " The screen is getting displayed with 'Search his Page', 'Advance Search', and 'Close' icons.</div>"
			+ "<div><b>*</b>" + " By default, 'Plant Code' is getting displayed in 'Search By' field.</div>"
			+ "<div><b>*</b>" + " The screen is getting displayed with 'Add' and 'Cancel' buttons.</div>"
			+ "<div><b>*</b>" + " Add button is getting displayed in disabled mode.</div>"),
	AddItem_Plant_SS("'Plant List' window.<div>"),

	Find_PlantName_DC("Enter the required 'Plant Name' in 'Find' field"), Find_PlantName_SS("'Plant Name'"),
	Find_PlantName2_DC("Enter the another required 'Plant Name' in 'Find' field"),

	AddButtonAgainstPlantName_DC("Click on 'Add' against required Plant Code."),
	AddButtonAgainstPlantName_AC("'Selected Plant Code should be displayed in 'Selected Items' field."),
	AddButtonAgainstPlantName_AR("'Selected Plant Code is getting displayed in 'Selected Items' field."),
	AddButtonAgainstPlantName_SS("'Add' button"),

	AddPlantName_DC("Click on 'Add' button"),
	AddPlantName_AC("Selected Plant Name should be displayed for 'Plant Name' field."),
	AddPlantName_AR("Selected Plant Name is geeting displayed for 'Plant Name' field."), AddPlantName_SS("'Add'"),

	AddItemDepartment_DC("Click on 'Add Item' for 'Department' field"),
	AddItemDepartment_AC("'Departments List' window should be displayed.</div>"),
	AddItemDepartment_AR("'Departments List' window is getting displayed.</div>"),
	AddItemDepartment_SS("'Departments List' window.<div>"),

	AddItemReportingTo_DC("Click on 'Add Item' for 'Reporting To' field"),
	AddItemReportingTo_AC("'Reporting To Users List' window should be displayed.</div>"),
	AddItemReportingTo_AR("'Reporting To Users List' window is getting displayed.</div>"),
	AddItemReportingTo_SS("'Reporting To Users List' window.<div>"),
	Find_ReportingTo_DC("Enter the required user name in 'Find' search box."), Find_ReportingTo_SS("'User Name'"),
	Find_Department_DC("Enter the 'Department Code' of above registered Department in Department field."),
	Find_Department_SS("'Department Code'"), DepartmentRadioBtn_DC("Select the required 'Department Code'"),
	
	
	Find_Department_310_DC("Enter the 'Department' of above registered Department in Department field."),
	Find_Department_310_SS("'Department'"), DepartmentRadioBtn_310_DC("Select the required 'Department'"),
	DepartmentRadioBtn_310__SS("'Department'"),
	
	
	
	
	DepartmentRadioBtn_SS("'Department Code'"), ReortingToRadioBtn_DC("Select the required 'User Name'"),
	ReportingToRadioBtn_SS("'User Name'"), Department_AddButton_DC("Click on 'Add' button."),
	Department_AddButton_AC("Selected  Department Code(Department Name) should be displayed for 'Department' field."),
	Department_AddButton_AR("Selected  Department Code(Department Name) is getting displayed for 'Department' field."),
	
	Department_AddButton_310_AC("Selected  Department should be displayed for 'Department' field."),
	Department_AddButton_310_AR("Selected  Department is getting displayed for 'Department' field."),
	
	
	
	
	Department_AddButton_SS("'Add' button"), ReortingTo_AddButton_DC("Click on 'Add' button."),
	ReortingTo_AddButton_AC("Selected User Name should be displayed for 'Reporting To' field."),
	ReortingTo_AddButton_AR("Selected User Name is getting displayed for 'Reporting To' field."),
	ReortingTo_AddButton_SS("'Add' button"), InductionTraininReqNO_DC("Select 'Induction Training Required' as 'No'"),
	InductionTraininReqNO_SS("'Induction Training Required' as 'No'"),
	InductionTraininReqYes_DC("Select 'Induction Training Required' as 'Yes'"),
	InductionTraininReqYes_SS("'Induction Training Required' as 'Yes'"),

	SubmitProductModuleAssignwithEsign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'User Product / Module Assignment: Registration Initiation'."),
	SubmitProductModuleAssignwithEsign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'User Product / Module Assignment: Registration Initiation'."),

	EsignProceed_AC(
			"'User Product / Module Assignment Registration Initiated User Name: (User Name) confirmation message should be displayed with 'Done' button.</div>"),
	EsignProceed_AR(
			"'User Product / Module Assignment Registration Initiated User Name: (User Name) confirmation message is getting displayed with 'Done' button.</div>"),

	IM_UserProductAuditScreen_AC("'User Product / Module Assignment Audit Trails' screen should be displayed.</div>"
			+ "<div><b>*</b>"
			+ " The screen should be displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
			+ "<div><b>*</b>"
			+ " Screen should be displayed with 'User Name', 'Employee ID', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>"
			+ " The screen should contain the list of records for which the User Product/Module Assignment is completed.</div>"),
	IM_UserProductAuditScreen_AR("'User Product / Module Assignment Audit Trails' screen is getting displayed.</div>"
			+ "<div><b>*</b>"
			+ " The screen is getting displayed with 'Search this Page', 'Advance Search', and 'Total Records Count' icons.</div>"
			+ "<div><b>*</b>"
			+ " Screen is getting displayed with 'User Name', 'Employee ID', 'Initiated By', 'Initiated On' and 'Revision No.' columns.</div>"
			+ "<div><b>*</b>"
			+ " The screen contains the list of records for which the User Product/Module Assignment is completed.</div>"),
	UserProductAuditScreen_SearchBy_AC(
			"Option to search with 'Top 250 Records, User Name, Employee ID and Initiated Between' should be available."),
	UserProductAuditScreen_SearchBy_AR(
			"Option to search with 'Top 250 Records, User Name, Employee ID and Initiated Between' are available.</div>"),
	
	
	
	UserProductAuditScreen_310_SearchBy_AC(
			"Option to search with 'Top 250 Records, User Name, User ID, Employee ID and Initiated Between' should be available."),
	UserProductAuditScreen_310_SearchBy_AR(
			"Option to search with 'Top 250 Records, User Name,User ID, Employee ID and Initiated Between' are available.</div>"),
	
	

	UsersList_SearchBy_AC("Option to search with 'User Name and Employee ID' should be available."),
	UsersList_SearchBy_AR("Option to search with 'User Name and Employee ID' are available.</div>"),
	
	UsersList_SearchBy_310_AC("Option to search with 'User Name,User ID, and Employee ID' should be available."),
	UsersList_SearchBy_310_AR("Option to search with 'User Name ,User ID,and Employee ID' are available.</div>"),
	
	

	SearchBy_EmployeeID_DC("Select 'Employee ID'."), SearchBy_EmployeeID_SS("'Employee ID'."),

	Like_EmployeeID_DC("Enter the registered 'Employee ID' in search field."), Like_EmployeeID_SS("'Employee ID'"),

	Click_UserName_for_AuditTrails_DC("Click on the above registered 'User Name'."),
	Click_UserName_for_AuditTrails_AC(
			"'User Product / Module Assignment-Audit Trails: Revision No.: 0 - Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details entered during registration of ‘User Product / Module  Assignment.<div>"
					+ "<div><b>*</b> The 'Events' section should display only the Registration 'Initiated' transaction with 'Username', 'Date& Time' details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' and the 'No. of  Approvals Completed both should be read as ‘0’.<div/>"
					+ "<div><b>*</b> All the particulars should be displayed in read only format.</div>"),

	Click_UserName_for_AuditTrails_AR(
			"'User Product / Module Assignment-Audit Trails: Revision No.: 0 - Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> The screen is getting displayed with the details entered during registration of ‘User Product / Module  Assignment.<div>"
					+ "<div><b>*</b> The 'Events' section is displaying only the Registration 'Initiated' transaction with 'Username', 'Date& Time' details.</div>"
					+ "<div><b>*</b> Also, the 'No. of Approvals Required' and the 'No. of  Approvals Completed both reads as ‘0’.<div/>"
					+ "<div><b>*</b> All the particulars are getting displayed in read only format.</div>"),
	Click_UserName_for_AuditTrails_SS("'User Product / Module Assignment-Audit Trails."),

	Close_AuditTrails_UserProModAssign_AC(
			"'User Product / Module Assignment Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_UserProModAssign_AR(
			"'User Product / Module Assignment Audit Trails' screen  is getting displayed.</div>"),

	;

	private final String userProducAssignStrings;

	UserProducAssignStrings(String userProducAssignStrings) {

		this.userProducAssignStrings = userProducAssignStrings;

	}

	public String getUserProducAssignStrings() {
		return userProducAssignStrings;
	}

}
