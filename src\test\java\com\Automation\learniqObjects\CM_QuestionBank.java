package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.QuestionBankStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Strings.TrainingScheduleStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_QuestionBank extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[9]/a[1]")
	WebElement configMenu;

	@FindBy(id = "TopicName")
	WebElement dataEnter11;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[9]/a[1]")
	WebElement configurationMenu;

	@FindBy(xpath = "//*[@id='TMS_Course Manager']/li/a[text()='Approve']")
	WebElement Approve;

	@FindBy(xpath = "//*[@id='TMS_Course Manager_Approve']/li/a[text()='Question Bank']")
	WebElement ApproveQB;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[6]//a[contains(@class,'sub-menu')][contains(text(),'Prepare')]")
	WebElement prepareMenu;
	@FindBy(id = "TMS_Course Manager_Configure_MEN98")
	WebElement questionBankConfig;
	@FindBy(id = "TMS_Course Manager_Prepare_MEN98")
	WebElement prepareQBMenu;
	@FindBy(id = "btnAdvSearch")
	WebElement searchFilter;
	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']/li[2]")
	WebElement searchByDropdown;
	@FindBy(id = "TopicName")
	WebElement topicNameSearch;
	@FindBy(xpath = "//div[@class='table-responsive']//td[1]")
	WebElement displayedRecord;
	@FindBy(id = "displayBtn")
	WebElement display;
	@FindBy(id = "Config_IsEsignAtCreatInit")
	WebElement eSignAtInitCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyInit")
	WebElement eSignAtModInitCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeInit")
	WebElement eSignAtSTCInitCheckBox;
	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtInitAprCheckBox;
	@FindBy(id = "Config_IsEsignAtModifyAppr")
	WebElement eSignAtModAprCheckBox;
	@FindBy(id = "Config_IsEsignAtStatusChangeAppr")
	WebElement eSignAtSTCAprCheckBox;
	@FindBy(id = "select2-Config_NarAtCreate-container")
	WebElement noOfAprReqForRegDrpdwn;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']//li[1]")
	WebElement select0ForInitDropdown;
	@FindBy(id = "select2-Config_NarAtModify-container")
	WebElement noOfAprReqForModDrpdown;
	@FindBy(id = "Config_Remarks")
	WebElement remarks;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtCreate-results']/li[1]")
	WebElement searchSel1;
	@FindBy(xpath = "//ul[@id='select2-Config_NarAtModify-results']/li[1]")
	WebElement searchSel2;
	@FindBy(xpath = "//input[@id='CMQuesBank_NoOfMulChoicesQues']")
	WebElement multipleChoice;
	@FindBy(id = "CMQuesBank_multipleChoices0_QuestionDesc")
	WebElement mcq1;
	@FindBy(id = "CMQuesBank_multipleChoices1_QuestionDesc")
	WebElement mcq2;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices0_Choice1']")
	WebElement multipleChoiceQ1Ans1;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices0_Choice2']")
	WebElement multipleChoiceQ1Ans2;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices0_Choice3']")
	WebElement multipleChoiceQ1Ans3;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices0_Choice4']")
	WebElement multipleChoiceQ1Ans4;
	@FindBy(xpath = "//div[@id='MultipleDataDiv']/div[10]/div[1]/div[1]/div[1]/input[1]")
	WebElement mcq1RB2;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices0_Marks']")
	WebElement multipleChoiceQ1Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices1_Choice1']")
	WebElement multipleChoiceQ2Ans1;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices1_Choice2']")
	WebElement multipleChoiceQ2Ans2;
	@FindBy(xpath = "//div[@id='MultipleDataDiv']/div[26]/div[1]/div[1]/div[1]/input[1]")
	WebElement mcq2RB2;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices1_Choice3']")
	WebElement multipleChoiceQ2Ans3;
	@FindBy(xpath = "//input[@id='CMQuesBank_multipleChoices1_Choice4']")
	WebElement multipleChoiceQ2Ans4;
	@FindBy(id = "CMQuesBank_multipleChoices1_Marks")
	WebElement multipleChoiceQ2Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_NoOfFillintheBlanksQues']")
	WebElement fillInTheBlanks;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks0_QuestionDesc']")
	WebElement fillInTheBlanksQ1;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks0_Answer']")
	WebElement fillInTheBlanksQ1Ans;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks0_Marks']")
	WebElement fillInTheBlanksQ1Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks1_QuestionDesc']")
	WebElement fillInTheBlanksQ2;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks1_Answer']")
	WebElement fillInTheBlanksQ2Ans;
	@FindBy(xpath = "//input[@id='CMQuesBank_fillinTheBlanks1_Marks']")
	WebElement fillInTheBlanksQ2Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_NoOfTrueFalseQues']")
	WebElement trueOrFalse;
	@FindBy(xpath = "//input[@id='CMQuesBank_trueOrFalses0_QuestionDesc']")
	WebElement trueOrFalseQ1;
	@FindBy(xpath = "//div[@id='TrueDataDiv']/div[6]/div[1]/div[1]/div[1]/input[1]")
	WebElement trueOrFalseQ1RB1;
	@FindBy(xpath = "//input[@id='CMQuesBank_trueOrFalses0_Marks']")
	WebElement trueOrFalseQ1Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_trueOrFalses1_QuestionDesc']")
	WebElement trueOrFalseQ2;
	@FindBy(xpath = "//div[@id='TrueDataDiv']/div[13]/div[1]/div[1]/div[1]/input[1]")
	WebElement trueOrFalseQ2RB1;
	@FindBy(xpath = "//input[@id='CMQuesBank_trueOrFalses1_Marks']")
	WebElement trueOrFalseQ2Marks;
	@FindBy(xpath = "//input[@id='CMQuesBank_NoOfEassyQues']")
	WebElement essays;
	@FindBy(xpath = "//textarea[@id='CMQuesBank_essays0_QuestionDesc']")
	WebElement essayQ1;
	@FindBy(xpath = "//textarea[@id='CMQuesBank_essays0_Answer']")
	WebElement essayQ1Ans1;
	@FindBy(xpath = "//input[@id='CMQuesBank_essays0_Marks']")
	WebElement essayQ1Marks;
	@FindBy(xpath = "//textarea[@id='CMQuesBank_essays1_QuestionDesc']")
	WebElement essayQ2;
	@FindBy(xpath = "//textarea[@id='CMQuesBank_essays1_Answer']")
	WebElement essayQ2Ans1;
	@FindBy(xpath = "//input[@id='CMQuesBank_essays1_Marks']")
	WebElement essayQ2Marks;
	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;
	@FindBy(xpath = "//button[@id='btnSubmit' and @class='caliber-button-primary N5SubmitCls']")
	WebElement submit1;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//label[contains(text(),'Topic Name')]//following-sibling::span")
	WebElement auditTopicName;
	@FindBy(xpath = "//label[contains(text(),'Topic Unique Code')]//following-sibling::span")
	WebElement auditTopicUniqueCode;
	@FindBy(xpath = "//label[@for='CMQuesBankV1_QnbCode']//following-sibling::span")
	WebElement auditQBUniqueCode;
	@FindBy(xpath = "//label[contains(text(),'Category Name')]//following-sibling::span")
	WebElement auditQBCategoryName;
	@FindBy(xpath = "//label[contains(text(),'Subject Name')]//following-sibling::span")
	WebElement auditQBSubjectName;
	@FindBy(xpath = "//label[contains(text(),'Total Active Questions')]//following-sibling::span")
	WebElement auditTotalActiveQuestions;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[1]//td[1]")
	WebElement auditQuestion1;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[2]//td[1]")
	WebElement auditQuestion2;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[3]//td[1]")
	WebElement auditQuestion3;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[4]//td[1]")
	WebElement auditQuestion4;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[1]//td[2]")
	WebElement auditQuestionType1;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[2]//td[2]")
	WebElement auditQuestionType2;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[3]//td[2]")
	WebElement auditQuestionType3;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[4]//td[2]")
	WebElement auditQuestionType4;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[1]//td[3]")
	WebElement auditQuestionMarks1;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[2]//td[3]")
	WebElement auditQuestionMarks2;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[3]//td[3]")
	WebElement auditQuestionMarks3;
	@FindBy(xpath = "//th[text()='Question']/parent::tr/parent::thead//following-sibling::tbody//tr[4]//td[3]")
	WebElement auditQuestionMarks4;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Required:']//child::span")
	WebElement auditCompareTRNApprovalReqVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//span[text()='No. of Approvals Completed:']//child::span")
	WebElement auditCompareTRNApprovalComVal;
	@FindBy(xpath = "//div[@id='CompareTRN']//div[@class='event-div']//h6[@class='status_heading']")
	WebElement auditCompareTRNFinalStatus;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']")
	WebElement auditCompareTRNActionValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[1]")
	WebElement auditCompareTRNActionByValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[2]")
	WebElement auditCompareTRNDateTimeValue;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Audit Trails_MEN98']")
	WebElement questionBankAudit;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Topic Name')]")
	WebElement searchByTopicNameDropdown;
	@FindBy(id = "Description")
	WebElement topicNameLike;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(id = "displayBtn")
	WebElement apply;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//input[@id='true-TRUE']")
	WebElement trueOrFalseQ1RB1320;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[1]")
	WebElement VerifyTopicNameATListScreen;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[2]")
	WebElement VerifyTopicUniqueCodeATListScreen;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[3]")
	WebElement VerifyFullNameATListScreen;

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr/td[4]")
	WebElement InitiatedDate;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement QBApprovalScreenTitle;

	@FindBy(xpath = "//label[contains(text(),'Topic Name')]/following-sibling::span")
	WebElement VerifyApprovalScreenTopic;

	@FindBy(xpath = "//label[contains(text(),'Topic Unique Code')]/following-sibling::span")
	WebElement VerifyApprovalScreenTopicUniqueCode;

	@FindBy(xpath = "//label[@for='CMQuesBankV1_QnbCode']/following-sibling::span")
	WebElement VerifyApprovalScreenQBUniqueCode;

	@FindBy(xpath = "//label[contains(text(),'Total Active Questions')]/following-sibling::span")
	WebElement VerifyApprovalScreenTotalActQuestions;

	@FindBy(xpath = "//label[text()='Approve']")
	WebElement highlightApprove;

	@FindBy(xpath = "//label[text()='Approve']//preceding-sibling::input")
	WebElement ApproveDec;
	@FindBy(xpath = "//*[@id='Remarks']")
	WebElement enterRemarks;

	@FindBy(xpath = "//html/body/form/section/header")
	WebElement auditTrailsHeader;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']")
	WebElement verifyApprovedLabel;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[1]")
	WebElement verifyApprovedByValue;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[2]")
	WebElement verifyApprovedDateandTime;

	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Approved']//following-sibling::p[3]")
	WebElement verifyApprovedRemarks;

	@FindBy(xpath = "//label[normalize-space()='Topic Name']/following-sibling::input")
	WebElement verifyTopicNameinQBRegistration;

	@FindBy(xpath = "//label[normalize-space()='Topic Unique Code']/following-sibling::input")
	WebElement verifyTopicUniqueCodeinQBRegistration;

	@FindBy(xpath = "//label[normalize-space()='Unique Code']/following-sibling::input")
	WebElement verifyUniqueCodeinQBRegistration;

	@FindBy(id = "appAction")
	WebElement appAction;

	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Unique Code')]")
	WebElement searchByUniqueCode;

	@FindBy(xpath = "//input[@id='UniqueCode']")
	WebElement UniqueCode;

	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;

	@FindBy(xpath = "//label[normalize-space()='Multiple Choice']")
	WebElement SSLabelQuestions1;

	@FindBy(xpath = "//label[normalize-space()='True or False']")
	WebElement SSLabelQuestion2;

	@FindBy(xpath = "//label[normalize-space()='Fill In The Blanks']")
	WebElement SSLabelQuestionFB;

	@FindBy(xpath = "//div[9]/table/tbody/tr[1]/td[2]")
	WebElement VerifyMCQuestionAT;

	@FindBy(xpath = "//div[9]/table/tbody/tr[1]/td[3]")
	WebElement VerifyMCQuestionMarksAT;

	@FindBy(xpath = "//div[9]/table/tbody/tr[2]/td[2]")
	WebElement VerifyMCQuestionOption1AT;

	@FindBy(xpath = "//div[9]/table/tbody/tr[3]/td[2]")
	WebElement VerifyMCQuestionOption2AT;

	@FindBy(xpath = "//div[9]/table/tbody/tr[4]/td[2]")
	WebElement VerifyMCQuestionOption3AT;

	@FindBy(xpath = "//div[9]/table/tbody/tr[5]/td[2]")
	WebElement VerifyMCQuestionOption4AT;

	@FindBy(xpath = "//div[11]/table/tbody/tr[1]/td[2]")
	WebElement VerifyFBQuestionAT;

	@FindBy(xpath = "//div[11]/table/tbody/tr[1]/td[3]")
	WebElement VerifyFBQuestionMarksAT;

	@FindBy(xpath = "//div[13]/table/tbody/tr[1]/td[2]")
	WebElement VerifyTFQuestionAT;

	@FindBy(xpath = "//div[13]/table/tbody/tr[1]/td[3]")
	WebElement VerifyTFQuestionMarksAT;

	@FindBy(xpath = "//div[15]/table/tbody/tr[1]/td[2]")
	WebElement VerifyEssayQuestionAT;

	@FindBy(xpath = "//div[15]/table/tbody/tr[1]/td[2]")
	WebElement VerifyEssayQuestionMakrsAT;

	@FindBy(xpath = "//*[@id='esign_Activity']")
	WebElement EsignTitle;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "(//*[@id='select2-Config_NarAtCreate-results']//child::li)")
	WebElement clickApprovalCount;

	@FindBy(xpath = "//input[@role='searchbox']")
	WebElement approvalSearchBox;

	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[1]")
	WebElement auditTrailPageColumn1;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[2]")
	WebElement auditTrailPageColumn2;
	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[3]")
	WebElement auditTrailPageColumn3;

	@FindBy(xpath = "//table[@id='ListTab']/descendant::td[4]")
	WebElement auditTrailPageColumn4;

	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;

	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;

	@FindBy(id = "Config_IsEsignAtCreatAppr")
	WebElement eSignAtRegApprCheckBox;

	@FindBy(xpath = "//*[@id='TMS_Course Manager']/li/a[text()='Configuration Audit Trails']")
	WebElement configurationAuditTrails;

	@FindBy(xpath = "//*[@id='TMS_Course Manager_Configuration Audit Trails']/li/a[text()='Question Bank']")
	WebElement configurationAuditTrailsQB;

	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']")
	WebElement callEsignAtInitiaiton;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']")
	WebElement callEsignAtApproval;

	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;

	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;

	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	public void prepare_QuestionBankRegistration_AuditTrails_Yes(HashMap<String, String> testdata) {
		// String TopicVal = "TopicLEIQSJTN";

		String TopicVal = CM_Topic.getTopic();
		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(prepareMenu, CommonStrings.Prepare_DC.getCommonStrings(), CommonStrings.Prepare_AC.getCommonStrings(),
				CommonStrings.Prepare_AR.getCommonStrings(), CommonStrings.Prepare_SS.getCommonStrings());

		click2(prepareQBMenu, QuestionBankStrings.QB_Config_DC.getQBStrings(),
				QuestionBankStrings.PrepareQB_AC.getQBStrings(), QuestionBankStrings.PrepareQB_AR.getQBStrings(),
				QuestionBankStrings.PrepareQB_SS.getQBStrings());

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				QuestionBankStrings.SearchBy_AC.getQBStrings(), QuestionBankStrings.SearchBy_AR.getQBStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(searchByDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
		TimeUtil.shortWait();

		sendKeys2(topicNameSearch, QuestionBankStrings.Like_TopicName_DC.getQBStrings(),
				TopicVal + testdata.get("percentageSign"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TopicStrings.Like_TopicName_SS.getTopicStrings());

		TimeUtil.shortWait();
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();

		click_Three_SS(displayedRecord, essays, QuestionBankStrings.click_Topic_DC.getQBStrings(),
				QuestionBankStrings.click_Topic_AC.getQBStrings(), QuestionBankStrings.click_Topic_AR.getQBStrings(),
				QuestionBankStrings.click_Topic_SS.getQBStrings());
		TimeUtil.shortWait();

		scrollToViewElement(multipleChoice);

		sendKeysAndRemoveFocus(multipleChoice, QuestionBankStrings.MCTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsMC"), QuestionBankStrings.MCTypeCount_AC.getQBStrings(),
				QuestionBankStrings.MCTypeCount_AR.getQBStrings(), QuestionBankStrings.MCTypeCount_SS.getQBStrings());

		TimeUtil.shortWait();

		sendKeys2(mcq1, QuestionBankStrings.EnterMC_Question_DC.getQBStrings(), testdata.get("MCQQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans1, QuestionBankStrings.EnterMC_Option_a_DC.getQBStrings(), testdata.get("MCQ1A1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_a_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans2, QuestionBankStrings.EnterMC_Option_b_DC.getQBStrings(), testdata.get("MCQ1A2"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans3, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A3"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_c_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans4, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A4"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_d_SS.getQBStrings());

		click2(mcq1RB2, QuestionBankStrings.Select_MCAnswer_b_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_MCAnswer_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("MCQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		TimeUtil.mediumWait();
		fillInTheBlanks.clear();

		scrollToViewElement(fillInTheBlanks);

		sendKeysAndRemoveFocus(fillInTheBlanks, QuestionBankStrings.FBTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsFIN"), QuestionBankStrings.FBTypeCount_AC.getQBStrings(),
				QuestionBankStrings.FBTypeCount_AR.getQBStrings(), QuestionBankStrings.FBTypeCount_SS.getQBStrings());

		sendKeys2(fillInTheBlanksQ1, QuestionBankStrings.EnterFB_Question_DC.getQBStrings(),
				testdata.get("FillintheblankQ1"), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		sendKeys2(fillInTheBlanksQ1Ans, QuestionBankStrings.EnterFB_ESS_ANS_DC.getQBStrings(), testdata.get("FQ1A"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterFB_ESS_ANS_SS.getQBStrings());

		sendKeys2(fillInTheBlanksQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("FQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		TimeUtil.mediumWait();
		trueOrFalse.clear();

		scrollToViewElement(trueOrFalse);

		sendKeysAndRemoveFocus(trueOrFalse, QuestionBankStrings.TFTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsTF"), QuestionBankStrings.TFTypeCount_AC.getQBStrings(),
				QuestionBankStrings.TFTypeCount_AR.getQBStrings(), QuestionBankStrings.TFTypeCount_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1, QuestionBankStrings.EnterTF_Question_DC.getQBStrings(), testdata.get("TrueorFalseQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		click2(trueOrFalseQ1RB1, QuestionBankStrings.Select_True_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_True_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("TFQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());
		TimeUtil.mediumWait();

		essays.clear();

		scrollToViewElement(essays);

		sendKeysAndRemoveFocus(essays, QuestionBankStrings.EssayTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsEssay"), QuestionBankStrings.EssayTypeCount_AC.getQBStrings(),
				QuestionBankStrings.EssayTypeCount_AR.getQBStrings(),
				QuestionBankStrings.EssayTypeCount_SS.getQBStrings());

		sendKeys2(essayQ1, QuestionBankStrings.EnterEssay_Question_DC.getQBStrings(), testdata.get("ESSAYQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		sendKeys2(essayQ1Ans1, QuestionBankStrings.EnterFB_ESS_ANS_DC.getQBStrings(), testdata.get("ESSAYQ1A"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterFB_ESS_ANS_SS.getQBStrings());

		sendKeys2(essayQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("ESSAYQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		scrollToViewElement(submitButton);

		click2(submitButton, TopicStrings.Submit_DC.getTopicStrings(), QuestionBankStrings.Submit_AC.getQBStrings(),
				QuestionBankStrings.Submit_AR.getQBStrings(), TopicStrings.Submit_SS.getTopicStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		scrollToViewElement(courseManagerAuditTrails);
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();

		scrollToViewElement(questionBankAudit);
		TimeUtil.shortWait();
		click2(questionBankAudit, QuestionBankStrings.QB_Config_DC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AR.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_SS.getQBStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				QuestionBankStrings.SearchBy_Audit_AC.getQBStrings(),
				QuestionBankStrings.SearchBy_Audit_AR.getQBStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(searchByDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
		TimeUtil.shortWait();

		sendKeys2(topicNameLike, QuestionBankStrings.Like_TopicName_AuditTrails_DC.getQBStrings(),
				TopicVal + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TopicStrings.Like_TopicName_SS.getTopicStrings());

		TimeUtil.shortWait();
		click2(apply, CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());

		click_Five_SS_AuditTrails(displayedRecord, SSLabelQuestions1, SSLabelQuestionFB, auditCompareTRNApprovalReqVal,
				QuestionBankStrings.click_QB_AuditTrails_DC.getQBStrings(),
				QuestionBankStrings.Click_TopicBeforeApproval_AC.getQBStrings(),
				QuestionBankStrings.Click_TopicBeforeApproval_AR.getQBStrings(),
				QuestionBankStrings.click_QB_AuditTrails_SS.getQBStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();

		scrollToViewElement(auditQuestionMarks2);

		List<WebElement> QPTyepes = driver
				.findElements(By.xpath("//*[@id='CompareTRN']/div[2]/div/div/div/div/div/label"));
		for (WebElement ele : QPTyepes) {

			String QPType = ele.getText().trim();

			if (QPType.equals("Multiple Choice")) {

			}

			else if (QPType.equals("Fill In The Blanks")) {
				scrollToViewElement(VerifyFBQuestionAT);

			}

			else if (QPType.equals("True or False")) {
				scrollToViewElement(VerifyTFQuestionAT);

			}

			else if (QPType.equals("Essay")) {
				scrollToViewElement(VerifyEssayQuestionAT);
			}

		}

		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				QuestionBankStrings.click_QBAuditTrails_AC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AR.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_SS.getQBStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	public void QBRegistrationApproval_Configuration(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(configMenu, CommonStrings.CM_ConfigureMenu_DC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AC.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_AR.getCommonStrings(),
				CommonStrings.CM_ConfigureMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(questionBankConfig, QuestionBankStrings.QB_Config_DC.getQBStrings(),
				QuestionBankStrings.QB_Config_AC.getQBStrings(), QuestionBankStrings.QB_Config_AR.getQBStrings(),
				QuestionBankStrings.QB_Config_SS.getQBStrings());

		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		String configUniqueCodeValue = configurationUniqueCode.getAttribute("value");
		System.out.println(configUniqueCodeValue);

		SelectRadioBtnAndCheckbox(driver, eSignAtInitCheckBox, "Registration");
		SelectRadioBtnAndCheckboxRegistrationApproval(driver, eSignAtRegApprCheckBox, "Call E-sign At: Approval");
		TimeUtil.shortWait();
		click2(noOfAprReqForRegDrpdwn, CommonStrings.NoOfApprovals_DC.getCommonStrings(),
				CommonStrings.NoOfApprovals_AC.getCommonStrings(), CommonStrings.NoOfApprovals_AR.getCommonStrings(),
				CommonStrings.NoOfApprovals_SS.getCommonStrings());

		sendKeys2(approvalSearchBox, CommonStrings.Enter_Approval1_DC.getCommonStrings(), testData.get("ApprovalCount"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		click2(clickApprovalCount, CommonStrings.One_Approval_DC.getCommonStrings(),
				CommonStrings.Zero_Approvals_AC.getCommonStrings(), CommonStrings.Zero_Approvals_AR.getCommonStrings(),
				CommonStrings.Approval_1SS.getCommonStrings());

		scrollToViewElement(remarks);

		TimeUtil.shortWait();
		sendKeys2(remarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		click2(submitButton, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Submit_Button_AC.getCommonStrings(), CommonStrings.Submit_Button_AR.getCommonStrings(),
				CommonStrings.Submit_Button_SS.getCommonStrings());

		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(HashMap<String, String> testData) {
		// String TopicVal = "TopicLEIQTIBT";
		String TopicVal = CM_Topic.getTopic();
		waitForElementVisibile(menu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(Approve, CommonStrings.CM_ApproveMenu_DC.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_AC.getCommonStrings(), CommonStrings.CM_ApproveMenu_AR.getCommonStrings(),
				CommonStrings.CM_ApproveMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(ApproveQB, QuestionBankStrings.QB_Config_DC.getQBStrings(),
				QuestionBankStrings.QB_Approve_AC.getQBStrings(), QuestionBankStrings.QB_Approve_AR.getQBStrings(),
				QuestionBankStrings.QB_Config_SS.getQBStrings());
//		click2(ApproveQB, QuestionBankStrings.QB_Config_DC.getQBStrings(),
//				QuestionBankStrings.QB_Approve_AC.getQBStrings(), QuestionBankStrings.QB_Approve_AR.getQBStrings(),
//				QuestionBankStrings.QB_Config_SS.getQBStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				QuestionBankStrings.SearchBy_AC.getQBStrings(), QuestionBankStrings.SearchBy_AR.getQBStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
		TimeUtil.shortWait();
		sendKeys2(topicNameLike, QuestionBankStrings.Like_TopicName_DC.getQBStrings(),
				TopicVal + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), TopicStrings.Like_TopicName_SS.getTopicStrings());
		click2(searchFilterApplyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click_Five_SS(displayedRecord, SSLabelQuestions1, SSLabelQuestion2, enterRemarks,
				QuestionBankStrings.Click_TopicforApproval_DC.getQBStrings(),
				QuestionBankStrings.Click_TopicforApproval_AC.getQBStrings(),
				QuestionBankStrings.Click_TopicforApproval_AR.getQBStrings(),
				QuestionBankStrings.Click_TopicforApproval_SS.getQBStrings());
		scrollToViewElement(auditQuestionMarks2);
		click2(ApproveDec, CommonStrings.Select_Approve_DC.getCommonStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				CommonStrings.Select_Approve_SS.getCommonStrings());
		sendKeys2(enterRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("Approvalremarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submit, CommonStrings.Submit_Button_DC.getCommonStrings(),
				QuestionBankStrings.Esign_Approve_AC.getQBStrings(),
				QuestionBankStrings.Esign_Approve_AR.getQBStrings(), CommonStrings.Submit_SS.getCommonStrings());
		TimeUtil.shortWait();
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		scrollToViewElement(courseManagerAuditTrails);
		click2(courseManagerAuditTrails, CommonStrings.CM_AudiTrailsMenu_DC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AC.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_AR.getCommonStrings(),
				CommonStrings.CM_AudiTrailsMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(questionBankAudit);
		TimeUtil.shortWait();
		click2(questionBankAudit, QuestionBankStrings.QB_Config_DC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AR.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_SS.getQBStrings());
		TimeUtil.shortWait();
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				QuestionBankStrings.SearchBy_Audit_AC.getQBStrings(),
				QuestionBankStrings.SearchBy_Audit_AR.getQBStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByDropdown, TopicStrings.SearchBy_TopicName_DC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AC.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_AR.getTopicStrings(),
				TopicStrings.SearchBy_TopicName_SS.getTopicStrings());
		TimeUtil.shortWait();
		sendKeys2(topicNameLike, QuestionBankStrings.Like_TopicNameAfterApprovalAT_DC.getQBStrings(), TopicVal,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				TopicStrings.Like_TopicName_SS.getTopicStrings());

		TimeUtil.shortWait();
		click2(apply, CommonStrings.ApplyButton_DC.getCommonStrings(), CommonStrings.ApplyButton_AC.getCommonStrings(),
				CommonStrings.ApplyButton_AR.getCommonStrings(), CommonStrings.ApplyButton_SS.getCommonStrings());
		click_Five_SS_AuditTrails(displayedRecord, SSLabelQuestions1, SSLabelQuestionFB, auditCompareTRNApprovalReqVal,
				QuestionBankStrings.Click_TopicAfterApproval_DC.getQBStrings(),
				QuestionBankStrings.Click_TopicAfterApproval_AC.getQBStrings(),
				QuestionBankStrings.Click_TopicAfterApproval_AR.getQBStrings(),
				QuestionBankStrings.click_QB_AuditTrails_SS.getQBStrings());
		TimeUtil.shortWait();
		TimeUtil.shortWait();
		scrollToViewElement(auditQuestionMarks2);
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				QuestionBankStrings.click_QBAuditTrails_AC.getQBStrings(),
				QuestionBankStrings.click_QBAuditTrails_AR.getQBStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

}
