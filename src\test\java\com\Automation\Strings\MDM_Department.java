package com.Automation.Strings;

public enum MDM_Department {

	// MDM Icon
	Click_MDM_Menu_DC("Click on 'MDM' icon"),
	<PERSON>lick_MDM_Menu_AC("Options to select 'Masters, System Configuration and Sytem Manager' should be available."),

	<PERSON><PERSON>_MDM_Menu_AR("Options to select 'Masters, System Configuration and Sytem Manager' are available."),
	Click_MDM_Menu_SS("'MDM' icon"),

	// System configuration
	Click_MDM_SytemConfigurationMenu_DC("Click on 'System Configuration' menu."),
	Click_MDM_SytemConfigurationMenu_AC(
			"'Form Workflow Mapping' should be displayed with other assigned menus(if any)."),
	Click_MDM_SytemConfigurationMenu_AR("'Form Workflow Mapping' is getting be displayed."),
	Click_MDM_SytemConfigurationMenu_SS("'System Configuration'."),

	// Master
	Click_MasterMenu_DC("Click on 'Master' menu."),
	Click_MasterMenu_AC("Assigned menus under 'Master' should be displayed."),
	Click_MasterMenu_AR("'Assigned menus under 'Master' are getting displayed."), Click_MasterMenu_SS("'Master'."),

	// Department Menu

	Click_Department_Menu_DC("Click on 'Department' menu."),
	Click_Department_Menu_AC("Assigned menus under 'Department' should be displayed."),
	Click_Department_Menu_AR("Assigned menus under 'Department' are getting displayed."),
	Click_Department_Menu_SS("'Department'."),

	// Department Registration Menu

	Click_Registration_Menu_DC("Click on 'Registration' menu."),
	Click_DepartmentRegistration_Menu_AC("'Department Registration Initiation' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain 'Department Name, Department Code, Description, Department Type, Remark(s) / Reason(s)' fileds."),
	Click_DepartmentRegistration_Menu_AR("'Department Registration Initiation' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with 'Department Name, Department Code, Description, Department Type, Remark(s) / Reason(s)' fileds."),
	Click_DepartmentRegistration_Menu_SS("'Department Registration'."),

	// Department Audit Trails Menu
	Click_DepartmentAuditTrails_Menu_DC("Click on 'Audit Trails' menu."),
	Click_DepartmentAuditTrails_Menu_AC("'Department Audit Trail' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain 'Department Name, Department Code, Description, Intiated By, Intiated On' columns.</div>"
			+ "<div><b>* </b>List of registered Departments should be displayed</div>"),
	Click_DepartmentAuditTrails_Menu_AR("'Department Audit Trail' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with 'Department Name, Department Code, Description, Intiated By, Intiated On' columns.</div>"
			+ "<div><b>* </b>List of registered Departments are getting displayed</div>"),
	Click_DepartmentAuditTrails_Menu_SS("'Department Audit Trails'."),

	// FormWork Flow Menu

	Click_FormWorkflow_Menu_DC("Click on 'Form Workflow Mapping' menu."),
	Click_FormWorkflow_Menu_AC(
			"'Modification' menu should be displayed along with other assigned menus(if any) under'Form Workflow Mapping' menu."),
	Click_FormWorkflow_Menu_AR(
			"'Modification' menu is getting displayed with other assigned menus under 'Form Workflow Mapping' menu."),
	Click_FormWorkflow_Menu_SS("'Form Workflow Mapping'"),

	// Form Work Flow Modification menu

	Click_FormWorkflowModificiation_Menu_DC("Click on 'Modification' menu."),
	Click_FormWorkflowModificiation_Menu_AC("'Form Workflow Mapping Modification' screen should be displayed.<div>"
			+ "<div><b>* </b>The screen should contain ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen should contain ‘Form Workflow Mapping Code’, ‘Revision No’ and ‘Initiated By’,‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>The screen should contain a list of all the registered 'Form Workflow Mapping Codes'.</div>"),
	Click_FormWorkflowModificiation_Menu_AR("'Form Workflow Mapping Modification' screen is getting displayed.<div>"
			+ "<div><b>* </b>The screen is getting displayed with ‘Total Records Count’, ‘Advanced Search’ and ‘Search this Page’ icons.</div>"
			+ "<div><b>* </b>The screen should is getting displayed with ‘Form Workflow Mapping Code’, ‘Revision No’ and ‘Initiated By’,‘Initiated On’ columns.</div>"
			+ "<div><b>* </b>The screen is getting displayed with a list of all the registered 'Form Workflow Mapping Codes'.</div>"),
	Click_FormWorkflowModificiation_Menu_SS("'Form Workflow Mapping Modification'."),

	// Enter Form Work Flow Map code
	Enter_FormWorkFlowMapcode_DC(
			"Enter the Department related 'Form Workflow Mapping Code' in 'Form Workflow Mapping Code' search box."),
	Enter_FormWorkFlowMapcode_SS("'Form Workflow Mapping Code'."),

	// Click on Form Work flow Map code
	Click_On_FormWorkFlowMapCode_DC("Click on the required 'Form Workflow Mapping Code'."),
	Click_On_FormWorkFlowMapCode_AC(
			"'Form Workflow Mapping Modification Initiation’ screen should be displayed.</div>"),
	Click_On_FormWorkFlowMapCode_AR(
			"'Form Workflow Mapping Modification Initiation’ screen is getting displayed.</div>"),
	Click_On_FormWorkFlowMapCode_SS("'Form Workflow Mapping Modification Initiation'"),

	// Removed Selected Work Flow
	Remove_Selected_WorkFlow_DC("Click on '×' against the selected Workflow under 'Work Flow' field."),
	Remove_Selected_WorkFlow_AC("Selected 'Work Flow should be removed'."),
	Remove_Selected_WorkFlow_AR("Selected 'Work Flow is getting removed'."), Remove_Selected_WorkFlow_SS("'×'."),

	// Click Add Item for Work Flow Code
	Click_AddItem_DC("Click on 'Add Item' for 'Work Flow' field."),
	Click_AddItem_AC("'Workflow' window should be displayed."),
	Click_AddItem_AR("'Workflow' window is getting displayed."), Click_AddItem_SS("'Workflow'."),

	// Enter 1 Approval Work Flow Code
	Enter_WorkflowCode1Approval_DC("Enter the '1 Approval_N' in 'Work Flow Code' search box"),
	Enter_WorkflowCode1Approval_SS("'1 Approval_N'"),

	// Select 1 Approval Work Flow Code
	Select_WorkFlowCode_DC("'Select '1 Approval_N'"),

	// Click OK Buttton
	Click_OK_DC("Click on 'OK' button"),
	Click_OK_AC("Selected value should be displayed under 'Work Flow Code' field."),
	Click_OK_AR("Selected value is getting displayed under 'Work Flow Code' field."), Click_OK_SS("'OK'"),

	// Configuration Confirmation message

	Config_Proceed_AC(
			"'Form Workflow Mapping Modification Initiation' Form Workflow Mapping Code : (Form Workflow Mapping Code) Transaction Intiaited message should be displayed with 'Done' button."),
	Config_Proceed_AR(
			"'Form Workflow Mapping Modification Initiation' Form Workflow Mapping Code : (Form Workflow Mapping Code) Transaction Intiaited message should be is getting displayed with 'Done' button."),

	// Enter Department Name

	Enter_DeptName_DC("Enter the value less than or equals to '60' characters in 'Department Name' field."),
	Enter_DeptName_SS("'Department Name'."),

	// Enter Department Name

	Enter_DeptCode_DC("Enter the value less than or equals to '10' characters in 'Department Code' field."),
	Enter_DeptCode_SS("'Department Code'."),

	// Enter Department Description

	Enter_DeptDescription_DC(
			"Enter the value less than or equals to '250' characters in 'Department Description' field."),
	Enter_DeptDescription_SS("'Department Decription'."),

	// Click Department drop down

	Click_DeptDD_DC("Click on 'Department Type' dropdown."),
	Click_DeptDD_AC("Option to select list of Department Types should be available. "),
	Click_DeptDD_AR("Option to select list of Department Types are available. "), Click_DeptDD_SS("'Department Type'"),

	// Select Dept Type

	Select_DeptType_DC("Select the required Department Type."),

	// Department Registration Confirmation

	Department_Proceed_AC(
			"'Department Registration Initiation' Department Code : (Department Code) Transaction Initiaited message should be displayed with 'Done' button."),
	Department_Proceed_AR(
			"'Department Registration Initiation' Department Code : (Department Code) Transaction Initiaited is getting displayed with 'Done' button."),

	// Audit Trails DepartmentCode

	Enter_DeptCodeAT_DC(
			"Enter the Department Code of the above registered Department in the Department Code search filter."),
	Enter_DeptCodeApprovedAT_DC(
			"Enter the Department Code of the above approved Department in the Department Code search filter."),
	
	//MDM 
	
	Click_MDM_Tab_DC("Click on 'MDM' tab."),
	Click_MDM_Tab_AC("Pedning tasks should be displayed for the logged in user under 'My Tasks' section."),
	Click_MDM_Tab_AR("Pedning tasks are getting displayed for the logged in user under 'My Tasks' section."),
	Click_MDM_Tab_SS("'MDM'"),	
	
	// Click On Registered Department

	Click_DeptCodeAT_DC("Click on the above registered record."),
	Click_DeptCodeATApproval_DC("Click on the above approved record."),
	Click_DeptCodeATApproval_AC(
			"'Department - Audit Trail - Revision No.: 0 -Registration'screen should be displayed.</div>"
					+ "<div><b>* </b>The screen should contain the details of the Department entered during registration.</div>"
					+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section should contain  Registration ‘Initiated’ and 'Approved' transactions with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘1’ and ‘1’ respectively.</div>"
					+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),
	Click_DeptCodeATApproval_AR(
			"'Department - Audit Trail - Revision No.: 0 -Registration'screen is getting displayed.</div>"
					+ "<div><b>* </b>The screen is getting displayed with the details of the Department entered during registration.</div>"
					+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
					+ "<div><b>* </b>The ‘Events’ section is getting displayed with Registration ‘Initiated’ and 'Approved'transaction with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
					+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘1’ and ‘1’ respectively.</div>"
					+ "<div><b>*</b>All the particulars are getting displayed in read only format.</div>"),

	Click_DeptCodeAT_AC("'Department - Audit Trail - Revision No.: 0 -Registration'screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain the details of the Department entered during registration.</div>"
			+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section should contain only with the Registration ‘Initiated’ transactions with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘1’ and ‘1’ respectively.</div>"
			+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),

	Click_DeptCodeAT_AR("'Department - Audit Trail - Revision No.: 0 -Registration'screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with the details of the Department entered during registration.</div>"
			+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section is getting displayed with only the Registration ‘Initiated’ transaction with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘1’ and ‘1’ respectively.</div>"
			+ "<div><b>*</b>All the particulars are getting displayed in read only format.</div>"),

	Click_DeptCodeAT_SS("''Department - Audit Trail'"),

	// My Task Department

	Click_Department_MyTask_DC("Click on the Department under 'My Task' section."),
	Click_Department_MyTask_AC("'Department My Task' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain list of Department which are under Approval"),
	Click_Department_MyTask_AR("'Department My Task' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with list of Department which are under 'Approval'"),
	Click_Department_MyTask_SS("'Department My Task'"),

	// Department Approval Screens

	Click_DeptName_Approval_AC("'Department Registraiton Approval' screen should be displayed."),

	Click_DeptName_Approval_AR("'Department Registraiton Approval' screen is getting displayed."),
	Click_DeptName_Approval_SS("'Department Registration Approval'"),

	// Registration Submit
	Click_DepartmentRegSubmit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Department - Registration Initiation"),
	Click_DepartmentRegSubmit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Department - Registration Initiation"),
//Configuration Submit 

	Click_DepartmentConfiguration_Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Form Workflow Mapping - Modification Initiation"),
	Click_DepartmentConfiguration_Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Form Workflow Mapping - Modification Initiation"),
	// Registration Approval Submit

	Click_DepartmentApprovalSubmit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Department - Registration Approval"),
	Click_DepartmentApprovalSubmit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Department - Registration Approval"),

	// Registration Submit

	Click_DepartmentREGSubmit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Department - Registration Initiation"),
	Click_DepartmentREGSubmit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Department - Registration Initiation"),

	// Click Proceed Department Registration

	Click_DeptRegistrationApprove_AC(
			"'Department Registration Approval' Department Code  : (Department Code ) Transaction Approved message should be displayed with 'Done' button."),
	Click_DeptRegistrationApprove_AR(
			"'Department Registration Approval' Department Code  : (Department Code ) Transaction Approved message is getting displayed with 'Done' button."),

	// Close Audit Trails

	Close_AuditTrails_AC("'Department Audit Trail' screen should be displayed."),
	Close_AuditTrails_AR("'Department Audit Trail' screen is getting displayed."),

	// Common Masters Menu

	Click_CommonMasterMenu_DC("Click on 'Common Masters Plant Mapping' menu"),
	Click_CommonMasterMenu_AC("Assigned menus under 'Common Masters Plant Mapping' should be displayed."),
	Click_CommonMasterMenu_AR("Assigned menus under 'Common Masters Plant Mapping' are getting displayed."),
	Click_CommonMasterMenu_SS("'Common Masters Plant Mapping'"),

	// Common Masters Modification Menu
	Click_Modification_Menu_DC("Click on 'Modification' menu"),
	Click_CommonMaster_Modification_Menu_AC("'Plant Mapping Modification' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain 'Plant Mapping Code, Plant Name, Initiated By, Initiated On, Revision No.' coloumns</div>"
			+ "<div><b>* </b>The screen should contain list of plant names for which Plant Mapping Code registration has been already completed</div>"),

	Click_CommonMaster_Modification_Menu_AR("'Plant Mapping Modification' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with 'Plant Mapping Code, Plant Name, Initiated By, Initiated On, Revision No.' coloumns</div>"
			+ "<div><b>* </b>The screen is getting displayed with list of plant names for which Plant Mapping Code registration has been already completed</div>"),
	Click_CommonMaster_Modification_Menu_SS("'Modification'"),

	// CommonMasters

	Enter_MasterPlantCode_DC("Enter the Plant Mapping Code of 'Master Plant' in Plant Mapping Code search box."),
	Enter_MasterPlantCode_SS("'Plant Mapping Code'"),

	Click_MasterPlantCode_DC("Click on the plant mapping code of Master Plant"),
	Click_MasterPlantCode_AC("'Plant Mapping Modification Initiation' screen should be  displayed.</div>"
			+ "<div><b>* </b>Option to add Departments should be available.</div>"),
	Click_MasterPlantCode_AR("'Plant Mapping Modification Initiation' screen is getting displayed.</div>"
			+ "<div><b>* </b>Option to add Departments is available.</div>"),

//Click Add Item for Department

	Click_AddItemDept_DC("Click on 'Add Item' for Department."),
	Click_AddItemDept_AC("'Department' window should be displayed.</div>"
			+ "<div><b>* </b>The window should contain 'Department Code, Department Name' columns.</div>"
			+ "<div><b>* </b>All the registered departments should be displayed.</div>"),
	Click_AddItemDept_AR("'Department' window is getting displayed.</div>"
			+ "<div><b>* </b>The window is geting displayed 'Department Code, Department Name' columns.</div>"
			+ "<div><b>* </b>All the registered departments are getting displayed.</div>"),

	Click_AddItemDept_SS("'Department' window"),

	// Click Add against Department Code
	Click_ADD_DC("Click on 'Add' button"), Click_ADD_AC("Selected Department should be added under Department field"),
	Click_ADD_AR("Selected Department is getting added under Department field"),

	ClickDeptAdd_DC("Click on 'Add' against above department"),
	ClickDeptAdd_AC("Selected Item should be added under 'Selected Items' coloumn."),
	ClickDeptAdd_AR("Selected Item is getting added under 'Selected Items' coloumn."), ClickDeptAdd_SS("'Add'"),

	// Click View more

	Click_ViewMore_DC("Click on 'View+(no)' under Deparment field."),
	Click_ViewMore_AC("Above selected Department should be displayed with other departments(if any)."),
	Click_ViewMore_AR("Above selected Department is getting displayed."), Click_ViewMore_SS("'View+(no)'"),

	// Click Submit Common Masters

	Click_CommonMasterSubmit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Plant Mapping - Modification Initiation'.</div>"),
	Click_CommonMasterSubmit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Plant Mapping - Modification Initiation'.</div>"),

	// Click CommonMansters Audit Trails
	Click_CommonMastersPlantMap_AC("'Plant Mapping Audit Trail' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain list of the Plants"),
	Click_CommonMastersPlantMap_AR("'Plant Mapping Audit Trail' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with list of the Plants"),
	Click_CommonMastersPlantMap_SS("'Plant Mapping Audit Trail'"),

	// Enter Modified Plant Code

	Enter_ModdPlantCode_DC(
			"Enter the plant mapping code of the above modified plant in the Plant Mapping Code search box"),

	// Click Modified Plant Code

	Click_ModdPlantCode_DC(
			"Click on the plant mapping code of the above modified plant in the Plant Mapping Code search box."),
	Click_ModdPlantCode_AC("'Transactions' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain list of transaction tabs."),

	Click_ModdPlantCode_AR("'Transactions' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen is getting displayed with list of Transaction tab."),
	Click_ModdPlantCode_SS("'Transactions'"),

//Click ModTab

	Click_ModTab_DC("Click on the latest Modification Tab"),
	Click_ModTab_AC("Selected Tab should be displayed with blue border<div>"
			+ "<div><b>* </b>'Proceed' button should be enabled.</div>"),
	Click_ModTab_AR("Selected Tab is getting displayed with blue border<div>"
			+ "<div><b>* </b>'Proceed' button is getting enabled.</div>"),
	Click_ModTab_SS("'Modification' Tab"),

	// Click Procced

	Click_Proceed_DC("Click on 'Proceed' button."),

	Click_Proceed_AC("'Plant Mapping - Audit Trail' screen should be displayed.</div>"
			+ "<div><b>* </b>The screen should contain the details of the Plant Mapping Code entered/selected during modification.</div>"
			+ "<div><b>* </b>‘Final Status’ should be displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section should contain only the Modification ‘Initiated’ transaction with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both should be read as ‘0’ and ‘0’ respectively.</div>"
			+ "<div><b>*</b>All the particulars should be displayed in read only format.</div>"),
	Click_Proceed_AR("'Plant Mapping - Audit Trail' screen is getting displayed.</div>"
			+ "<div><b>* </b>The screen should contain the details of the Plant Mapping Code entered/selected during modification.</div>"
			+ "<div><b>* </b>‘Final Status’ is getting displayed as ‘Initiated’.</div>"
			+ "<div><b>* </b>The ‘Events’ section is getting displayd with only the Modification ‘Initiated’ transaction with ‘Username, Date& Time and Remarks/ Reasons’ details.</div>"
			+ "<div><b>* </b>Also, the ‘No. of Approvals Required’ and the ‘No. of Approvals Completed’ both are read as ‘0’ and ‘0’ respectively.</div>"
			+ "<div><b>*</b>All the particulars are getting displayed in read only format.</div>"),

	// Select Required Effective Date

	Select_EffectiveDate_DC("Select 'Effective Date' greater than current date."),
	Select_EffectiveDate_AC("Selected date should be displayed for the 'Effective Date' field."),
	Select_EffectiveDate_AR("Selected date is getting displayed for the 'Effective Date' field."),
	Select_EffectiveDate_SS("'Effective Date'"),

	// Effective Date Calender

	Click_EffectiveDate_DC("Click on calender icon for 'Effective Date' field."),
	Click_EffectiveDate_AC("Calender should be displayed</div>"
			+ "<div><b>*</b> By default current date should be highlighted in blue colour.</div>"),
	Click_EffectiveDate_AR("Calender is getting displayed</div>"
			+ "<div><b>*</b> By default current date is gettingd highlighted in blue colour.</div>"),
	Click_EffectiveDate_SS("‘'Effective Date'’ button");

	private final String departmentStrings;

	MDM_Department(String MDMDepartmentStrings) {

		this.departmentStrings = MDMDepartmentStrings;

	}

	public String getMDM_DepartmentStrings() {
		return departmentStrings;
	}

}
