package com.Automation.learniqObjects;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.RecordAttendanceStrings;
import com.Automation.Strings.TrainerStrings;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.Markup;
import com.aventstack.extentreports.markuputils.MarkupHelper;

public class CM_RecordAttendance extends OQActionEngine {

	public static int AbsentUsersCount;
	public static List<String> listAbsentUsers = new ArrayList<>();
	public static String[] AbsentUsers;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[7]//a[contains(@class,'sub-menu')][contains(text(),'Record')]")
	WebElement recordMenu;

	@FindBy(id = "TMS_Course Manager_Record_MEN09")
	WebElement attendanceMenu;

	@FindBy(xpath = "//span[@id='select2-SearchType-container']")
	WebElement searchByNew;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Course Name']")
	WebElement searchByCourseName;

	@FindBy(id = "CourseName")
	WebElement courseNameField;

	@FindBy(id = "displayBtn")
	WebElement applyNew;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//*[text()='Candidates List']")
	WebElement candidatesListHeader;

	@FindBy(xpath = "//span[@id='MainTitle']")
	WebElement mainHeader;
	@FindBy(xpath = "//table[@id='ListTabUser2']/tbody/tr")
	List<WebElement> candidateListRows;

	@FindBy(id = "btnModal_N5Transcations_AddUsersList")
	WebElement additionalUsersAddIcon;

	@FindBy(id = "N5Transcations_AddUsersListbtnClientSearch")
	WebElement additionalUsersSearchFilter;

	@FindBy(xpath = "//div[@id='multipopupfilter2_filter']/label/input[@type='search']")
	WebElement searchString;

	@FindBy(xpath = "//table[@id='multipopupfilter2']/tbody/tr/td/button[@type='button']")
	WebElement addBtn;

	@FindBy(xpath = "//*[@id'multipopupfilter2']/tbody/tr[12/td[5]")
	WebElement addBtn2;

	@FindBy(id = "N5Transcations_AddUsersList_selectBtn")
	WebElement usersListAddBtn;

	@FindBy(id = "sessionAtndValues_AdditionalUsersRem")
	WebElement additionalUsersRemarksReasons;

	@FindBy(xpath = "//h6[text()='Actual']")
	WebElement actualHeader;

	@FindBy(xpath = "//h6[text()='Venue(s)']")
	WebElement venueField;

	@FindBy(id = "btnModal_Attendance_EvalPopUpVC")
	WebElement evaluatorBtn;

	@FindBy(xpath = "//span[@id='select2-Attendance_EvalPopUpVC_selectDdl-container']/parent::span//following-sibling::span[@class='select2-selection__arrow']")
	WebElement evaluatorListDropDown;

	@FindBy(xpath = "//ul[@id='select2-Attendance_EvalPopUpVC_selectDdl-results']//li[text()='Employee Name']")
	WebElement searchByEmployeeName;

	@FindBy(xpath = "//ul[@id='select2-Attendance_EvalPopUpVC_selectDdl-results']//li[text()='Employee ID']")
	WebElement searchByEmployeeID;

	@FindBy(xpath = "//input[@id='Attendance_EvalPopUpVC_FindTxt']")
	WebElement evaluatorFindTextbox;

	@FindBy(xpath = "//input[@id='Attendance_EvalPopUpVC_DisplayBtn']")
	WebElement applyBtn;

	@FindBy(xpath = "(//table[@id='ListTab']/tbody/tr/td/input[@type='radio'])[1]")
	WebElement selectFilteredEvaluator;

	@FindBy(xpath = "//button[@id='Attendance_EvalPopUpVC_selectBtn']")
	WebElement addNew;

//	@FindBy(xpath = "//table[@id='multipopupfilter2']/tbody/tr")
//	List<WebElement> additionalUsersEmployeeList;

	@FindBy(id = "btnPreview")
	WebElement preview;

	@FindBy(id = "btnSubmit")
	WebElement submit;

	@FindBy(xpath = "//table[@id='ListTabUser2']//thead//th[contains(text(),'Select All')]//input")
	WebElement selectALL;

	@FindBy(xpath = "//span[@id='select2-VerbalFlag-container']")
	WebElement evaluationMethodDropDown;

	@FindBy(xpath = "//li[text()='Verbal']")
	WebElement evaluationMethodVerbal;

	@FindBy(id = "TypeOFAtndRD_2")
	WebElement selectManual;

	public void recordAttendance_OnlineSession_2AdditionalUsers(HashMap<String, String> testData) {

		String AbsentUser = CM_CourseSession.getAbsentEmployee();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();

		if (CM_QuestionPaper.EvaluationType.equals("Manual Evaluation")) {
			click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

			click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());

			click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
					CM_QuestionPaper.Evaluator, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());

			TimeUtil.shortWait();

			click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

			click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		}

//		TimeUtil.mediumWait();
//		try {
//			click2(selectManual, "Select manual",
//					"Selection should be acepted and op[tion to select present/ absent should be available",
//					"Selection is getting accepted and option to select present/absent is available", "Manual");
//		}catch(NoSuchElementException e) {
//			System.out.println(e.getMessage());
//		}

		scrollToViewElement(candidatesListHeader);
		// scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);
//		scrollToViewElement(additionalUsersAddIcon);
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		waitForElementVisibile(additionalUsersSearchFilter);
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserTBR(), RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserQ(), RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//		click2(addBtn2, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
////		searchString.clear();
//		scrollToViewElement(additionalUsersRemarksReasons);
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
		scrollToViewElement(actualHeader);
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

//	public void recordAttendanceOfflineSessionType_VerbalEvaluation(HashMap<String, String> testData) {
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
//				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
//				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
//
//		switchToBodyFrame(driver);
//
//		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
//				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
//				CommonStrings.SearchBy_SS.getCommonStrings());
//
//		TimeUtil.mediumWait();
//
//		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
//
//		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
//				CM_BatchFormation.courseName, CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
//
//		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(actualHeader);
//
//		selectEndTimeInRecordAttendanceOQFormat();
//
//		scrollToViewElement(venueField);
//
//		click2(evaluationMethodDropDown, RecordAttendanceStrings.Click_Eval_Method_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_SS.getRecordAttendanceStrings());
//
//		click2(evaluationMethodVerbal, RecordAttendanceStrings.Select_Verbal_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_SS.getRecordAttendanceStrings());
//
//		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());
//
//		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());
//
//		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
//				testData.get("EvaluatorName"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(candidatesListHeader);
//
//		selectMultipleCheckBoxes(candidateListRows,
//				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), testData);
//
//		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(), selectUserName, skippedUserName);
//
//		scrollToViewElement(additionalUsersAddIcon);
//
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				testData.get("EmployeeID"),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		scrollToViewElement(additionalUsersRemarksReasons);
//
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(), selectUserName, skippedUserName);
//
//		scrollToViewElement(preview);
//
//		TimeUtil.shortWait();
//
//		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(submit);
//
//		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//	}
//
//	// To select Result radio button either Pass or Fail
//
//	public void selectResultRadioButton(List<WebElement> element, String stepDescription, String acceptanceCriteria,
//			String actualResult, String scName, String selectUser, String skippedUser) {
//
//		try {
//			n = n + 1;
//
//			String[][] data = {
//					{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
//							"<b>Actual Result</b>" },
//					{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
//							"<div><b>* </b>" + actualResult } };
//
//			Markup tableMarkup = MarkupHelper.createTable(data);
//			String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
//			test.log(Status.PASS, modifiedMarkup);
//			attachScreenshot(driver, true, scName, "click2");
//
//			for (WebElement row : element) {
//
//				WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));
//
//				WebElement employeeNameElement = row.findElement(By.xpath("./td[1]"));
//
//				WebElement passRadioButton = row.findElement(By.xpath(".//div/label[text()='Pass']"));
//
//				WebElement failRadioButton = row.findElement(By.xpath(".//div/label[text()='Fail']"));
//
//				if (checkbox.isSelected()) {
//
//					if (employeeNameElement.getText().contains(selectUser)) {
//						passRadioButton.click();
//					} else if (employeeNameElement.getText().contains(skippedUser)) {
//						failRadioButton.click();
//					}
//				}
//			}
//
//			// TimeUtil.shortWait();
//			--screenshotCounter;
//			attachScreenshot(driver, true, scName, "click2");
//			screenshotCounter = Math.round(screenshotCounter);
//		} catch (Exception e) {
//			test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
//			test.log(Status.FAIL, "Exception :" + e.getMessage());
//			attachScreenshot(driver, true, scName, "click2");
//		}
//	}

	public void recordAttendance_OnlineSession_1AdditionalUser(HashMap<String, String> testData) {

		String AbsentUser = CM_CourseSession.getAbsentEmployee();

		// String AbsentUser = "DawnEVCU.DawnEVCU";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewNUCP" + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.mediumWait();
//		try {
//			click2(selectManual, "Select manual",
//					"Selection should be acepted and op[tion to select present/ absent should be available",
//					"Selection is getting accepted and option to select present/absent is available", "Manual");
//		}catch(NoSuchElementException e) {
//			System.out.println(e.getMessage());
//		}

		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);

//		scrollToViewElement(additionalUsersAddIcon);
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		waitForElementVisibile(additionalUsersSearchFilter);
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserQ(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//		scrollToViewElement(additionalUsersRemarksReasons);
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
		scrollToViewElement(actualHeader);
		TimeUtil.longwait();
		TimeUtil.longwait();
		TimeUtil.longwait();
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_OfflineSession_2AdditionalUsers(HashMap<String, String> testData) {

		String AbsentUser = CM_CourseSession.getAbsentEmployee();

		// String AbsentUser = "DawnZOO";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
//				"CRSNewIPUM"+"%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());

		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();

		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());
		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());
		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				CM_QuestionPaper.Evaluator, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);
//		scrollToViewElement(additionalUsersAddIcon);
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		waitForElementVisibile(additionalUsersSearchFilter);
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserQ(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserTBR(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//		scrollToViewElement(additionalUsersRemarksReasons);
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
		scrollToViewElement(actualHeader);
		TimeUtil.longwait();
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_CandidatesList_SelectAll(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());

		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				// "AutomationCourseJBLH", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());

		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		waitForElementVisibile(candidatesListHeader);
		scrollToViewElement(candidatesListHeader);
		click2(selectALL, RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings());
		scrollToViewElement(mainHeader);
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_Offline_Evaluation_No_2AdditionalUsers(HashMap<String, String> testData) {

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());

		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());

		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();

		scrollToViewElement(actualHeader);

		TimeUtil.longwait();

		selectEndTimeInRecordAttendanceOQFormat();

		scrollToViewElement(candidatesListHeader);

//		if (testData.containsKey("EmployeeID")) {
//
//			String user = testData.get("EmployeeID");

		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(),
				CM_CourseSession.getAbsentEmployee());
//		}

//		scrollToViewElement(additionalUsersAddIcon);
//
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				testData.get("EmployeeID"),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		scrollToViewElement(additionalUsersRemarksReasons);
//
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());

		scrollToViewElement(preview);

		TimeUtil.shortWait();

		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();

		scrollToViewElement(submit);

		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

//	public void recordAttendance_Offline_Evaluation_Verbal_2AdditionalUsers(HashMap<String, String> testData) {
//
////		String courseName = "CRSNewHTHC";
//		String courseName = CM_Course.Course;
//
//		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
//				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
//				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
//				CommonStrings.CM_Menus_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
//				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
//				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
//
//		switchToBodyFrame(driver);
//
//		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
//				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
//				CommonStrings.SearchBy_SS.getCommonStrings());
//
//		TimeUtil.mediumWait();
//
//		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
//
//		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
//				courseName, CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
//
//		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(actualHeader);
//
//		click2(evaluationMethodDropDown, RecordAttendanceStrings.Click_Eval_Method_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_SS.getRecordAttendanceStrings());
//
//		click2(evaluationMethodVerbal, RecordAttendanceStrings.Select_Verbal_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_SS.getRecordAttendanceStrings());
//
//		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());
//
//		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());
//
//		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
//				testData.get("EvaluatorName") + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.shortWait();
//
//		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(candidatesListHeader);
//
//		// Candidate List - Selection
//		selectMultipleCheckBoxes(candidateListRows,
//				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(),
//				CM_CourseSession.getAbsentEmployee());
//
//		// Candidate List Result - Pass/Fail
//		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(), CM_CourseSession.QualifiedEmployee,
//				CM_CourseSession.getToBeRetrainedEmployeeName());
//
////		scrollToViewElement(additionalUsersAddIcon);
////
////		// Additional User Selection
////		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
////
////		// For 1st User
////		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
////
////		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
////				CM_CourseSession.getAddUserQ(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
////
////		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
////
////		// For 2nd user
////		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
////				CM_CourseSession.getAddUserTBR(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
////
////		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
////
////		scrollToViewElement(additionalUsersRemarksReasons);
////
////		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
////				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
////				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
////
////		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
////				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
////
////		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
////				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
////				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(), CM_CourseSession.getAddUserQ(),
////				CM_CourseSession.getAddUserTBR());
//
//		selectEndTimeInRecordAttendanceOQFormat();
//
//		TimeUtil.shortWait();
//
//		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(submit);
//
//		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		switchToDefaultContent(driver);
//	}

	// To select Result radio button either Pass or Fail

//	public void selectResultRadioButton(List<WebElement> element, String stepDescription, String acceptanceCriteria,
//			String actualResult, String scName, String selectUser, String skippedUser) {
//
//		try {
//			if (isReportedRequired == true) {
//				n = n + 1;
//
//				String[][] data = {
//						{ "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>",
//								"<b>Actual Result</b>" },
//						{ String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria,
//								"<div><b>* </b>" + actualResult } };
//
//				Markup tableMarkup = MarkupHelper.createTable(data);
//				String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
//				test.log(Status.PASS, modifiedMarkup);
//				attachScreenshot(driver, true, scName, "click2");
//				// TimeUtil.shortWait();
//				--screenshotCounter;
//				attachScreenshot(driver, true, scName, "click2");
//				screenshotCounter = Math.round(screenshotCounter);
//			} else {
//				for (WebElement row : element) {
//
//					WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));
//
//					WebElement employeeNameElement = row.findElement(By.xpath(".//td[1]"));
//
//					WebElement passRadioButton = row
//							.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTPass_')]"));
//
//					WebElement failRadioButton = row
//							.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTFail_')]"));
//
//					if (checkbox.isSelected()) {
//
//						if (employeeNameElement.getText().contains(selectUser)) {
////								jsExecutor.executeScript("arguments[0].click();", passRadioButton);
//							passRadioButton.click();
//						} else if (employeeNameElement.getText().contains(skippedUser)) {
////								jsExecutor.executeScript("arguments[0].click();", failRadioButton);
//							failRadioButton.click();
//						}
//					}
//				}
//
//			}
//		} catch (Exception e) {
//			if (isReportedRequired == true) {
//				test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
//				test.log(Status.FAIL, "Exception :" + e.getMessage());
//				attachScreenshot(driver, true, scName, "click2");
//			} else {
//				System.out.println(e.getMessage());
//			}
//		}
//	}
//	
//	

//	public void selectResultRadioButton(List<WebElement> element, String stepDescription, String acceptanceCriteria,
//			String actualResult, String scName, String selectUser, String skippedUser) {
//
//	
//	try {
//	    if (isReportedRequired == true) {
//	        n = n + 1;
//	        String[][] data = {
//	            { "<b>Step No</b>", "<b>Step Description</b>", "<b>Acceptance Criteria</b>", "<b>Actual Result</b>" },
//	            { String.valueOf(n), stepDescription, "<div><b>* </b>" + acceptanceCriteria, "<div><b>* </b>" + actualResult }
//	        };
//	        Markup tableMarkup = MarkupHelper.createTable(data);
//	        String modifiedMarkup = modifyColumnWidth(tableMarkup.getMarkup());
//	        test.log(Status.PASS, modifiedMarkup);
//	        attachScreenshot(driver, true, scName, "click2");
//	        --screenshotCounter;
//	        attachScreenshot(driver, true, scName, "click2");
//	        screenshotCounter = Math.round(screenshotCounter);
//	    } else {
//	        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
//	        for (WebElement row : element) {
//	            WebElement checkbox = row.findElement(By.xpath(".//input[@type='checkbox']"));
//	            WebElement employeeNameElement = row.findElement(By.xpath(".//td[1]"));
//	            WebElement passRadioButton = wait.until(ExpectedConditions.elementToBeClickable(row.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTPass_')]"))));
//	            WebElement failRadioButton = wait.until(ExpectedConditions.elementToBeClickable(row.findElement(By.xpath(".//input[@type='radio' and contains(@id, 'USRRESULTFail_')]"))));
//
//	            if (checkbox.isSelected()) {
//	                if (employeeNameElement.getText().contains(selectUser)) {
//	                    ((JavascriptExecutor) driver).executeScript("arguments[0].click();", passRadioButton);
//	                } else if (employeeNameElement.getText().contains(skippedUser)) {
//	                    ((JavascriptExecutor) driver).executeScript("arguments[0].click();", failRadioButton);
//	                }
//	            }
//	        }
//	    }
//	} catch (Exception e) {
//	    if (isReportedRequired == true) {
//	        test.log(Status.FAIL, " <b>Step No. " + n + "</b> " + " Failed to click " + actualResult);
//	        test.log(Status.FAIL, "Exception :" + e.getMessage());
//	        attachScreenshot(driver, true, scName, "click2");
//	    } else {
//	        System.out.println(e.getMessage());
//	    }
//	
//
//	
//	
//	}
//	}
//	

	// This method is for recrding the attendnace for single user With out any
	// additional users

	public void recordAttendance_CandidatesList_SelectAll_1Absent(HashMap<String, String> testData) {
		String AbsentUser = CM_CourseSession.getAbsentEmployee();

		// String AbsentUser = "DawnDF6N.DawnDF6N";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());

		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);

		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());

		switchToDefaultContent(driver);
	}

	public void recordAttendance_OnlineSession_CountMismatch(HashMap<String, String> testData) {
		String AbsentUser = CM_VerifyCourseSessionScreen.getAbsentEmployee();

		// String AbsentUser = "DawnEVCU.DawnEVCU";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewNUCP" + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());

		TimeUtil.mediumWait();
		try {

			if (selectManual.isDisplayed()) {
				click2(selectManual, "Select manual",
						"Selection should be acepted and op[tion to select present/ absent should be available",
						"Selection is getting accepted and option to select present/absent is available", "Manual");
			}
		} catch (NoSuchElementException e) {
			System.out.println(e.getMessage());
		}
		TimeUtil.mediumWait();

		TimeUtil.shortWait();
		waitForElementVisibile(candidatesListHeader);
		scrollToViewElement(candidatesListHeader);
		TimeUtil.mediumWait();
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);

		listAbsentUsers.add(CM_VerifyCourseSessionScreen.AbsentEmployeeID);

		AbsentUsersCount = listAbsentUsers.size();
		System.out.println("Absent User Count is: " + AbsentUsersCount);
		AbsentUsers = listAbsentUsers.toArray(new String[0]);
		scrollToViewElement(actualHeader);
		try {
			selectTimeInRecordAttendanceOQFormat();
		} catch (Throwable e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_OnlineSession_CountMismatch_1AbsentUser(HashMap<String, String> testData) {

		String AbsentUser = CM_VerifyCourseSessionScreen.getAbsentEmployee();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();

		if (CM_QuestionPaper.EvaluationType.equals("Manual Evaluation")) {
			click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

			click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());

			click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
					CM_QuestionPaper.Evaluator, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());

			TimeUtil.shortWait();

			click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

			click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		}

		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);
//		scrollToViewElement(additionalUsersAddIcon);
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		waitForElementVisibile(additionalUsersSearchFilter);
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserTBR(), RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserQ(), RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//		click2(addBtn2, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
////		searchString.clear();
//		scrollToViewElement(additionalUsersRemarksReasons);
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
		scrollToViewElement(actualHeader);
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_OnlineSession_manualEval_CountMismatch(HashMap<String, String> testData) {

		String AbsentUser = CM_VerifyCourseSessionScreen.getAbsentEmployee();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		TimeUtil.mediumWait();
		if (CM_QuestionPaper.EvaluationType.equals("Manual Evaluation")) {
			click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

			click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());

			click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
					CM_QuestionPaper.Evaluator, CommonStrings.sendKeys_AC.getCommonStrings(),
					CommonStrings.sendKeys_AR.getCommonStrings(),
					RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

			click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
					CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
					CommonStrings.ApplyButton_SS.getCommonStrings());

			TimeUtil.shortWait();

			click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

			click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
					RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		}

		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);
//		stendanceStrings.ADD_SS.getRecordAttendanceStrings());
		scrollToViewElement(actualHeader);
		try {
			selectTimeInRecordAttendanceOQFormat();
		} catch (Throwable e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());

		TimeUtil.longwait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_OfflineSession_CountMismatch_New(HashMap<String, String> testData) {

		String AbsentUser = CM_VerifyCourseSessionScreen.getAbsentEmployee();

		// String AbsentUser = "DawnEVCU.DawnEVCU";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
				// "CRSNewNUCP" + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.mediumWait();

		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());
		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());
		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				CM_QuestionPaper.Evaluator, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		
		TimeUtil.mediumWait();
		try {

			if (selectManual.isDisplayed()) {
				click2(selectManual, "Select manual",
						"Selection should be acepted and op[tion to select present/ absent should be available",
						"Selection is getting accepted and option to select present/absent is available", "Manual");
			}
		} catch (NoSuchElementException e) {
			System.out.println(e.getMessage());
		}
		TimeUtil.mediumWait();

		TimeUtil.shortWait();

		scrollToViewElement(candidatesListHeader);
		selectMultipleCheckBoxes(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);

		listAbsentUsers.add(CM_VerifyCourseSessionScreen.AbsentEmployeeID);

		AbsentUsersCount = listAbsentUsers.size();
		System.out.println("Absent User Count is: " + AbsentUsersCount);
		AbsentUsers = listAbsentUsers.toArray(new String[0]);
		scrollToViewElement(actualHeader);
		try {
			selectTimeInRecordAttendanceOQFormat();
		} catch (Throwable e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_Offline_VerbalEvaluation_CountMismatch(HashMap<String, String> testData) {

		String AbsentUser = CM_VerifyCourseSessionScreen.getAbsentEmployee();

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());
		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				CM_Course.getCourse(), CommonStrings.sendKeys_AC.getCommonStrings(),
//				"OffVerbalEvalTTVO" + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		TimeUtil.mediumWait();
		try {

			if (selectManual.isDisplayed()) {
				click2(selectManual, "Select manual",
						"Selection should be acepted and op[tion to select present/ absent should be available",
						"Selection is getting accepted and option to select present/absent is available", "Manual");
			}
		} catch (NoSuchElementException e) {
			System.out.println(e.getMessage());
		}
		TimeUtil.mediumWait();

		TimeUtil.shortWait();
		waitForElementVisibile(candidatesListHeader);
		scrollToViewElement(candidatesListHeader);
		TimeUtil.mediumWait();

//		click2(evaluationMethodDropDown, RecordAttendanceStrings.Click_Eval_Method_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Eval_Method_SS.getRecordAttendanceStrings());
//
//		click2(evaluationMethodVerbal, RecordAttendanceStrings.Select_Verbal_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Select_Verbal_SS.getRecordAttendanceStrings());
//
//		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

//		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());
//
//		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
//				testData.get("EvaluatorName") + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());
//
//		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
//				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
//				CommonStrings.ApplyButton_SS.getCommonStrings());
//
//		TimeUtil.shortWait();

//		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());
//
//		scrollToViewElement(candidatesListHeader);

//		selectMultipleCheckBoxes(candidateListRows,
//				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(), AbsentUser);
//
//		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(),
//				CM_VerifyCourseSessionScreen.getQualifiedEmployee(),
//				CM_VerifyCourseSessionScreen.getToBeRetrainedEmployeeName());

//		scrollToViewElement(actualHeader);
//		try {
//			selectTimeInRecordAttendanceOQFormat();
//		} catch (Throwable e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		// selectEndTimeInRecordAttendanceOQFormat();
//		scrollToViewElement(preview);
//		TimeUtil.shortWait();
//		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
//		TimeUtil.shortWait();
//		scrollToViewElement(submit);
//		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
//		TimeUtil.shortWait();
//		switchToDefaultContent(driver);
//		
//		

		scrollToViewElement(actualHeader);

		click2(evaluationMethodDropDown, RecordAttendanceStrings.Click_Eval_Method_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_SS.getRecordAttendanceStrings());

		click2(evaluationMethodVerbal, RecordAttendanceStrings.Select_Verbal_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_SS.getRecordAttendanceStrings());

		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());

		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				testData.get("EvaluatorName") + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();

		scrollToViewElement(candidatesListHeader);

		selectMultipleCheckBoxesAndSelectRadioButton(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(),
				CM_VerifyCourseSessionScreen.AbsentEmployee, // skipped user
				CM_VerifyCourseSessionScreen.QualifiedEmployee, // Qualified
				CM_VerifyCourseSessionScreen.ToBeRetrainedEmployee // To be retrained
		);

		listAbsentUsers.add(CM_VerifyCourseSessionScreen.AbsentEmployeeID);

		AbsentUsersCount = listAbsentUsers.size();
		System.out.println("Absent User Count is: " + AbsentUsersCount);
		AbsentUsers = listAbsentUsers.toArray(new String[0]);
		scrollToViewElement(actualHeader);
		try {
			selectTimeInRecordAttendanceOQFormat();
		} catch (Throwable e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

	public void recordAttendance_Offline_Evaluation_Verbal_2AdditionalUsers(HashMap<String, String> testData) {

//		String courseName = "CRSNewHTHC";
		String courseName = CM_Course.Course;
		// String courseName = "CRSNewBVQB";

		// String courseName = "CRSNewMFBN";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(attendanceMenu, RecordAttendanceStrings.RecrdAttendanceScreen_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RecrdAttendanceScreen_SS.getRecordAttendanceStrings());

		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordAttendanceStrings.SearchBy_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_AR.getRecordAttendanceStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());

		TimeUtil.mediumWait();

		click2(searchByCourseName, RecordAttendanceStrings.SearchBy_CourseName_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.SearchBy_CourseName_SS.getRecordAttendanceStrings());

		sendKeys2(courseNameField, RecordAttendanceStrings.Like_WithExam_CourseName_DC.getRecordAttendanceStrings(),
				courseName, CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Like_CourseName_SS.getRecordAttendanceStrings());

		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(displayedRecord, RecordAttendanceStrings.RegisteredCourse_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.RegisteredCourse_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		TimeUtil.mediumWait();
		try {
			click2(selectManual, "Select manual",
					"Selection should be acepted and op[tion to select present/ absent should be available",
					"Selection is getting accepted and option to select present/absent is available", "Manual");
		} catch (NoSuchElementException e) {
			System.out.println(e.getMessage());
		}
		scrollToViewElement(actualHeader);

		click2(evaluationMethodDropDown, RecordAttendanceStrings.Click_Eval_Method_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Eval_Method_SS.getRecordAttendanceStrings());

		click2(evaluationMethodVerbal, RecordAttendanceStrings.Select_Verbal_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Select_Verbal_SS.getRecordAttendanceStrings());

		click2(evaluatorBtn, RecordAttendanceStrings.Click_AddItem_Evaluator_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_AddItem_Evaluator_SS.getRecordAttendanceStrings());

		click2(evaluatorListDropDown, RecordAttendanceStrings.click_Search_Dropdown_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.click_Search_Dropdown_SS.getRecordAttendanceStrings());

		click2(searchByEmployeeName, RecordAttendanceStrings.Select_EmployeeName_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		sendKeys2(evaluatorFindTextbox, RecordAttendanceStrings.Enter_Evaluatorname_DC.getRecordAttendanceStrings(),
				testData.get("EvaluatorName") + "%", CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordAttendanceStrings.Select_EmployeeName_SS.getRecordAttendanceStrings());

		click2(applyBtn, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());

		TimeUtil.shortWait();

		click2(selectFilteredEvaluator, RecordAttendanceStrings.Select_Eval_DC.getRecordAttendanceStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AC.getCommonStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		click2(addNew, RecordAttendanceStrings.Click_Add_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Click_Add_SS.getRecordAttendanceStrings());

		scrollToViewElement(candidatesListHeader);

		selectMultipleCheckBoxesAndSelectRadioButton(candidateListRows,
				RecordAttendanceStrings.CandidateList_SelectAll_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.CandidateList_SelectAll_SS.getRecordAttendanceStrings(),
				CM_CourseSession.getAbsentEmployee(), // skipped user
				CM_CourseSession.QualifiedEmployee, // Qualified
				CM_CourseSession.ToBeRetrainedEmployee // To be retrained
		);

//		selectResultRadioButton(candidateListRows,
//			    RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//			    CommonStrings.Selction_AC.getCommonStrings(),
//			    CommonStrings.Selction_AR.getCommonStrings(),
//			    RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(),
//			    CM_CourseSession.QualifiedEmployee,
//			    CM_CourseSession.ToBeRetrainedEmployee);
//		
//		scrollToViewElement(additionalUsersAddIcon);
//
//		// Additional User Selection
//		click2(additionalUsersAddIcon, RecordAttendanceStrings.ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		// For 1st User
//		click2(additionalUsersSearchFilter, RecordAttendanceStrings.SEARCH_FILTER_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_SS.getRecordAttendanceStrings());
//
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserQ(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		// For 2nd user
//		sendKeys2(searchString, RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SD.getRecordAttendanceStrings(),
//				CM_CourseSession.getAddUserTBR(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ENTER_ADDITIONAL_USERS_SS.getRecordAttendanceStrings());
//
//		click2(addBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		scrollToViewElement(additionalUsersRemarksReasons);
//
//		sendKeys2(additionalUsersRemarksReasons, TrainerStrings.Enter_Remarks_DC.getTrainerStrings(),
//				testData.get("RemarksReasons"), CommonStrings.sendKeys_AC.getCommonStrings(),
//				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.REMARKS_REASONS.getCommonStrings());
//
//		click2(usersListAddBtn, RecordAttendanceStrings.ADD_SD.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.FINAL_ADD_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.SEARCH_FILTER_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.ADD_SS.getRecordAttendanceStrings());
//
//		selectResultRadioButton(candidateListRows, RecordAttendanceStrings.Select_Pass_DC.getRecordAttendanceStrings(),
//				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//				RecordAttendanceStrings.Select_Pass_SS.getRecordAttendanceStrings(), CM_CourseSession.getAddUserQ(),
//				CM_CourseSession.getAddUserTBR());

//		selectEndTimeInRecordAttendanceOQFormat();
//
//		TimeUtil.shortWait();
//
//		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		scrollToViewElement(submit);
//
//		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
//				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());
//
//		TimeUtil.shortWait();
//
//		switchToDefaultContent(driver);
//		

		scrollToViewElement(actualHeader);
		selectEndTimeInRecordAttendanceOQFormat();
		scrollToViewElement(preview);
		TimeUtil.shortWait();
		click2(preview, RecordAttendanceStrings.previewBtn_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.previewBtn_SS.getRecordAttendanceStrings());
		TimeUtil.shortWait();
		scrollToViewElement(submit);
		click2(submit, RecordAttendanceStrings.Submit_Button_DC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AC.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_AR.getRecordAttendanceStrings(),
				RecordAttendanceStrings.Submit_Button_SS.getRecordAttendanceStrings());

		TimeUtil.shortWait();
		switchToDefaultContent(driver);
	}

}
