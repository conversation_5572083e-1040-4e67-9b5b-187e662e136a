package com.Automation.learniqCRITICALScenarios.GTP_Proposed_CountMismatch_Scnearios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_QuestionPaper;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class INTERIM_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/Interim_P1_TestData/INTERIM_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION.xlsx";
	String OutputData = "./Employee_Mismatch_Final_Output/INTERIM_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION.xlsx";

	public INTERIM_IT_ON_WITH_ASSESSMENT_MANUAL_EVALUATION() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 0, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Topic Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic Registration");
		}

		clearSheetData(OutputData, 0);
		getOutPutPath(OutputData);
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();

		InitiateTopic.topic_Registration(testData);

	}

	// Test Method for Course Registration

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseReg");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}

		Initiate_Course.Refresher_Course_Registration(testData);

	}

	// Test Method for Trainer Modification
	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}

		trainer.trainer_Modification_AuditTrails(testData);
	}

	ExcelUtilUpdated GTPData = new ExcelUtilUpdated(ExcelPath, "ProposeIGTP");

	@DataProvider(name = "ProposeIGTP")
	public Object[][] getGTPData() throws Exception {
		Object[][] obj = new Object[GTPData.getRowCount()][1];
		for (int i = 1; i <= GTPData.getRowCount(); i++) {
			HashMap<String, String> testData = GTPData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "ProposeIGTP", enabled = true)
	public void proposeigtp(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Propose IGTP")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Propose IGTP");
		}

		InterimGTP.ProposeInterim_GTP(testData);
		TimeUtil.longwait();
	}

	// Test Method for CourseSession Registration
	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "SessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}

		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "BeforeSession");

		verifyCSScreen.compareEmployeeDatabeforeCourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compareEmployeeDataBefore_Sesion_At_CSRData();

		verifyCSScreen.courseSession_IT_Online_WithExam_ManualEvaluation(testData);

		// Opne Course Sessipn screen after Session
		verifyCSScreen.CheckToBePlannedBeforeSession(testData, "AfterSession");

		verifyCSScreen.compareEmployeeData_After_CourseSessionAtCourseSessionScreen();

		CSRReport.TBPCSRReport();

		CSRReport.compare_EmployeeData_After_Session_At_CSRReport();

	}

	// Test Method for BatchFormation Registration
	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation Registration");
		}

		BatchFormation.batchFormation_NotResponded_Skipped(testData);

	}

	// Test Method for QuestionBank Registration
	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "QuestionBank")
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
	}

	// Test Method for QuestionPaper Registration
	// -------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration");
		}

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.ITType);

	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Record Attendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Record Attendance");
		}

		RecordAttendance.recordAttendance_OnlineSession_manualEval_CountMismatch(testData);

		Logout.signOutPage();

	}

	@Test(priority = 9, enabled = true)
	public void Respond_QuestionPaper_Qualify() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user  Qualified")
					.assignAuthor(CM_VerifyCourseSessionScreen.QualifiedTraineeID)
					.assignCategory("Respond QuestionPaper and make user Qualified");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.QualifiedTraineeID, CM_VerifyCourseSessionScreen.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.ITType,
				CM_VerifyCourseSessionScreen.QualifiedTraineePsw);
		Logout.signOutPage();

	}

	@Test(priority = 10, enabled = true)
	public void Respond_QuestionPaper_ToBeRetrained() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user to To Be Retrained")

					.assignAuthor(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID)

					.assignCategory("Respond QuestionPaper and make user to  To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		Logout.signOutPage();
	}

	// Test Method for Retake Attempt Pending
	// (ToBeRetrained)-------------------------------------------------------------------------------------------

	@Test(priority = 11, enabled = true)
	public void Respond_QuestionPaper_Toretake_Attempts_Pending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user to  retake Attempts Pending")

					.assignAuthor(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID)

					.assignCategory("Respond QuestionPaper and make user  retake Attempts Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_VerifyCourseSessionScreen.retakeUserID,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);
		epiclogin.plant1();
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.retakeEmployeePassword);

		Logout.signOutPage();

	}

	@Test(priority = 12, enabled = true)
	public void Respond_QuestionPaper_toRetakePendingEvaluationPending() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user to Retake Pending Evaluation Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond QuestionPaper and make user to Retake Pending Evaluation Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 13, enabled = true)
	public void evaluate_AnswerPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
					.assignCategory("Evaluate Answer Paper");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
				CM_QuestionPaper.EvaluatorPassword);

		epiclogin.plant1();

		Evaluate.manualEvaluation();
		Logout.signOutPage();
	}

	@Test(priority = 14, enabled = true)
	public void Respond_QuestionPaper_ToBeRetrained_lastAttempt() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user to To Be Retrained")

					.assignAuthor(CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID)

					.assignCategory("Respond QuestionPaper and make user to To Be Retrained");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineeID,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.ToBeRetrainedTraineePsw);

		Logout.signOutPage();

	}

	@Test(priority = 15, enabled = true)
	public void evaluate_AnswerPaper_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
					.assignCategory("Evaluate Answer Paper");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
				CM_QuestionPaper.EvaluatorPassword);

		epiclogin.plant1();

		Evaluate.manualEvaluation();
		Logout.signOutPage();
	}

	@Test(priority = 16, enabled = true)
	public void toRetakePendingEvaluationPending_AfterRetake() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user to Retake Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond QuestionPaper make user to Retake Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.retake_Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 17, enabled = true)
	public void EvaluationPending_() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond QuestionPaper and make user Evaluation Pending")
					.assignAuthor(CM_VerifyCourseSessionScreen.retakeUserID)
					.assignCategory("Respond QuestionPaper and make user Evaluation Pending");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_VerifyCourseSessionScreen.Eval_Pending_UserID,
				CM_VerifyCourseSessionScreen.Eval_Pending_EmployeePassword);

		epiclogin.plant1();

		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType,
				CM_VerifyCourseSessionScreen.Eval_Pending_EmployeePassword);

		Logout.signOutPage();
	}

	@Test(priority = 18, enabled = true)
	public void checkCourseSessionAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session screen after keeping employees in different states")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session screen after keeping employees in different states");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

		epiclogin.plant1();
		verifyCSScreen.checkCourseSession_IT_With_Assessment_After_Keeping_Employees_In_Different_States();

	}

	@Test(priority = 19, enabled = true)
	public void checkCourseSessionReportAfterAllStates() {
		if (isReportedRequired == true) {
			test = extent.createTest("Course Session Report screen after keeping employees in different states")
					.assignAuthor(CM_VerifyCourseSessionScreen.getQPPendingUserID())
					.assignCategory("Course Session Report screen after keeping employees in different states");
		}

		CSRReport.TBPCSRReport_IT_WithAssesment_After_keping_Emplpoyees_In_Diff_States();

		Logout.signOutPage();
	}
}