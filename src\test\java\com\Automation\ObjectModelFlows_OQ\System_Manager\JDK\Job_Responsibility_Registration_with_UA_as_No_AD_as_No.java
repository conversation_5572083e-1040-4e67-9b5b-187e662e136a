package com.Automation.ObjectModelFlows_OQ.System_Manager.JDK;

import java.util.HashMap;

import org.openqa.selenium.JavascriptExecutor;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class Job_Responsibility_Registration_with_UA_as_No_AD_as_No extends OQActionEngine {

	public static String SSOTab = "";
	public static String EpiqTab = "";
	String ExcelPath = "./learnIQTestData/Object_Model_Flows/System_Manager/JobResponsibility/JR_With_UA_NO_AD_NO/JR_REG_UA_NO_AD_NO.xlsx";
	String SSO_ExcelPath = "./learnIQTestData/SSO_UserRegistrationPrerequisites.xlsx";

	public Job_Responsibility_Registration_with_UA_as_No_AD_as_No() {

		super(ConfigsReader.getPropValue("SSOUrl"));
		//super(ConfigsReader.getPropValue("applicationUrl"));
		
	}
	
	
	
	
	ExcelUtilUpdated excel0 = new ExcelUtilUpdated(SSO_ExcelPath, "USREG");

	@DataProvider(name = "RegistrationUser")
	public Object[][] getRegistrationUser() throws Exception {
		Object[][] obj = new Object[excel0.getRowCount()][1];
		for (int i = 1; i <= excel0.getRowCount(); i++) {
			HashMap<String, String> testData = excel0.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
		
	}

	@Test(priority = 0, dataProvider = "RegistrationUser", enabled = true)
	public void userReg(HashMap<String, String> testData) {
		test = extent.createTest("Pre-Requisite: 1.0 User Registration Approval with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre-Requisite: 1.0 User Registration Approval with Audit Trails");
		SSOTab=driver.getWindowHandle();
		epiclogin.loginToSSOApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("SSOUserID"),
				ConfigsReader.getPropValue("SSOPassword"));
		TimeUtil.longwait();
		UserReg.sso_UserRegistration(testData);
		test = extent.createTest("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails");
		UserProductModuleAssignment.UserProductModuleAssignment_With_AuditTrails(testData);
		Logout.SSOsignOutPage();
		/*
		 * ((JavascriptExecutor)
		 * driver).executeScript("window.open(arguments[0], '_blank');",
		 * ConfigsReader.getPropValue("applicationUrl")); n = 0; screenshotCounter = 0;
		 */

	}
	
	
	
	@Test(priority = 1, enabled = true)
	public void ResetPasswordEPIQ() {

		test = extent.createTest("Pre Requisite: 6.0 Reset Login Passoword")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 6.0 Reset Login Passoword");
		((JavascriptExecutor) driver).executeScript("window.open(arguments[0], '_blank');", ConfigsReader.getPropValue("applicationUrl"));
		 for (String handle : driver.getWindowHandles()) {
		        if (!handle.equals(SSOTab)) {
		            driver.switchTo().window(handle);
		            EpiqTab=driver.getWindowHandle();
		            break;
		        }
		    }
		epiclogin.loginToEPIQforResetPassword(ConfigsReader.getPropValue("company"));

		UserReg.userReg_resetPasswordEpiq(ConfigsReader.getPropValue("SSONewUserRegPassword"));

	}
	
	
	

	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "JR_Central_Configuration");

	@DataProvider(name = "JobResp_CentralConfig")
	public Object[][] getJR_Central_Config() throws Exception {
		Object[][] obj = new Object[excel1.getRowCount()][1];
		for (int i = 1; i <= excel1.getRowCount(); i++) {
			HashMap<String, String> testData = excel1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "JobResp_CentralConfig", enabled = true)
	public void JR_Central_Configuration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Set User Acceptance and Autorized Deputy as 'No' in central configuration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Set User Acceptance and Autorized Deputy as 'No' in central configuration");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EPIQDefaultID"),
					ConfigsReader.getPropValue("EPIQDefaultIPSW"));
			epiclogin.plant1();
			centralConfg.jobResponsibilityCentralConfigurations(testData);
			Logout.signOutPage();
			 
		}

	}
	
	
	
	

	ExcelUtilUpdated excel2 = new ExcelUtilUpdated(ExcelPath, "E-SignConfiguration");

	@DataProvider(name = "JobResp_EsignConfig")
	public Object[][] getEsignConfigurationData() throws Exception {
		Object[][] obj = new Object[excel2.getRowCount()][1];
		for (int i = 1; i <= excel2.getRowCount(); i++) {
			HashMap<String, String> testData = excel2.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}
	@Test(priority = 3, dataProvider = "JobResp_EsignConfig", enabled = true)
	public void Job_Resp_Configuration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Configure E-Sign for Job Responsibility Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Configure E-Sign for Job Responsibility Registration");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobresponsbility_Configuration(testData);
			TimeUtil.longwait();
			TimeUtil.longwait();
			TimeUtil.longwait();
			 
		}

	}
	
	
	ExcelUtilUpdated excel3 = new ExcelUtilUpdated(ExcelPath, "JR_REG");

	@DataProvider(name = "JR_Registration")
	public Object[][] getJRRegistrationData() throws Exception {
		Object[][] obj = new Object[excel3.getRowCount()][1];
		for (int i = 1; i <= excel3.getRowCount(); i++) {
			HashMap<String, String> testData = excel3.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}
	@Test(priority = 4, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Registration Initiation");
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO(testData);
			test = extent.createTest("Audit trails of Job Responsibility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Audit trails of Job Responsibility Registration Initiation");
			String ApprovalStatus ="Under Approval";
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO_AuditTrails(testData, ApprovalStatus);
			Logout.signOutPage();
			
		}

	}
	
	@Test(priority = 5, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_Approve(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Approve Job Responsobility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Approve Job Responsobility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			epiclogin.plant1();
			String ApprovaScreen = "IntiateApprove";
			JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData, Constants.APPROVE_ACTIONVAL, ApprovaScreen);
			String ApprovalStatus ="Direct Approved";
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO_AuditTrails(testData,ApprovalStatus);
			//Logout.signOutPage();
		}

	}
	
	@Test(priority = 6, enabled = true)
	public void print_JR_After_Registration_Approve() {

	    test = extent.createTest("Reinitiate Job Responsibility with Validation")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Reinitiation Flow");
	   print_JR.jobResponsibilityReport("");
	   Logout.signOutPage();
	}
	
	@Test(priority = 7, dataProvider = "RegistrationUser", enabled = true)
	public void userReg2(HashMap<String, String> testData) {
		test = extent.createTest("Pre-Requisite: 1.0 User Registration Approval with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre-Requisite: 1.0 User Registration Approval with Audit Trails");
		driver.switchTo().window(SSOTab);
		epiclogin.loginToSSOApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("SSOUserID"),
				ConfigsReader.getPropValue("SSOPassword"));
		TimeUtil.longwait();
		UserReg.sso_UserRegistration(testData);
		test = extent.createTest("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails");
		UserProductModuleAssignment.UserProductModuleAssignment_With_AuditTrails(testData);
		Logout.SSOsignOutPage();
		/*
		 * ((JavascriptExecutor)
		 * driver).executeScript("window.open(arguments[0], '_blank');",
		 * ConfigsReader.getPropValue("applicationUrl")); n = 0; screenshotCounter = 0;
		 */

	}
	
	
	
	@Test(priority = 8, enabled = true)
	public void ResetPasswordEPIQ2() {

		test = extent.createTest("Pre Requisite: 6.0 Reset Login Passoword")

				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

				.assignCategory("Pre Requisite: 6.0 Reset Login Passoword");
		driver.switchTo().window(EpiqTab);
		epiclogin.loginToEPIQforResetPassword(ConfigsReader.getPropValue("company"));

		UserReg.userReg_resetPasswordEpiq(ConfigsReader.getPropValue("SSONewUserRegPassword"));

	}
	
	@Test(priority = 9, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Job Responsibility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Job Responsibility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO(testData);
			Logout.signOutPage();
			
			
		}
	}
	
	
	@Test(priority = 10, dataProvider = "JR_Registration", enabled = true)
	public void Job_Resp_Registration_Return(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Return Job Responsobility Registration Initiation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Return Job Responsobility Registration Initiation");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
			epiclogin.plant1();
			JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(testData, Constants.RETURN_ACTIONVAL, "");
			Logout.signOutPage();
		}
	}
	
//	ExcelUtilUpdated rit = new ExcelUtilUpdated(ExcelPath, "JR_REG_REINI");
//
//	@DataProvider(name = "RI_Transfer")
//	public Object[][] getRI_Transfer() throws Exception {
//		Object[][] obj = new Object[rit.getRowCount()][1];
//		for (int i = 1; i <= rit.getRowCount(); i++) {
//			HashMap<String, String> testData = rit.getTestDataInMap(i);
//			obj[i - 1][0] = testData;
//		}
//		return obj;
//	}
	

	@DataProvider(name = "JR_RI_TRANSFER")
	public Object[][] getJR_RI_TransferDataData() throws Exception {
	    ExcelUtilUpdated regExcel = new ExcelUtilUpdated(ExcelPath, "JR_REG");
	    ExcelUtilUpdated reinitExcel = new ExcelUtilUpdated(ExcelPath, "JR_REG_REINI");

	    int rowCount = Math.min(regExcel.getRowCount(), reinitExcel.getRowCount()); // match rows

	    Object[][] obj = new Object[rowCount][2];
	    for (int i = 1; i <= rowCount; i++) {
	        HashMap<String, String> regData = regExcel.getTestDataInMap(i);
	        HashMap<String, String> reinitData = reinitExcel.getTestDataInMap(i);

	        obj[i - 1][0] = regData;     // for validation
	        obj[i - 1][1] = reinitData; 
	       
	    }
	    return obj;
	}
	
	
	
	
	
	@Test(priority =11, dataProvider = "JR_RI_TRANSFER", enabled = true)
	public void Job_Resp_Registration_RI_Transfer(HashMap<String, String> regData, HashMap<String, String> reinitData) {

		if (isReportedRequired == true) {
			test = extent.createTest("RI Transfer of Re-Initiation Task")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RI Transfer of Re-Initiation Task");
			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
					ConfigsReader.getPropValue("EpicUserPWD"));
			epiclogin.plant1();
			JobResponsibility.JR_RI_Transfer(regData, reinitData,"");
			Logout.signOutPage();
		}
	}
	
	
	@DataProvider(name = "JR_ReinitiationData")
	public Object[][] getJR_ReinitiationData() throws Exception {
	    ExcelUtilUpdated regExcel = new ExcelUtilUpdated(ExcelPath, "JR_REG");
	    ExcelUtilUpdated reinitExcel = new ExcelUtilUpdated(ExcelPath, "JR_REG_REINI");

	    int rowCount = Math.min(regExcel.getRowCount(), reinitExcel.getRowCount()); // match rows

	    Object[][] obj = new Object[rowCount][2];
	    for (int i = 1; i <= rowCount; i++) {
	        HashMap<String, String> regData = regExcel.getTestDataInMap(i);
	        HashMap<String, String> reinitData = reinitExcel.getTestDataInMap(i);

	        obj[i - 1][0] = regData;     // for validation
	        obj[i - 1][1] = reinitData; 
	       
	    }
	    return obj;
	}
	
	
	@Test(priority = 12, dataProvider = "JR_ReinitiationData", enabled = true)
	public void JR_Reinitiation_Test(HashMap<String, String> regData, HashMap<String, String> reinitData) {

	    test = extent.createTest("Reinitiate Job Responsibility with Validation")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Reinitiation Flow");

	    // Login
	    epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
	    		reinitData.get("TransaferToUserID"), reinitData.get("TransaferToPSW"));
		epiclogin.plant1();
    String ApprovalStatus="Re-Initiation Under Approval";
	    JobResponsibility.reinitiateJobResponsibility(regData, reinitData);
	    JobResponsibility.jobResponsibiltyRegWith_UA_NO_AD_NO_AuditTrails(reinitData,ApprovalStatus);
	    Logout.signOutPage();
		
	}
	
	
	@Test(priority = 13, dataProvider = "JR_ReinitiationData", enabled = true)
	public void JR_Reinitiation_Approval(HashMap<String, String> regData, HashMap<String, String> reinitData) {

	    test = extent.createTest("Approve Job Responsibility Registraion Re-Initiation")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Approve Job Responsibility Registraion Re-Initiation");

	    // Login
	    epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		String Approve ="ReInitApproval";
		JobResponsibility.jobResponsibilityLineOfApproversApproveWithAudiTrails(reinitData, Constants.APPROVE_ACTIONVAL,Approve);
	}

	@Test(priority = 14, enabled = true)
	public void print_JR_After_Registration_Re_Initiation_Approve() {

	    test = extent.createTest("Job Responsibility Repot after Rei")
	        .assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
	        .assignCategory("Job Responsibility Repot after Re");
	    
	    String Stage = "Re_Initiate";
	   print_JR.jobResponsibilityReport(Stage);
	   Logout.signOutPage();
	}

}