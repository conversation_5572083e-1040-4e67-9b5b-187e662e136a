package com.Automation.Strings;

public enum RecordDRStrings {

	Record_DR_Menu_DC("Click on 'Document Reading' sub menu."),
	Record_DR_Menu_AC("'Document Reading' screen should be displayed.</div>"
			+ "<div><b>*</b>Screen should contain list of course session names with 'Course Session Name, Unique code, Session Type, Initiated Date and RTCB' columns.</div>"),
	Record_DR_Menu_AR("'Document Reading' screen is getting displayed.</div>"
			+ "<div><b>*</b>Screen contians list of course session names with 'Course Session Name, Unique code, Session Type, Initiated Date and RTCB' columns.</div>"),
	Record_DR_Menu_SS("'Document Reading'"),

	SearchBy_atRecordDR_AC(
			"Option to search with 'Top 250 Records, Course Session, Name and Initiated Between' should be available.</div>"),
	SearchBy_atRecordDR_AR(
			"Option to search with 'Top 250 Records, Course Session, Name and Initiated Between' are available.</div>"),

	Select_CoursSessionName_DC("Select 'Course Session Name'."),
	Select_CoursSessionName_AC("Selection should be accepted.<div>"),
	Select_CoursSessionName_AR("Selection is getting accepted.<div>"),
	Select_CoursSessionName_SS("'Course Session Name'."),

	Like_CSName_DC("Enter the above registered 'Course Session Name' in 'Like' field."),
	Like_CSName_SS("'Course Session Name'"),

	Click_CS_for_RecordDR_DC("Click on the above registered 'Course Session Name'."),
	Click_CS_for_RecordDR_AC("'Document Reading' screen should should be diplayed.</div>"+
			"<div><b>*</b> Screen should contain Course Session details, Document List Employee List</div>"+
			"<div><b>*</b> Option to select the employees should be available</div>"),
	Click_CS_for_RecordDR_AR("'Document Reading' screen is getting diplayed.</div>"+
			"<div><b>*</b> Screen is getting displayed with Course Session details, Document List Employee List</div>"+
			"<div><b>*</b> Option to select the employees is available.</div>"),
	Click_CS_for_RecordDR_SS("'Document Reading'."),

	Slect_Employees_DC("Select 'Select All' checkbox"), Slect_Employees_AC("Selected employee(s) should be accepted."),
	Slect_Employees_AR("Selected employee(s) is getting accepted."), Slect_Employees_SS("Select Employees"),

	RecordDR_Confirmation_SS("'Confirmation message'.");

	private final String recordDR;

	RecordDRStrings(String RecordDR) {

		this.recordDR = RecordDR;

	}

	public String getRecordDRStrings() {
		return recordDR;
	}

}
