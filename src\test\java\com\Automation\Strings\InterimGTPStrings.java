package com.Automation.Strings;

public enum InterimGTPStrings {

	GTP_DC("Click on 'Group Training Plan' submenu."), InterimGTP_DC("Click on 'Interim GTP' submenu."),
	GTP_Config_AC("'Group Training Plan Configuration Registration' screen should be displayed."),
	GTP_Config_AR("'Group Training Plan Configuration Registration' screen is getting displayed."),
	GTP_Config_SS("'Group Training Plan Configuration Registration'."),

	Click_DoneatGTPConfig_AC("'Group Training Plan Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatGTPConfig_AR("'Group Training Plan Configuration Registration' screen is getting displayed.</div>"),

	Propopse_IGTP_AC("'Interim GTP' screen should be displayed.</div>"
			+ "<div><b>* </b>List of master and plant courses for which the Group Training Plan is not yet proposed should be displayed.</div>"),
	Propopse_IGTP_AR("'Interim GTP' screen is getting displayed.</div>"
			+ "<div><b>* </b>List of master and plant courses for which the Group Training Plan is not yet proposed are getting displayed."),
	Propopse_IGTP_SS("'Interim GTP'."),

	SearchBy_AC(
			"Option to search with 'Top 250 Records, Course Name, Course Code, Training Type' should be available."),
	SearchBy_AR("Option to search with 'Top 250 Records, Course Name, Course Code, Training Type' is available."),
	SearchBy_SS("'Search By'."),

	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR("Selection is getting accepted.</div>"), SearchBy_CourseName_SS("'Course Name'"),

	Like_CourseName_DC("Enter the above registered course name for which the Group Training Plan is not yet proposed."),

	Click_Req_Course_DC("Click on the required course for which the Interim GTP needs to be proposed."),
	Click_Req_Course_AC("'Interim GTP Registration Initiation' screen should be displayed."),
	Click_Req_Course_AR("'Interim GTP Registration Initiation' screen is getting displayed."),
	Click_Req_Course_SS("'Interim GTP Registration Initiation'"),

	Enter_IGTPName_DC("Enter the value less than or equal to 250 characters in 'Interim GTP' field."),
	Enter_IGTPName_SS("'Interim GTP'"),

	Enter_Desc_DC("Enter the value less than or equal to 250 characters in 'Description' field."),
	Enter_User_Name("Enter the required 'User Name'"), Enter_Desc_SS("'Description'"),

	Select_username_DC("Select 'User Name' option."), Select_username_SS("'User Name'"),

	Select_subgroup_DC("Select 'Subgroup Name' option."), Select_subgroup_SS("'Subgroup Name'"),

	Enter_Username_DC("Enter the require user name."),

	Click_FetchRecords_DC("Click on 'Fetch Records'"),
	Click_FetchRecords_AC("Record(s) should be displayed based on the search criteria under 'Available Users' column'"),
	Click_FetchRecords_AR(
			"Record(s) is/are getting displayed based on the search criteria under 'Available Users' column'"),
	Click_FetchRecords_SS("'Fetch Records.'"),

	Click_RequiredUser_DC("Select the required user under 'Available Users'"),
	Click_RequiredUser_AC("Selected user should be moved to 'Selected Users' column'"),
	Click_RequiredUser_AR("Selected user is getting moved to 'Selected Users' column'"),
	Click_RequiredUser_SS("'Available Users'."),

	Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Interim GTP: Registration Initiation'.</div>"),
	Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Interim GTP: Registration Initiation'.</div>"),

	Esign_IGTP_AC(
			"'Group Training Plan Registration Initiated Unique Code: (Unique Code)' confirmation message should be displayed with 'Done' button.</div>"),
	Esign_IGTP_AR(
			"'Group Training Plan Registration Initiated Unique Code: (Unique Code)' confirmation message is getting with 'Done' button.</div>"),

	click_InterimGTPAT_AC("'Group Training Plan Audit Trails' screen should be displayed.<div>"
			+ "<div><b>* </b> List of course for which the GTP and IGTP proposed should be displayed.</div>"),
	click_InterimGTPAT_AR("'Group Training Plan Audit Trails' screen is getting displayed.<div>"
			+ "<div><b>* </b> List of course for which the GTP and IGTP proposed are getting displayed.</div>"),
	click_InterimGTPAT_SS("'Group Training Plan Audit Trials'"),

	Close_AuditTrailsTopic_AC("'Topic Audit' screen should be displayed.</div>"),
	Close_AuditTrailsTopic_AR("'Topic Audit trails' screen is getting displayed."),

	SearchBy_IGTP_AC(
			"Option to search with 'Top 250 Records, Group Training Plan Name, Unique Code and Initiated Between' should be diplayed.<div>"),
	SearchBy_IGTP_AR(
			"Option to search with 'Top 250 Records, Group Training Plan Name, Unique Code and Initiated Between' is getting diplayed.<div>"),

	SearchBy_GTPName_DC("Select 'Group Training Plan Name'."), SearchBy_GTPName_SS("'Group Training Plan Name'"),

	Enter_IGTP_DC("Enter the 'Group Training Plan Name' of the above registered Interim GTP."),
	Enter_IGTP_SS("'Group Training Plan Name'"),

	IGTP_for_AuditTrails_DC("Click on the above registered Interim GTP Name."),
	GTP_for_AuditTrails_AC(
			"'Group Training Plan-Audit Trails: Revision No.:Title 0-Registration' screen should be displayed.</div>"
					+ "<div><b>*</b> 'All the entered/selected details at the time of registration should be displayed accurately.</div>"),

	GTP_for_AuditTrails_AR(
			"'Group Training Plan-Audit Trails: Revision No.:Title 0-Registration' screen is getting displayed.</div>"
					+ "<div><b>*</b> 'All the entered/selected details at the time of registration are getting displayed accurately.</div>"),
	GTP_for_AuditTrails_SS("'Group Training Plan-Audit Trails'"),

	Click_RequiredSubgroup_DC("Select the required Subgroup under 'Available Subgroups'"),
	Click_RequiredSubgroup_AC("Selected Subgroup user should be moved to 'Selected Users' column'"),
	Click_RequiredSubgroup_AR("Selected Subgroup user is getting moved to 'Selected Users' column'"),
	Click_RequiredSubgroup_SS("'Available Users'."),

	;

	private final String interimGTP;

	InterimGTPStrings(String InterimGTP) {

		this.interimGTP = InterimGTP;

	}

	public String getInterimGTPStrings() {
		return interimGTP;

	}

}
