package com.Automation.Strings;

public enum UserRegistrationStrings {

	Click_IM_DC("Click on 'Identity Manager'"), Click_IM_AC("Assigned Menus should be displayed."),
	Click_IM_AR("Assigned Menus are getting displayed."), Click_IM_SS("'Identity Manager'"),

	// User Registration Menu

	Click_UserReg_DC("Click on 'User Registration'."),
	Click_UserReg_AC("'User Registration Registration Initiation' screen should be displayed."),
	Click_UserReg_AR("'User Registration Registration Initiation' screen is getting displayed."),
	Click_UserReg_SS("'User Registration'"),

	Click_ADDItemDesig_DC("Click on 'Add Item' for 'Designation'."),
	Click_ADDItemDesig_AC("'Designation List' window should be displayed."),
	Click_ADDItemDesig_AR("'Designation List' window is getting displayed."), Click_ADDItemDesig_SS("'Designation'"),

	// Enter Required Desgination Code.

	Enter_DesgCode_DC("Enter the valid 'Desgination code' in 'Find' textbox"), Enter_DesgCode_SS("'Designation Code'"),
	Enter_Designation_DC("Enter the valid 'Desgination' in 'Find' textbox"), Enter_Designation_SS("'Designation'"),
	// Select Required Designation

	Select_Desg_DC("Select the required 'Designation'"),

	// After Add button at desgination

	AddDesig_AC("Selected Designation should be displayed for 'Designation' field."),
	AddDesig_AR("Selected Designation is getting displayed for 'Designation' field."),

	// Enter First Name and Last Name

	Enter_FN_DC("Enter the value less than or equals to 40 characters in 'First Name' field."),
	Enter_FN_SS("'First Name'"),

	Enter_LN_DC("Enter the value less than or equals to 40 characters in 'Last Name' field."),
	Enter_LN_SS("'Last Name'"),

	// User Id and Employee Id

	Enter_UID_DC("Enter the value less than or equals to 25 characters in 'User ID' field."), Enter_UID_SS("'User ID'"),

	Enter_EID_DC("Enter the value less than or equals to 25 characters in 'Employee ID' field."),
	Enter_EID_SS("'Employee ID'"),

	// Enter Email

	Enter_Email_DC("Enter the valid Mail ID  in 'Email ID' field."), Enter_EMail_SS("'Email ID'"),

	// Esign User Registration

	Esign_UserReg_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'User Registration: Registration Initiation'."),
	Esign_UserReg_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'User Registration: Registration Initiation'."),

	Esign_Proceed_AC(
			"'User Registration Registration User ID: (User ID) confirmation message should be displayed with 'Done' button.</div>"),
	Esign_Proceed_AR(
			"'User Registration Registration User ID: (User ID) confirmation message is getting displayed with 'Done' button.</div>"),

	// Decison

	Decision_Confirm_DC("Select ‘Decision’ as ‘Confirm’."), Decision_Confirm_SS("'Confirm'"),
	Decision_Confirm_AC("Selected value should be displayed for the field.</div>"
	+ "<div><b>*</b>Remark(s) / Reason(s) should be auto filled with ‘--‘.<div>"),
	Decision_Confirm_AR("Selected value is getting displayed for the field.</div>"
			+ "<div><b>*</b>Remark(s) / Reason(s) is getting displayed and auto filled with ‘--‘.<div>"),
	// User Confirmation submit button

	UserConfirm_Submit_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'User Registration: Registration Approval: Approve'.</div>"),
	UserConfirm_Submit_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'User Registration: Registration Approval: Approve'.</div>"),

	// Esign User Registration Confirmation

	Proceed_UserConfirmation_AC("'Login Password' screen should be displayed."),
	Proceed_UserConfirmation_AR("'Login Password' screen is getting displayed."),

	// Password Reset

	CurrentPassword_DC("Enter valid Password in the 'Current Password' field."),
	CurrentPassword_AC("Entered value should be displayed in 'Current Password' field in hidden mode."),
	CurrentPassword_AR("Entered value is getting displayed in 'Current Password' field in hidden mode."),
	CurrentPassword_SS("'Current Password'"),

	NewPassword_DC("Enter valid Password in the 'New Password' field."),
	NewPassword_AC("Entered value should be displayed in 'New Password' field in hidden mode."),
	NewPassword_AR("Entered value is getting displayed in 'New Password' field in hidden mode."),
	NewPassword_SS("'New Password'"),

	ConmfirmPassword_DC("Enter valid Password in the 'Confirm Password' field."),
	ConmfirmPassword_AC("Entered value should be displayed in 'Conmfirm Password' field in hidden mode."),
	ConmfirmPassword_AR("Entered value is getting displayed in 'Conmfirm Password' field in hidden mode."),
	ConmfirmPassword_SS("'Conmfirm Password'"),

	// Password reset submit button

	Submit_Button_AC(
			"'Password Changed Successfully' confirmation message should be displayed.</div>"),
	Submit_Button_AR(
			"'Password Changed Successfully' confirmation message is getting displayed.</div>"),

	// Reporting to button

	Click_ADDItemReportingTo_AC("'Reporting To Users List' window should be displayed."),
	Click_ADDItemReportingTo_AR("'Reporting To Users List' window is getting displayed."),
	Click_ADDItemReportingTo_SS("'Reporting To'"),

	// Enter Required Reporting To Code.

	Enter_ReportingTo_DC("Enter the valid 'Reporting To' in 'Find' textbox"),

	// Select Required ReportingTo

	Select_ReportingTo_DC("Select the required 'ReportingTo'"),

	Click_UserRegistrationDC("Click on the 'User Registration' menu."),
	Click_UserRegistrationAC("‘User Registration-Initiation Initiation’ screen should be displayed..</div>"
			+ "<div><b>*</b>Screen should contain ‘Company Code’, ‘Company Name’, ‘Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’ ,'Auto Deactivation','Reporting To' and ‘Temporary Password Allotted’ fields<div>"
			+ "<div><b>*</b>‘Company Code’, ‘Company Name’ and ‘Temporary Password’ Allotted’ fields should be displayed in readable format only.<div>"
			+ "<div><b>*</b>The screen should contain ‘View Existing List’ and 'Submit’ buttons..<div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Users List’ should be displayed <if any>.<div>"),
	Click_UserRegistrationAR("‘User Registration-Initiation Initiation’ screen is getting displayed..</div>"
			+ "<div><b>*</b>Screen is getting displayed with ‘Company Code’, ‘Company Name’, ‘Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’ 'Auto Deactivation','Reporting To' and ‘Temporary Password Allotted’ fields<div>"
			+ "<div><b>*</b>‘Company Code’, ‘Company Name’ and ‘Temporary Password’ Allotted’ fields is getting displayed in readable format only.<div>"
			+ "<div><b>*</b>The screen is displaying ‘View Existing List’ and 'Submit’ buttons..<div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Users List’ is getting displayed <if any>.<div>"),

	Click_UserRegistrationSS("'User Registration'"),

//User Registration  in Audit Trails 

	// Search by UserName

	SearchBy_UserName1_DC("Select 'User Name'."), SearchBy_UserName1_AC("Selection should be accepted.</div>"),
	SearchBy_UserName1_AR(" Selection is getting accepted.</div>"), SearchBy_UserName1_SS("'User Name'."),
	
	
	

	// Search by UserID

	SearchBy_UserID_DC("Select 'User ID'."), SearchBy_UserID_AC("Selection should be accepted.</div>"),
	SearchBy_UserID_AR(" Selection is getting accepted.</div>"), SearchBy_UserID_SS("'User ID'."),
	
	
	
//like UserName
	Like_UserName_DC("Enter the above registered 'User Name' in 'Like' text box."),
	Like_UserName_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
	Like_UserName_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
	Like_UserName_SS("User Name"),

	
	//like UserID
		Like_UserID_DC("Enter the 'User ID' of the above registered User."),
		Like_UserID_AC("<div><b>*</b> Entered value should be displayed for the field.</div>"),
		Like_UserID_AR("<div><b>*</b> Entered value is getting displayed for the field.</div>"),
		Like_UserID_SS("User ID"),

	
	
	
	
	
	
	// Click User Registration

	Click_UserRegistration_for_AuditTrails_DC("Click on the above registered 'User Name'."),
	Click_UserRegistration_for_AuditTrails_AC(
			"‘User Registration - Audit Trails- Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details of the User entered during registration.<div>"
					+ "<div><b>*</b> The ‘Events’ section should display only the Registration ‘Initiated’ transaction with ‘Username, Date& Time details.<div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘0’.<div>"
					+ "<div><b>*</b>All the particulars should  be displayed in read only format..<div>"),
	Click_UserRegistration_for_AuditTrails_AR(
			"‘User Registration - Audit Trails- Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen is getting displayed with the details of the User entered during registration.<div>"
					+ "<div><b>*</b>‘Events’ section is displaying only the Registration ‘Initiated’ transaction with ‘Username, Date& Time details.<div>"
					+ "<div><b>*</b> Also, the ‘No. of Approvals Required’ reads as ‘1’ and the ‘No. of Approvals Completed’ reads as ‘0’<div>"
					+ "<div><b>*</b>All the particulars is getting  displayed in read only format.<div>"),

	
	
	
	
	
	Click_UserRegistrationApproval_for_AuditTrails_AC(
			"‘User Registration - Audit Trails- Revision No.: 0 -Registration’ screen should be displayed.</div>"
					+ "<div><b>*</b> The screen should be displayed with the details of the User entered during registration.<div>"
					+ "<div><b>*</b> The ‘Events’ section should display only the Registration ‘Initiated’ transaction with ‘Username, Date& Time details.<div>"
					+ "<div><b>*</b>Also, the ‘No. of Approvals Required’ should be read as ‘1’ and the ‘No. of Approvals Completed’ should be read as ‘1’.<div>"
					+ "<div><b>*</b>All the particulars should  be displayed in read only format..<div>"),
	Click_UserRegistrationApproval_for_AuditTrails_AR(
			"‘User Registration - Audit Trails- Revision No.: 0 -Registration’ screen is getting displayed.</div>"
					+ "<div><b>*</b>The screen is getting displayed with the details of the User entered during registration.<div>"
					+ "<div><b>*</b>‘Events’ section is displaying only the Registration ‘Initiated’ transaction with ‘Username, Date& Time details.<div>"
					+ "<div><b>*</b> Also, the ‘No. of Approvals Required’ reads as ‘1’ and the ‘No. of Approvals Completed’ reads as ‘1’<div>"
					+ "<div><b>*</b>All the particulars is getting  displayed in read only format.<div>"),

	
	
	
	
	
	
	
	
	
	
	Click_UserRegistration_for_AuditTrails_SS("'User Registration Audit Trails'."),

	Like_User_Name_DC("Enter the above registered 'User Name' in 'Like' field."),
	Like_User_Name_AC(" Entered value should be displayed.</div>"), Like_User_Name_AR("'User Name'."),
	Like_User_Name_SS("User Name"),

	// Department Dropdown

	Departmentdropdown_DC("Click on   Search by 'Department' drop down."),
	Departmentdropdown_AC("'Option to select ‘Department Code’ and ‘Department Name’ should be available."),
	Departmentdropdown_AR("'Option to select ‘Department Code’ and ‘Department Name’are available."),
	Departmentdropdown_SS("'Department'"),
	// Select Department Name
	SelectDepartmentName_DC("Select the 'Department Name'."), SelectDepartmentName_AC("Selection should be accepted."),
	SelectDepartmentName_AR("Selection is getting accepted."), SelectDepartmentName_SS("'Department Name'"),

	// Search by User Name

	SearchBy_UserName_DC("Click on 'Search By' dropdown."),
	SearchBy_UserName_AC(
			"Option to search with 'Top 250 Records, User Name, User ID, and  Initiated Between  should be diplayed.<div>"),
	SearchBy_UserName_AR(
			"Option to search with 'Top 250 Records, User Name, User ID, Initiated Between  is getting diplayed.<div>"),
	SearchBy_UserName_SS("'Search By'."),

	// Designation Dropdown

	Designationdropdown_DC("Click on  Search by 'Designation' drop down."),
	Designationdropdown_AC("'Option to select ‘Designation Code’ and ‘Designation’ should be available."),
	Designationdropdown_AR("'Option to select ‘Designation Code’ and ‘Designation’are available."),
	Designationdropdown_SS("'Designation'"),

	// Select Designation

	SelectDesignation_DC("Select the  'Designation'."), SelectDesignation_AR("Selection should be accepted."),
	SelectDesignation_AC("Selection is getting accepted."), SelectDesignation_SS("'Designation'"),
	// close

	Close_AuditTrails_UserRegistration_AC("'User Registration Audit Trails' screen should be displayed.</div>"),
	Close_AuditTrails_UserRegistration_AR("'User Registration Audit Trails' screen  is getting displayed.</div>"),

	SSO_AudittrailsUserRegistrationMenu_AC("‘User Registration Audit Trails’ screen should be displayed.<div>"
			+ "<div><b>*</b> Screen should contain‘Search this Page’, ‘Advanced Search’ and‘Total Records Count’ icons <div>"
			+ "<div><b>*</b>Screen should contain ‘UserName’, ‘User ID’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’columns..<div>"),

	SSO_AudittrailsUserRegistrationMenu_AR("‘User Registration Audit Trails’ screen is getting displayed.<div>"
			+ "<div><b>*</b> Screen is displaying with ‘Search this Page’, ‘Advanced Search’ and‘Total Records Count’ icons <div>"
			+ "<div><b>*</b>Screen is displaying with ‘UserName’, ‘User ID’, ‘Initiated By’, ‘Initiated On’ and ‘Revision No.’columns.<div>"),

	// Click on
	SSO_AudittrailsUserRegistrationMenu_DC("Click on 'UserRegistration' menu."),

	SSO_AudittrailsUserRegistrationMenu_SS("'User Registration'"),

	SearchBy_DC("Click on ‘Advanced Search’ icon."),

	SearchBy_SS("‘Advanced Search’icon."),
	SearchBy_AC(
			"Option to search with 'Top 250 Records, User Name, User ID, and  Initiated Between  should be diplayed.<div>"),
	SearchBy_AR(
			"Option to search with 'Top 250 Records, User Name, User ID, Initiated Between  is getting diplayed.<div>"),

	
	Click_UserRegistration_310DC("Click on the 'User Registration' menu."),
	Click_UserRegistration_310AC("‘User Registration-Initiation Initiation’ screen should be displayed..</div>"
			+ "<div><b>*</b>Screen should contain ‘Company Code’, ‘Company Name’, ‘Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’ , and ‘Temporary Password Allotted’ fields<div>"
			+ "<div><b>*</b>‘Company Code’, ‘Company Name’ and ‘Temporary Password’ Allotted’ fields should be displayed in readable format only.<div>"
			+ "<div><b>*</b>The screen should contain ‘View Existing List’ and 'Submit’ buttons..<div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Users List’ should be displayed <if any>.<div>"),
	Click_UserRegistration_310AR("‘User Registration-Initiation Initiation’ screen is getting displayed..</div>"
			+ "<div><b>*</b>Screen is getting displayed with ‘Company Code’, ‘Company Name’, ‘Designation’, ‘First Name’, ‘Last Name’, ‘User ID’, ‘Employee ID’, ‘Email ID’ and ‘Temporary Password Allotted’ fields<div>"
			+ "<div><b>*</b>‘Company Code’, ‘Company Name’ and ‘Temporary Password’ Allotted’ fields is getting displayed in readable format only.<div>"
			+ "<div><b>*</b>The screen is displaying ‘View Existing List’ and 'Submit’ buttons..<div>"
			+ "<div><b>*</b>Under ‘View Existing’, registered active records of ‘Users List’ is getting displayed <if any>.<div>"),
	
	UserConfirm_Submit_310_AC(
			"'Login Password Change Login' Page should be displayed.</div>"),
	UserConfirm_Submit_310_AR(
			"'Login Password Change Login'  Page is getting displayed.</div>"),
	
	UserConfirm_Submit_310_SS("'Login Password Change Login'");
	
	private final String userREgStrings;

	UserRegistrationStrings(String UserRegisStrings) {

		this.userREgStrings = UserRegisStrings;

	}

	public String getUserRegistrationStrings() {
		return userREgStrings;
	}

}
