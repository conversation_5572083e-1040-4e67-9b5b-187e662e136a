package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.QuestionBankStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Strings.TrainingScheduleStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Self_Study_QB extends OQActionEngine {

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_NoOfMulChoicesQues']")
	WebElement multipleChoice;
	@FindBy(id = "CMQuesBankSelfStudy_multipleChoices0_QuestionDesc")
	WebElement mcq1;

	@FindBy(id = "CMQuesBank_multipleChoices1_QuestionDesc")
	WebElement mcq2;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_multipleChoices0_Choice1']")
	WebElement multipleChoiceQ1Ans1;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_multipleChoices0_Choice2']")
	WebElement multipleChoiceQ1Ans2;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_multipleChoices0_Choice3']")
	WebElement multipleChoiceQ1Ans3;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_multipleChoices0_Choice4']")
	WebElement multipleChoiceQ1Ans4;

	@FindBy(xpath = "//div[@id='MultipleDataDiv']/div[10]/div[1]/div[1]/div[1]/input[1]")
	WebElement mcq1RB2;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_multipleChoices0_Marks']")
	WebElement multipleChoiceQ1Marks;

	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_NoOfTrueFalseQues']")
	WebElement trueOrFalse;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_trueOrFalses0_QuestionDesc']")
	WebElement trueOrFalseQ1;
	@FindBy(xpath = "//div[@id='TrueDataDiv']/div[6]/div[1]/div[1]/div[1]/input[1]")
	WebElement trueOrFalseQ1RB1;
	@FindBy(xpath = "//input[@id='CMQuesBankSelfStudy_trueOrFalses0_Marks']")
	WebElement trueOrFalseQ1Marks;

	@FindBy(xpath = "//button[@id='btnSubmit']")
	WebElement submit;
	@FindBy(xpath = "//button[@id='btnSubmit' and @class='caliber-button-primary N5SubmitCls']")
	WebElement submit1;
	@FindBy(id = "btnSubmit")
	WebElement submitButton;
	@FindBy(xpath = "//div[@id='CompareTRN']//p[text()='Initiated']//following-sibling::p[3]")
	WebElement auditCompareTRNRemarksVal1;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']//li//a[text()='Audit Trails']")
	WebElement courseManagerAuditTrails;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager_Audit Trails']//li//a[text()='Question Bank']")
	WebElement questionBankAudit;
	@FindBy(xpath = "//span[@class='select2-results']/ul[1]/li[contains(text(),'Topic Name')]")
	WebElement searchByTopicNameDropdown;
	@FindBy(id = "Description")
	WebElement topicNameLike;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(id = "displayBtn")
	WebElement apply;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement close;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;

	@FindBy(id = "displayBtn")
	WebElement searchFilterApplyBtn;

	@FindBy(xpath = "//label[normalize-space()='Multiple Choice']")
	WebElement SSLabelQuestions1;

	@FindBy(xpath = "//label[normalize-space()='True or False']")
	WebElement SSLabelQuestion2;

	@FindBy(xpath = "//label[normalize-space()='Fill In The Blanks']")
	WebElement SSLabelQuestionFB;

	@FindBy(xpath = "//*[@id='esign_Activity']")
	WebElement EsignTitle;

	@FindBy(xpath = "//input[@class='select2-search__field']")
	WebElement ApprovalValue;

	@FindBy(xpath = "//input[@class='caliber-textbox unique-control']")
	WebElement configurationUniqueCode;

	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esignpwd;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	@FindBy(xpath = "//a[@title='Prepare Question Bank']")
	WebElement prepareQB;

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[@id='MenuModule_TMS']/li/div/following-sibling::a[text()='Course Manager']")
	WebElement courseManagermenu;

	@FindBy(xpath = "//a[text()='Edit']")
	WebElement Edit;
	
	@FindBy(xpath = "//*[@data-menupath='Edit -> Self-Study Question Bank']")
	WebElement EditSSQB;
	
	
	
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[6]//a[contains(@class,'sub-menu')][contains(text(),'Prepare')]")
	WebElement prepareMenu;

	@FindBy(xpath = "//*[@data-menupath='Prepare -> Self-Study Question Bank']")
	WebElement prepareSelfStudyQB;

	@FindBy(xpath = "//span[@class='select2-selection__arrow']")
	WebElement searchBy;

	@FindBy(xpath = "//li[text()='Course Name']")
	WebElement searchByCoursename;
	
	@FindBy(xpath = "//li[text()='Question Bank Name']")
	WebElement searchByQBname;
	

	@FindBy(id = "CourseName")
	WebElement CourseName;

	@FindBy(id = "displayBtn")
	WebElement applyBtn;
	@FindBy(id = "Description")
	WebElement QBName;
	

	@FindBy(xpath = "//*[@id='ListTab']/tbody/tr[1]")
	WebElement clickCourse;
	
	@FindBy(id = "CMQuesBankSelfStudy_Remarks")
	WebElement remarks;

	public void selfStudyQuestioBank(HashMap<String, String> testdata) {
		// String TopicVal = "TopicLEIQIVNX";
		click2(prepareQB, QuestionBankStrings.Select_MCAnswer_b_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_MCAnswer_b_SS.getQBStrings());
		TimeUtil.mediumWait();
		sendKeysAndRemoveFocus(multipleChoice, QuestionBankStrings.MCTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsMC"), QuestionBankStrings.MCTypeCount_AC.getQBStrings(),
				QuestionBankStrings.MCTypeCount_AR.getQBStrings(), QuestionBankStrings.MCTypeCount_SS.getQBStrings());

		TimeUtil.shortWait();

		sendKeys2(mcq1, QuestionBankStrings.EnterMC_Question_DC.getQBStrings(), testdata.get("MCQQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans1, QuestionBankStrings.EnterMC_Option_a_DC.getQBStrings(), testdata.get("MCQ1A1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_a_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans2, QuestionBankStrings.EnterMC_Option_b_DC.getQBStrings(), testdata.get("MCQ1A2"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans3, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A3"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_c_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans4, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A4"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_d_SS.getQBStrings());

		click2(mcq1RB2, QuestionBankStrings.Select_MCAnswer_b_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_MCAnswer_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("MCQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		TimeUtil.mediumWait();
		trueOrFalse.clear();

		scrollToViewElement(trueOrFalse);

		sendKeysAndRemoveFocus(trueOrFalse, QuestionBankStrings.TFTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsTF"), QuestionBankStrings.TFTypeCount_AC.getQBStrings(),
				QuestionBankStrings.TFTypeCount_AR.getQBStrings(), QuestionBankStrings.TFTypeCount_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1, QuestionBankStrings.EnterTF_Question_DC.getQBStrings(), testdata.get("TrueorFalseQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		click2(trueOrFalseQ1RB1, QuestionBankStrings.Select_True_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_True_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("TFQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

//		try {
//			sendKeys2(remarks, "Enter required remarks", "Remarks", "", "", "");	
//			
//		}
//		catch(Exception e) {
//			
//			System.out.println("This is not editing");
//		}
		
		scrollToViewElement(submitButton);

		click2(submitButton, TopicStrings.Submit_DC.getTopicStrings(), QuestionBankStrings.Submit_AC.getQBStrings(),
				QuestionBankStrings.Submit_AR.getQBStrings(), TopicStrings.Submit_SS.getTopicStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}

	public void selfStudyQuestioBank_through_menus(HashMap<String, String> testdata) {
		// String TopicVal = "TopicLEIQIVNX";
		switchToDefaultContent(driver);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		scrollToViewElement(prepareMenu);
		click2(prepareMenu, CommonStrings.Prepare_DC.getCommonStrings(), CommonStrings.Prepare_AC.getCommonStrings(),
				CommonStrings.Prepare_AR.getCommonStrings(), CommonStrings.Prepare_SS.getCommonStrings());
		click2(prepareSelfStudyQB, "Click on Self Study Question Bank", "", "", "");
		switchToBodyFrame(driver);

		click2(searchBy, "Click on Search By drop down", "", "", "");

		click2(searchByCoursename, "Select Course Name", "", "", "");

		sendKeys2(CourseName, "Enter above registered course name", CM_SelfStudyCourse.Course, "", "", "");
		click2(applyBtn, "Click on Apply button", "", "", "");
		click2(clickCourse, "Click on above registred course", "", "", "");
		TimeUtil.mediumWait();
		sendKeysAndRemoveFocus(multipleChoice, QuestionBankStrings.MCTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsMC"), QuestionBankStrings.MCTypeCount_AC.getQBStrings(),
				QuestionBankStrings.MCTypeCount_AR.getQBStrings(), QuestionBankStrings.MCTypeCount_SS.getQBStrings());

		TimeUtil.shortWait();

		sendKeys2(mcq1, QuestionBankStrings.EnterMC_Question_DC.getQBStrings(), testdata.get("MCQQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans1, QuestionBankStrings.EnterMC_Option_a_DC.getQBStrings(), testdata.get("MCQ1A1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_a_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans2, QuestionBankStrings.EnterMC_Option_b_DC.getQBStrings(), testdata.get("MCQ1A2"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans3, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A3"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_c_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Ans4, QuestionBankStrings.EnterMC_Option_c_DC.getQBStrings(), testdata.get("MCQ1A4"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Option_d_SS.getQBStrings());

		click2(mcq1RB2, QuestionBankStrings.Select_MCAnswer_b_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_MCAnswer_b_SS.getQBStrings());

		sendKeys2(multipleChoiceQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("MCQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		TimeUtil.mediumWait();
		trueOrFalse.clear();

		scrollToViewElement(trueOrFalse);

		sendKeysAndRemoveFocus(trueOrFalse, QuestionBankStrings.TFTypeCount_DC.getQBStrings(),
				testdata.get("NumberofQuestionsTF"), QuestionBankStrings.TFTypeCount_AC.getQBStrings(),
				QuestionBankStrings.TFTypeCount_AR.getQBStrings(), QuestionBankStrings.TFTypeCount_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1, QuestionBankStrings.EnterTF_Question_DC.getQBStrings(), testdata.get("TrueorFalseQ1"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.EnterMC_Question_SS.getQBStrings());

		click2(trueOrFalseQ1RB1, QuestionBankStrings.Select_True_DC.getQBStrings(),
				CommonStrings.dropdown_AC.getCommonStrings(), CommonStrings.dropdown_AR.getCommonStrings(),
				QuestionBankStrings.Select_True_SS.getQBStrings());

		sendKeys2(trueOrFalseQ1Marks, QuestionBankStrings.Enter_Marks__DC.getQBStrings(), testdata.get("TFQ1marks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				QuestionBankStrings.Enter_Marks__SS.getQBStrings());

		scrollToViewElement(submitButton);

		click2(submitButton, TopicStrings.Submit_DC.getTopicStrings(), QuestionBankStrings.Submit_AC.getQBStrings(),
				QuestionBankStrings.Submit_AR.getQBStrings(), TopicStrings.Submit_SS.getTopicStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}
	
	
	public void Edit_selfStudyQuestioBank() {
		// String TopicVal = "TopicLEIQIVNX";
		if(CM_SelfStudyCourse.OpenForAll.equals("Yes")) {
			click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
					CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
			click2(courseManagermenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
					CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
					CommonStrings.CM_Menus_SS.getCommonStrings());
			click2(Edit, "Click on Edit", "", "", "");
			click2(EditSSQB, "Click on Self Study Question Bank", "", "", "");
			switchToBodyFrame(driver);
			click2(searchBy, "Click on Search By drop down", "", "", "");
			click2(searchByQBname, "Select Course Name", "", "", "");

			sendKeys2(QBName, "Enter above registered course name", CM_SelfStudyCourse.Course+"%", "", "", "");
			click2(applyBtn, "Click on Apply button", "", "", "");
			click2(clickCourse, "Click on above Question bank name", "", "", "");
		}
		waitForElementVisibile(remarks);
			sendKeys2(remarks, "Enter required remarks", "Remarks", "", "", "");	
			
		
		scrollToViewElement(submitButton);

		click2(submitButton, TopicStrings.Submit_DC.getTopicStrings(), QuestionBankStrings.Submit_AC.getQBStrings(),
				QuestionBankStrings.Submit_AR.getQBStrings(), TopicStrings.Submit_SS.getTopicStrings());
		try {
			if (esignpwd.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esignpwd, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EpicUserPWD"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				TimeUtil.shortWait();
				click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AC.getCourseSessionStrings(),
						CourseSessionStrings.Esign_ProceedCourseSession_AR.getCourseSessionStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
				TimeUtil.shortWait();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		switchToDefaultContent(driver);
		TimeUtil.shortWait();

	}


}
