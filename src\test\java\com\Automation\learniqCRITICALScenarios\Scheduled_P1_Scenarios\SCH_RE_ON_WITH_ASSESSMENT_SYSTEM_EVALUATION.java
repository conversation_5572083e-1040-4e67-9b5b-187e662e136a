package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;

/**
 * Verify Online RE type Session with assessment for scheduled course and make
 * at least one employee qualified, To Be Retrained with System evaluation and
 * by viewing Individual employee report at each transaction starting from
 * course session.
 */

public class SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION extends OQActionEngine {

	public SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION() {
		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	String ExcelPath = "./learnIQTestData/SCH_RE_ON_WITH_ASSESSMENT_SYSTEM_EVALUATION.xlsx";

	// ----------Topic - Test Method---------- //

	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic  Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic  Registration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//			epiclogin.masterPlant();
		//
//			InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//					Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//			Logout.signOutPage();

//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//			epiclogin.plant1();
		//
//			InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//			Logout.signOutPage();
	}
	// Test Method for Course Configuration, Registration, Approve with
	// AuditTrails----------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Course  Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course Registration");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//			ConfigsReader.getPropValue("EpicUserPWD"));

		// epiclogin.masterPlant();
		//
		// Initiate_Course.courseConfiguration_Reg(testData);

		// epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

		// Logout.signOutPage();

		// epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//			ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
		// epiclogin.plant1();
		//
		// Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
		// Logout.signOutPage();
	}

	// Test Method for TrainingSchedule Configuration, Modification, Approve with
	// AuditTrails---------------------------------------------------------------------------------------------

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("TrainingSchedule Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Modification");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();
//
//		TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);

//		TrainingShcedule.trainingScheduleAuditTrail();
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//		TrainingShcedule.approveModifyTrainingScheduled(testData);
//		TrainingShcedule.trainingScheduleAuditTrail();
//		Logout.signOutPage();
	}

	// Test Method for CourseSession Configuration, Registration, Approve with
	// AuditTrails

	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Online_DocumentReading_WithExam(testData);

		// CourseSession.courseSessionAuditTrails();

//		if (isReportedRequired == true) {
//			test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Individual Employee Report for Course Session Under Approval");
//			}
//			IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//					Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL, Constants.REType);
//
//			Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report for Course Session Proposed Status");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.Session_PROPOSED_FOR_RE,
				Constants.REType);
		//
		// Logout.signOutPage();
	}
	// Test Method for QuestionBank Configuration, Registration, Approve with
	// AuditTrails-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "QuestionBank")
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//					ConfigsReader.getPropValue("EpicUserPWD"));
//			
//			epiclogin.plant1();
		//
//			epiclogin.masterPlant();
//			PrepareQB.QBRegistrationApproval_Configuration(testData);
//			epiclogin.navigateTolearnIQPlant();
		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);
//			Logout.signOutPage();
//			epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//					ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//			epiclogin.plant1();
//			PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);
//			Logout.signOutPage();
	}
	// Test Method for QuestionPaper Registration with Manual
	// Evaluation-------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration with Manual Evaluation")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration with Manual Evaluation");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOnline, Constants.REType);

		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user
	// Qualified and view IER

	@Test(priority = 7, enabled = true)
	public void qualifiedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond Document Reading").assignAuthor(CM_CourseSession.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getQualifiedTraineeID(),
				CM_CourseSession.QualifiedTraineePsw);

		epiclogin.plant1();

		RespondDR.respondDocReading(CM_CourseSession.QualifiedTraineePsw);
		if (isReportedRequired == true) {
			test = extent.createTest(
					"Individual Employee Report of Document Reading Completed and Question paper Pending employee")
					.assignAuthor(CM_CourseSession.getQualifiedTraineeID()).assignCategory(
							"Individual Employee Report of Document Reading Completed and Question paper Pending employee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.DR_COMPLETED,
				Constants.REType);
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user Qualified")
					.assignAuthor(CM_CourseSession.getQualifiedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user Qualified");
		}
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_QUALIFIED, Constants.REType,
				CM_CourseSession.QualifiedTraineePsw);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Qualified Trainee")
					.assignAuthor(CM_CourseSession.getQualifiedTraineeID())
					.assignCategory("Individual Employee Report of Qualified Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.REType);

		Logout.signOutPage();
	}

	// Test Method for Respond Document Reading and Respond to QP and make user To
	// Be Retrained and view IER

	@Test(priority = 8, enabled = true)
	public void toBeRetrainedEmp_RespondDocReading_RespondQuestionPaper() {
		if (isReportedRequired == true) {
			test = extent.createTest("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrianed")
					.assignAuthor(CM_CourseSession.getToBeRetrainedTraineeID())
					.assignCategory("Respond DocumentReading, Respond QuestionPaper and make user To Be Retrianed");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				CM_CourseSession.getToBeRetrainedTraineeID(), CM_CourseSession.ToBeRetrainedTraineePsw);
		epiclogin.plant1();
		RespondDR.respondDocReading(CM_CourseSession.ToBeRetrainedTraineePsw);
		RespondQP.respondQP(Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType,
				CM_CourseSession.ToBeRetrainedTraineePsw);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of To-Be-Retrained Trainee")
					.assignAuthor(CM_CourseSession.ToBeRetrainedEmployeeID)
					.assignCategory("Individual Employee Report of To-Be-Retrained Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.REType);
//			if (isReportedRequired == true) {
//			test = extent.createTest("Check Trainees at Course Session Screen")
//					.assignAuthor( ConfigsReader.getPropValue("EpicUserID"))
//					.assignCategory("Check Trainees at Course Session Screen");
//			}
		// CourseSession.verify_Employees_At_Coursesession();
		Logout.signOutPage();
	}

//	// Test Method for Respond Document Reading and Respond to QP and make user To
//	// QPPending and view IER
//
//	@Test(priority = 9, enabled = true)
//	public void respondDocReading() {
//
//		test = extent
//				.createTest("Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status")
//				.assignAuthor(CM_CourseSession.getQPPendingUserID()).assignCategory(
//						"Respond DocumentReading, Respond QuestionPaper with Questionpaper Pending Trainee status");
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_CourseSession.getQPPendingUserID(),
//				CM_CourseSession.QPPendingUserPsw);
//
//		epiclogin.plant1();
//
//		RespondDR.respondDocReading(CM_CourseSession.QPPendingUserPsw);
//
//		test = extent.createTest("Individual Employee Report of Question Paper Pending Trainee")
//				.assignAuthor(CM_CourseSession.getQPPendingUserID())
//				.assignCategory("Individual Employee Report of Question Paper Pending Trainee");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.getQPPendingUserID(),
//				Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED);
//
//		Logout.signOutPage();
//
//		n = 0;
//		screenshotCounter = 0;
//	}
}
