package com.Automation.learniqObjects;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseSessionStrings;
import com.Automation.Strings.RespondDRStrings;
import com.Automation.Strings.RespondQPStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_Respond_MissedQuestionAnalysis extends OQActionEngine {

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;

	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;

	@FindBy(xpath = "//a[contains(@class,'sub-menu')][contains(text(),'Respond')]")
	WebElement respond;

	@FindBy(id = "TMS_Course Manager_Respond_MEN72")
	WebElement respondMQA;

	@FindBy(id = "select2-SearchType-container")
	WebElement searchByNew;

	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Batch Name']")
	WebElement searchByBatchName;

	@FindBy(id = "BatchDesc")
	WebElement textBox_BatchName;

	@FindBy(id = "displayBtn")
	WebElement display;

	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;

	@FindBy(xpath = "//input[@class='caliber-Labeled-option Confirm']")
	WebElement checkbox_Confirm;

	@FindBy(xpath = "//textarea[@id='Comments']")
	WebElement textBox_Comments;

	@FindBy(xpath = "//button[@id='btnSubmit' and @class='caliber-button-primary SubmitCls']")
	WebElement button_Submit;

	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;

	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;

	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;

	@FindBy(xpath = "//a[@id='cfnMsg_Next']")
	WebElement done;

	public void respondMissedQuestionAnalysis(String esignPsw) {
		String CourseNameVal = CM_Course.getCourse();
		// String CourseNameVal = "CypressEODS";
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(respond, CommonStrings.Respond_menu_DC.getCommonStrings(),
				CommonStrings.Respond_menu_AC.getCommonStrings(), CommonStrings.Respond_menu_AR.getCommonStrings(),
				CommonStrings.Respond_menu_DC.getCommonStrings());
		click2(respondMQA, RespondQPStrings.Respond_MQA_menu_DC.getRespondQPStrings(),
				RespondQPStrings.Respond_MQA_menu_AC.getRespondQPStrings(),
				RespondQPStrings.Respond_MQA_menu_AR.getRespondQPStrings(),
				RespondQPStrings.Respond_MQA_menu_SS.getRespondQPStrings());
		switchToBodyFrame(driver);

		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RespondQPStrings.SearchBy_AC.getRespondQPStrings(), RespondQPStrings.SearchBy_AR.getRespondQPStrings(),
				CommonStrings.SearchBy_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(searchByBatchName, RespondQPStrings.SearchBy_CourseName_DC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AC.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_AR.getRespondQPStrings(),
				RespondQPStrings.SearchBy_CourseName_SS.getRespondQPStrings());
		sendKeys2(textBox_BatchName, RespondQPStrings.Like_CourseNameForQp_DC.getRespondQPStrings(),
				CourseNameVal + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RespondQPStrings.Like_CourseName_SS.getRespondQPStrings());
		TimeUtil.shortWait();
		click2(display, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		click2(displayedRecord, RespondQPStrings.Click_Course_DC.getRespondQPStrings(),
				RespondQPStrings.Click_Course_AC.getRespondQPStrings(),
				RespondQPStrings.Click_Course_AR.getRespondQPStrings(),
				RespondQPStrings.Click_Course_SS.getRespondQPStrings());
		TimeUtil.shortWait();
		click2(checkbox_Confirm, CourseSessionStrings.Confirm_CheckBox_DC.getCourseSessionStrings(),
				CourseSessionStrings.Confirm_CheckBox_AC.getCourseSessionStrings(),
				CourseSessionStrings.Confirm_CheckBox_AR.getCourseSessionStrings(),
				CourseSessionStrings.Confirm_CheckBox_SS.getCourseSessionStrings());
		sendKeys2(textBox_Comments, CourseSessionStrings.MQARemarks_DC.getCourseSessionStrings(), "MQA Submit",
				CourseSessionStrings.MQARemarks_AC.getCourseSessionStrings(),
				CourseSessionStrings.MQARemarks_AR.getCourseSessionStrings(),
				CourseSessionStrings.MQARemarks_SS.getCourseSessionStrings());

		TimeUtil.shortWait();
		scrollToViewElement(button_Submit);
		click2(button_Submit, TopicStrings.Submit_DC.getTopicStrings(),
				RespondQPStrings.Submit_AC.getRespondQPStrings(), RespondQPStrings.Submit_AR.getRespondQPStrings(),
				TopicStrings.Submit_SS.getTopicStrings());
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), esignPsw,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();

		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AC.getRespondDRStrings(),
				RespondDRStrings.Esign_ProceedRSPDOC_AR.getRespondDRStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.mediumWait();
		;

		switchToDefaultContent(driver);
	}
}