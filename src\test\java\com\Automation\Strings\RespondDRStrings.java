package com.Automation.Strings;

public enum RespondDRStrings {

	// Menus

	DRMenu_DC("Click on 'Document Reading' submenu."),

	DRMenu_AC("'Document Reading Course List' screen should be diplayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Regular Course(s), Mandatory Course(s) and Open Self-Study Course(s) tabs.</div>"
			+ "<div><b>*</b> By default 'Regular Course(s)' tab should be" + "</br>" + "selected."),
	DRMenu_AR("'Document Reading Course List' screen is getting diplayed.</div>" 
	+ "<div><b>*</b> Screen is getting displayed with 'Regular Course(s), Mandatory Course(s) and Open Self-Study Course(s) tabs.</div>"
			+ "<div><b>*</b> By default 'Regular Course(s)' tab is getting" + "</br>" + "selected."),
	DRMenu_SS("'Document Reading'"),

	Enter_CSName_RSPDR_DC("Enter above registered Course Session Name in 'Search This Page' text box."),
	Enter_CSName_RSPDR_SS("'Course Session Name'"),

	Click_CourseSession_DC("Click on the above registred Course Session Name."),
	Click_CourseSession_AC("'Document Reading Course List' screen should be diplayed.</div>"
			+ "<div><b>*</b> Screen should contain 'Course Name, Course Code, Training Type, Course Session Name, Topic Name and Document List' details accurately.</div>"
			+ "<div><b>*</b> Option to read the document should be available.</div>"),
	Click_CourseSession_AR("'Document Reading Course List' screen is getting diplayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Course Name, Course Code, Training Type, Course Session Name, Topic Name and Document List' details accurately.</div>"
			+ "<div><b>*</b> Option to read the document is available.</div>"),

	Click_CourseSession_SS("Document Reading"),

	Click_To_Read_DC("Click on 'Click To Read' against the required document"),
	Click_To_Read_AC("Preview of the document should be displayed.</div>"),
	Click_To_Read_AR("Preview of the document is getting displayed.</div>"), Click_To_Read_SS("Preview"),

	Preview_Close_DC("Click on 'Close' icon."),
	Preview_Close_AC("'Document Reading Course List' screen should be diplayed.</div>"
			+ "<div><b>*</b> Option to select the 'Mark Reading Status As' should be enabled.</div>"),
	Preview_Close_AR("'Document Reading Course List' is getting diplayed.</div>"
			+ "<div><b>*</b> Option to select the 'Mark Reading Status As' is getting enabled.</div>"),
	Preview_Close_SS("Mark Reading Status As."),

	Select_Completed_DC("Select Mark Reading Status As' as 'Completed'."),
	Select_Completed_AC("Selection should be acccepted.</div>"), Select_Completed_AR("Selection is getting acccepted."),
	Select_Completed_SS("Completed"),

	RespondDR_Esign_AC(
			"'Meaning of This Electronic Signature' window should be displayed as 'Respond To Document Reading:'.</div>"),

	RespondDR_Esign_AR(
			"'Meaning of This Electronic Signature' window is getting displayed as 'Respond To Document Reading:'.</div>"),

	Esign_ProceedRSPDOC_AC(
			"'Document Reading Status Updated' confirmation message should be displayed with 'Done' button."),
	Esign_ProceedRSPDOC_AR(
			"'Document Reading Status Updated' confirmation message is getting  displayed with 'Done' button."),

	Like_CourseName_DC("Enter the course name for which the doument readin is completed."),

	Submit_Button_DC("Click on 'Submit' button and click 'OK' at alert(if any).");

	private final String respondDR;

	RespondDRStrings(String RespondDR) {

		this.respondDR = RespondDR;

	}

	public String getRespondDRStrings() {
		return respondDR;
	}
}
