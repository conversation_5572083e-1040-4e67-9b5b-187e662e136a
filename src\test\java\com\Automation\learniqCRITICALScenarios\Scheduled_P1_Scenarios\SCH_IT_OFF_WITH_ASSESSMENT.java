package com.Automation.learniqCRITICALScenarios.Scheduled_P1_Scenarios;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_CourseSession;
import com.Automation.learniqObjects.CM_QuestionPaper;

/**
 * Verify Offline IT Session with assessment for scheduled course and make
 * atleast one employee skipped, absent, qualified, To Be Retrained by viewing
 * Individual employee report at each transaction starting from course session,
 * also add at least 2 additional users and make sure that should be qualified
 * and to be retrained and view IER for those employees.
 */

public class SCH_IT_OFF_WITH_ASSESSMENT extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SCH_IT_OFF_WITH_ASSESSMENT.xlsx";

	public SCH_IT_OFF_WITH_ASSESSMENT() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}

	// Test Method for Topic Registration
	ExcelUtilUpdated topicData = new ExcelUtilUpdated(ExcelPath, "TopicRegistration");

	@DataProvider(name = "TopicReg")
	public Object[][] getTopicData() throws Exception {
		Object[][] obj = new Object[topicData.getRowCount()][1];
		for (int i = 1; i <= topicData.getRowCount(); i++) {
			HashMap<String, String> testData = topicData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 1, dataProvider = "TopicReg", enabled = true)
	public void Topic_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Topic  Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Topic  Registration");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));

//				epiclogin.masterPlant();
		//
//				InitiateTopic.topicConfiguration_Registration_Approval(testData, ConfigsReader.getPropValue("ConfigureRemarks"),
//						Constants.CONFIGURE_CONFIRMATION_TEXT);

		epiclogin.plant1();
		;

		InitiateTopic.topic_Registration(testData);

//				Logout.signOutPage();

//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//				epiclogin.plant1();
		//
//				InitiateTopic.TopicRegistration_Approval_AuditTrails_Yes(testData);
		//
//				Logout.signOutPage();

	}

	// Test Method for Course Registration
	ExcelUtilUpdated CourseData = new ExcelUtilUpdated(ExcelPath, "CourseRegistration");

	@DataProvider(name = "CourseRegistrationData")
	public Object[][] getCourseData() throws Exception {
		Object[][] obj = new Object[CourseData.getRowCount()][1];
		for (int i = 1; i <= CourseData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 2, dataProvider = "CourseRegistrationData", enabled = true)
	public void Course_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("Course Registration")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Course  Registration");
		}
//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//						ConfigsReader.getPropValue("EpicUserPWD"));

//				epiclogin.masterPlant();
		//
//				Initiate_Course.courseConfiguration_Reg(testData);

//				epiclogin.plant1();

		Initiate_Course.Course_Registration(testData);

//				Logout.signOutPage();

//				epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//						ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		//
//				epiclogin.plant1();
		//
//				Initiate_Course.course_Approval_AuditTrials_Yes(testData);
		//
//				Logout.signOutPage();

	}

	// Test Method for TrainingSchedule  Modification

	ExcelUtilUpdated TrainingSch_Data = new ExcelUtilUpdated(ExcelPath, "ModifyTrainingSchedule");

	@DataProvider(name = "modifyTrainingSchedule")
	public Object[][] getModifyTrainingSchedule() throws Exception {
		Object[][] obj = new Object[TrainingSch_Data.getRowCount()][1];
		for (int i = 1; i <= TrainingSch_Data.getRowCount(); i++) {
			HashMap<String, String> testData = TrainingSch_Data.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 3, dataProvider = "modifyTrainingSchedule", enabled = true)
	public void TrainingSchedule_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {

			test = extent.createTest("TrainingSchedule  Modification")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("TrainingSchedule Modification");
		}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));
//					epiclogin.plant1();
//					TrainingShcedule.trainingScheduleConfiguration(testData);

		TrainingShcedule.modifyTrainingScheduled(testData);

//					TrainingShcedule.trainingScheduleAuditTrail();
//					Logout.signOutPage();
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//					epiclogin.plant1();
//					TrainingShcedule.approveModifyTrainingScheduled(testData);
//					TrainingShcedule.trainingScheduleAuditTrail();
//					Logout.signOutPage();
	}

	// Test Method for Trainer  Modification

	ExcelUtilUpdated TrainerMod = new ExcelUtilUpdated(ExcelPath, "TrainerModification");

	@DataProvider(name = "TrainerModification")
	public Object[][] getTrainerData() throws Exception {
		Object[][] obj = new Object[TrainerMod.getRowCount()][1];
		for (int i = 1; i <= TrainerMod.getRowCount(); i++) {
			HashMap<String, String> testData = TrainerMod.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 4, dataProvider = "TrainerModification", enabled = true)
	public void Trainer_Modification(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Trainer Modification")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))

					.assignCategory("Trainer Modification");
		}
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//							ConfigsReader.getPropValue("EpicUserPWD"));
//					epiclogin.plant1();
//					trainer.TrainerModificationConfigurations(testData);
		trainer.trainer_Modification_AuditTrails(testData);
//					Logout.signOutPage();
//					epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//							ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//					epiclogin.plant1();
//					trainer.modification_Trainer_Approval_AuditTrials_Yes(testData);
//					Logout.signOutPage();

	}
	// Test Method for CourseSession Registration
	ExcelUtilUpdated CourseSessionData = new ExcelUtilUpdated(ExcelPath, "CourseSessionRegAppr");

	@DataProvider(name = "CourseSession")
	public Object[][] getCourseSessionData() throws Exception {
		Object[][] obj = new Object[CourseSessionData.getRowCount()][1];
		for (int i = 1; i <= CourseSessionData.getRowCount(); i++) {
			HashMap<String, String> testData = CourseSessionData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 5, dataProvider = "CourseSession", enabled = true)
	public void CourseSession_Registration(HashMap<String, String> testData) throws InterruptedException {
		if (isReportedRequired == true) {
			test = extent.createTest("CourseSession  Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("CourseSession Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// CourseSession.courseSessionConfiguration(testData);

		CourseSession.courseSession_Offline_WithExam(testData);

//		test = extent.createTest("Individual Employee Report for Course Session Under Approval")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Under Approval");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONUNDERAPPROVAL);
//
//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//		epiclogin.plant1();
//
//		CourseSession.courseSession_Approve(testData);
//
//		test = extent.createTest("Individual Employee Report for Course Session Proposed Status")
//				.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
//				.assignCategory("Individual Employee Report for Course Session Proposed Status");
//
//		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
//				Constants.EMPLOYEESTATUS_AS_COURSESESSIONPROPOSED);

		// Logout.signOutPage();

	}

	// Test Method for BatchFormation Configuration, Registration

	ExcelUtilUpdated BatchFormationData = new ExcelUtilUpdated(ExcelPath, "BatchFormationReg");

	@DataProvider(name = "batchFormation")
	public Object[][] getBatchFormation() throws Exception {
		Object[][] obj = new Object[BatchFormationData.getRowCount()][1];
		for (int i = 1; i <= BatchFormationData.getRowCount(); i++) {
			HashMap<String, String> testData = BatchFormationData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 6, dataProvider = "batchFormation", enabled = true)
	public void BatchFormation_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("BatchFormation Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("BatchFormation  Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		// BatchFormation.batchFormationConfiguration(testData);

		BatchFormation.batchFormation_Offline_Users(testData);

	//	BatchFormation.proposeBatchFormationAuditTrail();

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Employee not selected in batch")
					.assignAuthor(CM_CourseSession.SkippedEmployeeID)
					.assignCategory("Individual Employee Report For Employee not selected in batch");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_NOT_SELECTED, Constants.ITType);

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report For Employee selected in batch")
					.assignAuthor(CM_CourseSession.SkippedEmployeeID)
					.assignCategory("Individual Employee Report For Employee selected in batch");
		}

		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_BATCHTRAINEE_SELECTED, Constants.ITType);

		// Logout.signOutPage();

	}

	// Test Method for QuestionBank Registration

	ExcelUtilUpdated QBData = new ExcelUtilUpdated(ExcelPath, "QuestionBankRegApprove");

	@DataProvider(name = "QuestionBank")
	public Object[][] getdocument_preparation_RequestFlow() throws Exception {
		Object[][] obj = new Object[QBData.getRowCount()][1];
		for (int i = 1; i <= QBData.getRowCount(); i++) {
			HashMap<String, String> testData = QBData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 7, dataProvider = "QuestionBank")
	public void QuestionBank_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionBank Registration").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionBank Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.masterPlant();
//
//		PrepareQB.QBRegistrationApproval_Configuration(testData);
//
//		epiclogin.navigateTolearnIQPlant();

		PrepareQB.prepare_QuestionBankRegistration_AuditTrails_Yes(testData);

//		Logout.signOutPage();
//
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
//				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
//
//		epiclogin.plant1();
//
//		PrepareQB.prepare_QuestionBankRegistrationApproval_AuditTrails_Yes(testData);
//
//		Logout.signOutPage();

	}

	// Test Method for QuestionPaper Registration
	// -------------------------------------------------------------------------------------------

	ExcelUtilUpdated QPReg = new ExcelUtilUpdated(ExcelPath, "QPReg");

	@DataProvider(name = "QuestionPaperData")
	public Object[][] getQuestionPaperDataRE() throws Exception {
		Object[][] obj = new Object[QPReg.getRowCount()][1];
		for (int i = 1; i <= QPReg.getRowCount(); i++) {
			HashMap<String, String> testData = QPReg.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 8, dataProvider = "QuestionPaperData", enabled = true)
	public void QuestionPaper_Registration(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("QuestionPaper Registration")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("QuestionPaper Registration");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		questionPaper.QPRegistration_AuditTrails_Yes(testData, Constants.sessionInfoOffline, Constants.ITType);

		// Logout.signOutPage();

	}

	// Test Method for
	// RecordAttendance-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordAttendanceData = new ExcelUtilUpdated(ExcelPath, "RecordAttendance");

	@DataProvider(name = "recordAttendance")
	public Object[][] getRecordAttendance() throws Exception {
		Object[][] obj = new Object[RecordAttendanceData.getRowCount()][1];
		for (int i = 1; i <= RecordAttendanceData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordAttendanceData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 9, dataProvider = "recordAttendance", enabled = true)
	public void recordAttendance(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("RecordAttendance").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("RecordAttendance");
		}
//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
//				ConfigsReader.getPropValue("EpicUserPWD"));
//
//		epiclogin.plant1();

		RecordAttendance.recordAttendance_OnlineSession_2AdditionalUsers(testData);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Skipped Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Skipped Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.SkippedEmployeeID, Constants.EMPLOYEESTATUS_AS_SKIPPED,
				Constants.ITType);
		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Absent Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Absent Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.AbsentEmployeeID, Constants.EMPLOYEESTATUS_AS_ABSENT,
				Constants.ITType);

		// Logout.signOutPage();
	}

	// Test Method for
	// RecordMarks-------------------------------------------------------------------------------------------

	ExcelUtilUpdated RecordMarksData = new ExcelUtilUpdated(ExcelPath, "Record Marks");

	@DataProvider(name = "Record Marks")
	public Object[][] get_RecordMarks() throws Exception {
		Object[][] obj = new Object[RecordMarksData.getRowCount()][1];
		for (int i = 1; i <= RecordMarksData.getRowCount(); i++) {
			HashMap<String, String> testData = RecordMarksData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
	}

	@Test(priority = 10, dataProvider = "Record Marks", enabled = true)
	public void recordMarks(HashMap<String, String> testData) {

//		test = extent.createTest("Evaluate Answer Paper").assignAuthor(CM_QuestionPaper.EvaluatorUserID)
//				.assignCategory("Evaluate Answer Paper");

//		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), CM_QuestionPaper.EvaluatorUserID,
//				CM_QuestionPaper.EvaluatorPassword);
//
//		epiclogin.plant1();
		if (isReportedRequired == true) {
			test = extent.createTest("Evaluate Answer Paper").assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Evaluate Answer Paper");
		}
		RecordMarks.RecordMarks(testData);

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of Qualified Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of Qualified Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.QualifiedEmployeeID, Constants.EMPLOYEESTATUS_AS_QUALIFIED,
				Constants.ITType);

		if (isReportedRequired == true) {
			test = extent.createTest("Individual Employee Report of ToBeRetrained Trainee")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Individual Employee Report of ToBeRetrained Trainee");
		}
		IERReport.individualEmployeeReport(CM_CourseSession.ToBeRetrainedEmployeeID,
				Constants.EMPLOYEESTATUS_AS_TO_BE_RETRAINED, Constants.ITType);

		Logout.signOutPage();
	}

	@AfterTest
	public void afterTest() {
		extent.flush();
		MyScreenRecorder.stopRecording();
		driver.quit();
	}
}
