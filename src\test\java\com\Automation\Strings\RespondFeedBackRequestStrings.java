package com.Automation.Strings;

public enum RespondFeedBackRequestStrings {

	FeedbackRequestMenu_DC("Click on 'Feedback Request' submenu."),
	FeedbackRequestMenu_AC("'Feedback Request' screen should be displayed.</div>"
			+ "<div><b>*</b> Screen should be displayed with 'Batch Name ,Course Name,Training Type,Course Session Date' columns.</div>"),

	FeedbackRequestMenu_AR("'Feedback Request' screen is getting displayed.</div>"
			+ "<div><b>*</b> Screen is getting displayed with 'Batch Name ,Course Name,Training Type,Course Session Date' columns.</div>"),
	FeedbackRequestMenu_SS("'Feedback Request'"),

	SearchBy1_DC("Click the above 'Course Name' of the batch for which the above user is selected at record attendence."),
	//
	SearchBy1_SS("'Batch Name'"),

	SearchBy1_AC("Feedback Request' and 'Response to Feedback:<Feedback Template Name>' screen should be displayed.</div>"),
			
	SearchBy1_AR("'Feedback Request' and 'Response to Feedback:<Feedback Template Name>' screen is getting displayed.</div>"),
		
	sendKeys_AC("Entered value should be displayed for the field."),
	sendKeys_AR("Entered value is getting displayed for the field."),
	remarks_DC("Enter the required value in 'Response' field."), remarks_SS("'Response'"),
	
	SearchBy2_DC("Enter the above 'Course Name' of the batch for which the above user is selected at record attendence."),
			SearchBy2_SS("'Course Name'"),

	
	Submit_DC("Click on 'Submit' button."),
	Submit_AC("'Response Submitted' confirmation message should be displayed with 'Done' button.</div>"),
	Submit_AR("'Response Submitted'  confirmation message is getting displayed with 'Done' button.</div>"),
	Submit_SS("'Submit'");
	private final String RespondFeedBackRequestStrings;

	RespondFeedBackRequestStrings(String RespondFeedBackRequestStrings) {

		this.RespondFeedBackRequestStrings = RespondFeedBackRequestStrings;

	}

	public String getRespondFeedBackRequestStrings() {
		return RespondFeedBackRequestStrings;
	}

}
