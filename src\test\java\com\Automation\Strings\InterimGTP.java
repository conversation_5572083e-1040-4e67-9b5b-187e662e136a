package com.Automation.Strings;

public enum InterimGTP {

	GTP_DC("Click on 'Group Training Plan' submenu."),
	GTP_Config_AC("'Group Training Plan Configuration Registration' screen should be displayed."),
	GTP_Config_AR("'Group Training Plan Configuration Registration' screen is getting displayed."),
	GTP_Config_SS("'Group Training Plan Configuration Registration'."),

	Click_DoneatGTPConfig_AC("'Group Training Plan Configuration Registration' screen should be displayed.</div>"),
	Click_DoneatGTPConfig_AR("'Group Training Plan Configuration Registration' screen is getting displayed.</div>"),

	Propopse_IGTP_AC("Interim GTP screen should be displayed.</div>"
			+ "<div><b>* </b>List of master and plant courses for which the Group Training Plan is not yet proposed should be displayed.</div>"),
	Propopse_IGTP_AR("Interim GTP screen is getting displayed.</div>"
			+ "<div><b>* </b>List of master and plant courses for which the Group Training Plan is not yet proposed are getting displayed."),
	Propopse_IGTP_SS("'Interim GTP'."),

	SearchBy_AC(
			"Option to search with 'Top 250 Records, Course Name, Course Code, Training Type' should be available."),
	SearchBy_AR("Option to search with 'Top 250 Records, Course Name, Course Code, Training Type' is available."),
	SearchBy_SS("'Search By'."),

	SearchBy_CourseName_DC("Select 'Course Name'."), SearchBy_CourseName_AC("Selection should be accepted.</div>"),
	SearchBy_CourseName_AR("Selection is getting accepted.</div>"), SearchBy_CourseName_SS("'Course Name'"),

	Like_CourseName_DC("Enter the above registered course name for which the Group Training Plan is not yet proposed.");

	private final String interimGTP;

	InterimGTP(String InterimGTP) {

		this.interimGTP = InterimGTP;

	}

	public String getInterimGTPStrings() {
		return interimGTP;

	}

}
