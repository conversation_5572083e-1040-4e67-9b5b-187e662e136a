package com.Automation.ObjectModelFlows_OQ.System_Manager.SubGroup;

import java.util.HashMap;

import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;
import com.Automation.learniqObjects.CM_VerifyCourseSessionScreen;

public class Subgroup_Registration_ extends OQActionEngine {
	String ExcelPath = "./learnIQTestData/Object_Model_Flows/System_Manager/SubGroup/SubgroupRegistration.xlsx";

	public Subgroup_Registration_() {

		super(ConfigsReader.getPropValue("applicationUrl"));
	}
	// Test Method for subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subgroupconfigData = new ExcelUtilUpdated(ExcelPath, "SubgroupRegandApproval");

	@DataProvider(name = "subgroupConReg")
	public Object[][] getsubgroupconfigData() throws Exception {
		Object[][] obj = new Object[subgroupconfigData.getRowCount()][1];
		for (int i = 1; i <= subgroupconfigData.getRowCount(); i++) {
			HashMap<String, String> testData = subgroupconfigData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 0, dataProvider = "subgroupConReg", enabled = true)
	public void subGroupConfigReg(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Configuration").assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup Configuration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Initiate_SubGroup.SubgroupRegistrationApproval_Configuration(testData);

	}

	// Test Method for subgroup Registration ad Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 1, dataProvider = "subgroupConReg", enabled = true)
	public void subGroupReg(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Registration  with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Registration  with Audit Trails");
		}

		Initiate_SubGroup.subgroup_Registration(testData);
		Initiate_SubGroup.subgroup_Registration_AuditTrails(testData);

	}

	// Test Method for Verification of subgroup modify
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 2, dataProvider = "subgroupConReg", enabled = true)
	public void VerifyModification(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification before  Subgroup Approval")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification before  Subgroup Approval");
		}

		Initiate_SubGroup.subgroup_Modification(testdata);
		Logout.signOutPage();
	}

	// Test Method for Verification of subgroup Approve and Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 3, dataProvider = "subgroupConReg", enabled = true)
	public void subgroupapp(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Approval with Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Approval with Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		Initiate_SubGroup.Subgroup_Approval(testData);

	Initiate_SubGroup.Subgroup_Approval_AuditTrails(testData);

	}

//Test Method for Verification of subgroup modify
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 4, dataProvider = "subgroupConReg", enabled = true)
	public void VerifyModification1(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification after  Subgroup Approval")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification after  Subgroup Approval");
		}

		Initiate_SubGroup.subgroup_Modification_afterapproval(testdata);
		Logout.signOutPage();
	}

	// -----------------------------------------------------------------------------------

	// Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subgroupReturnData = new ExcelUtilUpdated(ExcelPath, "SubRegReturnReniatApp");

	@DataProvider(name = "subReturn")
	public Object[][] getsubgroupRegData() throws Exception {
		Object[][] obj = new Object[subgroupReturnData.getRowCount()][1];
		for (int i = 1; i <= subgroupReturnData.getRowCount(); i++) {
			HashMap<String, String> testData = subgroupReturnData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	// Test Method for Subgroup Registration and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 5, dataProvider = "subReturn", enabled = true)
	public void subGroupReg111(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Registration  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Subgroup  Registration  And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Initiate_SubGroup.subgroup_Registration(testData);
		Initiate_SubGroup.subgroup_Registration_AuditTrails(testData);

		Logout.signOutPage();

	}

	// Test Method for Subgroup Return and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 6, dataProvider = "subReturn", enabled = true)
	public void subGroupReturn(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Return  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Return  And Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		Initiate_SubGroup.Subgroup_Return(testData);
		Initiate_SubGroup.Subgroup_Return_AuditTrails(testData);

		Logout.signOutPage();

	}
	// Test Method for Subgroup Reinitiate and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 7, dataProvider = "subReturn", enabled = true)
	public void subGroupReiniate(HashMap<String, String> testData) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Re-Initiate  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID320"))
					.assignCategory("Subgroup Re-Initiate  And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Initiate_SubGroup.Subgroup_Return_Reinitiate(testData);

		Initiate_SubGroup.Subgroup_Return_Reinitiate_AuditTrails(testData);

	}

	// Test Method for Verification of subgroup modify
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 8, dataProvider = "subReturn", enabled = true)
	public void VerifyModification111111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification before  Subgroup Approval")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification before  Subgroup Approval");
		}

		Initiate_SubGroup.subgroup_Modification(testdata);
		Logout.signOutPage();
	}

	// Test Method for Subgroup Approval and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 9, dataProvider = "subReturn", enabled = true)
	public void subapproval(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Approval And Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Approval And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		Initiate_SubGroup.Subgroup_Reinitiate_Approval(testdata);

		Initiate_SubGroup.Subgroup_Reinitiate_Approval_AuditTrails(testdata);
	}

	// Test Method for Verification of subgroup modify after approval
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 10, dataProvider = "subReturn", enabled = true)
	public void VerifyModification11111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification after  Subgroup Approval")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification after  Subgroup Approval");
		}

		Initiate_SubGroup.subgroup_Modification_afterapproval(testdata);
		Logout.signOutPage();
	}

	// -------------------------------------------------------------------------------------------------

//Test Method for Subgroup Configuration
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subgroupRIData = new ExcelUtilUpdated(ExcelPath, "SubRegRetRITransferReniatApp");

	@DataProvider(name = "subRITransfer")
	public Object[][] getsubgroupRegData11() throws Exception {
		Object[][] obj = new Object[subgroupRIData.getRowCount()][1];
		for (int i = 1; i <= subgroupRIData.getRowCount(); i++) {
			HashMap<String, String> testData = subgroupRIData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	// Test Method for Subgroup Registration and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 11, dataProvider = "subRITransfer", enabled = true)
	public void subGroupReg11(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Registration  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Subgroup  Registration  And Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		Initiate_SubGroup.subgroup_Registration(testData);
		Initiate_SubGroup.subgroup_Registration_AuditTrails(testData);

		Logout.signOutPage();

	}

	// Test Method for Subgroup Return and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 12, dataProvider = "subRITransfer", enabled = true)
	public void subGroupReturn1(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Return  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Return  And Audit Trails");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		Initiate_SubGroup.Subgroup_Return(testData);
		Initiate_SubGroup.Subgroup_Return_AuditTrails(testData);
		Logout.signOutPage();

	}

	// Test Method for Subgroup RI Transfer and AuditTrails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 13, dataProvider = "subRITransfer", enabled = true)
	public void subGroupRITransfer(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  RI Transfer").assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  RI Transfer");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Initiate_SubGroup.Subgroup_RI_Transfer(testData);

		Logout.signOutPage();

	}

	// Test Method for Subgroup Reinitiate with First Initiator
//		// ----------------------------------------------------------------------------------------------------------------------------------------------
	//
	@Test(priority = 14, dataProvider = "subRITransfer", enabled = true)
	public void subGroupRITransferFirstIniator(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Verification of   Subgroup Reinitiate  with first Initiator")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Verification of   Subgroup Reinitiate  with first Initiator");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();

		Initiate_SubGroup.Subgroup_RI_Transfer_Firstinitiator(testData);

		Logout.signOutPage();

	}

	// Login with RI TransferUser and Reiniate Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 15, dataProvider = "subRITransfer", enabled = true)
	public void subGroupRITransferUser(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Reinitiate  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EPIQApprovalID"))
					.assignCategory("Subgroup  Reinitiate  And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), testData.get("RITransferUser"),
				testData.get("RITransferPassword"));

		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIconReinitiateuser();
		Initiate_SubGroup.Subgroup_RITranfer_Reiniate(testData);

		Initiate_SubGroup.Subgroup_Reiniate_RI_AuditTrails(testData);

		Logout.signOutPage();

	}

	// Login with RI TransferUser and Approve Audit Trails
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 16, dataProvider = "subRITransfer", enabled = true)
	public void subapproval11(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Approval And Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Approval And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();

		Initiate_SubGroup.Subgroup_RITransfer_Reinitiate_Approval(testdata);

		Initiate_SubGroup.Subgroup_RITransfer_Reinitiate_Approval_AuditTrails(testdata);
		Logout.signOutPage();
	}

	// Test Method for Verification of subgroup modify after approval
	// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 17, dataProvider = "subRITransfer", enabled = true)
	public void VerifyModification1111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification after  Subgroup Approval")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification after  Subgroup Approval");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.subgroup_Modification_afterapproval(testdata);
		Logout.signOutPage();
	}
//------------------------------------------------------------------------

// Test Method for Subgroup Configuration
// ----------------------------------------------------------------------------------------------------------------------------------------------

	ExcelUtilUpdated subRegDropData = new ExcelUtilUpdated(ExcelPath, "SubRegistrationDrop");

	@DataProvider(name = "subRegDrop")
	public Object[][] getsubgroupRegData1() throws Exception {
		Object[][] obj = new Object[subRegDropData.getRowCount()][1];
		for (int i = 1; i <= subRegDropData.getRowCount(); i++) {
			HashMap<String, String> testData = subRegDropData.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;

	}

	@Test(priority = 18, dataProvider = "subRegDrop", enabled = true)
	public void subGroupConfigReg1(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup Configuration").assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))
					.assignCategory("Subgroup Configuration");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Initiate_SubGroup.SubgroupRegistrationApproval_Configuration(testData);

	}
// Test Method for Subgroup Registration and AuditTrails
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 19, dataProvider = "subRegDrop", enabled = true)
	public void subGroupReg1(HashMap<String, String> testData) {
		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Registration  And Audit Trails")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Subgroup  Registration  And Audit Trails");
		}

		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();
		getInitiatorDetails();

		Initiate_SubGroup.subgroup_Registration(testData);
		Initiate_SubGroup.subgroup_Registration_AuditTrails(testData);

		Logout.signOutPage();

	}

//// Test Method for Verification of subgroup modify
//// ----------------------------------------------------------------------------------------------------------------------------------------------
//
	@Test(priority = 20, dataProvider = "subRegDrop", enabled = true)
	public void VerifyModification111(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification before  Subgroup Drop")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification before  Subgroup Drop");

		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.subgroup_Modification(testdata);
		Logout.signOutPage();
	}

// Test Method for Subgroup Approval and AuditTrails
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 21, dataProvider = "subRegDrop", enabled = true)
	public void subapproval1(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Drop And Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Drop And Audit Trails");
		}

		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"),
				ConfigsReader.getPropValue("EPIQApprovalID"), ConfigsReader.getPropValue("EPIQApprovalPSW"));
		epiclogin.plant1();
		Initiate_SubGroup.getFirstNameLastNameFromProfileIcon();

		Initiate_SubGroup.Subgroup_Drop(testdata);

		Initiate_SubGroup.Subgroup_Drop_AuditTrails(testdata);
		Logout.signOutPage();
	}

// Test Method for Verification of subgroup modify after approval
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 22, dataProvider = "subRegDrop", enabled = true)
	public void VerifyModification11(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Verify SubGroup Modification after  Subgroup Drop")
					.assignAuthor(ConfigsReader.getPropValue("EpicUserID"))
					.assignCategory("Verify SubGroup Modification after  Subgroup Drop");
		}
		epiclogin.loginToApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("EpicUserID"),
				ConfigsReader.getPropValue("EpicUserPWD"));
		epiclogin.plant1();
		Initiate_SubGroup.subgroup_Modification_afterapproval(testdata);

	}

// Test Method for check status Change inactive
// ----------------------------------------------------------------------------------------------------------------------------------------------

	@Test(priority = 23, dataProvider = "subRegDrop", enabled = true)
	public void subgroupstatuschange(HashMap<String, String> testdata) {

		if (isReportedRequired == true) {
			test = extent.createTest("Subgroup  Drop And Audit Trails")

					.assignAuthor(ConfigsReader.getPropValue("EpicUserPWD"))

					.assignCategory("Subgroup  Drop And Audit Trails");
		}

		Initiate_SubGroup.Subgroup_StatusChange_Inactive(testdata);

		Logout.signOutPage();
	}

}