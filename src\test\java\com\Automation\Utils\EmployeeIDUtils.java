package com.Automation.Utils;

import java.util.ArrayList;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.time.Duration;

import com.Automation.learniqBase.TestEngine;

/**
 * Utility class for Employee ID operations
 * Contains methods to retrieve and print Employee IDs from the employee table
 */
public class EmployeeIDUtils {

    private static WebDriver driver = TestEngine.driver;

    /**
     * Method to print Employee IDs one by one from the employee table
     * Uses XPath: //div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div
     */
    public static void printEmployeeIDsOneByOne() {
        try {
            // XPath to get all Employee ID elements
            String employeeIDXPath = "//div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div";
            
            // Find all Employee ID elements
            List<WebElement> employeeIDElements = driver.findElements(By.xpath(employeeIDXPath));
            
            System.out.println("=== Employee IDs Found ===");
            System.out.println("Total Employee IDs: " + employeeIDElements.size());
            System.out.println("==========================");
            
            // Print each Employee ID one by one
            for (int i = 0; i < employeeIDElements.size(); i++) {
                try {
                    WebElement employeeIDElement = employeeIDElements.get(i);
                    
                    // Wait for element to be visible and get text
                    waitForElementVisible(employeeIDElement);
                    String employeeID = employeeIDElement.getText().trim();
                    
                    // Print Employee ID with index
                    System.out.println("Employee ID " + (i + 1) + ": " + employeeID);
                    
                    // Optional: Add a small delay between prints for better readability
                    TimeUtil.shortWait();
                    
                } catch (Exception e) {
                    System.out.println("Error reading Employee ID at index " + (i + 1) + ": " + e.getMessage());
                }
            }
            
            System.out.println("==========================");
            System.out.println("Finished printing all Employee IDs");
            
        } catch (Exception e) {
            System.out.println("Error in printEmployeeIDsOneByOne method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Method to get all Employee IDs as a List of Strings
     * @return List of Employee IDs
     */
    public static List<String> getEmployeeIDsList() {
        List<String> employeeIDs = new ArrayList<>();
        
        try {
            // XPath to get all Employee ID elements
            String employeeIDXPath = "//div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div";
            
            // Find all Employee ID elements
            List<WebElement> employeeIDElements = driver.findElements(By.xpath(employeeIDXPath));
            
            System.out.println("Retrieving " + employeeIDElements.size() + " Employee IDs...");
            
            // Get text from each Employee ID element
            for (int i = 0; i < employeeIDElements.size(); i++) {
                try {
                    WebElement employeeIDElement = employeeIDElements.get(i);
                    waitForElementVisible(employeeIDElement);
                    String employeeID = employeeIDElement.getText().trim();
                    
                    if (!employeeID.isEmpty()) {
                        employeeIDs.add(employeeID);
                        System.out.println("Retrieved Employee ID " + (i + 1) + ": " + employeeID);
                    }
                    
                } catch (Exception e) {
                    System.out.println("Error reading Employee ID at index " + (i + 1) + ": " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("Error in getEmployeeIDsList method: " + e.getMessage());
            e.printStackTrace();
        }
        
        return employeeIDs;
    }

    /**
     * Method to print Employee IDs with custom formatting
     * @param prefix Custom prefix for each Employee ID
     * @param includeIndex Whether to include index numbers
     */
    public static void printEmployeeIDsWithCustomFormat(String prefix, boolean includeIndex) {
        try {
            List<String> employeeIDs = getEmployeeIDsList();
            
            System.out.println("=== Custom Formatted Employee IDs ===");
            System.out.println("Total Employee IDs: " + employeeIDs.size());
            System.out.println("=====================================");
            
            for (int i = 0; i < employeeIDs.size(); i++) {
                String output = prefix;
                
                if (includeIndex) {
                    output += " " + (i + 1) + ": ";
                } else {
                    output += " ";
                }
                
                output += employeeIDs.get(i);
                System.out.println(output);
            }
            
            System.out.println("=====================================");
            
        } catch (Exception e) {
            System.out.println("Error in printEmployeeIDsWithCustomFormat method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Method to search for a specific Employee ID
     * @param targetEmployeeID The Employee ID to search for
     * @return true if found, false otherwise
     */
    public static boolean searchEmployeeID(String targetEmployeeID) {
        try {
            List<String> employeeIDs = getEmployeeIDsList();
            
            System.out.println("Searching for Employee ID: " + targetEmployeeID);
            
            for (int i = 0; i < employeeIDs.size(); i++) {
                if (employeeIDs.get(i).equals(targetEmployeeID)) {
                    System.out.println("Employee ID '" + targetEmployeeID + "' found at position " + (i + 1));
                    return true;
                }
            }
            
            System.out.println("Employee ID '" + targetEmployeeID + "' not found in the list");
            return false;
            
        } catch (Exception e) {
            System.out.println("Error in searchEmployeeID method: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Method to get count of Employee IDs
     * @return Number of Employee IDs found
     */
    public static int getEmployeeIDsCount() {
        try {
            String employeeIDXPath = "//div[text()='Employee Name']/ancestor::tr[2]//table/tbody/tr/td[2]/div/div";
            List<WebElement> employeeIDElements = driver.findElements(By.xpath(employeeIDXPath));
            
            int count = employeeIDElements.size();
            System.out.println("Total Employee IDs count: " + count);
            return count;
            
        } catch (Exception e) {
            System.out.println("Error in getEmployeeIDsCount method: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * Method to print Employee IDs in a specific range
     * @param startIndex Starting index (1-based)
     * @param endIndex Ending index (1-based)
     */
    public static void printEmployeeIDsInRange(int startIndex, int endIndex) {
        try {
            List<String> employeeIDs = getEmployeeIDsList();
            
            // Convert to 0-based indexing
            int start = startIndex - 1;
            int end = endIndex - 1;
            
            // Validate range
            if (start < 0 || end >= employeeIDs.size() || start > end) {
                System.out.println("Invalid range. Available Employee IDs: 1 to " + employeeIDs.size());
                return;
            }
            
            System.out.println("=== Employee IDs (Range: " + startIndex + " to " + endIndex + ") ===");
            
            for (int i = start; i <= end; i++) {
                System.out.println("Employee ID " + (i + 1) + ": " + employeeIDs.get(i));
            }
            
            System.out.println("=================================================");
            
        } catch (Exception e) {
            System.out.println("Error in printEmployeeIDsInRange method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Helper method to wait for element visibility
     * @param element WebElement to wait for
     */
    private static void waitForElementVisible(WebElement element) {
        try {
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
            wait.until(ExpectedConditions.visibilityOf(element));
        } catch (Exception e) {
            System.out.println("Element visibility timeout: " + e.getMessage());
        }
    }
}
