package com.Automation.learniqObjects;

import java.util.HashMap;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

import com.Automation.learniqBase.*;
import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.CourseStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;

public class SetCentralConfiguration extends OQActionEngine {

	public static String CentralRetakeAssessment = "";

	public static String getCetralRetakeAssessment() {
		return CentralRetakeAssessment;
	}

	public static void setCetralRetakeAssessment(String CentralRetakeAssessment) {
		CentralRetakeAssessment = CentralRetakeAssessment;
	}

	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//*[@id='MenuModule_TMS']/li[2]")
	WebElement sysMangerClick;
	@FindBy(xpath = "//ul[@id='TMS_System Manager']/li[2]")
	WebElement adminClick;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Admin & Security']/li/a[contains(text(), 'Initiate')]")
	WebElement initiate;
	@FindBy(xpath = "//ul[@id='TMS_System Manager_Admin & Security']/li/a[contains(text(), 'Central Configuration Audit Trails')]")
	WebElement setCentralConfigurationAuditTrails;
	@FindBy(xpath = "//a[@id='TMS_System Manager_Admin & Security_learnIQ953_learnIQ953']")
	WebElement CentralConfigurationAuditTrails;
	@FindBy(xpath = "//*[@id=\"TMS_System Manager_Admin & Security\"]/li[1]/ul/li[3]")
	WebElement setCentralclick;
	@FindBy(xpath = "//input[@id='CentralConfiguration_OJTRequired'][@value='1']")
	WebElement ojtSelect;
	@FindBy(id = "CentralConfiguration_Remarks")
	WebElement centralRemarks;
	@FindBy(id = "btnSubmit")
	WebElement submitClick;
	@FindBy(id = "btnSubmit_1")
	WebElement submitClick1;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-title']")
	WebElement highLightScreenTitle;
	@FindBy(id = "txtESignPassword")
	WebElement eSign1;
	@FindBy(id = "Submit_Esign")
	WebElement eSignProceed;
	@FindBy(id = "btnSubmit")
	WebElement submit;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobRspAccRequired\"]")
	WebElement jobResponsibilityAcceptanceNO;
	@FindBy(xpath = "//input[@value=\"0\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceAsNO;

	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[5]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]")
	WebElement jobResponsibilityAcceptanceNOAuditTrails;
	@FindBy(xpath = "//div[5]//div[2]//div[2]//div[1]//div[1]//div[1]//div[1]//span[1]")
	WebElement authorizedDeputyAcceptanceAsNOAuditTrails;

	@FindBy(xpath = "//label[text()='User Acceptance']/parent::div/div/span")
	WebElement jobResponsibilityAcceptanceYesAuditTrails;
	@FindBy(xpath = "//label[text()='Authorized Deputy Acceptance']/parent::div/div/span")
	WebElement authorizedDeputyAcceptanceAsYesAuditTrails;
	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]")
	WebElement SystemManagerAudittrails;

	@FindBy(xpath = "//label[text()='On Job Training Required']")
	WebElement labelOJTRequired;

	@FindBy(xpath = "//label[@for='CentralConfiguration_OJTRequired']")
	WebElement labelOJTRequiredLable;

	@FindBy(xpath = "//label[text()='On Job Training Required']//following-sibling::div//label[2]")
	WebElement labelOJTYes;
	@FindBy(xpath = "//label[contains(text(),'User Acceptance')]")
	WebElement userAcceptanceLabel;
	@FindBy(xpath = "//label[contains(text(),'Authorized Deputy Acceptance')]")
	WebElement authorizedDeputyAcceptanceLabel;
	@FindBy(xpath = "//label[contains(text(),'Authorized Deputy Acceptance')]//following-sibling::div//label[1]")
	WebElement authorizedDeputyAcceptanceNoLabel;
	@FindBy(xpath = "//label[contains(text(),'User Acceptance')]//following-sibling::div//label[1]")
	WebElement userAcceptanceNoLabel;
	@FindBy(id = "Submit_Esign")
	WebElement proceebbtn;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//span[text()='Record Attendance ']")
	WebElement RecordAttendance;
	@FindBy(xpath = "//label[text()='Biometric Required']")
	WebElement BiometricRequiredLabel;
	@FindBy(xpath = "//*[@id='CentralConfiguration_BioMetricRequired' and @value='0']")
	WebElement RecordAttedanceNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_JobRspAccRequired' and @value='0']")
	WebElement userAcceptanceasNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_JobDepAccRequired' and @value='0']")
	WebElement authorizedDeputyasNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_BioMetricRequired' and @value='1']")
	WebElement RecordAttedanceOpt;
	@FindBy(xpath = "//span[text()='Target Date for Question Paper Response  ']")
	WebElement TargetDateforQP;
	@FindBy(xpath = "//label[text()='Target Date for Question Paper Response']")
	WebElement TargetDateforQPLabel;
	@FindBy(xpath = "//*[@id='CentralConfiguration_QnpTimeLimitRequired' and @value='0']")
	WebElement TargetDateforQPNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_QnpTimeLimitRequired' and @value='1']")
	WebElement TargetDateforQPYes;
	@FindBy(xpath = "//span[text()='Retake Assessment ']")
	WebElement RetakeAssessmentReq;
	@FindBy(xpath = "//label[text()='Retake Assessment Required']")
	WebElement RetakeAssessmentReqLabel;
	@FindBy(xpath = "//*[@id='CentralConfiguration_RetakeRequired' and @value ='0']")
	WebElement RetakeAssessmentNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_RetakeRequired' and @value ='1']")
	WebElement RetakeAssessmentYes;
	@FindBy(xpath = "//span[text()='Jumbled Questions']")
	WebElement JumbledQuestionsEle;
	@FindBy(xpath = "//label[text()='Jumbled Questions Required']")
	WebElement JumbledQuestionsLabel;
	@FindBy(xpath = "//*[@id='CentralConfiguration_JumblingRequired' and @value ='0']")
	WebElement JumbledQuestionsNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_JumblingRequired' and @value ='1']")
	WebElement JumbledQuestionsYes;
	@FindBy(xpath = "//span[text()='Pending Evaluation Days ']")
	WebElement PendingEvalDays;
	@FindBy(xpath = "//label[text()='Evaluation Days Period']")
	WebElement EvaluationDaysPeriod;
	@FindBy(xpath = "//label[text()='Evaluation Days Period']//following-sibling::input")
	WebElement EvaluationDaysPeriodValue;
	@FindBy(xpath = "//span[text()='Pending Answerpaper Submission Days ']")
	WebElement PendingAsnwerPaper;
	@FindBy(xpath = "//label[text()='Answerpaper Submission Days Period']")
	WebElement AsnwerPaperSubDays;
	@FindBy(xpath = "//label[text()='Answerpaper Submission Days Period']//following-sibling::input")
	WebElement AsnwerPaperSubDaysVlue;
	@FindBy(xpath = "//span[text()='Assignment(s) For Course Sessions']")
	WebElement Assignments;
	@FindBy(xpath = "//label[text()='Assignment Required']")
	WebElement AssignmentsReq;
	@FindBy(xpath = "//*[@id='CentralConfiguration_UsrAssignmntReq' and @value='0']")
	WebElement AssignmentsNo;
	@FindBy(xpath = "//*[@id='CentralConfiguration_UsrAssignmntReq' and @value='1']")
	WebElement AssignmentsYes;
	@FindBy(xpath = "//button[text()='Proceed']")
	WebElement ProceedAudit;
	@FindBy(xpath = "//span[text()='System Manager']")
	WebElement menu1;
	@FindBy(xpath = "//a[text()='Admin & Security']")
	WebElement adminsecurity;
	@FindBy(xpath = "//a[@id='CENINIT']//following-sibling::span")
	WebElement centralconfiguration;
	@FindBy(xpath = "(//a[text()='Initiate'])[2]")
	WebElement initiate1;
	@FindBy(xpath = "//a[@id='CENINIT']//following-sibling::span")
	WebElement temporaypassword;
	@FindBy(xpath = "//input[@id='ToUserRbl_0'and @value='1']")
	WebElement manual;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//input[@id='ToUserRbl_1']")
	WebElement email;
	@FindBy(xpath = "//div[@class='sub-page-layout-header-main']")
	WebElement highlightAuditScreenWindowTitle;
	@FindBy(xpath = "//textarea[@id='CentralConfiguration_UserConsent']")
	WebElement manual1;
	@FindBy(xpath = "//textarea[@id='CentralConfiguration_Remarks']")
	WebElement manual2;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div//span")
	WebElement modificationLastTab;
	@FindBy(xpath = "//button[text()='Proceed']/parent::div/preceding-sibling::div[1]/div/div/div[last()]/div")
	WebElement ModTab;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Revision No.:')]")
	WebElement revisionNoTitleCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'0 -')]")
	WebElement revisionNoValueCompareTRN;
	@FindBy(xpath = "//div[@data-target='#CompareTRN']//span[contains(text(),'Modification')]")
	WebElement ModNumber;
	WebElement configurationUniqueCode;
	@FindBy(id = "ConfigAuditV1_ConfigCode")
	WebElement auditTrailUniqueCode;
	@FindBy(xpath = "//span[@class='popup-close-button']")
	WebElement auditClose;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Initiation']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Initiation_Reg_Value;
	@FindBy(xpath = "//h6[text()='Call E-sign At: Approval']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement auditEsignAt_Approval_Reg_Value;
	@FindBy(xpath = "//h6[text()='No. of Approvals Required:']//parent::div//following-sibling::div[1]//label[contains(text(),'Registration')]//following-sibling::span")
	WebElement noOfApprovalsReqAt_Registration_Value;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated By')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedBy;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Initiated On')]/following-sibling::span")
	WebElement configureAuditTrailsInitiatedOn;
	@FindBy(xpath = "//div[@id='CompareTRN']//label[contains(text(),'Remark(s) / Reason(s)')]/following-sibling::span")
	WebElement configureAuditTrailsRemarks;
	@FindBy(xpath = "//span[normalize-space()='Configured']")
	WebElement configureAuditTrailsRemarks1;

	@FindBy(xpath = "/html[1]/body[1]/form[1]/section[1]/section[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[3]")
	WebElement auditTrailPageColumn4;
	@FindBy(xpath = "//*[@id='CentralConfiguration_JobRspAccRequired' and @value='1']")
	WebElement userAcceptanceasYes;
	@FindBy(xpath = "//input[@value=\"1\"][@id=\"CentralConfiguration_JobDepAccRequired\"]")
	WebElement authorizedDeputyAcceptanceAsYes;
	@FindBy(xpath = "//input[@value='0'][@id='CentralConfiguration_JobDepAccRequired']")
	WebElement authorizedDeputyAcceptanceAsNo;

	@FindBy(xpath = "//input[@id='CentralConfiguration_OJTRequired'][@value='0']")
	WebElement ojtSelectNo;

	@FindBy(xpath = "//input[@id='CentralConfiguration_OJTRequired'][@value='0']//parent::label")
	WebElement ojtSelectLabelNo;
	@FindBy(xpath = "//span[normalize-space()='On Job Training']")
	WebElement ojtSelectlabel;

	@FindBy(id = "txtESignPassword")
	WebElement esign_psw;

	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;

	/**
	 * This method is for Set Central Configuration
	 *
	 */
	public void set_OJT_JobResponsibilty_CentralConfig(String ConfigureRemarks, String eSign) {

		// TimeUtil.longwait();

		// driver.navigate().refresh();
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(initiate, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());

		click2(setCentralclick, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

		switchToBodyFrame(driver);
		TimeUtil.shortWait();

		highLightElement(driver, labelOJTRequiredLable, "OJT", test);
		// highlightEle2(labelOJTRequired);
		boolean status = ojtSelect.isSelected();
		if (status == true) {
		} else {
			click2(ojtSelect, CommonStrings.Select_OJT_YES_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_OJT_YES_SS.getCommonStrings());

		}

		TimeUtil.shortWait();
		// highlightEle2(userAcceptanceLabel);
		TimeUtil.shortWait();
		boolean UserAcceptancestatus = jobResponsibilityAcceptanceNO.isSelected();
		if (UserAcceptancestatus == true) {
			click2(jobResponsibilityAcceptanceNO, CommonStrings.Select_UserAcceptance_NO_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_UserAcceptance_NO_SS.getCommonStrings());
		} else {

		}

		// highLightElement(driver, authorizedDeputyAcceptanceLabel, "Authorized Deputy
		// Acceptance", test);

		boolean ADstatus = authorizedDeputyAcceptanceNoLabel.isSelected();
		if (ADstatus == true) {

			click2(authorizedDeputyAcceptanceNoLabel, CommonStrings.Select_AD_NO_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_AD_NO_SS.getCommonStrings());

		} else {

		}

		sendKeys2(centralRemarks, CommonStrings.Remarks_DC.getCommonStrings(), ConfigureRemarks,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		// Esign(eSign);
		sendKeys2(eSign1, CommonStrings.Submit_Button_DC.getCommonStrings(), eSign,
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				CommonStrings.Esign_Proceed_AC.getCommonStrings(), CommonStrings.Esign_Proceed_AR.getCommonStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);
	}

//Classroom Type sessions Configurations 	

	public void classroomTypeSessionConfigurations(String BiometricRequired, String TargetDate, String TargetDateValue,
			String RetakeAssessment, String RetakeAttempts, String RetakeRelease, String JumbledQuestions,
			String EvaluationDays, String AnswerpaperSubmission, String AssignmentsRequired, String ConfigureRemarks,
			String eSign) {

		setCetralRetakeAssessment(CentralRetakeAssessment = RetakeAssessment);

		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		// js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(initiate, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());

		click2(setCentralclick, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		js1.executeScript("arguments[0].scrollIntoView();", RecordAttendance);
		highLightElement(driver, RecordAttendance, "Confirmation Message", test);
		highLightElement(driver, BiometricRequiredLabel, "Confirmation Message", test);
		if (BiometricRequired.equals("No")) {
			boolean status = RecordAttedanceNo.isSelected();
			if (status == true) {
			} else {
				click2(RecordAttedanceNo, CommonStrings.Select_Biometric_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.BiometricRequired_SS.getCommonStrings());

			}
			js1.executeScript("arguments[0].scrollIntoView();", TargetDateforQP);
			highLightElement(driver, TargetDateforQP, "Confirmation Message", test);
			highLightElement(driver, TargetDateforQPLabel, "Confirmation Message", test);

		}
		if (TargetDate.equals("No")) {

			boolean status = TargetDateforQPNo.isSelected();
			if (status == true) {
			} else {
				click2(TargetDateforQPNo, CommonStrings.Select_TargetDate_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_TargetDate_SS.getCommonStrings());

			}

		}

		highLightElement(driver, RetakeAssessmentReq, "Confirmation Message", test);
		highLightElement(driver, RetakeAssessmentReqLabel, "Confirmation Message", test);

		if (RetakeAssessment.equals("No")) {

			boolean status = RetakeAssessmentNo.isSelected();
			if (status == true) {
			} else {
				click2(RetakeAssessmentNo, CommonStrings.Select_RetakeAssessment_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_RetakeAssessment_SS.getCommonStrings());

			}

		}
		js1.executeScript("arguments[0].scrollIntoView();", JumbledQuestionsEle);
		highLightElement(driver, JumbledQuestionsEle, "Confirmation Message", test);
		highLightElement(driver, JumbledQuestionsLabel, "Confirmation Message", test);

		if (JumbledQuestions.equals("No")) {

			boolean status = JumbledQuestionsNo.isSelected();
			if (status == true) {
			} else {
				click2(JumbledQuestionsNo, CommonStrings.Select_JumbledQuestions_DC.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_JumbledQuestions_SS.getCommonStrings());

			}

		}
		js1.executeScript("arguments[0].scrollIntoView();", PendingEvalDays);
		highLightElement(driver, PendingEvalDays, "Confirmation Message", test);
		highLightElement(driver, EvaluationDaysPeriod, "Confirmation Message", test);
		sendKeys2(EvaluationDaysPeriodValue, CommonStrings.Enter_PendingEvalZero_DC.getCommonStrings(), EvaluationDays,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Enter_PendingEval_SS.getCommonStrings());

		highLightElement(driver, PendingAsnwerPaper, "Confirmation Message", test);
		highLightElement(driver, AsnwerPaperSubDays, "Confirmation Message", test);
		sendKeys2(AsnwerPaperSubDaysVlue, CommonStrings.Enter_AnswerPaperSubZero_DC.getCommonStrings(),
				AnswerpaperSubmission, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(), CommonStrings.Enter_AnswerPaperSub_SS.getCommonStrings());

		js1.executeScript("arguments[0].scrollIntoView();", Assignments);
		highLightElement(driver, Assignments, "Confirmation Message", test);
		highLightElement(driver, AssignmentsReq, "Confirmation Message", test);

		if (AssignmentsRequired.equals("No")) {

			boolean status = AssignmentsNo.isSelected();
			if (status == true) {
			} else {
				click2(AssignmentsNo, CommonStrings.Select_AssignmentsReqNo_DC.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_AssignmentsReq_SS.getCommonStrings());

			}

		}
		js1.executeScript("arguments[0].scrollIntoView();", centralRemarks);
		sendKeys2(centralRemarks, CommonStrings.Remarks_DC.getCommonStrings(), ConfigureRemarks,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		js1.executeScript("arguments[0].scrollIntoView();", submitClick);
		click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		Esign(eSign);
		js1.executeScript("arguments[0].scrollIntoView();", proceebbtn);
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				CommonStrings.Esign_Proceed_AC.getCommonStrings(), CommonStrings.Esign_Proceed_AR.getCommonStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);

	}

	public void set_TemporayPassword_CentralConfig(String temporarypassword, String UserConsent, String remarks) {

		waitForElementVisibile(menu1);
		click2(menu1, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(adminsecurity, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(initiate1, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(centralconfiguration, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
		TimeUtil.shortWait();

//	if (temporarypassword.equals("Manual")) {
//		click2(manual, CourseStrings.SubgroupNameLike_DC.getCourseStrings(), 
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
//	}
//
//	if (temporarypassword.equals("Through mail")) {
//		click2(email, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
//	}

//	if (temporarypassword.equals("Manual")) {
//		click2(manual, CourseStrings.SubgroupNameLike_DC.getCourseStrings(), 
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
//	}
//
//	if (temporarypassword.equals("Through mail")) {
//		click2(email, CourseStrings.SubgroupNameLike_DC.getCourseStrings(),
//				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
//				CourseStrings.SubgroupNameLike_SS.getCourseStrings());
//	}
//	

		if (temporarypassword.equals("Manual")) {

			boolean status = manual.isSelected();
			if (status == true) {
			} else {
				click2(manual, CommonStrings.Select_AssignmentsReqNo_DC.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_AssignmentsReq_SS.getCommonStrings());

			}

			sendKeys2(manual1, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(), UserConsent,
					CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
					CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
					CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

//	manual1.clear();
//	sendKeys2(manual1, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),UserConsent,
//			CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
//			CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
//			CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

			// SelectRadioBtnAndCheckbox(driver, manual, "manual");
			TimeUtil.shortWait();

			sendKeys2(manual2, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(), remarks,
					CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
					CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
					CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());
			click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
					CommonStrings.Esign_Default_AC.getCommonStrings(),
					CommonStrings.Esign_Default_AR.getCommonStrings(),
					CommonStrings.SubmitwithEsign_SS.getCommonStrings());
			TimeUtil.shortWait();
		}
	}

	public void jobResponsibilityCentralConfigurations(String UserAcceptance, String AuthorizedDeputyAcceptance,
			String ConfigureRemarks, String eSign) {

		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		// js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);

		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(initiate, CommonStrings.SM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());

		click2(setCentralclick, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Topic Approval Intiation", test);
		js1.executeScript("arguments[0].scrollIntoView();", userAcceptanceLabel);
		highLightElement(driver, userAcceptanceLabel, "Confirmation Message", test);

		if (UserAcceptance.equals("Yes")) {
			boolean status = userAcceptanceasNo.isSelected();
			if (status == true) {
				click2(userAcceptanceasYes, CommonStrings.Select_Biometric_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.BiometricRequired_SS.getCommonStrings());

			} else {

			}
			js1.executeScript("arguments[0].scrollIntoView();", authorizedDeputyAcceptanceLabel);

			highLightElement(driver, authorizedDeputyAcceptanceLabel, "Confirmation Message", test);

		}
		if (AuthorizedDeputyAcceptance.equals("Yes")) {

			boolean status = authorizedDeputyAcceptanceAsNO.isSelected();
			if (status == true) {
				click2(authorizedDeputyAcceptanceAsYes, CommonStrings.Select_TargetDate_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_TargetDate_SS.getCommonStrings());
			} else {

			}

		}

		js1.executeScript("arguments[0].scrollIntoView();", centralRemarks);
		sendKeys2(centralRemarks, CommonStrings.Remarks_DC.getCommonStrings(), ConfigureRemarks,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		js1.executeScript("arguments[0].scrollIntoView();", submitClick);
		click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		Esign(eSign);
		js1.executeScript("arguments[0].scrollIntoView();", proceebbtn);
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				CommonStrings.Esign_Proceed_AC.getCommonStrings(), CommonStrings.Esign_Proceed_AR.getCommonStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);

		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(setCentralConfigurationAuditTrails, CommonStrings.Centalconfigurationaudittrail_DC.getCommonStrings(),
				CommonStrings.Centalconfigurationaudittrail_AC.getCommonStrings(),
				CommonStrings.Centalconfigurationaudittrail_AR.getCommonStrings(),
				CommonStrings.Centalconfigurationaudittrail_SS.getCommonStrings());

		click2(CentralConfigurationAuditTrails,
				CommonStrings.Click_CentralConfigurationAuditTrails_DC.getCommonStrings(),
				CommonStrings.Click_CentralConfigurationAuditTrails_AC.getCommonStrings(),
				CommonStrings.Click_CentralConfigurationAuditTrails_AR.getCommonStrings(),
				CommonStrings.Click_CentralConfigurationAuditTrails_SS.getCommonStrings());

		switchToBodyFrame(driver);
		String modifyNumber = auditTrailPageColumn4.getText();
		String auditRevisionValue = modifyNumber + " - Modification";
		click2(SystemManagerAudittrails, CommonStrings.Click_Centralfor_ConfigAuditTrails_DC.getCommonStrings(),
				CommonStrings.Click_Centralfor_ConfigAuditTrails_AC.getCommonStrings(),
				CommonStrings.Click_Centralfor_ConfigAuditTrails_AR.getCommonStrings(),
				CommonStrings.Click_Centralfor_ConfigAuditTrails_SS.getCommonStrings());
		driver.switchTo().frame(0);
		js1.executeScript("arguments[0].scrollIntoView();", modificationLastTab);
		String revisionTitle = Constants.REVISIONNUM_TITLE;
		String modifyLastTabRevisionno = revisionTitle + " :" + modifyNumber;
		verifyExactCaption(modificationLastTab, modifyLastTabRevisionno, "Modify LastTab Revision No");
		clickAndWaitforNextElement(ModTab, ProceedAudit, CommonStrings.Click_LastesModTab_DC.getCommonStrings(),
				CommonStrings.Click_LastesModTab_AC.getCommonStrings(),
				CommonStrings.Click_LastesModTab_AR.getCommonStrings(),
				CommonStrings.Click_LastesModTab_SS.getCommonStrings());
		click2(ProceedAudit, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				CommonStrings.Click_CentralConfig_Proceed_AC.getCommonStrings(),
				CommonStrings.Click_CentralConfig_Proceed_AR.getCommonStrings(),
				CommonStrings.Click_CentralConfig_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, highlightAuditScreenWindowTitle, "Configuration screen", test);
		TimeUtil.shortWait();
		verifyExactCaption(revisionNoTitleCompareTRN, revisionTitle, "Revision No.:");
		verifyExactCaption(ModNumber, auditRevisionValue, "Modification");
		js1.executeScript("arguments[0].scrollIntoView();", jobResponsibilityAcceptanceYesAuditTrails);
		verifyExactCaption(jobResponsibilityAcceptanceYesAuditTrails, UserAcceptance, "Modification");
		verifyExactCaption(authorizedDeputyAcceptanceAsYesAuditTrails, AuthorizedDeputyAcceptance, "Modification");
		verifyExactCaption(configureAuditTrailsRemarks1, ConfigureRemarks, "Remarks");
		click2(auditClose, CommonStrings.Close_AuditTrails_DC.getCommonStrings(),
				CommonStrings.Close_CentralConfigurationAuditTrails_AC.getCommonStrings(),
				CommonStrings.Close_CentralConfigurationAuditTrails_AR.getCommonStrings(),
				CommonStrings.Close_Icon_SS.getCommonStrings());
		TimeUtil.shortWait();
		switchToDefaultContent(driver);

	}

	/**
	 * This method is for Set Central Configuration
	 *
	 */
	public void set_OJT_JobResponsibilty_CentralConfig_No(String ConfigureRemarks, String eSign) {

		TimeUtil.longwait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());

		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(initiate, CommonStrings.CM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());

		click2(setCentralclick, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());

		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		// highlightEle2(labelOJTRequired);
		highLightElement(driver, ojtSelectlabel, "OJT No", test);
		boolean status = ojtSelect.isSelected();
		if (status == true) {

			highLightElement(driver, ojtSelectLabelNo, "OJT No", test);

			click2(ojtSelectNo, CommonStrings.Select_OJT_YES_DC.getCommonStrings(),
					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
					CommonStrings.Select_OJT_YES_SS.getCommonStrings());

		} else {

		}

//		TimeUtil.shortWait();
//		// highlightEle2(userAcceptanceLabel);
//		TimeUtil.shortWait();
//		boolean UserAcceptancestatus = jobResponsibilityAcceptanceNO.isSelected();
//		if (UserAcceptancestatus == true) {
//		} else {
//			click2(jobResponsibilityAcceptanceNO, CommonStrings.Select_UserAcceptance_NO_DC.getCommonStrings(),
//					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//					CommonStrings.Select_UserAcceptance_NO_SS.getCommonStrings());
//
//		}
//
//		// highLightElement(driver, authorizedDeputyAcceptanceLabel, "Authorized Deputy
//		// Acceptance", test);
//
//		boolean ADstatus = authorizedDeputyAcceptanceNoLabel.isSelected();
//		if (ADstatus == true) {
//		} else {
//			click2(authorizedDeputyAcceptanceNoLabel, CommonStrings.Select_AD_NO_DC.getCommonStrings(),
//					CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
//					CommonStrings.Select_AD_NO_SS.getCommonStrings());
//
//		}

		sendKeys2(centralRemarks, CommonStrings.Remarks_DC.getCommonStrings(), ConfigureRemarks,
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());

		click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();

		TimeUtil.shortWait();
		// Esign(eSign);
		sendKeys2(eSign1, CommonStrings.Submit_Button_DC.getCommonStrings(), eSign,
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceebbtn, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				CommonStrings.Esign_Proceed_AC.getCommonStrings(), CommonStrings.Esign_Proceed_AR.getCommonStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		TimeUtil.shortWait();
		highLightElement(driver, confirmationText, "Confirmation Message", test);
		switchToDefaultContent(driver);
	}
	public void jobResponsibilityCentralConfigurations(HashMap<String, String> testData) {
		 
		JavascriptExecutor js1 = (JavascriptExecutor) driver;
		// js1.executeScript("arguments[0].scrollIntoView();", auditTrailsMenu);
 
		TimeUtil.mediumWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.Default_LearnIQ_ICON_AR.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
 
		click2(sysMangerClick, CommonStrings.Click_SYSM_DC.getCommonStrings(),
				CommonStrings.Click_SYSM_AC.getCommonStrings(), CommonStrings.Click_SYSM_AR.getCommonStrings(),
				CommonStrings.Click_SYSM_SS.getCommonStrings());
		click2(adminClick, CommonStrings.Click_AdminSec_DC.getCommonStrings(),
				CommonStrings.Click_AdminSec_AC.getCommonStrings(), CommonStrings.Click_AdminSec_AR.getCommonStrings(),
				CommonStrings.Click_AdminSec_SS.getCommonStrings());
		click2(initiate, CommonStrings.SM_InitiateMenu_DC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AC.getCommonStrings(),
				CommonStrings.Click_DefaultInitiate_AR.getCommonStrings(),
				CommonStrings.CM_InitiateMenu_SS.getCommonStrings());
 
		click2(setCentralclick, CommonStrings.Click_SetCentralConfig_DC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AC.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_AR.getCommonStrings(),
				CommonStrings.Click_SetCentralConfig_SS.getCommonStrings());
 
		switchToBodyFrame(driver);
		TimeUtil.shortWait();
		highLightElement(driver, highLightScreenTitle, "Topic Approval Intiation", test);
		js1.executeScript("arguments[0].scrollIntoView();", userAcceptanceLabel);
		highLightElement(driver, userAcceptanceLabel, "Confirmation Message", test);
 
		if (testData.get("UserAcceptance").equals("Yes")) {
				click2(userAcceptanceasYes, CommonStrings.Select_Biometric_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.BiometricRequired_SS.getCommonStrings());
 
			} else if(testData.get("UserAcceptance").equals("No")){
				
				click2(userAcceptanceasNo, CommonStrings.Select_Biometric_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.BiometricRequired_SS.getCommonStrings());
			}
			js1.executeScript("arguments[0].scrollIntoView();", authorizedDeputyAcceptanceLabel);
 
			highLightElement(driver, authorizedDeputyAcceptanceLabel, "Confirmation Message", test);
 
		
		if (testData.get("AuthorizedDeputyAcceptance").equals("Yes")) {
				click2(authorizedDeputyAcceptanceAsYes, CommonStrings.Select_TargetDate_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_TargetDate_SS.getCommonStrings());
			} else if(testData.get("AuthorizedDeputyAcceptance").equals("No")){
				click2(authorizedDeputyAcceptanceAsNo, CommonStrings.Select_TargetDate_No.getCommonStrings(),
						CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
						CommonStrings.Select_TargetDate_SS.getCommonStrings());
			}
 
 
		js1.executeScript("arguments[0].scrollIntoView();", centralRemarks);
		sendKeys2(centralRemarks, CommonStrings.Remarks_DC.getCommonStrings(), testData.get("ConfigureRemarks"),
				CommonStrings.sendKeys_AC.getCommonStrings(), CommonStrings.sendKeys_AR.getCommonStrings(),
				CommonStrings.Remarks_SS.getCommonStrings());
		js1.executeScript("arguments[0].scrollIntoView();", submitClick);
		click2(submitClick, CommonStrings.Submit_Button_DC.getCommonStrings(),
				CommonStrings.Esign_Default_AC.getCommonStrings(), CommonStrings.Esign_Default_AR.getCommonStrings(),
				CommonStrings.SubmitwithEsign_SS.getCommonStrings());
		TimeUtil.shortWait();
 
		TimeUtil.shortWait();
		try {
			if (esign_psw.getDomAttribute("id").equals("txtESignPassword")) {
				sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(),
						ConfigsReader.getPropValue("EPIQDefaultIPSW"), CommonStrings.Password_AC.getCommonStrings(),
						CommonStrings.Password_AR.getCommonStrings(), CommonStrings.Password_SS.getCommonStrings());
				// TimeUtil.shortWait();
				click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
						CourseStrings.Esign_ProceedCourse_AC.getCourseStrings(),
						CourseStrings.Esign_ProceedCourse_AR.getCourseStrings(),
						CommonStrings.Esign_Proceed_SS.getCommonStrings());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		switchToDefaultContent(driver);
 
	}
 
	}
 