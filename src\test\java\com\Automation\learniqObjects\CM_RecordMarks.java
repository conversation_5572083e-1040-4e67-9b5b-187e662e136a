package com.Automation.learniqObjects;

import java.util.HashMap;
import java.util.Properties;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import com.Automation.Strings.CommonStrings;
import com.Automation.Strings.RecordMarksStrings;
import com.Automation.Strings.TopicStrings;
import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.TimeUtil;
import com.Automation.learniqBase.OQActionEngine;

public class CM_RecordMarks extends OQActionEngine {
	Properties prop;
	@FindBy(xpath = "//a[@class='caliber-product-tms product-icon1']")
	WebElement menu;
	@FindBy(xpath = "//ul[1]/li[2]/a[contains(text(),'Course Manager')]")
	WebElement courseManagerMenu;
	@FindBy(xpath = "//ul[@id='TMS_Course Manager']/li[7]//a[contains(@class,'sub-menu')][contains(text(),'Record')]")
	WebElement recordMenu;
	@FindBy(xpath = "//a[@id='TMS_Course Manager_Record_MEN71']")
	WebElement marksMenu;
	@FindBy(xpath = "//i[@class='ft-filter']")
	WebElement searchFilter;
	@FindBy(xpath = "//span[@id='select2-SearchType-container']")
	WebElement searchByNew;
	@FindBy(id = "BatchDesc")
	WebElement courseNameField1;
	@FindBy(id = "displayBtn")
	WebElement applyNew;
	@FindBy(xpath = "//table[@id='ListTab']/tbody/tr[1]/td[1]")
	WebElement displayedRecord;
	@FindBy(xpath = "//input[@name='offlineuserslist[0].AqcuiredMaks']")
	WebElement aquiredMarks1;
	@FindBy(id = "btnSubmit")
	WebElement submitBtn;
	@FindBy(id = "txtESignPassword")
	WebElement eSign1;
	@FindBy(id = "Submit_Esign")
	WebElement eSignProceed;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[2]")
	WebElement searchByUniqueCode1;
	@FindBy(id = "cfnMsg_Next")
	WebElement confirmationDone;
	@FindBy(xpath = "//ul[@id='select2-SearchType-results']//li[text()='Batch Name']")
	WebElement searchByBatchName;
	@FindBy(id = "BatchDesc")
	WebElement batchNameLike;
	@FindBy(xpath = "//*[@id='ListTab']//tbody/tr/td[7]")
	WebElement result;
	@FindBy(xpath = "//span[@class='confirmation_text']")
	WebElement confirmationText;
	@FindBy(xpath = "//input[@id='txtESignPassword']")
	WebElement esign_psw;
	@FindBy(xpath = "//button[@id='Submit_Esign']")
	WebElement proceed;
	@FindBy(xpath = "//span[contains(text(),'To Be Re-trained')]")
	WebElement resultretrained;
	@FindBy(xpath = "//input[@name='offlineuserslist[1].AqcuiredMaks']")
	WebElement aquiredMarks2;
	@FindBy(xpath = "RecordMarksComments")
	WebElement remarks;
	@FindBy(xpath = "//span[@id='esign_Activity']")
	WebElement confirmationText1;
	@FindBy(xpath = "//table[@id='ListTab']//tbody//tr[1]//td[5]//input")
	WebElement enterAM;

	public CM_RecordMarks() {
		PageFactory.initElements(driver, this);
	}

	public void RecordMarks(HashMap<String, String> testData) {
		TimeUtil.shortWait();
		click2(menu, CommonStrings.LearnIQ_ICON_DC.getCommonStrings(), CommonStrings.LearnIQ_ICON_AC.getCommonStrings(),
				CommonStrings.LearnIQ_ICON_AR.getCommonStrings(), CommonStrings.LearnIQ_ICON_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(courseManagerMenu, CommonStrings.CM_Menus_DC.getCommonStrings(),
				CommonStrings.CM_Menus_AC.getCommonStrings(), CommonStrings.CM_Menus_AR.getCommonStrings(),
				CommonStrings.CM_Menus_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(recordMenu, CommonStrings.CM_RecordMenu_DC.getCommonStrings(),
				CommonStrings.CM_RecordMenu_AC.getCommonStrings(), CommonStrings.CM_RecordMenu_AR.getCommonStrings(),
				CommonStrings.CM_RecordMenu_SS.getCommonStrings());
		click2(marksMenu, RecordMarksStrings.RecordMarks_DC.getRecordMarksStrings(),
				RecordMarksStrings.RecordMarks_AC.getRecordMarksStrings(),
				RecordMarksStrings.RecordMarks_AR.getRecordMarksStrings(),
				RecordMarksStrings.RecordMarks_SS.getRecordMarksStrings());
		switchToBodyFrame(driver);
		click2(searchByNew, CommonStrings.SearchBy_DC.getCommonStrings(),
				RecordMarksStrings.SearchBy_AC.getRecordMarksStrings(),
				RecordMarksStrings.SearchBy_AR.getRecordMarksStrings(), CommonStrings.SearchBy_SS.getCommonStrings());
		click2(searchByBatchName, RecordMarksStrings.SelectBatchName_DC.getRecordMarksStrings(),
				CommonStrings.Selction_AC.getCommonStrings(), CommonStrings.Selction_AR.getCommonStrings(),
				RecordMarksStrings.SelectBatchName_SS.getRecordMarksStrings());
		sendKeys2(batchNameLike, RecordMarksStrings.Like_batchname_DC.getRecordMarksStrings(),
//				"CRSNewEZEX" + "%" ,CommonStrings.sendKeys_AC.getCommonStrings(),
				CM_Course.getCourse() + Constants.PERCENTAGE_SIGN, CommonStrings.sendKeys_AC.getCommonStrings(),
				CommonStrings.sendKeys_AR.getCommonStrings(),
				RecordMarksStrings.Like_batchname_DC.getRecordMarksStrings());
		click2(applyNew, CommonStrings.ApplyButton_DC.getCommonStrings(),
				CommonStrings.ApplyButton_AC.getCommonStrings(), CommonStrings.ApplyButton_AR.getCommonStrings(),
				CommonStrings.ApplyButton_SS.getCommonStrings());
		TimeUtil.mediumWait();
		click2(displayedRecord, RecordMarksStrings.Click_Record_DC.getRecordMarksStrings(),
				RecordMarksStrings.Click_Record_AC.getRecordMarksStrings(),
				RecordMarksStrings.Click_Record_AR.getRecordMarksStrings(),
				RecordMarksStrings.Click_Record_SS.getRecordMarksStrings());
		TimeUtil.mediumWait();
		String empID;
		for (int i = 0; i < 4; i++) {
			if (i == 0) {
				empID = testData.get("QemployeeID1").trim();
				WebElement employee = driver.findElement(
						By.xpath("//td[normalize-space()='" + empID + "']/following-sibling::td[3]/input"));
				TimeUtil.shortWait();
				scrollToViewElement(employee);
				TimeUtil.shortWait();
				sendKeysAndRemoveFocus(employee, RecordMarksStrings.Enter_AQMarks_DC.getRecordMarksStrings(),
						testData.get("aquiredMarksQualified"),
						RecordMarksStrings.Enter_AQMarks_AC.getRecordMarksStrings(),
						RecordMarksStrings.Enter_AQMarks_AR.getRecordMarksStrings(),
						RecordMarksStrings.Enter_AQMarks_SS.getRecordMarksStrings());
			}

			else if (i == 1) {
				empID = testData.get("TBRemployeeID2").trim();
				WebElement employee = driver.findElement(
						By.xpath("//td[normalize-space()='" + empID + "']/following-sibling::td[3]/input"));
				TimeUtil.shortWait();
				scrollToViewElement(employee);
				TimeUtil.shortWait();
				sendKeysAndRemoveFocus(employee, RecordMarksStrings.Enter_AQMarks_DC.getRecordMarksStrings(),
						testData.get("aquiredMarksRetrained"),
						RecordMarksStrings.Enter_AQMarks_AC.getRecordMarksStrings(),
						RecordMarksStrings.Enter_AQMarks_AR.getRecordMarksStrings(),
						RecordMarksStrings.Enter_AQMarks_SS.getRecordMarksStrings());
			}

//			else if (i == 2) {
//				empID = testData.get("employeeID3").trim();
//				WebElement employee = driver.findElement(
//						By.xpath("//td[normalize-space()='" + empID + "']/following-sibling::td[3]/input"));
//				TimeUtil.shortWait();
//				scrollToViewElement(employee);
//				TimeUtil.shortWait();
//				sendKeysAndRemoveFocus(employee, RecordMarksStrings.Enter_AQMarks_DC.getRecordMarksStrings(),
//						testData.get("aquiredMarksQualified"), RecordMarksStrings.Enter_AQMarks_AC.getRecordMarksStrings(),
//						RecordMarksStrings.Enter_AQMarks_AR.getRecordMarksStrings(),
//						RecordMarksStrings.Enter_AQMarks_SS.getRecordMarksStrings());
//			}
//
//			else if (i == 3) {
//				empID = testData.get("employeeID4").trim();
//				WebElement employee = driver.findElement(
//						By.xpath("//td[normalize-space()='" + empID + "']/following-sibling::td[3]/input"));
//				TimeUtil.shortWait();
//				scrollToViewElement(employee);
//				TimeUtil.shortWait();
//				sendKeysAndRemoveFocus(employee, RecordMarksStrings.Enter_AQMarks_DC.getRecordMarksStrings(),
//						testData.get("aquiredMarksRetrained"), RecordMarksStrings.Enter_AQMarks_AC.getRecordMarksStrings(),
//						RecordMarksStrings.Enter_AQMarks_AR.getRecordMarksStrings(),
//						RecordMarksStrings.Enter_AQMarks_SS.getRecordMarksStrings());
//			}
//
//		}
		}
		TimeUtil.shortWait();
		scrollToViewElement(submitBtn);
		click2(submitBtn, TopicStrings.Submit_DC.getTopicStrings(),
				RecordMarksStrings.Submit_AC.getRecordMarksStrings(),
				RecordMarksStrings.Submit_AR.getRecordMarksStrings(), TopicStrings.Submit_SS.getTopicStrings());
		TimeUtil.shortWait();
		sendKeys2(esign_psw, CommonStrings.Password_DC.getCommonStrings(), CM_QuestionPaper.EvaluatorPassword,
				CommonStrings.Password_AC.getCommonStrings(), CommonStrings.Password_AR.getCommonStrings(),
				CommonStrings.Password_SS.getCommonStrings());
		TimeUtil.shortWait();
		click2(proceed, CommonStrings.Esign_Proceed_DC.getCommonStrings(),
				RecordMarksStrings.Esign_RecordMarks_Proceed_AC.getRecordMarksStrings(),
				RecordMarksStrings.Esign_RecordMarks_Proceed_AR.getRecordMarksStrings(),
				CommonStrings.Esign_Proceed_SS.getCommonStrings());
		switchToDefaultContent(driver);
	}

}
