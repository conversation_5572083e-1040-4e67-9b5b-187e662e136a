package com.Automation.learniqCRITICALScenarios.PreRequisites310;

import java.util.HashMap;

import org.testng.annotations.AfterTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.Automation.Utils.ConfigsReader;
import com.Automation.Utils.Constants;
import com.Automation.Utils.ExcelUtilUpdated;
import com.Automation.Utils.MyScreenRecorder;
import com.Automation.learniqBase.*;


public class SSO_UserRegistrationPrerequisites extends OQActionEngine {

	String ExcelPath = "./learnIQTestData/SSO_UserRegistrationPrerequisites.xlsx";

//

	public SSO_UserRegistrationPrerequisites() {

		super(ConfigsReader.getPropValue("SSOUrl"));
	}


	ExcelUtilUpdated excel1 = new ExcelUtilUpdated(ExcelPath, "USREG");

	@DataProvider(name = "RegistrationUser")
	public Object[][] getRegistrationUser() throws Exception {
		Object[][] obj = new Object[excel1.getRowCount()][1];
		for (int i = 1; i <= excel1.getRowCount(); i++) {
			HashMap<String, String> testData = excel1.getTestDataInMap(i);
			obj[i - 1][0] = testData;
		}
		return obj;
		
	}

	@Test(priority = 0, dataProvider = "RegistrationUser", enabled = true)
	public void userReg(HashMap<String, String> testData) {
		test = extent.createTest("Pre-Requisite: 1.0 User Registration Approval with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre-Requisite: 1.0 User Registration Approval with Audit Trails");
		epiclogin.loginToSSOApplication(ConfigsReader.getPropValue("company"), ConfigsReader.getPropValue("SSOUserID"),
				ConfigsReader.getPropValue("SSOPassword"));
		UserReg.sso_UserRegistration(testData);
		test = extent.createTest("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails")
				.assignAuthor(ConfigsReader.getPropValue("SSOUserID"))
				.assignCategory("Pre Requisite: 2.0 User Product / Module Assignment with Audit Trails");
		UserProductModuleAssignment.UserProductModuleAssignment_With_AuditTrails(testData);
		Logout.SSOsignOutPage();
		n = 0;
		screenshotCounter = 0;

	}

}